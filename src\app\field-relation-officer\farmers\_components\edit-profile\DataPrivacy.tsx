import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Link1Icon } from '@radix-ui/react-icons';
import Link from 'next/link';
import React, { useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import SignatureCanvas from 'react-signature-canvas';

import FormTitle from '@/components/common/forms/form-title';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { dataPrivacySchema, TDataPrivacySchema } from '@/app/field-relation-officer/_components/schemas';
import { IDataPrivacy, IFarmerDataPrivacy, ISafeParseResult } from '@/app/field-relation-officer/_components/types';
import useFarmer from '@/lib/hooks/fro/useFarmer';
import { base64toFile, cn } from '@/lib/utils';

interface IDataPrivacyProps {
  data: IFarmerDataPrivacy;
}

const DataPrivacy = ({ data }: IDataPrivacyProps) => {
  const sigCanvasRef = useRef<SignatureCanvas>(null);
  const { updateFarmer } = useFarmer();
  const [previewUserImage, setPreviewUserImage] = useState<string | null>(data?.user_img || null);
  const [hasDrawnSignature, setHasDrawnSignature] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues: values,
    setValue,
  } = useForm<TDataPrivacySchema>({
    resolver: zodResolver(dataPrivacySchema),
    defaultValues: {
      isAgreeUsingData: !!(data && data.is_agree_using_data === 1),
      isAgreeVisitingFarm: !!(data && data.is_agree_visiting_farm === 1),
      isAgreeSharingData: !!(data && data.is_agree_sharing_data === 1),
      userImage: null,
      signature: null,
    },
  });

  const handleSignatureEnd = async () => {
    const base64 = sigCanvasRef.current?.toDataURL('image/png');
    const signatureFile = await base64toFile(base64, 'signature.png');
    setValue('signature', signatureFile);
    setHasDrawnSignature(true);
  };

  const handleClearSignature = () => {
    sigCanvasRef.current?.clear();
    setValue('signature', null);
  };

  const formSubmit = handleSubmit(async () => {
    const { data, success } = dataPrivacySchema.safeParse(values()) as ISafeParseResult<IDataPrivacy>;

    if (success) {
      await updateFarmer(data);
      console.log('data privacy:', data);
    }
  });

  return (
    <div>
      <FormTitle title="Consent Declaration and Data Privacy Policy Statement" />
      <form onSubmit={formSubmit}>
        <div className="flex flex-col gap-8">
          <div className="flex flex-col gap-6">
            <div className="flex items-start gap-3">
              <input type="checkbox" {...register('isAgreeUsingData', { required: true })} id="isAgreeUsingData" />
              <p className="text-sm">
                Sumasang-ayon ako na maaaring gamitin ng{' '}
                <strong>KITA AGRITECH CORPORATION (&quot;KITA AGRITECH&quot;)</strong> ang impormasyong ito para sa
                pagpaparehistro ko sa Farmer Kita Program, alinsunod sa Data Privacy Policy Statement na nasa ibaba.
              </p>
            </div>
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                {...register('isAgreeVisitingFarm', { required: true })}
                id="isAgreeVisitingFarm"
              />
              <p className="text-sm">
                Pinapayagan ko ang <strong>KITA AGRITECH</strong> na bumisita sa aking sakahan at ako ay magbibigay ng
                karagdagang mga dokumento, at pahintulot sa pagkuha ng mga larawan at bidyo, upang beripikahin ang
                impormasyong aking inilagay sa form na ito.
              </p>
            </div>
            <div className="flex items-start gap-3">
              <input type="checkbox" {...register('isAgreeSharingData', { required: true })} id="isAgreeSharingData" />
              <p className="text-sm">
                Pinapayagan ko ang <strong>KITA AGRITECH</strong> na ibahagi ang mga impormasyong aking ibinigay sa mga
                Partner Financial Institutions nito kung sakali man na ako ay mag-apply para sa Farm Production Loan
                bilang bahagi ng KYC procedures nito.
              </p>
            </div>
            {(errors.isAgreeUsingData || errors.isAgreeVisitingFarm || errors.isAgreeSharingData) && (
              <p className="text-sm text-red-500">Lahat ng checkbox ay kailangang i-check para makapagpatuloy.</p>
            )}
          </div>

          <div className="mx-auto grid w-full items-center gap-3 text-center">
            <Label htmlFor="userImage">
              Take a photo of the farmer holding the signed Consent and Data Privacy Statement.
            </Label>
            <Input
              id="userImage"
              type="file"
              className="mx-auto max-w-sm"
              {...register('userImage')}
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  const previewUrl = URL.createObjectURL(file);
                  setPreviewUserImage(previewUrl);
                }
              }}
            />
            {errors.userImage && <p className="text-sm text-red-500">{errors.userImage.message}</p>}
          </div>

          <div className={cn('justify-center gap-4 hidden', (data?.user_img || data?.signature) && 'flex')}>
            {data?.user_img && (
              <Link
                href={data?.user_img}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm font-bold text-blue-600"
              >
                <Link1Icon /> Farmer Photo
              </Link>
            )}

            {data?.signature && !hasDrawnSignature && (
              <Link
                href={data?.signature}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm font-bold text-blue-600"
              >
                <Link1Icon /> Signature
              </Link>
            )}
          </div>
          <Card>
            <CardContent className="space-y-4 p-4">
              {errors.signature && <p className="text-sm text-red-500">{errors.signature.message}</p>}
              <div className="rounded-md border border-gray-300">
                <SignatureCanvas
                  ref={sigCanvasRef}
                  penColor="black"
                  canvasProps={{ className: 'w-full h-40 rounded-md' }}
                  onEnd={handleSignatureEnd}
                />
              </div>
              <div className="flex gap-2">
                <Button variant="outline" type="button" onClick={handleClearSignature}>
                  Clear
                </Button>
              </div>
            </CardContent>
          </Card>
          <div className="mt-8 flex justify-end gap-4">
            <Button type="submit">Save</Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default DataPrivacy;
