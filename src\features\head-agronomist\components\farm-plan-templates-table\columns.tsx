'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { PencilLineIcon } from 'lucide-react';
import Link from 'next/link';
import { MdOutlineContentPasteSearch } from 'react-icons/md';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import { IFarmPlanTemplate } from '../../types/farmplan-templates';

const Action = ({ row }: { row: any }) => {
  const data: IFarmPlanTemplate = row.original;

  return (
    <div className="flex items-center justify-end gap-2">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" variant="outline" size="sm" asChild>
            <Link href={`/head-agronomist/edit/?id=${data.id}`}>
              <PencilLineIcon className="size-4" />
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Edit</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" variant="outline" size="sm" asChild>
            <Link href={`/head-agronomist/audit-logs?id=${data.id}`}>
              <MdOutlineContentPasteSearch className="size-4" />
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Audit Logs</p>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};

export const ActionHeader = () => {
  return (
    <div className="flex items-center justify-end gap-2">
      <Button className="h-8" asChild>
        <Link href="/head-agronomist/create">Create Master Template</Link>
      </Button>
    </div>
  );
};

export const columns: ColumnDef<IFarmPlanTemplate>[] = [
  {
    id: 'crop_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Crop Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.crop.name}</div>;
    },
    accessorFn: (row) => row.crop.name,
  },
  {
    id: 'service_area',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Service Area" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.location}</div>;
    },
    accessorFn: (row) => row.location,
  },
  {
    id: 'version_number',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Version No." />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.version_number}</div>;
    },
    accessorFn: (row) => row.version_number,
  },
  {
    id: 'date_time_modified',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Modified" />,
    cell: ({ row }) => {
      const data = row.original;
      const dateValue = data.updated_at;
      const date = dateValue ? new Date(dateValue) : null;
      if (!date || isNaN(date.getTime())) {
        return ' ';
      }

      return <div className="min-w-max">{`${format(date, 'dd MMM yyyy')} | ${format(date, 'hh:mm a')}`}</div>;
    },
    accessorFn: (row) => {
      const date = new Date(row.updated_at);
      return format(date, 'dd MMM yyyy');
    },
  },
  {
    id: 'date_time_created',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Created" />,
    cell: ({ row }) => {
      const data = row.original;
      const dateValue = data.created_at;
      const date = dateValue ? new Date(dateValue) : null;
      if (!date || isNaN(date.getTime())) {
        return ' ';
      }

      return <div className="min-w-max">{`${format(date, 'dd MMM yyyy')} | ${format(date, 'hh:mm a')}`}</div>;
    },
    accessorFn: (row) => {
      const date = new Date(row.created_at);
      return format(date, 'dd MMM yyyy');
    },
  },
  {
    id: 'created_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Created by" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {`${data.agronomistUser.agronomist.first_name} ${data.agronomistUser.agronomist.last_name}`}
        </div>
      );
    },
    accessorFn: (row) => `${row.agronomistUser.agronomist.first_name} ${row.agronomistUser.agronomist.last_name}`,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];
