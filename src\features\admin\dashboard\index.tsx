'use client';

import Link from 'next/link';

import { Button } from '@/components/ui/button';

import { CropPrice } from './components/crop-price';
import { LoadingTable } from './components/loading-table';
import { MarketplaceSalesSummary } from './components/marketplace-sales-summary';
import { MarketplaceTopProductsTable } from './components/marketplace-top-products-table';
import { columnsMarketplaceTopProducts } from './components/marketplace-top-products-table/columns';
import { NewRegisteredAccounts } from './components/new-registered-accounts';
import SelectDate from './components/select-date';
import { TopTransactionValueTable } from './components/top-transaction-value-table';
import { columnsTopTransactionValue } from './components/top-transaction-value-table/columns';
import { TotalRegisteredAccounts } from './components/total-registered-accounts';
import { TradingSalesSummary } from './components/tradingpost-sales-summary';
import { TradingPostTopCropsTable } from './components/tradingpost-top-crops-table';
import { columnsTradingPostTopCrops } from './components/tradingpost-top-crops-table/columns';
import useDashboard from './hooks/useDashboard';

export default function Dashboard() {
  const { marketplaceTopProductsQuery, tradingPostTopCropsQuery, topTransactionValueQuery } = useDashboard();

  return (
    <div className="grid gap-6 p-6 lg:p-8">
      <div className="flex justify-end">
        <SelectDate />
      </div>

      <div className="flex flex-col gap-6 2xl:flex-row">
        <div className="card 2xl:flex-1">
          <h2 className="font-poppins font-semibold text-adminDashboard-title">Sales Summary</h2>

          <div className="mt-6 flex flex-col gap-6 sm:flex-row">
            <TradingSalesSummary />
            <MarketplaceSalesSummary />
          </div>
        </div>

        <div className="card 2xl:w-[46%] 3xl:w-2/5">
          <h2 className="font-poppins font-semibold text-adminDashboard-title">Registered Accounts</h2>

          <div className="mt-6 flex flex-col gap-6 sm:flex-row">
            <NewRegisteredAccounts />
            <TotalRegisteredAccounts />
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-6 2xl:flex-row">
        <div className="grid gap-6 2xl:flex-1">
          <div className="card">
            <div className="flex items-center justify-between gap-4">
              <h2 className="font-poppins font-semibold text-adminDashboard-title">
                Top-Selling Products in the Marketplace
              </h2>

              <Button
                variant="secondary"
                className="h-8 rounded-full bg-[#F4F7FE] text-xs font-bold text-[#4318FF] hover:bg-[#E0E3FF]"
                asChild
              >
                <Link href="/admin/marketplace-top-products">See All</Link>
              </Button>
            </div>

            {marketplaceTopProductsQuery.isLoading ? (
              <LoadingTable />
            ) : (
              <MarketplaceTopProductsTable
                columns={columnsMarketplaceTopProducts}
                data={marketplaceTopProductsQuery.data}
              />
            )}
          </div>

          <div className="card">
            <div className="flex items-center justify-between gap-4">
              <h2 className="font-poppins font-semibold text-adminDashboard-title">
                Top-Selling Crops in the Trading Post
              </h2>

              <Button
                variant="secondary"
                className="h-8 rounded-full bg-[#F4F7FE] text-xs font-bold text-[#4318FF] hover:bg-[#E0E3FF]"
              >
                <Link href="/admin/tradingpost-top-crops">See All</Link>
              </Button>
            </div>

            {tradingPostTopCropsQuery.isLoading ? (
              <LoadingTable />
            ) : (
              <TradingPostTopCropsTable columns={columnsTradingPostTopCrops} data={tradingPostTopCropsQuery.data} />
            )}
          </div>
        </div>

        <div className="card 2xl:w-[46%] 3xl:w-2/5">
          <div className="flex items-center justify-between gap-4">
            <h2 className="font-poppins font-semibold text-adminDashboard-title">Top Transactions Value</h2>

            <Button
              variant="secondary"
              className="h-8 rounded-full bg-[#F4F7FE] text-xs font-bold text-[#4318FF] hover:bg-[#E0E3FF]"
              asChild
            >
              <Link href="/admin/top-transactions-value">See All</Link>
            </Button>
          </div>

          {topTransactionValueQuery.isLoading ? (
            <LoadingTable />
          ) : (
            <TopTransactionValueTable columns={columnsTopTransactionValue} data={topTransactionValueQuery.data} />
          )}
        </div>
      </div>

      <CropPrice />
    </div>
  );
}
