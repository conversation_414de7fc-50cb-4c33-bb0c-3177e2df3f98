'use client';

import { useHookstate } from '@hookstate/core';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { catchError } from '@/lib/utils';

import useShuruUtils from '../utils/useShuruUtils';

export default function useShuruCreditScoring() {
  const gStateP = useGlobalStatePersist();
  const gState = useGlobalState();
  const shuruData = useShuruUtils();
  const router = useRouter();

  const group = useHookstate(gState.admin.creditScoreMgt.groups.selected);
  const farmer = useHookstate(gState.shuruCreditScoring.farmer);
  const rules = useHookstate(gState.shuruCreditScoring.rules);

  const beforeLoanRules = useHookstate(gState.shuruCreditScoring.beforeRules);
  const beforeDetails = useHookstate(gState.shuruCreditScoring.beforeDetails);
  const duringDetails = useHookstate(gState.shuruCreditScoring.duringDetails);

  const getRules = async (creditScoreGroupId: number) => {
    try {
      const _data = await axios.get(`/admin/creditscoring/rules/${creditScoreGroupId}`).then((res) => res.data);

      if (_data?.status_code === 200) {
        const _res = _data.response;
        rules.set(_res);

        // before
        shuruData.setupBeforeAccountProfile();
        shuruData.setupBeforeAgricultureActivity();
        shuruData.setupBeforeTransactionRecords();

        // during
        shuruData.setupDuringAgricultureActivity();
        shuruData.setupDuringTransactionRecords();

        // after
        shuruData.setupAfterCreditHistory();
        shuruData.setupAfterAgricultureActivity();
      }
    } catch (e) {
      catchError(e, 'getRules');
      if (e?.response?.data?.message === 'Financing company not found') {
        router.push('/admin/credit-score-management');
      }
    }
  };

  const getCreditScoreByFarmer = async (farmerId: number | string) => {
    try {
      const _data = await axios
        .get(
          `/${getUserType(gStateP['user']['user']['user_type'].value)}/creditscoring/credit-score/${group.value}/${farmerId}`,
        )
        .then((res) => res.data);
      console.log('getCreditScoreByFarmer', _data.response);

      if (_data?.status_code === 200) {
        farmer.set(_data.response);
      }
    } catch (e) {
      catchError(e, 'getCreditScoreByFarmer');
    }
  };

  const updateRules = async (creditScoreGroupId: number, data: any) => {
    try {
      const _res = await axios
        .put(`/admin/creditscoring/update-rules/${creditScoreGroupId}`, data)
        .then((res) => res.data);
      console.log('updateRules: ', _res);
      await getRules(creditScoreGroupId);

      toast.success('Success', {
        description: 'Rules updated successfully',
      });
    } catch (e) {
      catchError(e, 'updateRules');
    }
  };

  return { getRules, getCreditScoreByFarmer, duringDetails, farmer, beforeLoanRules, beforeDetails, updateRules };
}
