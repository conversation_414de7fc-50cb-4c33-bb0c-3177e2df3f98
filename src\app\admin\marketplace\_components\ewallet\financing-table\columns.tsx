'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import {
  FulfillmentTypeLabels,
  OrderStatusLabels,
  RequestStatusLabels,
  TransactionTypeLabels,
} from '@/app/admin/marketplace/_components/Enums';
import { getUserType } from '@/lib/constants';
import { cn } from '@/lib/utils';

export const columns = [
  {
    id: 'transaction_type',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Type" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex">
          <div className={cn('text-white px-4 py-2 text-xs', TransactionTypeLabels[data.type].color)}>
            {TransactionTypeLabels[data.type].label}
          </div>
        </div>
      );
    },
  },
  {
    id: 'date_time',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${new Date(data.created_at).toLocaleDateString('en-US', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })} | ${new Date(data.created_at).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      const data = row.original;
      return `${new Date(data.created_at).toLocaleDateString('en-US', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })}`;
    },
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Badge className={RequestStatusLabels[data.topupRequest.status].color}>
          {RequestStatusLabels[data.topupRequest.status].label}
        </Badge>
      );
    },
    accessorFn: (row) => {
      return `${RequestStatusLabels[row.topupRequest.status].label}`;
    },
  },
  {
    id: 'amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${Number(data.amount).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      return `${row.total_price}`;
    },
  },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    accessorFn: (row) => {
      const { processedBy } = row.topupRequest;
      return `${processedBy.finance ? `${processedBy.finance.first_name} ${processedBy.finance.last_name}` : processedBy.email}`;
    },
  },
];
