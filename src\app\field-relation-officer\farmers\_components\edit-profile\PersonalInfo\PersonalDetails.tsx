import { useEffect } from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  UseFormRegister,
  UseFormReturn,
  UseFormSetValue,
  UseFormWatch,
} from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { CIVIL_STATUS } from '@/app/field-relation-officer/_components/constants';
import { TPersonalInformationSchema } from '@/app/field-relation-officer/_components/schemas';
import { NATIONALITIES } from '@/lib/constants/nationality';
import { cn } from '@/lib/utils';

interface IPersonalDetailsProps {
  register: UseFormRegister<TPersonalInformationSchema>;
  control: Control<TPersonalInformationSchema>;
  errors: FieldErrors<TPersonalInformationSchema>;
  setValue: UseFormSetValue<TPersonalInformationSchema>;
  watch: UseFormWatch<TPersonalInformationSchema>;
}

const PersonalDetails = ({ register, control, errors, watch, setValue }: IPersonalDetailsProps) => {
  const civilStatus = watch('civilStatus');

  useEffect(() => {
    if (civilStatus !== 'MARRIED') {
      setValue('spouseName', '');
      setValue('spouseMobileNumber', '');
    }
  }, [civilStatus, setValue]);

  return (
    <div>
      <FormTitle title="Personal Information" />
      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        <FormField name="firstName" label="First Name" errors={errors} required>
          <Input
            {...register('firstName', { required: 'First Name is required' })}
            placeholder="Enter First Name"
            className={cn('focus-visible:ring-primary', errors.firstName && 'border-red-500')}
          />
        </FormField>

        <FormField name="middleName" label="Middle Name" errors={errors}>
          <Input
            {...register('middleName')}
            placeholder="Enter Middle Name"
            className={cn('focus-visible:ring-primary', errors.middleName && 'border-red-500')}
          />
        </FormField>

        <FormField name="lastName" label="Last Name" errors={errors} required>
          <Input
            {...register('lastName', { required: 'Last Name is required' })}
            placeholder="Enter Last Name"
            className={cn('focus-visible:ring-primary', errors.lastName && 'border-red-500')}
          />
        </FormField>

        <FormField name="birthDate" label="Birth Date" errors={errors} required>
          <Input
            {...register('birthDate', { required: 'Birth Date is required' })}
            type="date"
            className={cn('focus-visible:ring-primary', errors.birthDate && 'border-red-500')}
          />
        </FormField>

        <FormField name="placeOfBirth" label="Place of Birth" errors={errors}>
          <Input
            {...register('placeOfBirth')}
            placeholder="Enter Place of Birth"
            className={cn('focus-visible:ring-primary', errors.placeOfBirth && 'border-red-500')}
          />
        </FormField>

        <FormField name="gender" label="Gender" errors={errors} required>
          <Controller
            control={control}
            name="gender"
            rules={{ required: 'Gender is required' }}
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger className={cn(errors.gender && 'border-red-500')}>
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="MALE">Male</SelectItem>
                    <SelectItem value="FEMALE">Female</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        <FormField name="nationality" label="Nationality" errors={errors} required>
          <Controller
            control={control}
            name="nationality"
            rules={{ required: 'Nationality is required' }}
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger className={cn(errors.nationality && 'border-red-500')}>
                  <SelectValue placeholder="Select nationality" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {NATIONALITIES.map((nat, idx) => (
                      <SelectItem key={idx} value={nat}>
                        {nat}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        <FormField name="email" label="Email address" errors={errors}>
          <Input
            {...register('email')}
            placeholder="Enter Email"
            type="text"
            className={cn('focus-visible:ring-primary', errors.email && 'border-red-500')}
          />
        </FormField>

        <FormField name="mobileNumber" label="Mobile number" errors={errors} required>
          <Input
            {...register('mobileNumber', { required: 'Mobile number is required' })}
            placeholder="Enter number"
            className={cn(errors.mobileNumber && 'border-red-500')}
          />
        </FormField>

        <FormField name="telephoneNumber" label="Telephone Number" errors={errors}>
          <Input
            {...register('telephoneNumber')}
            placeholder="Enter Telephone Number"
            className={cn(errors.telephoneNumber && 'border-red-500')}
          />
        </FormField>

        <FormField name="facebookName" label="Facebook Name" errors={errors}>
          <Input
            {...register('facebookName')}
            placeholder="Enter Facebook name"
            className={cn(errors.facebookName && 'border-red-500')}
          />
        </FormField>

        <FormField name="civilStatus" label="Civil Status" errors={errors}>
          <Controller
            control={control}
            name="civilStatus"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger className={cn(errors.civilStatus && 'border-red-500')}>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {CIVIL_STATUS.map((option, idx) => (
                      <SelectItem key={idx} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {civilStatus === 'MARRIED' && (
          <>
            <FormField name="spouseName" label="Spouse Name" errors={errors}>
              <Input
                {...register('spouseName')}
                placeholder="Enter Spouse name"
                className={cn(errors.spouseName && 'border-red-500')}
              />
            </FormField>
            <FormField name="spouseMobileNumber" label="Spouse mobile number " errors={errors}>
              <Input
                {...register('spouseMobileNumber')}
                placeholder="Enter Spouse mobile number"
                className={cn(errors.spouseMobileNumber && 'border-red-500')}
              />
            </FormField>
          </>
        )}
      </div>
    </div>
  );
};

export default PersonalDetails;
