import { ETemplateItemType } from '@/features/head-agronomist/types/farmplan-templates';

// Template sub-item interface
export interface ITemplateSubItem {
  farmPlanTemplateSubItemId?: number;
  farmPlanSubItemId?: number;
  expectedDate: string | Date;
  itemName: string;
  unit: string;
  quantity: number;
  unitCost: number;
  notes?: string;
  marketplaceProductId?: number;
  totalAmount?: number; // For UI calculation only
}

// Template item interface
export interface ITemplateItem {
  farmPlanTemplateItemId?: number;
  farmPlanItemId?: number;
  name: string;
  type: ETemplateItemType;
  subItems: ITemplateSubItem[];
}

// Main template interface
export interface ITemplateForm {
  cropId: number;
  versionNumber: string;
  location: string;
  contingencyForFluctuation: number;
  interestRate: number;
  numberOfMonthsPerTenor: number;
  aorPerMonth: number;
  items: ITemplateItem[];
}
