'use client';

export let LEFT_TAB = [
  {
    name: 'Credit Score',
    value: 'credit_score',
  },
  {
    name: 'Account Profile',
    value: 'account_profile',
  },
  {
    name: 'Transactions',
    value: 'transactions',
  },
  {
    name: 'Credit History',
    value: 'credit_history',
  },
  {
    name: 'Agriculture Activity',
    value: 'agriculture_activity',
  },
];

export const TRANSACTION_TAB = [
  {
    name: 'Trading Post',
    value: 'trading_post',
  },
  {
    name: 'Marketplace',
    value: 'marketplace',
  },
  {
    name: 'Sales Transaction',
    value: 'sales_transaction',
  },
];

export const PROFILE_TAB = [
  {
    name: 'Basic Info',
    value: 'basic_info',
  },
  {
    name: 'Career & Academic',
    value: 'career_academic',
  },
  {
    name: 'Identification Docs',
    value: 'identification_docs',
  },
  {
    name: 'Family & References',
    value: 'family_profile',
  },
  {
    name: 'Property Ownership',
    value: 'property_ownership',
  },
  {
    name: 'Farm Details',
    value: 'farm_details',
  },
  {
    name: 'Crop Insurance',
    value: 'crop_insurance',
  },
  {
    name: 'Landbank Reqts',
    value: 'landbank_reqts',
  },
];

export const CREDIT_TAB = [
  {
    name: 'Loan Availed',
    value: 'loan_availed',
  },
  {
    name: 'Loan Payments',
    value: 'loan_payments',
  },
  {
    name: 'Loan History',
    value: 'loan_history',
  },
];

export const BANK_NAMES = [
  'BDO UNIBANK INC',
  'LAND BANK OF THE PHILIPPINES',
  'METROPOLITAN BANK & TCO',
  'BANK OF THE PHIL ISLANDS',
  'CHINA BANKING CORP',
  "RIZAL COMM'L BANKING CORP",
  'PHIL NATIONAL BANK',
  'SECURITY BANK CORP',
  'UNION BANK OF THE PHILS',
  'DEVELOPMENT BANK OF THE PHIL',
  'EAST WEST BANKING CORP',
  'ASIA UNITED BANK CORPORATION',
  'CITIBANK, N.A.',
  'HONGKONG & SHANGHAI BANKING CORP',
  'BANK OF COMMERCE',
  'ROBINSONS BANK CORPORATION',
  'PHIL TRUST COMPANY',
  'PHIL BANK OF COMMUNICATIONS',
  'MUFG BANK, LTD.',
  'MAYBANK PHILIPPINES INCORPORATED',
  'MIZUHO BANK LTD - MANILA BRANCH',
  'STANDARD CHARTERED BANK',
  'CTBC BANK (PHILIPPINES) CORP',
  'JP MORGAN CHASE BANK NATIONAL ASSN.',
  'BANK OF CHINA (HONGKONG) LIMITED-MANILA BRANCH',
  'PHILIPPINE VETERANS BANK',
  'DEUTSCHE BANK AG',
  'AUSTRALIA AND NEW ZEALAND BANKING GROUP LIMITED',
  'SUMITOMO MITSUI BANKING CORPORATION-MANILA BRANCH',
  'CIMB BANK PHILIPPINES INC',
  'BDO PRIVATE BANK, INC.',
  'BANK OF AMERICA N.A.',
  'KEB HANA BANK - MANILA BRANCH',
  'ING BANK N.V.',
  "MEGA INT'L COMM'L BANK CO LTD",
  'BANGKOK BANK PUBLIC COMPANY LIMITED',
  'SHINHAN BANK - MANILA BRANCH',
  'INDUSTRIAL AND COMMERCIAL BANK OF CHINA LIMITED - MANILA BRANCH',
  'INDUSTRIAL BANK OF KOREA MANILA BRANCH',
  'HUA NAN COMMERCIAL BANK LTD MANILA BRANCH',
  'CATHAY UNITED BANK CO LTD - MANILA BRANCH',
  'CHANG HWA COMMERCIAL BANK LTD - MANILA BRANCH',
  'UNITED OVERSEAS BANK LIMITED MANILA BRANCH',
  'FIRST COMMERCIAL BANK LTD MANILA BRANCH',
  'AL-AMANAH ISLAMIC INVESTMENT BANK OF THE PHILS',
];

export enum LandbankRequirementType {
  LBP_REGISTRATION_FORM = 'LBP_REGISTRATION_FORM',
  KITA_REGISTRATION_FORM = 'KITA_REGISTRATION_FORM',
  CERTIFIED_TRUE_COPY_OF_LAND_TITLE = 'CERTIFIED_TRUE_COPY_OF_LAND_TITLE',
  MARO_CERTIFICATE_FOR_ARBS = 'MARO_CERTIFICATE_FOR_ARBS',
  REGISTRATION_WITH_RSBA_OR_ENROLLMENT_SIGNED_BY_MAO = 'REGISTRATION_WITH_RSBA_OR_ENROLLMENT_SIGNED_BY_MAO',
  ENDORSEMENT_BY_DAR = 'ENDORSEMENT_BY_DAR',
  PROOF_OF_ATTENDANCE_ON_SEMINAR = 'PROOF_OF_ATTENDANCE_ON_SEMINAR',
  BARANGAY_CLEARANCE_CERTIFICATE = 'BARANGAY_CLEARANCE_CERTIFICATE',
  CIS_FORM = 'CIS_FORM',
  FARM_PLAN = 'FARM_PLAN',
  CO_BORROWER_FORM = 'CO_BORROWER_FORM',
  TRIPARTITE_AGREEMENT = 'TRIPARTITE_AGREEMENT',
  PRODUCTION_TECHNICAL_MARKETING_AGREEMENT = 'PRODUCTION_TECHNICAL_MARKETING_AGREEMENT',
  GEO_TAGGING = 'GEO_TAGGING',
  CROP_INSURANCE_FORM = 'CROP_INSURANCE_FORM',
  MANAGEMENT_SERVICES_AGREEMENT = 'MANAGEMENT_SERVICES_AGREEMENT',
}

export const LandbankRequirementLabels: Record<LandbankRequirementType, string> = {
  [LandbankRequirementType.LBP_REGISTRATION_FORM]: 'LBP Registration Form',
  [LandbankRequirementType.KITA_REGISTRATION_FORM]: 'KITA Registration Form',
  [LandbankRequirementType.CERTIFIED_TRUE_COPY_OF_LAND_TITLE]: 'Certified True Copy of Land Title',
  [LandbankRequirementType.MARO_CERTIFICATE_FOR_ARBS]: 'MARO Certificate for ARBs',
  [LandbankRequirementType.REGISTRATION_WITH_RSBA_OR_ENROLLMENT_SIGNED_BY_MAO]:
    'Registration with RSBA or Enrollment Signed by MAO',
  [LandbankRequirementType.ENDORSEMENT_BY_DAR]: 'Endorsement by DAR',
  [LandbankRequirementType.PROOF_OF_ATTENDANCE_ON_SEMINAR]: 'Proof of Attendance on Seminar',
  [LandbankRequirementType.BARANGAY_CLEARANCE_CERTIFICATE]: 'Barangay Clearance Certificate',
  [LandbankRequirementType.CIS_FORM]: 'CIS Form',
  [LandbankRequirementType.FARM_PLAN]: 'Farm Plan',
  [LandbankRequirementType.CO_BORROWER_FORM]: 'Co-Borrower Form',
  [LandbankRequirementType.TRIPARTITE_AGREEMENT]: 'Tripartite Agreement',
  [LandbankRequirementType.PRODUCTION_TECHNICAL_MARKETING_AGREEMENT]: 'Production Technical Marketing Agreement',
  [LandbankRequirementType.GEO_TAGGING]: 'Geo Tagging',
  [LandbankRequirementType.CROP_INSURANCE_FORM]: 'Crop Insurance Form',
  [LandbankRequirementType.MANAGEMENT_SERVICES_AGREEMENT]: 'Management Services Agreement',
};
