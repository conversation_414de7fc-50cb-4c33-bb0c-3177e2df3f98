'use client';

import { useRouter } from 'next/navigation';
import { FC } from 'react';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

import { LoadingTable } from '../../components/loading-table';
import { TopTransactionsValueTable } from './components/top-transactions-value-table';
import { columnsTopTransactionsValue } from './components/top-transactions-value-table/columns';
import useTopTransactionsValue from './hooks/useTopTransactionsValue';

const TopTransactionsValue: FC = () => {
  const router = useRouter();
  const { topTransactionValueQuery } = useTopTransactionsValue();

  return (
    <div className="p-6 lg:p-8">
      <div className="pb-4">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Top Transactions Value</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink onClick={() => router.replace('/admin')}>Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Top Transactions Value</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {topTransactionValueQuery.isLoading ? (
        <LoadingTable />
      ) : (
        <TopTransactionsValueTable columns={columnsTopTransactionsValue} data={topTransactionValueQuery.data} />
      )}
    </div>
  );
};

export default TopTransactionsValue;
