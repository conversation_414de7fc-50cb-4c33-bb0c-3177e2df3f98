// Template item type enum
export enum ETemplateItemType {
  INPUTS = 'INPUTS',
  LABOR = 'LABOR',
  OTHERS = 'OTHERS',
  NON_CASH = 'NON-CASH',
  NON_KITA_SUBSIDIZED = 'NON-KITA-SUBSIDIZED',
  KITA_SUBSIDIZED = 'KITA-SUBSIDIZED',
}

// ------------------------------
// API Response Interfaces for /agronomist/farmplan/template/viewAll
// ------------------------------

// Pagination meta interface
export interface IPaginationMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  first_page: number;
  first_page_url: string;
  last_page_url: string;
  next_page_url: string | null;
  previous_page_url: string | null;
}

// Crop interface
export interface ICrop {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
  harvest_days: number | null;
  is_sync: number;
  image: string | null;
  keywords: string | null;
  sap_item_code: string | null;
}

// Farm plan template sub item interface (API response)
export interface IFarmPlanTemplateSubItem {
  id: number;
  farm_plan_template_item_id: number;
  farm_plan_template_id: number;
  expected_date: string;
  item_name: string;
  unit: string;
  quantity: number;
  unit_cost: number;
  total_amount: number;
  notes: string;
  marketplace_product_id: number;
  created_at: string;
  updated_at: string;
}

// Farm plan template item interface (API response)
export interface IFarmPlanTemplateItem {
  id: number;
  farm_plan_template_id: number;
  name: string;
  slug: string;
  type: ETemplateItemType;
  total_amount: number | null;
  created_at: string;
  updated_at: string;
  farmPlanTemplateSubItems: IFarmPlanTemplateSubItem[];
}

// Agronomist profile interface
export interface IAgronomist {
  id: number;
  first_name: string;
  last_name: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}

// Agronomist user interface
export interface IAgronomistUser {
  id: number;
  email: string;
  username: string | null;
  status: number;
  user_type: number;
  user_img: string | null;
  remember_me_token: string | null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: string | null;
  agronomist: IAgronomist;
}

// Farm plan template interface (API response)
export interface IFarmPlanTemplate {
  id: number;
  crop_id: number;
  version_number: string;
  location: string;
  agronomist_user_id: number;
  contingency_for_fluctuation: number;
  interest_rate: number;
  number_of_months_per_tenor: number;
  aor_per_month: number;
  created_at: string;
  updated_at: string;
  crop: ICrop;
  farmPlanTemplateItems: IFarmPlanTemplateItem[];
  agronomistUser: IAgronomistUser;
}

// Main API response interface
export interface IFarmPlanTemplateResponse {
  status: number;
  data: {
    meta: IPaginationMeta;
    data: IFarmPlanTemplate[];
  };
}

// ------------------------------
// Audit Log Interfaces
// ------------------------------

// Log action enum
export enum ELogAction {
  UPDATE = 'UPDATE',
  REMOVED = 'REMOVED',
  ADDED = 'ADDED',
}

// Processed by user interface (for audit logs)
export interface IProcessedByUser {
  id: number;
  email: string;
  username: string | null;
  agronomist: IAgronomist;
}

// Farm plan template log interface
export interface IFarmPlanTemplateLog {
  id: number;
  farm_plan_template_id: number;
  reason: string;
  processed_by_id: number;
  action: ELogAction;
  category: string;
  item_name: string;
  created_at: string;
  updated_at: string;
  processedBy: IProcessedByUser;
}

// Audit logs API response interface
export interface IFarmPlanTemplateLogsResponse {
  status: number;
  data: {
    meta: IPaginationMeta;
    data: IFarmPlanTemplateLog[];
  };
}
