'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

import { IFarmPlanResponse } from '../../hooks/useEditFarmPlan';
import { buildHomeAddress, getFarmAddress, getFarm<PERSON><PERSON> } from '../../utils/address-utils';

interface IViewFarmerInfoProps {
  farmPlan: IFarmPlanResponse;
}

export default function ViewFarmerInfo({ farmPlan }: IViewFarmerInfoProps) {
  const farmer = farmPlan.user?.farmer;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-kitaph-primary">Farmer Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="text-sm font-medium text-gray-600">Farmer Name</div>
          <div className="text-base font-medium">
            {farmer ? `${farmer.first_name} ${farmer.last_name}`.trim() : 'N/A'}
          </div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Email</div>
          <div className="text-base">{farmPlan.user?.email || 'N/A'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Home Address</div>
          <div className="text-base">{buildHomeAddress(farmer)}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Farm Address</div>
          <div className="text-base">{getFarmAddress(farmer)}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Farm Area (Hectares)</div>
          <div className="text-base">{getFarmArea(farmer)}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Mobile Number</div>
          <div className="text-base">{farmer?.mobile_number || 'N/A'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">User ID</div>
          <div className="text-base">{farmPlan.user?.id || 'N/A'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Username</div>
          <div className="text-base">{farmPlan.user?.username || 'N/A'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">User Status</div>
          <div className="text-base">{farmPlan.user?.status === 1 ? 'Active' : 'Inactive'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Created Date</div>
          <div className="text-base">
            {farmer?.created_at ? new Date(farmer.created_at).toLocaleDateString() : 'N/A'}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
