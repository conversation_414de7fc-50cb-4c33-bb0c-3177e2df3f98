'use client';

import Link from 'next/link';

export default function SettingsLayout({ children }) {
  return (
    <div className="flex w-full flex-1 flex-col">
      <main className="flex min-h-[calc(100vh_-_theme(spacing.16))] flex-1 flex-col gap-4 p-4 md:gap-8 md:p-10">
        <div className="mx-auto grid w-full max-w-6xl gap-2">
          <h1 className="text-3xl font-semibold">Settings</h1>
        </div>

        <div className="mx-auto grid w-full max-w-6xl items-start gap-6 md:grid-cols-[180px_1fr] lg:grid-cols-[250px_1fr]">
          <nav className="grid gap-4 text-sm text-muted-foreground">
            <Link href="#">Profile</Link>
            <Link href="#" className="font-semibold text-primary">
              Security
            </Link>
          </nav>

          {children}
        </div>
      </main>
    </div>
  );
}
