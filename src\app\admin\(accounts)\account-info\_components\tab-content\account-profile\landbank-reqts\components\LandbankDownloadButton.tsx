'use client';

import { useHookstate } from '@hookstate/core';
import { saveAs } from 'file-saver';
import JSZ<PERSON> from 'jszip';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';

import { useGlobalState } from '@/lib/store';

export const LandbankDownloadButton = () => {
  const gState = useGlobalState();
  const selectedCount = gState.landbankReqs.selectedAttachments.length;
  const [isDownloading, setIsDownloading] = useState(false);

  const data = useHookstate(gState.selected.accountInfo['info']);
  const farmerRequirements = data.farmer.farmerLandbankRequirements.value || [];

  if (selectedCount === 0) {
    return null;
  }

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      const urls = gState.landbankReqs.selectedAttachments.get();

      if (!Array.isArray(urls) || urls.length === 0) {
        throw new Error('No files selected for download');
      }

      const typeMap: Record<string, string> = {};
      farmerRequirements.forEach((req) => {
        if (req.attachment) {
          typeMap[req.attachment] = req.type;
        }
      });

      const zip = new JSZip();
      const errors: string[] = [];
      let fileCount = 0;
      const nameCount: Record<string, number> = {}; // Track filename usage

      const getSafeFileName = (url: string): string => {
        // Extract original filename and extension
        const originalName = url.substring(url.lastIndexOf('/') + 1);
        const extension = originalName.includes('.') ? originalName.split('.').pop() || '' : '';

        // Get type or fallback to original name without extension
        const baseName = typeMap[url] || originalName.replace(/\.[^/.]+$/, '');

        // Sanitize filename (remove unsafe chars)
        const safeName =
          baseName
            .replace(/[^a-zA-Z0-9_\u00A0-\uFFFF-]/g, '_') // Allow unicode
            .replace(/_{2,}/g, '_') // Remove duplicate underscores
            .replace(/^_+|_+$/g, '') || 'document'; // Trim underscores

        // Handle duplicates
        const count = (nameCount[safeName] || 0) + 1;
        nameCount[safeName] = count;

        const finalName = count === 1 ? safeName : `${safeName}_${count}`;

        return extension ? `${finalName}.${extension}` : finalName;
      };

      for (const url of urls) {
        try {
          const response = await fetch(url);
          if (!response.ok) throw new Error(`HTTP ${response.status}`);

          const blob = await response.blob();
          if (blob.size === 0) throw new Error('Empty file content');

          const filename = getSafeFileName(url);
          zip.file(filename, blob);
          fileCount++;
        } catch (error: any) {
          errors.push(`Error processing ${url}: ${error.message}`);
        }
      }

      if (fileCount === 0) {
        throw new Error('No files could be downloaded');
      }

      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
      });

      saveAs(zipBlob, 'landbank-requirements.zip');

      toast.success('Success', {
        description: `Download completed with ${fileCount} files. ${errors.length} failed.`,
      });
    } catch (error: any) {
      let message = 'Download failed. Please try again.';

      if (error.message.includes('Failed to fetch')) {
        message = 'Network error. Please check your connection.';
      } else if (error.message.includes('No files could be downloaded')) {
        message = 'Could not download any files. They might be unavailable.';
      } else if (error.message.includes('No files selected')) {
        message = 'No files selected for download.';
      }

      toast.error(message, {
        description: error,
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button type="button" size="lg" onClick={handleDownload} disabled={isDownloading} className="w-full md:w-auto">
      {isDownloading ? (
        <span className="flex items-center">
          <span className="mr-2 animate-spin">⏳</span>
          Preparing Download...
        </span>
      ) : (
        `Download Selected (${selectedCount})`
      )}
    </Button>
  );
};
