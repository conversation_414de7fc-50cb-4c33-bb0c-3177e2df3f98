'use client';

import { useHookstate } from '@hookstate/core';
import { PaperclipIcon, Plus, X } from 'lucide-react';
import { useEffect } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import useFarmer from '@/lib/hooks/useFarmer';
import usePublic from '@/lib/hooks/usePublic';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { PROFILE_TAB } from '../../constants';

const OPTION_LAND_CATEGORIES = [
  {
    label: 'IRRIGATED',
    value: 'IRRIGATED',
  },
  {
    label: 'RAIN FED',
    value: 'RAIN FED',
  },
  {
    label: 'IRRIGATED & RAIN FED',
    value: 'IRRIGATED & RAIN FED',
  },
];

export default function FarmDetails() {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();
  const {
    getAllCrops,
    getSeeds,
    getCropType,
    getFertilizer,
    getChemicals,
    OPTION_CROPTYPE,
    OPTION_CROPS,
    OPTION_CHEMICAL,
    OPTION_FERTILIZER,
    OPTION_CROPS_PLANTED,
  } = usePublic();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
  } = useForm({
    defaultValues: {
      farmAddress: data?.farmer?.farmerInfo?.farm_address?.value || '',
      farmArea: data?.farmer?.farmerInfo?.farm_area?.value || ('' as any),
      farmOwnership: `${data?.farmer?.farmerInfo?.farm_ownership?.value}` || '',
      priceBasedBy: data?.farmer?.farmerInfo?.price_based_by?.value || '',
      cropsPlanted:
        data?.farmer?.cropsPlanted?.value.map((v) => ({
          label: v.crop.name,
          value: `${v.crop.id}-${v.crop.name}`,
        })) || [],
      subCropsPlanted:
        data?.farmer?.subCropsPlanted?.value.map((v) => ({
          label: v.crop.name,
          value: `${v.crop.id}-${v.crop.name}`,
        })) || [],
      seedSubcategory:
        data?.farmer?.seedSubcategories?.value.map((v) => ({
          label: v.seedSubcategory.name,
          value: `${v.seedSubcategory.id}-${v.seedSubcategory.name}`,
        })) || [],
      fertilizer:
        data?.farmer?.fertilizers?.value.map((v) => ({
          label: v.fertilizer.name,
          value: `${v.fertilizer.id}-${v.fertilizer.name}`,
        })) || [],
      chemical:
        data?.farmer?.chemicals?.value.map((v) => ({
          label: v.chemical.name,
          value: `${v.chemical.id}-${v.chemical.name}`,
        })) || [],
      farmerVehicle:
        data?.farmer?.farmerVehicles?.value.map((v) => ({
          vehicleOwned: v.vehicle_owned,
          vehiclePlateNumber: v.vehicle_plate_number,
          orcr: '',
        })) || [],
      landCategory:
        data?.farmer?.farmerInsurance?.land_category?.value
          ?.split(',')
          .map((v) => ({ label: v.trim(), value: v.trim() })) || [],
      crop:
        data?.farmer?.farmerInsurance?.crop?.value?.split(',').map((v) => ({ label: v.trim(), value: v.trim() })) || [],
      phase: data?.farmer?.farmerInsurance?.phase?.value || '',
      ownerCultivator: data?.farmer?.farmerInsurance?.owner_cultivator?.value || '',
      tenant: data?.farmer?.farmerInsurance?.tenant?.value || '',
      cltEp: data?.farmer?.farmerInsurance?.clt_ep?.value || '',
      lessee: data?.farmer?.farmerInsurance?.lessee?.value || '',
      // Purchaser Information fields
      purchaserSellingLocation: data?.farmer?.farmerInfo?.purchaser_selling_location?.value || '',
      purchaserFullname: data?.farmer?.farmerInfo?.purchaser_fullname?.value || '',
      purchaserContactNumber: data?.farmer?.farmerInfo?.purchaser_contact_number?.value || '',
    },
  });
  const farmerVehicle = useFieldArray({
    name: 'farmerVehicle',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      farmerVehicle: _data.farmerVehicle.map((s: any) => ({
        vehicleOwned: s.vehicleOwned,
        vehiclePlateNumber: s.vehiclePlateNumber,
      })),
      cropsPlanted: _data.cropsPlanted.map((item: any) => item.value.split('-')[0]),
      subCropsPlanted: _data.subCropsPlanted.map((item: any) => item.value.split('-')[0]),
      seedSubcategory: _data.seedSubcategory.map((item: any) => item.value.split('-')[0]),
      fertilizer: _data.fertilizer.map((item: any) => item.value.split('-')[0]),
      chemical: _data.chemical.map((item: any) => item.value.split('-')[0]),
      landCategory: _data.landCategory.map((item: any) => item.value).join(','),
      crop: _data.crop.map((item: any) => item.value).join(','),
      userId: data.farmer.user_id.value,
      // Include purchaser information fields
      purchaserSellingLocation: _data.purchaserSellingLocation,
      purchaserFullname: _data.purchaserFullname,
      purchaserContactNumber: _data.purchaserContactNumber,
    };

    if (_data.farmOwnership === 'null') {
      delete updatedData.farmOwnership;
    }

    _data.farmerVehicle.map((s: any) => {
      if (s.orcr) {
        updatedData = {
          ...updatedData,
          [`farmerVehicle_vehicleOrcr_${s.vehiclePlateNumber}`]: s.orcr[0],
        };
      }
    });

    console.log('Farm Details: ', updatedData);
    updateFarmer(updatedData);
    dirty.set(false);
  };

  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  useEffect(() => {
    Promise.all([getAllCrops(), getSeeds(), getCropType(), getFertilizer(), getChemicals()]);
  }, []);

  return (
    <form id={PROFILE_TAB[5].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Farm Address */}
        <FormField name="farmAddress" label="Farm Address" errors={errors}>
          <Input
            {...register('farmAddress')}
            className={cn(
              'focus-visible:ring-primary',
              errors.farmAddress && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Farm Address"
          />
        </FormField>

        {/* Farm Area (hectare/s) */}
        <FormField name="farmArea" label="Farm Area (sqm)" errors={errors}>
          <Input
            {...register('farmArea', {
              required: false,
              validate: {
                isGreaterThanZero: (v) => (v ? (Number(v) || 0) > 0 || 'Farm Area must be greater than 0' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.farmArea && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Farm Area (hectare/s)"
          />
        </FormField>

        {/* Farm Ownership */}
        <FormField name="farmOwnership" label="Farm Ownership" errors={errors}>
          <Controller
            control={control}
            name="farmOwnership"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.farmOwnership && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Farm Ownership" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="0">NOT OWNED</SelectItem>
                    <SelectItem value="1">OWNED</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {/* Price Determined By */}
        <FormField name="priceBasedBy" label="Price Determined By" errors={errors}>
          <Controller
            control={control}
            name="priceBasedBy"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.priceBasedBy && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Price Determined By" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="BAPTC">BAPTC</SelectItem>
                    <SelectItem value="NVAT">NVAT</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
      </div>

      <div className="font-dmSans mt-8 text-xl font-bold text-primary">Vehicle Information</div>
      <div className="space-y-4 pr-14">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {farmerVehicle.fields.map((field, index) => {
            const errorForField = errors?.farmerVehicle?.[index];
            const isLast = data.farmer.farmerVehicles.length < index + 1;
            const orcImg = isLast ? '' : data.farmer.farmerVehicles[index].vehicle_orcr.value || '';
            const orcImgSplit = orcImg?.split('/');
            const orcName = orcImgSplit[orcImgSplit.length - 1];

            return (
              <div key={field.id} className="grid items-start gap-4 pb-3 pt-7 sm:grid-cols-2 xl:grid-cols-3">
                {/* Vehicle Plate No. */}
                <FormField
                  name={`farmerVehicle.${index}.vehiclePlateNumber`}
                  label="Vehicle Plate No."
                  errors={errors}
                  className="w-full max-w-sm"
                >
                  <Input
                    {...register(`farmerVehicle.${index}.vehiclePlateNumber` as const, {
                      required: false,
                      validate: {
                        isValidPlate: (v) => (v ? /^[A-Z0-9]{5,7}$/.test(v) || 'Invalid plate number' : true),
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vehiclePlateNumber && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Plate No."
                  />
                </FormField>

                {/* Vehicle Brand & Model */}
                <FormField
                  name={`farmerVehicle.${index}.vehicleOwned`}
                  label="Vehicle Brand & Model"
                  errors={errors}
                  className="w-full max-w-sm"
                >
                  <Input
                    {...register(`farmerVehicle.${index}.vehicleOwned` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vehicleOwned && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Vehicle Brand & Model"
                  />
                </FormField>

                {/* Upload ORCR */}
                <FormField
                  name={`farmerVehicle.${index}.orcr`}
                  label="Upload ORCR"
                  errors={errors}
                  className="w-full max-w-sm"
                >
                  <div className="relative">
                    <Input
                      {...register(`farmerVehicle.${index}.orcr` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.orcr && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="file"
                      placeholder="Enter Upload ORCR"
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => farmerVehicle.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>

                  {orcImg && (
                    <div className="">
                      <a className="flex items-center gap-2 text-primary hover:underline" href={orcImg} target="_blank">
                        <PaperclipIcon className="size-4" />
                        {orcName}
                      </a>
                    </div>
                  )}
                </FormField>
              </div>
            );
          })}
        </div>

        {/* Add More Vehicle */}
        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              farmerVehicle.append({
                vehicleOwned: '',
                vehiclePlateNumber: '',
                orcr: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Vehicle</span>
          </Button>
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Crop Details</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Crops Planted */}
        {OPTION_CROPS_PLANTED.length > 0 && (
          <FormField name="cropsPlanted" label="Main Crops Planted" errors={errors}>
            <Controller
              control={control}
              name="cropsPlanted"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS_PLANTED}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Sub Crops Planted */}
        {OPTION_CROPS_PLANTED.length > 0 && (
          <FormField name="subCropsPlanted" label="Sub Crops Planted" errors={errors}>
            <Controller
              control={control}
              name="subCropsPlanted"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS_PLANTED}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Crop Type */}
        {OPTION_CROPTYPE.length > 0 && (
          <FormField name="seedSubcategory" label="Crop Type" errors={errors}>
            <Controller
              control={control}
              name="seedSubcategory"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPTYPE}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Fertilizers */}
        {OPTION_FERTILIZER.length > 0 && (
          <FormField name="fertilizer" label="Fertilizers" errors={errors}>
            <Controller
              control={control}
              name="fertilizer"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_FERTILIZER}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Chemicals */}
        {OPTION_CHEMICAL.length > 0 && (
          <FormField name="chemical" label="Chemicals" errors={errors}>
            <Controller
              control={control}
              name="chemical"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CHEMICAL}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                  groupBy="group"
                />
              )}
            />
          </FormField>
        )}
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Land Details</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Land Category */}
        <FormField name="landCategory" label="Land Category" errors={errors}>
          <Controller
            control={control}
            name="landCategory"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTION_LAND_CATEGORIES}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        {/* Crops */}
        {OPTION_CROPS.length > 0 && (
          <FormField name="crop" label="Crops" errors={errors}>
            <Controller
              control={control}
              name="crop"
              render={({ field: { onChange, value } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS}
                  placeholder="Select from selection or create new"
                  creatable
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
          </FormField>
        )}

        {/* Phase */}
        <FormField name="phase" label="Phase" errors={errors}>
          <Input
            {...register('phase')}
            className={cn('focus-visible:ring-primary', errors.phase && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Phase"
          />
        </FormField>

        {/* Owner Cultivator */}
        <FormField name="ownerCultivator" label="Owner Cultivator" errors={errors}>
          <Input
            {...register('ownerCultivator')}
            className={cn(
              'focus-visible:ring-primary',
              errors.ownerCultivator && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Owner Cultivator"
          />
        </FormField>

        {/* Tenant */}
        <FormField name="tenant" label="Tenant" errors={errors}>
          <Input
            {...register('tenant')}
            className={cn('focus-visible:ring-primary', errors.tenant && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Tenant"
          />
        </FormField>

        {/* CLT/EP */}
        <FormField name="cltEp" label="CLT/EP" errors={errors}>
          <Input
            {...register('cltEp')}
            className={cn('focus-visible:ring-primary', errors.cltEp && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter CLT/EP"
          />
        </FormField>

        {/* Lessee */}
        <FormField name="lessee" label="Lessee" errors={errors}>
          <Input
            {...register('lessee')}
            className={cn('focus-visible:ring-primary', errors.lessee && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Lessee"
          />
        </FormField>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Purchaser Information</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Selling Location for Harvested Crops */}
        <FormField name="purchaserSellingLocation" label="Selling Location for Harvested Crops" errors={errors}>
          <Input
            {...register('purchaserSellingLocation', {
              required: false,
              maxLength: {
                value: 100,
                message: 'Selling Location must not exceed 100 characters',
              },
              validate: {
                notOnlySpaces: (v) =>
                  v ? (v as string).trim().length > 0 || 'Selling Location cannot be only spaces' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserSellingLocation && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Selling Location for Harvested Crops"
          />
        </FormField>

        {/* Buyer's Full Name */}
        <FormField name="purchaserFullname" label="Buyer's Full Name" errors={errors}>
          <Input
            {...register('purchaserFullname', {
              required: false,
              validate: {
                notOnlySpaces: (v) => (v ? (v as string).trim().length > 0 || 'Full name cannot be only spaces' : true),
                validName: (v) =>
                  v
                    ? /^[a-zA-Z\s\-\.\']+$/.test(v as string) ||
                      'Full name can only contain letters, spaces, hyphens, periods, and apostrophes'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserFullname && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Full Name"
          />
        </FormField>

        {/* Buyer's Contact No. */}
        <FormField name="purchaserContactNumber" label="Buyer's Contact No." errors={errors}>
          <Input
            {...register('purchaserContactNumber', {
              required: false,
              validate: {
                isValidContactNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                      'Invalid contact number format (e.g. 09123456789)'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserContactNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Contact No."
          />
        </FormField>
      </div>
    </form>
  );
}
