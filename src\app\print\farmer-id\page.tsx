'use client';

import { useHookstate } from '@hookstate/core';
import { ArrowLeftRight } from 'lucide-react';
import { useRef, useState } from 'react';
import { QRCode } from 'react-qrcode-logo';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import useClient from '@/lib/hooks/utils/useClient';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { formatId } from '@/lib/utils';

function CardFront() {
  const gStateP = useGlobalStatePersist();
  const farmer = useHookstate(gStateP.print.farmerId);

  const fullName = (firstName, middleName, lastName) => {
    if (middleName) {
      const middleInitial = middleName.charAt(0).toUpperCase();
      return `${firstName} ${middleInitial}. ${lastName}`;
    }
    return `${firstName} ${lastName}`;
  };

  return (
    <CardContent className="relative size-full font-poppins">
      {farmer.value && (
        <>
          {/* Background Image */}
          <img className="absolute inset-0 size-full" src="/print/farmer-id/front.png" alt="" />

          {/* Kita Logo */}
          <div className="absolute inset-4">
            <img className="" src="/print/farmer-id/logo.png" alt="" />
          </div>

          <div className="absolute inset-y-0 right-[-3.6rem] flex items-center">
            <div className="-rotate-90 text-[0.95rem] font-bold tracking-widest text-[#C0C0C0] [text-shadow:_-1px_1px_2px_rgb(0_0_0_/_0.4)]">
              Farmer Entry Card
            </div>
          </div>

          <div className="absolute bottom-4 left-4 text-sm text-[#CCCCCC]">
            <div className="font-bold tracking-widest [text-shadow:_1px_1px_1px_rgb(0_0_0_/_0.4)]">
              {fullName(
                farmer['farmer']['first_name'].value,
                farmer['farmer']['middle_name'].value,
                farmer['farmer']['last_name'].value,
              )}
            </div>
            <div className="tracking-wider [text-shadow:_1px_1px_1px_rgb(0_0_0_/_0.4)]">
              {formatId(farmer['id'].value)}
            </div>
          </div>
        </>
      )}
    </CardContent>
  );
}

function CardBack() {
  const gStateP = useGlobalStatePersist();
  const farmer = useHookstate(gStateP.print.farmerId);

  return (
    <CardContent className="relative size-full font-poppins">
      <img className="absolute inset-0 size-full" src="/print/farmer-id/front.png" alt="" />

      <div className="absolute inset-0 grid place-items-center">
        <div className="grid gap-2">
          {farmer.value && (
            <div className="grid place-items-center">
              <QRCode
                size={112}
                qrStyle="dots"
                bgColor="#00000000"
                fgColor="#303030"
                value={farmer['farmer']['qr_code'].value}
              />
            </div>
          )}

          <div className="px-4 text-center text-[0.3rem]">
            This card is a property of Kita Agritech Corp. If found, please return to Kita Agritech Corp. you may
            contact us at +63270064158 or email <NAME_EMAIL>. Misuse, duplication, or alteration of this card is
            prohibited. In case of loss, report immediately to Kita Agritech Corp.
          </div>
        </div>
      </div>

      <style jsx global>{`
        .card {
          width: 300px;
          height: 300px;
          background: linear-gradient(135deg, #e0e0e0, #b0b0b0);
          border-radius: 10px;
          padding: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </CardContent>
  );
}

export default function FarmerId() {
  const contentRef = useRef<HTMLDivElement>(null);
  const reactToPrintFn = useReactToPrint({ contentRef });
  const [isFlipped, setIsFlipped] = useState(false);

  const { isClient } = useClient();

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  return (
    <div className="grid min-h-screen place-items-center">
      <div className="mx-auto w-full max-w-4xl p-4">
        <div className="mb-4 flex justify-between print:hidden">
          <Button onClick={() => reactToPrintFn()}>Print ID Card</Button>
          <Button className="md:hidden" onClick={handleFlip} variant="outline">
            <ArrowLeftRight className="mr-2 size-4" /> Flip Card
          </Button>
        </div>

        {/* Print Content */}
        {isClient && (
          <div
            ref={contentRef}
            className="flex flex-col items-center rounded-xl border-2 border-dashed bg-gray-50 p-8 sm:flex-row sm:justify-center print:flex-col print:border-none print:bg-transparent"
          >
            <Card className="perspective h-[2.125in] w-[3.375in] overflow-hidden rounded-2xl md:m-4 md:shrink-0 print:my-[2.9in] print:h-[2.125in] print:w-[3.375in]">
              <div
                className={`preserve-3d relative size-full transition-transform duration-500 ${isFlipped ? 'rotate-y-180' : ''}`}
              >
                <div className="backface-hidden absolute size-full">
                  <CardFront />
                </div>
                <div className="backface-hidden rotate-y-180 absolute size-full print:hidden">
                  <CardBack />
                </div>
              </div>
            </Card>

            <div className="hidden md:block print:block">
              <Card className="h-[2.125in] w-[3.375in] overflow-hidden rounded-2xl md:m-4 md:shrink-0 print:my-[2.9in] print:h-[2.125in] print:w-[3.375in]">
                <CardBack />
              </Card>
            </div>
          </div>
        )}

        <div className="mt-4 text-center text-sm text-gray-500 print:hidden">
          This card is a property of Kita Agritech Corp. If found, please return to Kita Agritech Corp. you may contact
          us at +63270064158 or email <NAME_EMAIL>. Misuse, duplication, or alteration of this card is prohibited.
          In case of loss, report immediately to Kita Agritech Corp.
        </div>
        <style jsx global>{`
          @media print {
            @page {
              size: landscape;
            }
          }
          .perspective {
            perspective: 1000px;
          }
          .preserve-3d {
            transform-style: preserve-3d;
          }
          .backface-hidden {
            backface-visibility: hidden;
          }
          .rotate-y-180 {
            transform: rotateY(180deg);
          }
        `}</style>
      </div>
    </div>
  );
}
