'use client';

import { useHookstate } from '@hookstate/core';
import { Timeline, TimelineConnector, TimelineHeader, TimelineIcon, TimelineItem } from '@material-tailwind/react';
import { CircleCheckIcon } from 'lucide-react';
import { MdRadioButtonChecked } from 'react-icons/md';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion-custom';

import useCreditScoring from '@/lib/hooks/admin/useCreditScoring';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

export default function CreditScoreTabContent() {
  const gState = useGlobalState();
  const loanStage = useHookstate(gState.creditScoring.loanStage);
  const { beforeDetails, duringDetails, afterDetails } = useCreditScoring();

  const isBefore = loanStage.get({ noproxy: true }) === 'BEFORE';
  const isDuring = loanStage.get({ noproxy: true }) === 'DURING';
  const isAfter = loanStage.get({ noproxy: true }) === 'AFTER';

  return (
    <div className="mt-6">
      <Timeline>
        <Accordion className="" type="single" collapsible>
          {isAfter && afterDetails['categories']?.get({ noproxy: true })?.length > 0 && (
            <AccordionItem className="" value="after">
              <TimelineItem className={cn('min-h-[5.4rem]')}>
                <TimelineConnector className="!w-[78px] " />
                <AccordionTrigger className="">
                  <TimelineHeader
                    className={cn(
                      'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                      isAfter ? 'bg-white' : 'bg-gray-200',
                    )}
                  >
                    <TimelineIcon className="p-3" variant="ghost" color={isAfter ? 'orange' : 'green'}>
                      {isAfter ? <MdRadioButtonChecked className="size-5" /> : <CircleCheckIcon className="size-5" />}
                    </TimelineIcon>
                    <div className="flex w-full items-center justify-between">
                      <div className="text-left">
                        <div className="text-lg font-bold text-kitaph-primary">After Loan Stage</div>
                      </div>
                      <div className="text-lg font-bold text-kitaph-primary">
                        {afterDetails['computedScore']?.value}%
                      </div>
                    </div>
                  </TimelineHeader>
                </AccordionTrigger>

                <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                  {afterDetails['categories']?.get({ noproxy: true })?.map((category, index) => (
                    <div key={index}>
                      <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                        <div>{category.name}</div>
                        <div className="text-kitaph-primary">{`${category.computedScore} / ${category.score}`}</div>
                      </div>
                      <div className="grid max-w-lg gap-1 pl-10">
                        {category['rules']?.map((rule, ruleIndex) => (
                          <div key={ruleIndex}>
                            <div className="flex justify-between">
                              <div>{rule.name}</div>
                              <div>{`${rule.computedScore}/${rule.score}`}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </AccordionContent>
              </TimelineItem>
            </AccordionItem>
          )}

          {(isDuring || isAfter) && duringDetails['categories']?.get({ noproxy: true })?.length > 0 && (
            <AccordionItem className="" value="during">
              <TimelineItem className={cn('min-h-[5.4rem]')}>
                <TimelineConnector className="!w-[78px] " />
                <AccordionTrigger className="">
                  <TimelineHeader
                    className={cn(
                      'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                      isDuring ? 'bg-white' : 'bg-gray-200',
                    )}
                  >
                    <TimelineIcon className="p-3" variant="ghost" color={isDuring ? 'orange' : 'green'}>
                      {isDuring ? <MdRadioButtonChecked className="size-5" /> : <CircleCheckIcon className="size-5" />}
                    </TimelineIcon>
                    <div className="flex w-full items-center justify-between">
                      <div className="text-left">
                        <div className="text-lg font-bold text-kitaph-primary">During Loan Stage</div>
                      </div>
                      <div className="text-lg font-bold text-kitaph-primary">
                        {duringDetails['computedScore']?.value}%
                      </div>
                    </div>
                  </TimelineHeader>
                </AccordionTrigger>

                <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                  {duringDetails['categories']?.get({ noproxy: true })?.map((category, index) => (
                    <div key={index}>
                      <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                        <div>{category.name}</div>
                        <div className="text-kitaph-primary">{`${category.computedScore} / ${category.score}`}</div>
                      </div>
                      <div className="grid max-w-lg gap-1 pl-10">
                        {category['rules']?.map((rule, ruleIndex) => (
                          <div key={ruleIndex}>
                            <div className="flex justify-between">
                              <div>{rule.name}</div>
                              <div>{`${rule.computedScore}/${rule.score}`}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </AccordionContent>
              </TimelineItem>
            </AccordionItem>
          )}

          {beforeDetails['categories']?.get({ noproxy: true })?.length > 0 && (
            <AccordionItem className="" value="before">
              <TimelineItem className={cn('min-h-[5.4rem]')}>
                <AccordionTrigger className="">
                  <TimelineHeader
                    className={cn(
                      'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                      isBefore ? 'bg-white' : 'bg-gray-200',
                    )}
                  >
                    <TimelineIcon className="p-3" variant="ghost" color={isBefore ? 'orange' : 'green'}>
                      {isBefore ? <MdRadioButtonChecked className="size-5" /> : <CircleCheckIcon className="size-5" />}
                    </TimelineIcon>
                    <div className="flex w-full items-center justify-between">
                      <div className="text-left">
                        <div className="text-lg font-bold text-kitaph-primary">Before Loan Stage</div>
                      </div>
                      <div className="text-lg font-bold text-kitaph-primary">
                        {beforeDetails['computedScore']?.value}%
                      </div>
                    </div>
                  </TimelineHeader>
                </AccordionTrigger>

                <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                  {beforeDetails['categories']?.get({ noproxy: true })?.map((category, index) => (
                    <div key={index}>
                      <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                        <div>{category.name}</div>
                        <div className="text-kitaph-primary">{`${category.computedScore} / ${category.score}`}</div>
                      </div>
                      <div className="grid max-w-lg gap-1 pl-10">
                        {category['rules']?.map((rule, ruleIndex) => (
                          <div key={ruleIndex}>
                            <div className="flex justify-between">
                              <div>{rule.name}</div>
                              <div>{`${rule.computedScore}/${rule.score}`}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </AccordionContent>
              </TimelineItem>
            </AccordionItem>
          )}
        </Accordion>
      </Timeline>
    </div>
  );
}
