'use client';

import { useHookstate } from '@hookstate/core';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { catchError } from '@/lib/utils';

export default function useLoanApplication() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const loading = useHookstate(gState.loading.submitLoanApplication);
  const latestLoanApplication = useHookstate(gState.selected.accountInfo.loanApplication.latest);

  const submitLoanApplication = async (data) => {
    try {
      loading.set(true);
      const _res = await axios.post(`/admin/loanapplication/submit`, data).then((res) => res.data);
      console.log('submitLoanApplication: ', _res);

      toast.success('Success', {
        description: 'Loan application has been submitted',
      });
    } catch (e) {
      catchError(e, 'submitLoanApplication');
    } finally {
      loading.set(false);
    }
  };

  const getLatest = async (userId) => {
    try {
      const _res = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanapplication/view/latest`, {
          params: {
            farmerUserId: userId,
          },
        })
        .then((res) => res.data.data);
      console.log('getLatest: ', _res);

      if (_res && _res.credit_score_groups) {
        const groups = gState.admin.creditScoreMgt.groups.data;
        const selectedGroups = _res.credit_score_groups.split(', ');

        selectedGroups.map((v) => {
          const find = groups.find((g) => g['name'].value === v);
          if (find.value) {
            find['checked'].set(true);
            find['disabled'].set(true);
          }
        });
      }

      latestLoanApplication.set(_res);
    } catch (e) {
      catchError(e, 'getLatest');
    }
  };

  return { loading, latestLoanApplication, submitLoanApplication, getLatest };
}
