'use client';

import { useHookstate } from '@hookstate/core';

import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import { PROFILE_TAB } from '../../constants';
import BasicProfile from './basic-profile';
import CareerAcademic from './career-academic';
import { DirtyDialog } from './dirty-dialog';
import FamilyProfile from './family-profile';
import FarmDetails from './farm-details';
import FarmInsurance from './farm-insurance';
import IdentificationDocs from './identification-docs';
import LandbankReqts from './landbank-reqts';
import { LandbankDownloadButton } from './landbank-reqts/components/LandbankDownloadButton';
import PropertyOwnership from './property-ownership';
import { SaveConfirmation } from './save-confirmation';

export default function AccountProfileTabContent() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const profileTab = useHookstate(gState.selected.accountInfo.tabs.profile);
  const activeStep = useHookstate(gState.selected.accountInfo.tabs.activeStep);

  const cached = useHookstate({
    profileTab: '',
    activeStep: 0,
  });
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);
  const confirmDialog = useHookstate(false);
  const dirtyDialog = useHookstate(false);
  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';

  return (
    <div className="mt-8 grid gap-6">
      {isAdmin && profileTab.value !== 'landbank_reqts' && (
        <div className="grid place-items-end">
          <Button
            type="button"
            size="lg"
            onClick={() => {
              confirmDialog.set(true);
            }}
            className="w-full md:w-auto"
          >
            Save Information
          </Button>
        </div>
      )}

      {isAdmin && profileTab.value === 'landbank_reqts' && (
        <div className="grid place-items-end">
          <LandbankDownloadButton />
        </div>
      )}

      {/* Tabs */}
      <ScrollArea className="">
        <div className="flex w-max items-center gap-8 pr-6">
          {PROFILE_TAB.map((tab, index) => {
            const isSelected = tab.value === profileTab.value;

            return (
              <button
                key={tab.value}
                className={cn(
                  'transition-all duration-300 ease-in-out whitespace-nowrap my-3',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => {
                  if (dirty.value && isAdmin) {
                    cached.set({
                      profileTab: tab.value,
                      activeStep: index,
                    });

                    dirtyDialog.set(true);
                    return;
                  }

                  activeStep.set(index);
                  profileTab.set(tab.value);
                }}
              >
                {tab.name}
              </button>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Content */}
      <div className="rounded-lg border-2 border-dashed bg-white p-6">
        {profileTab.value === 'basic_info' && <BasicProfile />}
        {profileTab.value === 'career_academic' && <CareerAcademic />}
        {profileTab.value === 'identification_docs' && <IdentificationDocs />}
        {profileTab.value === 'family_profile' && <FamilyProfile />}
        {profileTab.value === 'property_ownership' && <PropertyOwnership />}
        {profileTab.value === 'farm_details' && <FarmDetails />}
        {profileTab.value === 'crop_insurance' && <FarmInsurance />}
        {profileTab.value === 'landbank_reqts' && <LandbankReqts />}
      </div>

      <SaveConfirmation state={confirmDialog} activeStep={activeStep} />
      {isAdmin && (
        <DirtyDialog
          state={dirtyDialog}
          activeStep={activeStep}
          onDiscard={() => {
            dirty.set(false);
            activeStep.set(cached.activeStep.get({ noproxy: true }));
            profileTab.set(cached.profileTab.get({ noproxy: true }));
          }}
        />
      )}
    </div>
  );
}
