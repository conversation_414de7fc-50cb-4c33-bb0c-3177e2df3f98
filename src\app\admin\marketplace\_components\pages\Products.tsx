'use client';

import Link from 'next/link';
import { useEffect } from 'react';
import { useDebounce } from 'use-debounce';

import { Button } from '@/components/ui/button';

import useChemicals from '@/lib/hooks/useChemicals';
import useMarketplace from '@/lib/hooks/useMarketplace';
import useSeeds from '@/lib/hooks/useSeeds';
import { useGlobalState } from '@/lib/store';

import ProductTable from '../product-table';
import { columns } from '../product-table/columns';

export default function ProductsPage() {
  const gState = useGlobalState();
  const { getProducts } = useMarketplace();
  const { getSubcategory } = useChemicals();
  const { getSubcategory: getSeedsSub } = useSeeds();

  const [search] = useDebounce(gState.admin.pagination.products.search.value, 1000);

  useEffect(() => {
    getProducts();
  }, [
    search,
    gState.admin.pagination.products.page,
    gState.admin.pagination.products.pageSize,
    gState.admin.pagination.products.productType,
    gState.admin.pagination.products.productTypeCategory,
  ]);

  useEffect(() => {
    Promise.all([getSubcategory(), getSeedsSub()]);
  }, []);

  return (
    <div className="space-y-6 p-6 md:p-12">
      <ProductTable
        data={gState.admin.marketplace.products.data.get({ noproxy: true })}
        columns={columns}
        meta={gState.admin.marketplace.products['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
