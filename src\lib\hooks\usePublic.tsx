'use client';

import { useHookstate } from '@hookstate/core';
import { useState } from 'react';

import axios from '@/lib/api';

import { useGlobalState } from '../store';
import { useGlobalStatePersist } from '../store/persist';
import { removeDuplicates } from '../utils';

export default function usePublic() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const chemicals = useHookstate(gState.chemicals);
  const fertilizers = useHookstate(gState.fertilizers);
  const chemOptions = useHookstate([]);
  const seedOptions = useHookstate([]);
  const otherProductOptions = useHookstate([]);

  const [OPTION_CROPS, setOPTION_CROPS] = useState([]);

  const [OPTION_CROPS_PLANTED, setOPTION_CROPS_PLANTED] = useState([]);
  const [OPTION_SEED, setOPTION_SEED] = useState([]);
  const [OPTION_CROPTYPE, setOPTION_CROPTYPE] = useState([]);
  const [OPTION_FERTILIZER, setOPTION_FERTILIZER] = useState([]);
  const [OPTION_CHEMICAL, setOPTION_CHEMICAL] = useState([]);
  const [OPTION_OTHER_PRODUCT, setOPTION_OTHER_PRODUCT] = useState([]);

  const getAllCrops = async () => {
    try {
      const _data = await axios
        .get('/crops/viewAll', {
          params: {
            status: ['1'], // all active and inactive
          },
        })
        .then((res) => res.data.data);

      // remove duplicates
      const unique = removeDuplicates(_data, 'name');

      setOPTION_CROPS(
        unique.map((v) => ({
          label: v.name,
          value: v.name,
        })),
      );
      setOPTION_CROPS_PLANTED(
        unique.map((v) => ({
          label: v.name,
          value: `${v.id}-${v.name}`,
        })),
      );
      gStateP.fro.options.cropsPlanted.set(
        unique.map((v) => ({
          label: v.name,
          value: `${v.id}-${v.name}`,
        })),
      );
      gState.crops.set(unique);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getAllCrops: ', error);
    }
  };

  const getCropType = async () => {
    try {
      const _data = await axios
        .get('/seeds/subcategory/viewAll', {
          params: {
            status: ['1'], // all active and inactive
          },
        })
        .then((res) => res.data.data);

      // remove duplicates
      const unique = removeDuplicates(_data, 'name');

      setOPTION_CROPTYPE(
        unique.map((v) => ({
          label: v.name,
          value: `${v.id}-${v.name}`,
        })),
      );
      gState.cropTypes.set(unique);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getCropType: ', error);
    }
  };

  const getSeeds = async () => {
    try {
      const _data = await axios
        .get('/seeds/viewAll', {
          params: {
            status: ['1'], // all active and inactive
          },
        })
        .then((res) => res.data.data);

      // remove duplicates
      const unique = removeDuplicates(_data, 'name');

      // setOPTION_SEED(
      //   unique.map((v) => ({
      //     label: v.name,
      //     value: `${v.id}-${v.name}`,
      //     group: v.seedSubcategory ? v.seedSubcategory.name : 'NO CATEGORY',
      //   })),
      // );
      gState.seeds.set(unique);

      let options = [];
      unique.map((v) => {
        const subcatName = v.seedSubcategory ? v.seedSubcategory.name : 'NO CATEGORY';

        if (!options.find((o) => o.label === subcatName)) {
          options.push({ label: subcatName, options: [] });
        }
        options.find((o) => o.label === subcatName).options.push(`${v.id}-${v.name}`);
      });

      seedOptions.set(options);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getSeeds: ', error);
    }
  };

  const getFertilizer = async () => {
    try {
      const _data = await axios
        .get('/fertilizers/viewAll', {
          params: {
            status: ['1'], // all active and inactive
          },
        })
        .then((res) => res.data.data);

      // remove duplicates
      const unique = removeDuplicates(_data, 'name');

      setOPTION_FERTILIZER(
        unique.map((v) => ({
          label: v.name,
          value: `${v.id}-${v.name}`,
        })),
      );
      gState.fertilizers.set(unique);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getFertilizer: ', error);
    }
  };

  const getChemicals = async () => {
    try {
      const _data = await axios
        .get('/chemicals/viewAll', {
          params: {
            status: ['1'], // all active and inactive
          },
        })
        .then((res) => res.data.data);

      // remove duplicates
      const unique = removeDuplicates(_data, 'name');

      setOPTION_CHEMICAL(
        unique.map((v) => ({
          label: v.name,
          value: `${v.id}-${v.name}`,
          group: v.chemicalSubcategory ? v.chemicalSubcategory.name : 'NO CATEGORY',
        })),
      );
      gState.chemicals.set(unique);

      // create options like this:
      // const options = [
      //   { label: 'Fruits', options: ['Apple', 'Banana', 'Orange'] },
      //   { label: 'Vegetables', options: ['Carrot', 'Broccoli', 'Spinach'] },
      // ];
      let options = [];
      unique.map((v) => {
        const subcatName = v.chemicalSubcategory ? v.chemicalSubcategory.name : 'NO CATEGORY';

        if (!options.find((o) => o.label === subcatName)) {
          options.push({ label: subcatName, options: [] });
        }
        options.find((o) => o.label === subcatName).options.push(`${v.id}-${v.name}`);
      });

      chemOptions.set(options);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getChemicals: ', error);
    }
  };

  const getOtherProduct = async () => {
    try {
      const _data = await axios
        .get('/otherproducts/viewAll', {
          params: {
            status: ['1'], // all active and inactive
          },
        })
        .then((res) => res.data.data);

      setOPTION_SEED(
        _data.map((v) => ({
          label: v.name,
          value: `${v.id}-${v.name}`,
        })),
      );

      gState.otherProduct.set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getOtherProduct: ', error);
    }
  };

  return {
    getAllCrops,
    getSeeds,
    getFertilizer,
    getChemicals,
    getOtherProduct,
    getCropType,
    OPTION_CROPTYPE,
    OPTION_CHEMICAL,
    OPTION_FERTILIZER,
    OPTION_CROPS,
    OPTION_CROPS_PLANTED,
    OPTION_SEED,
    OPTION_OTHER_PRODUCT,
    chemicals,
    fertilizers,
    chemOptions,
    seedOptions,
    otherProductOptions,
  };
}
