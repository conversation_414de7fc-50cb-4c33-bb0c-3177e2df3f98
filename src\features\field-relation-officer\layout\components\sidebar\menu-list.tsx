'use client';

import { useRouter } from 'next/navigation';

import { useGlobalStatePersist } from '@/lib/store/persist';

import { MENU } from '../../constants';

interface IMenuListProps {
  onItemClick?: () => void;
}

export default function MenuList({ onItemClick }: IMenuListProps) {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();

  return (
    <>
      {MENU.map((menu) => {
        const isActive = menu.id === (gStateP.agronomist.activeMenu.value || 0);

        return (
          <div key={menu.id} className="flex gap-4">
            <div className={`w-1 rounded-r-3xl bg-blue-900 ${isActive ? 'visible' : 'invisible'}`} />

            <button
              className={`flex w-full items-center gap-3 rounded-md px-4 py-2 text-left text-sm font-bold transition duration-300 ease-in-out ${
                isActive ? 'bg-blue-400/20 text-primary' : 'text-slate-400'
              } hover:bg-blue-400/20 hover:text-primary`}
              onClick={() => {
                gStateP.agronomist.activeMenu.set(menu.id);
                if (onItemClick) onItemClick();
                router.push(menu.href);
              }}
            >
              {menu.icon}
              <span className="">{menu.name}</span>
            </button>
          </div>
        );
      })}
    </>
  );
}
