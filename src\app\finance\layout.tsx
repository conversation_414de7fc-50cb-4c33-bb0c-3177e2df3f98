'use client';

import { LogOut, Settings, StoreIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import LoadingScreen from '@/components/LoadingScreen';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { TAB_MARKETPLACE } from '@/lib/constants/enums';
import useLogin from '@/lib/hooks/useLogin';
import useProtected from '@/lib/hooks/utils/useProtected';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

const MENU = [
  {
    id: 0,
    name: 'Marketplace',
    icon: <StoreIcon className="size-5" />,
    href: '/finance',
  },
];

export default function FinanceLayout({ children }) {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { onLogout } = useLogin();
  const { loading } = useProtected();

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'Q' && (e.metaKey || e.ctrlKey) && e.shiftKey) {
        e.preventDefault();
        onLogout();
      } else if (e.key === 's' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        onSettings();
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  const onSettings = async () => {
    router.push('/finance/settings');
  };

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <ScrollArea className="h-screen">
      <main>
        <nav className="flex border-b">
          <div className="w-[290px] py-4">
            <img className="mx-auto h-[2.8rem]" src="/kita-logo.png" alt="kitaph logo" />
          </div>

          <div className="flex flex-1 items-end border-l px-6 pb-3">
            <div className="flex flex-1 items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                {/* find active Title */}
                <h1 className="font-inter text-xl font-bold">
                  {MENU.find((item) => item.id === gStateP.admin.activeMenu.value).name}
                </h1>

                {/* Marketplace Tabs */}
                {gStateP.admin.activeMenu.value === 0 && (
                  <Tabs
                    defaultValue={TAB_MARKETPLACE.EWALLET}
                    value={gStateP.finance.tabs.marketplace.value}
                    onValueChange={(v) => {
                      gStateP.finance.tabs.marketplace.set(v);
                    }}
                    onClick={() => {
                      if (window.location.pathname !== '/finance/') {
                        router.push('/finance/');
                      }
                    }}
                    className=""
                  >
                    <TabsList className="">
                      <TabsTrigger value={TAB_MARKETPLACE.EWALLET}>E-Wallet</TabsTrigger>
                      <TabsTrigger value={TAB_MARKETPLACE.PAYMENTS}>Payments</TabsTrigger>
                    </TabsList>
                  </Tabs>
                )}
              </div>

              {gStateP['user'].value && (
                <div className="flex items-center gap-3">
                  <div>
                    <span>Hello, </span>
                    <span className="font-medium capitalize">{gStateP['user']['user'].email.value.split('@')[0]}</span>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="link" size="icon" className="rounded-full">
                        <Avatar>
                          <AvatarImage
                            src={gStateP['user']['user'].user_img?.value}
                            alt={gStateP['user']['user'].email.value}
                          />
                          <AvatarFallback className="uppercase">
                            {gStateP['user']['user'].email.value.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="absolute -right-4 top-0 w-56">
                      <DropdownMenuLabel className="font-normal">
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium capitalize leading-none">
                            {gStateP['user']['user'].email.value.split('@')[0]}
                          </p>
                          <p className="text-xs leading-none text-muted-foreground">
                            {gStateP['user']['user'].email.value}
                          </p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuGroup>
                        {/* <DropdownMenuItem>
                            <User className="mr-2 h-4 w-4" />
                            <span>Profile</span>
                            <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
                        </DropdownMenuItem> */}
                        <DropdownMenuItem onClick={onSettings}>
                          <Settings className="mr-2 size-4" />
                          <span>Settings</span>
                          <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={onLogout}>
                        <LogOut className="mr-2 size-4" />
                        <span>Log out</span>
                        <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>
          </div>
        </nav>

        <div className="flex">
          {/* menu */}
          <section className="h-[calc(100vh-76.8px)] w-[290px] space-y-3 py-[32px] pr-4">
            {MENU.map((menu) => {
              const isActive = menu.id === gStateP.admin.activeMenu.value;

              return (
                <div key={menu.id} className="flex gap-6">
                  <div className={cn('w-1 bg-blue-900 rounded-r-3xl', isActive ? 'visible' : 'invisible')} />

                  <button
                    className={cn(
                      'w-full flex font-bold items-center gap-3 px-4 py-2 rounded-md transition duration-300 ease-in-out',
                      isActive ? 'text-primary bg-blue-400/20' : 'text-slate-400', // active
                      'hover:text-primary hover:bg-blue-400/20',
                    )}
                    onClick={() => {
                      gStateP.admin.activeMenu.set(menu.id);
                      router.push(menu.href);
                    }}
                  >
                    {menu.icon}
                    <span className="">{menu.name}</span>
                    {gStateP['user'].value &&
                      gStateP['user']['isFinance2'].value &&
                      gState.finance2.forApproval.value > 0 && (
                        <Badge
                          className="ml-auto flex size-6 shrink-0 items-center justify-center rounded-full"
                          variant="destructive"
                        >
                          {gState.finance2.forApproval.value}
                        </Badge>
                      )}
                  </button>
                </div>
              );
            })}
          </section>

          <section className="flex-1 border-l bg-gray-50">{children}</section>
        </div>
      </main>
    </ScrollArea>
  );
}
