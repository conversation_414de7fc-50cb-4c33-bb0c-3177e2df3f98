'use client';

import { useEffect } from 'react';

import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';

import { columns } from './farmer/Column';
import FarmerList from './farmer/List';

export default function SelectUser() {
  const gState = useGlobalState();
  const { viewAllFarmers } = useFarmer();

  useEffect(() => {
    viewAllFarmers();
  }, []);

  return (
    <div className="flex-1 py-12">
      <div className="mx-auto w-[68vw]">
        <FarmerList columns={columns} data={gState.encoder.farmers.get({ noproxy: true })} />
      </div>
    </div>
  );
}
