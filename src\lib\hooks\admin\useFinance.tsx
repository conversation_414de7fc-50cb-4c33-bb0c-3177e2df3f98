'use client';

import { useHookstate } from '@hookstate/core';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { catchError } from '@/lib/utils';

export default function useFinance() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const reqDetails = useHookstate(gStateP.finance1['reqDetails']);
  // const creditScoreDetails = useHookstate(gStateP.selected.creditScoreDetails);

  const creditScoreDetails = useHookstate(gState.creditScoring.selectedLoanHistory?.creditScoreDetails);
  const beforeDetails = useHookstate(gState.creditScoring?.selectedLoanHistory.beforeDetails);
  const duringDetails = useHookstate(gState.creditScoring?.selectedLoanHistory.duringDetails);
  const afterDetails = useHookstate(gState.creditScoring?.selectedLoanHistory.afterDetails);

  const getTopupRequests = async () => {
    try {
      const _topup = await axios
        .get('/admin/topup/viewAll', {
          params: {
            page: gState.finance1.pagination.topupReqs.page.value,
            pageSize: gState.finance1.pagination.topupReqs.pageSize.value,
            search: gState.finance1.pagination.topupReqs.search.value,
            startDate: gState.finance1.pagination.topupReqs.startDate.value,
            endDate: gState.finance1.pagination.topupReqs.endDate.value,
          },
        })
        .then((res) => res.data.data);
      console.log('getTopupRequests: ', _topup);

      gState.finance1.topupReqs.set(_topup);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('getTopupRequests: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getReqDetails = async (id) => {
    try {
      const _details = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/topup/view/${id}`)
        .then((res) => res.data.data);
      console.log('getReqDetails: ', _details);

      gStateP.finance1['reqDetails'].set(_details);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('getReqDetails: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const reqTopup = async (data) => {
    try {
      toast.loading('Requesting topup...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post('/admin/topup/request', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('reqTopup: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: 'Topup request sent',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('reqTopup: ', error);

      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const topupAction = async (action: 'approve' | 'reject', data) => {
    try {
      toast.loading(action === 'approve' ? 'Approving topup...' : 'Rejecting topup...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post(`/admin/topup/${action}`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('topupAction: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: `${action === 'approve' ? 'Approve' : 'Reject'} topup successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('topupAction: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getCreditScore = async (farmerId, creditScoreId) => {
    try {
      const res = await axios
        .get(`/admin/creditscore/farmer/${farmerId}/history/view/${creditScoreId}`)
        .then((res) => res.data);
      // console.log('getCreditScore: ', res.data);

      if (res?.status === 1) {
        console.log('getCreditScore: ', res.data);
        creditScoreDetails.set(res?.data);
        beforeDetails.set(res?.data?.before_stage_credit_score_summary);
        duringDetails.set(res?.data?.during_stage_credit_score_summary);
        afterDetails.set(res?.data?.after_stage_credit_score_summary);
      }
    } catch (e) {
      catchError(e, 'getCreditScore');
    }
  };

  return {
    reqDetails,
    getTopupRequests,
    reqTopup,
    getReqDetails,
    topupAction,
    getCreditScore,
    creditScoreDetails,
    beforeDetails,
    duringDetails,
    afterDetails,
  };
}
