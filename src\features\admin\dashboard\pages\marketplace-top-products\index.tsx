'use client';

import { useRouter } from 'next/navigation';
import { FC } from 'react';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

import { LoadingTable } from '../../components/loading-table';
import { MarketplaceTopProductsTable } from './components/marketplace-top-products-table';
import { columnsMarketplaceTopProducts } from './components/marketplace-top-products-table/columns';
import useMarketplaceTopProducts from './hooks/useMarketplaceTopProducts';

const MarketplaceTopProducts: FC = () => {
  const router = useRouter();
  const { marketplaceTopProductsQuery } = useMarketplaceTopProducts();

  return (
    <div className="p-6 lg:p-8">
      <div className="pb-4">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Marketplace Top Products</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink onClick={() => router.replace('/admin')}>Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Marketplace Top Products</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {marketplaceTopProductsQuery.isLoading ? (
        <LoadingTable />
      ) : (
        <MarketplaceTopProductsTable columns={columnsMarketplaceTopProducts} data={marketplaceTopProductsQuery.data} />
      )}
    </div>
  );
};

export default MarketplaceTopProducts;
