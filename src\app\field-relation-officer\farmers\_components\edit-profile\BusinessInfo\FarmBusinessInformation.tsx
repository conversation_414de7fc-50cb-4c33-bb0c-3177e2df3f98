'use client';

import { Control, Controller, FieldErrors, UseFormGetValues, UseFormRegister, UseFormWatch } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { LOAN_SOURCES, LOAN_USAGE, YES_NO_OPTIONS } from '@/app/field-relation-officer/_components/constants';
import { TBusinessInfoSchema } from '@/app/field-relation-officer/_components/schemas';

interface IFarmBusinessInformationProps {
  register: UseFormRegister<TBusinessInfoSchema>;
  control: Control<TBusinessInfoSchema>;
  errors: FieldErrors<TBusinessInfoSchema>;
  watch: UseFormWatch<TBusinessInfoSchema>;
}

const FarmBusinessInformation = ({ register, control, errors, watch }: IFarmBusinessInformationProps) => {
  const [isMemberOfOrganization, hasPastFarmLoans, hasNeedFarmLoan] = watch([
    'isMemberOfOrganization',
    'hasPastFarmLoans',
    'hasNeedFarmLoan',
  ]);

  return (
    <div className="mt-6">
      <FormTitle title="Farm Business Information" />
      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        <FormField
          name="isMemberOfOrganization"
          label="Member of a Cooperative or Farmers Association?"
          errors={errors}
          required
        >
          <Controller
            control={control}
            name="isMemberOfOrganization"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {isMemberOfOrganization === '1' && (
          <>
            <FormField name="organizationName" label="Name of Organization" errors={errors} required>
              <Input
                {...register('organizationName', { required: 'This field is required.' })}
                placeholder="Enter organization name"
              />
            </FormField>

            <FormField name="organizationPosition" label="Position (if any)" errors={errors}>
              <Input {...register('organizationPosition')} placeholder="Enter your position" />
            </FormField>
          </>
        )}

        <FormField name="hasPastFarmLoans" label="Do you have any past farm loans?" errors={errors} required>
          <Controller
            control={control}
            name="hasPastFarmLoans"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        {hasPastFarmLoans === '1' && (
          <>
            <FormField name="pastFarmLoans" label="Where did you get loans?" errors={errors}>
              <Controller
                control={control}
                name="pastFarmLoans"
                render={({ field: { onChange, value } }) => {
                  const existingValues = Array.isArray(value) ? value : (value || '').split(',').map((v) => v.trim());
                  const currentOptions = existingValues.filter((v) => v).map((v) => ({ label: v, value: v }));

                  const mergedOptions = [
                    ...LOAN_SOURCES,
                    ...currentOptions.filter(
                      (opt) => opt.value && opt.label && !LOAN_SOURCES.some((def) => def.value === opt.value),
                    ),
                  ];
                  return (
                    <MultipleSelector
                      value={currentOptions}
                      onChange={(selected) => onChange(selected.map((item) => item.value))}
                      defaultOptions={mergedOptions}
                      placeholder="Select from selection or create new"
                      creatable
                      emptyIndicator={
                        <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                          No results found.
                        </p>
                      }
                    />
                  );
                }}
              />
            </FormField>

            <FormField name="hasPastFarmLoanPaid" label="Are these already paid?" errors={errors} required>
              <Controller
                control={control}
                name="hasPastFarmLoanPaid"
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Answer" />
                    </SelectTrigger>
                    <SelectContent>
                      {YES_NO_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </FormField>
          </>
        )}

        <FormField name="hasNeedFarmLoan" label="Do you need a farm loan?" errors={errors} required>
          <Controller
            control={control}
            name="hasNeedFarmLoan"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        {hasNeedFarmLoan === '1' && (
          <>
            <FormField name="needFarmLoanReason" label="Where it will be used?" errors={errors}>
              <Controller
                control={control}
                name="needFarmLoanReason"
                render={({ field: { onChange, value } }) => {
                  const existingValues = Array.isArray(value) ? value : (value || '').split(',').map((v) => v.trim());
                  const currentOptions = existingValues.filter((v) => v).map((v) => ({ label: v, value: v }));

                  const mergedOptions = [
                    ...LOAN_USAGE,
                    ...currentOptions.filter(
                      (opt) => opt.value && opt.label && !LOAN_USAGE.some((def) => def.value === opt.value),
                    ),
                  ];
                  return (
                    <MultipleSelector
                      value={currentOptions}
                      onChange={(selected) => onChange(selected.map((item) => item.value))}
                      defaultOptions={mergedOptions}
                      placeholder="Select from selection or create new"
                      creatable
                      emptyIndicator={
                        <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                          No results found.
                        </p>
                      }
                    />
                  );
                }}
              />
            </FormField>
          </>
        )}
      </div>
    </div>
  );
};

export default FarmBusinessInformation;
