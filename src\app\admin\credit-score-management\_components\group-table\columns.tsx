'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { FilesIcon, MoreHorizontal, Pencil, SettingsIcon, ToggleLeft, ToggleRight, UserPlus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Controller, useForm } from 'react-hook-form';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { getUserType } from '@/lib/constants';
import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useUsers, { EditUserSchema, UserSchema } from '@/lib/hooks/useUsers';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

export const creditScoreGroupColumns = [
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;

      return <div className="min-w-max">{data.name}</div>;
    },
    accessorFn: (row) => `${row.name}`,
  },
  {
    id: 'description',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Description" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="line-clamp-2">{data.description}</div>;
    },
    accessorFn: (row) => `${row.description}`,
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const variant = {
        1: 'success',
        0: 'error',
      };

      return (
        <div className="flex items-center gap-3">
          <div className={cn('rounded-full w-2 h-2', data.status === 1 ? 'bg-green-500' : 'bg-red-500')}></div>
          <div>{data.status === 1 ? 'Active' : 'Inactive'}</div>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <Action row={row} />,
  },
];

export const CreditScoreGroupHeader = () => {
  const { createGroup } = useCreditScoreMgt();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: '',
      description: '',
    },
  });

  return (
    <>
      <Dialog>
        <DialogTrigger asChild>
          <div className="flex justify-end">
            <Button className="h-8" size="sm">
              Add Credit Score Group
            </Button>
          </div>
        </DialogTrigger>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-primary">Add Credit Score Group</DialogTitle>
            <DialogDescription>{`Fill up the forms to add a new credit score group. Click "Add" when you're ready.`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(createGroup)}>
            <div className="mt-3 grid gap-4">
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="name" className="pb-1 font-normal">
                  Name
                </Label>
                <Input
                  {...register('name', { required: 'Credit Score Group Name is required' })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.name && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter credit score group name"
                />
                {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="description" className="pb-1 font-normal">
                  Description
                </Label>
                <Textarea
                  {...register('description', { required: 'Description is required' })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.description && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  placeholder="Enter description"
                />
                {errors.description && <p className="form-error">{`${errors.description.message}`}</p>}
              </div>
            </div>

            <div className="flex justify-between gap-2 pt-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              <Button className="px-12" type="submit">
                Add
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

const Action = ({ row }) => {
  const data = row.original;
  const gStateP = useGlobalStatePersist();
  const { updateGroup, duplicateGroup, activateGroup, deactivateGroup } = useCreditScoreMgt();
  const router = useRouter();

  const deactivateConfirm = useHookstate(false);
  const activateConfirm = useHookstate(false);
  const duplicateConfirm = useHookstate(false);
  const editDialog = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      name: '',
      description: '',
      status: '',
    },
  });

  const onSubmit = async (submittedData) => {
    try {
      const updatedData = {
        ...submittedData,
        creditScoreGroupId: data.id,
      };
      await updateGroup(updatedData);
    } catch (e) {
      console.error(e);
    } finally {
      editDialog.set(false);
    }
  };

  return (
    <>
      <div className="flex justify-end gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="size-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuItem
              className="flex items-center"
              onClick={() => {
                reset({
                  name: data.name,
                  description: data.description,
                  status: `${data.status}`,
                });

                editDialog.set(true);
              }}
            >
              <Pencil className="mr-2 size-4" />
              <span>Edit</span>
            </DropdownMenuItem>

            <DropdownMenuItem
              className="flex items-center"
              onClick={() => {
                duplicateConfirm.set(true);
              }}
            >
              <FilesIcon className="mr-2 size-4" />
              <span>Duplicate</span>
            </DropdownMenuItem>

            <DropdownMenuItem
              className="flex items-center"
              onClick={() => {
                gStateP.selected['creditScoreGroup'].set(data);
                router.push(`./details?id=${data.id}`);
              }}
            >
              <SettingsIcon className="mr-2 size-4" />
              <span>Loan Stage Rules</span>
            </DropdownMenuItem>

            {data.status === 1 && (
              <>
                <DropdownMenuItem className="flex items-center" onClick={() => deactivateConfirm.set(true)}>
                  <ToggleLeft className="mr-2 size-4" />
                  <span>Deactivate</span>
                </DropdownMenuItem>
              </>
            )}

            {data.status === 0 && (
              <>
                <DropdownMenuItem className="flex items-center" onClick={() => activateConfirm.set(true)}>
                  <ToggleRight className="mr-2 size-4" />
                  <span>Activate</span>
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Deactivate Confirmation */}
        <AlertDialog open={deactivateConfirm.value} onOpenChange={deactivateConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will deactivate the credit score group {data.name}. This process cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  deactivateGroup({
                    creditScoreGroupId: data.id,
                    name: data.name,
                  });
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Activate Confirmation */}
        <AlertDialog open={activateConfirm.value} onOpenChange={activateConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will activate the credit score group {data.name}. This process cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  activateGroup({
                    creditScoreGroupId: data.id,
                    name: data.name,
                  });
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Duplicate Confirmation */}
        <AlertDialog open={duplicateConfirm.value} onOpenChange={duplicateConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will duplicate the credit score group {data.name}. This process cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  duplicateGroup({
                    name: data.name,
                    description: data.description,
                  });
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Edit Dialog */}
        <Dialog open={editDialog.value} onOpenChange={editDialog.set}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-primary">Edit Credit Score Group</DialogTitle>
              <DialogDescription>
                {`Fill up the forms to update the credit score group. Click "Update" when you're ready.`}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="mt-3 grid gap-4">
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="name" className="pb-1 font-normal">
                    Name
                  </Label>
                  <Input
                    {...register('name', { required: 'Credit Score Group Name is required' })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.name && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter credit score group name"
                  />
                  {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="description" className="pb-1 font-normal">
                    Description
                  </Label>
                  <Textarea
                    {...register('description', { required: 'Description is required' })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.description && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    placeholder="Enter description"
                  />
                  {errors.description && <p className="form-error">{`${errors.description.message}`}</p>}
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="status" className="pb-1 font-normal">
                    Status
                  </Label>
                  <Controller
                    control={control}
                    name="status"
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} defaultValue={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="1">Activate</SelectItem>
                            <SelectItem value="0">Inactive</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
                </div>
              </div>

              <div className="flex justify-between gap-2 pt-6">
                <DialogClose asChild>
                  <Button className="px-12" variant="outline" type="button">
                    Cancel
                  </Button>
                </DialogClose>
                <Button className="px-12" type="submit">
                  Update
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};
