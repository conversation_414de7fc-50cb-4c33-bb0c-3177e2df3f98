'use client';

import { useEffect, useState } from 'react';

import { cn } from '@/lib/utils';

const CircularProgress = ({
  percentage = 0,
  size = 'size-[100px]',
  progressBg = 'text-transparent',
  progressClassName = 'text-green-500',
  className = '',
  labelClassName = 'text-lg font-medium text-gray-800',
  animationDuration = '1.5s',
  total = 100,
  ...props
}) => {
  const circleRadius = 45;
  const circumference = 2 * Math.PI * circleRadius;
  const [offset, setOffset] = useState(circumference);

  useEffect(() => {
    const newOffset = circumference - (percentage / total) * circumference;
    setOffset(newOffset);
  }, [percentage, circumference]);

  return (
    <div className={cn('relative rounded-full overflow-hidden', size)} {...props}>
      <svg className={cn('-rotate-90', size, className)} width="100" height="100" viewBox="0 0 100 100">
        <circle
          className={cn(progressBg)}
          strokeWidth="10"
          stroke="currentColor"
          fill="transparent"
          r={circleRadius}
          cx="50"
          cy="50"
        />
        <circle
          className={cn(progressClassName)}
          strokeWidth="10"
          strokeDasharray={circumference}
          strokeDashoffset={circumference}
          style={{
            transition: `stroke-dashoffset ${animationDuration} ease-in-out`,
            strokeDashoffset: offset,
            transitionDelay: `0.5s`,
          }}
          strokeLinecap="round"
          stroke="currentColor"
          fill="transparent"
          r={circleRadius}
          cx="50"
          cy="50"
        />
      </svg>
      <div className={cn('absolute inset-0 grid place-items-center', labelClassName)}>{`${percentage}%`}</div>
    </div>
  );
};

export default CircularProgress;
