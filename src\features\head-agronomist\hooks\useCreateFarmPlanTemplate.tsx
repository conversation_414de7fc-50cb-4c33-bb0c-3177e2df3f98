'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { ITemplateForm } from '../components/templates/create-form/types/template';

// Mutation function for creating farm plan template
const createFarmPlanTemplate = async (data: ITemplateForm) => {
  const { data: res } = await axios.post('/agronomist/farmplan/template/create', data);
  return res;
};

// Hook for creating farm plan templates
export const useCreateFarmPlanTemplate = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Create mutation for API call
  const createTemplateMutation = useMutation({
    mutationFn: createFarmPlanTemplate,
  });

  // Submit template function
  const submitTemplate = async (data: ITemplateForm) => {
    try {
      setIsSubmitting(true);

      // Format the data, especially dates in subItems
      const formData: ITemplateForm = {
        ...data,
        items: data.items.map((item) => ({
          ...item,
          subItems: item.subItems.map((subItem) => ({
            ...subItem,
            expectedDate:
              subItem.expectedDate instanceof Date ? format(subItem.expectedDate, 'yyyy-MM-dd') : subItem.expectedDate,
          })),
        })),
      };

      console.log('Form submitted:', formData);
      const res = await createTemplateMutation.mutateAsync(formData);

      console.log('Form response:', res);
      toast.success('Success', {
        description: res.message || 'Farm plan template created successfully',
      });

      // Refetch templates
      queryClient.refetchQueries({
        queryKey: ['farmPlanTemplates'],
      });

      // Navigate back to templates list or dashboard
      router.push('/head-agronomist');
    } catch (e: any) {
      const error = e?.response?.data?.message || e.message;
      console.error('Error creating template:', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitTemplate,
    isSubmitting,
    createTemplateMutation,
  };
};
