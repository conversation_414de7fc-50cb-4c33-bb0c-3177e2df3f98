'use client';

import Link from 'next/link';
import { useEffect } from 'react';
import { useDebounce } from 'use-debounce';

import { Button } from '@/components/ui/button';

import useChemicals from '@/lib/hooks/useChemicals';
import useMarketplace from '@/lib/hooks/useMarketplace';
import useSeeds from '@/lib/hooks/useSeeds';
import { useGlobalState } from '@/lib/store';

import ProductTable from './_components/product-table';
import { columns } from './_components/product-table/columns';

export default function OrderProductsPage() {
  const gState = useGlobalState();
  const { getProducts } = useMarketplace();
  const { getSubcategory } = useChemicals();
  const { getSubcategory: getSeedsSub } = useSeeds();

  const [search] = useDebounce(gState.admin.pagination.products.search.value, 1000);

  useEffect(() => {
    getProducts(['1']);
  }, [
    search,
    gState.admin.pagination.products.page,
    gState.admin.pagination.products.pageSize,
    gState.admin.pagination.products.productType,
    gState.admin.pagination.products.productTypeCategory,
  ]);

  useEffect(() => {
    Promise.all([getSubcategory(), getSeedsSub()]);
  }, []);

  return (
    <div className="space-y-6 p-12">
      <ProductTable
        data={gState.admin.marketplace.products.data.get({ noproxy: true })}
        columns={columns}
        meta={gState.admin.marketplace.products['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
