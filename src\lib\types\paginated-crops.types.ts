export interface ICropsPaginatedResponse {
  status: 1;
  data: {
    meta: IPaginationMeta;
    data: ICropWithRelations[];
  };
}

export interface IPaginationMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  first_page: number;
  first_page_url: string;
  last_page_url: string;
  next_page_url: string | null;
  previous_page_url: string | null;
}

export interface ICropWithRelations {
  id: number;
  name: string;
  status: ECropStatus;
  created_at: string;
  updated_at: string;
  harvest_days: number;
  is_sync: ECropSyncStatus;
  image: string | null;
  keywords: string | null;
  cropPriceRanges: ICropPriceRange[];
  cropPrices: ICropPrice[];
  sap_item_code: string | null;
}

export interface ICropPrice {
  id: number;
  crop_id: number;
  selling_price: number;
  production_price: number;
  created_at: string;
  updated_at: string;
  is_sync: number;
}

export interface ICropPriceRange {
  id: number;
  crop_id: number;
  low_price: number;
  high_price: number;
  low_baptc_price: number;
  high_baptc_price: number;
  low_nvat_price: number;
  high_nvat_price: number;
  created_at: string;
  updated_at: string;
}

export enum ECropStatus {
  INACTIVE = 0,
  ACTIVE = 1,
}

export enum ECropSyncStatus {
  NOT_SYNCED = 0,
  SYNCED = 1,
}
