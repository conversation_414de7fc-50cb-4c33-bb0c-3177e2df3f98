import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { farmInformationSchema, TFarmInformationSchema } from '@/app/field-relation-officer/_components/schemas';
import { IFarmer, IFarmInformation, ISafeParseResult } from '@/app/field-relation-officer/_components/types';
import useFarmer from '@/lib/hooks/fro/useFarmer';

import FarmLocation from './FarmLocation';
import FarmPractices from './FarmPractices';

interface IFarmInformationProps {
  data: IFarmer;
}

const FarmInformation = ({ data }: IFarmInformationProps) => {
  const farmerInfo = data?.farmerInfo;
  const { updateFarmer } = useFarmer();
  const {
    register,
    control,
    formState: { errors },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TFarmInformationSchema>({
    resolver: zodResolver(farmInformationSchema),
    defaultValues: {
      // Farm Location
      farmAddressHouseNumber: farmerInfo?.farm_address_house_number || '',
      farmAddressStreet: farmerInfo?.farm_address_house_number || '',
      farmAddressRegion: '',
      farmAddressProvince: farmerInfo?.farm_address_province || '',
      farmAddressCity: farmerInfo?.farm_address_city || '',
      farmAddressBarangay: farmerInfo?.farm_address_barangay || '',
      farmAddressZipCode: farmerInfo?.farm_address_zip_code || '',
      farmArea: farmerInfo?.farm_area?.toString() || '',
      farmOwnership: farmerInfo?.farm_ownership?.toString() || '',
      otherFarmOwnership: farmerInfo?.other_farm_ownership || '',
      cropsPlanted: data?.cropsPlanted.map(({ crop }) => `${crop.id}-${crop.name}`)?.slice() || [],

      // Farm Practices
      waterSource: farmerInfo.water_source || '',
      fertilizerUsed: farmerInfo.fertilizer_used?.toString() || '',
      pesticideUsed: farmerInfo.pesticide_used?.toString() || '',
      farmImplements: farmerInfo.farm_implements || '',
    },
  });

  const handleNext = handleSubmit(async () => {
    const { data, success } = farmInformationSchema.safeParse(values()) as ISafeParseResult<IFarmInformation>;

    if (success) {
      await updateFarmer({ ...data, cropsPlanted: data.cropsPlanted.map((item) => item.split('-')[0].trim()) });
      console.log('FarmPractices:', data);
    }
  });

  return (
    <form onSubmit={handleNext}>
      <FarmLocation
        register={register}
        control={control}
        errors={errors}
        values={values}
        watch={watch}
        setValue={setValue}
      />
      <FarmPractices control={control} errors={errors} />

      <div className="mt-16 flex justify-end gap-4">
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
};

export default FarmInformation;
