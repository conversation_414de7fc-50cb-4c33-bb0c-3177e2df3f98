'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { QrCodeIcon } from 'lucide-react';
import QRCode from 'react-qr-code';

import { Button } from '@/components/ui/button';

import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalStatePersist } from '@/lib/store/persist';

export default function AccountInfo() {
  const gStateP = useGlobalStatePersist();
  const account = useHookstate(gStateP.selected.account.info);
  const { generateQR } = useFarmer();

  return (
    <div className="card">
      <h1 className="text-center text-xl font-bold text-kitaph-primary 2xl:text-left">Account Information</h1>

      <div className="mt-6 flex flex-col gap-8 2xl:flex-row">
        {account['farmer']['qr_code'].value ? (
          <div
            key={account['farmer']['qr_code'].value}
            className="cursor-pointer"
            style={{ height: 'auto', margin: '0 auto', maxWidth: 120, width: '100%' }}
            onClick={() => {
              gStateP.print.farmerId.set(account.get({ noproxy: true }));
              if (typeof window !== 'undefined') {
                window.open('/print/farmer-id', '_blank');
              }
            }}
          >
            <QRCode
              size={312}
              style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
              value={account['farmer']['qr_code'].value}
              viewBox={`0 0 256 256`}
            />
          </div>
        ) : (
          <Button onClick={() => generateQR(account['farmer']['id'].value, account['id'].value)}>
            <QrCodeIcon className="mr-2 size-4" />
            <span>Generate QR</span>
          </Button>
        )}

        <div className="flex-1">
          <dl className="grid gap-4 font-dmSans xl:grid-cols-2">
            <div>
              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {account['farmer']['first_name'].value} {account['farmer']['last_name'].value}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {`ID${account['id'].value.toString().padStart(9, '0')}`}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {account['farmer']['mobile_number'].value || '-'}
                </dd>
              </div>
            </div>

            <div>
              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">-</dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Birthday</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {account['farmer']['birth_date'].value
                    ? format(account['farmer']['birth_date'].value, 'MMMM dd, yyyy')
                    : '-'}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Email Address</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {account['email'].value}
                </dd>
              </div>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
}
