'use client';

import { bulkImportColumns } from '@/app/admin/(accounts)/accounts/_components/request/members-table/bulk-import-columns';
import { columns } from '@/app/admin/(accounts)/accounts/_components/request/members-table/columns';
import { useGlobalState } from '@/lib/store';

export default function useUserRequestsTab() {
  const gState = useGlobalState();

  const defaultData = gState.admin.members.data.get({ noproxy: true });
  const usersBulkData = gState.admin.usersBulk.data.get({ noproxy: true });

  return {
    0: {
      columns: columns,
      data: defaultData,
    },
    1: {
      columns: columns,
      data: defaultData,
    },
    2: {
      columns: columns,
      data: defaultData,
    },
    3: {
      columns: columns,
      data: defaultData,
    },
    4: {
      columns: bulkImportColumns,
      data: usersBulkData,
    },
  };
}
