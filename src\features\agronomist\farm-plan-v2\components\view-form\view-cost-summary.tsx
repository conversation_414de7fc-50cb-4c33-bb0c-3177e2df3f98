'use client';

import { useMemo } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IFarmPlanResponse } from '../../hooks/useEditFarmPlan';

interface IViewCostSummaryProps {
  farmPlan: IFarmPlanResponse;
}

export default function ViewCostSummary({ farmPlan }: IViewCostSummaryProps) {
  // Calculate costs from farm plan items
  const costSummary = useMemo(() => {
    const items = farmPlan.farmPlanItems || [];

    let farmInputsContingency = 0;
    let cashRequirements = 0;
    let farmersEquity = 0;

    items.forEach((item) => {
      const itemTotal = item.total_amount || 0;

      // Categorize based on item type
      if (item.type === 'INPUTS') {
        farmInputsContingency += itemTotal;
      } else if (item.type === 'CASH_REQUIREMENTS') {
        cashRequirements += itemTotal;
      } else if (
        item.type === 'FARMERS_EQUITY' ||
        item.type === 'NON_CASH' ||
        item.type === 'KITA_SUBSIDIZED' ||
        item.type === 'NON_KITA_SUBSIDIZED'
      ) {
        farmersEquity += itemTotal;
      }
    });

    const totalProductionCosts = farmInputsContingency + cashRequirements + farmersEquity;

    return {
      farmInputsContingency,
      cashRequirements,
      farmersEquity,
      totalProductionCosts,
      farmInputsPercentage: totalProductionCosts > 0 ? (farmInputsContingency / totalProductionCosts) * 100 : 0,
      cashRequirementsPercentage: totalProductionCosts > 0 ? (cashRequirements / totalProductionCosts) * 100 : 0,
      farmersEquityPercentage: totalProductionCosts > 0 ? (farmersEquity / totalProductionCosts) * 100 : 0,
    };
  }, [farmPlan.farmPlanItems]);

  const formatCurrency = (value: number) => {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-kitaph-primary">Cost Summary</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {/* Farm Inputs with Contingency */}
          <div className="grid grid-cols-4 gap-4 p-3 text-sm">
            <div className="font-medium">{formatPercentage(costSummary.farmInputsPercentage)}</div>
            <div className="col-span-2 text-kitaph-blue">Farm Inputs with Contingency (FIC)</div>
            <div className="text-right font-medium">{formatCurrency(costSummary.farmInputsContingency)}</div>
          </div>

          {/* Cash Requirements */}
          <div className="grid grid-cols-4 gap-4 p-3 text-sm">
            <div className="font-medium">{formatPercentage(costSummary.cashRequirementsPercentage)}</div>
            <div className="col-span-2 text-kitaph-blue">Cash Requirements (CR)</div>
            <div className="text-right font-medium">{formatCurrency(costSummary.cashRequirements)}</div>
          </div>

          {/* Farmer's Equity */}
          <div className="grid grid-cols-4 gap-4 p-3 text-sm">
            <div className="font-medium">{formatPercentage(costSummary.farmersEquityPercentage)}</div>
            <div className="col-span-2 text-kitaph-blue">Farmer&apos;s Equity (FE)</div>
            <div className="text-right font-medium">{formatCurrency(costSummary.farmersEquity)}</div>
          </div>

          {/* Total Production Costs */}
          <div className="grid grid-cols-4 gap-4 bg-blue-50 p-3 text-sm font-semibold">
            <div className="text-kitaph-blue">100%</div>
            <div className="col-span-2 text-right font-bold text-kitaph-blue">Total Production Costs (TPC)</div>
            <div className="text-right font-bold text-kitaph-blue">
              {formatCurrency(costSummary.totalProductionCosts)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
