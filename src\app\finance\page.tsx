'use client';

import { TAB_MARKETPLACE } from '@/lib/constants/enums';
import { useGlobalStatePersist } from '@/lib/store/persist';

import Finance1Page from './_components/marketplace/ewallet/finance1';
import Finance2Page from './_components/marketplace/ewallet/finance2';
import PaymentsPage from './_components/marketplace/payments/finance1/Payments';
import Finance2PaymentPage from './_components/marketplace/payments/finance2/Payments';

export default function Admin() {
  const gStateP = useGlobalStatePersist();

  return (
    <div>
      {gStateP.admin.activeMenu.value === 0 && (
        <>
          {gStateP.finance.tabs.marketplace.value === TAB_MARKETPLACE.EWALLET && (
            <>
              {gStateP['user'].value && gStateP['user']['isFinance1'].value && <Finance1Page />}
              {gStateP['user'].value && gStateP['user']['isFinance2'].value && <Finance2Page />}
            </>
          )}
          {gStateP.finance.tabs.marketplace.value === TAB_MARKETPLACE.PAYMENTS && (
            <>
              {gStateP['user'].value && gStateP['user']['isFinance1'].value && <PaymentsPage />}
              {gStateP['user'].value && gStateP['user']['isFinance2'].value && <Finance2PaymentPage />}
            </>
          )}
        </>
      )}
    </div>
  );
}
