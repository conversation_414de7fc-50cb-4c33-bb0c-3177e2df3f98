'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { ICreateFarmPlan } from '../types/create-farm-plan.types';

// Mutation function for creating farm plan
const createFarmPlan = async (data: ICreateFarmPlan) => {
  const { data: res } = await axios.post('/agronomist/farmplan/create', data);
  return res;
};

// Hook for creating farm plans
export const useCreateFarmPlan = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Create mutation for API call
  const createFarmPlanMutation = useMutation({
    mutationFn: createFarmPlan,
  });

  // Submit farm plan function
  const submitFarmPlan = async (data: ICreateFarmPlan) => {
    try {
      setIsSubmitting(true);

      // Format the data, especially dates in subItems and main date fields
      const formData: ICreateFarmPlan = {
        ...data,
        agronomistValidUntil: data.agronomistValidUntil
          ? format(new Date(data.agronomistValidUntil), 'yyyy-MM-dd')
          : '',
        headAgronomistValidUntil: data.headAgronomistValidUntil
          ? format(new Date(data.headAgronomistValidUntil), 'yyyy-MM-dd')
          : '',
        items: data.items.map((item) => ({
          ...item,
          subItems: item.subItems.map((subItem) => ({
            ...subItem,
            expectedDate: subItem.expectedDate
              ? subItem.expectedDate instanceof Date
                ? format(subItem.expectedDate, 'yyyy-MM-dd')
                : subItem.expectedDate
              : '',
          })),
        })),
      };

      console.log('Form submitted:', formData);
      const res = await createFarmPlanMutation.mutateAsync(formData);

      console.log('Form response:', res);
      toast.success('Success', {
        description: res.message || 'Farm plan created successfully',
      });

      // Refetch farm plans
      queryClient.refetchQueries({
        queryKey: ['farmPlans'],
      });

      // Navigate back to farm plans list
      router.push('/agronomist/farm-plan');
    } catch (e: any) {
      const error = e?.response?.data?.message || e.message;
      console.error('Error creating farm plan:', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitFarmPlan,
    isSubmitting,
    createFarmPlanMutation,
  };
};
