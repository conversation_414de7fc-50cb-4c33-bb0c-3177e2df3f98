'use client';

import { useGlobalState } from '@/lib/store';

import { breedColumns } from '../seed-table/breed-columns';
import { columns } from '../seed-table/columns';
import { subCategoryColumns } from '../seed-table/subcategory-columns';
import { varietyColumns } from '../seed-table/variety-columns';

export default function useSeedsTab() {
  const gState = useGlobalState();

  return {
    all: {
      id: 'seed',
      columns: columns,
      data: gState.admin.seeds.data.get({ noproxy: true }),
    },
    subcat: {
      id: 'seed-subcategory',
      columns: subCategoryColumns,
      data: gState.admin.seeds.subcategory.get({ noproxy: true }),
    },
    variety: {
      id: 'seed-variety',
      columns: varietyColumns,
      data: gState.admin.seeds.variety.get({ noproxy: true }),
    },
    breed: {
      id: 'seed-breed',
      columns: breedColumns,
      data: gState.admin.seeds.breed.get({ noproxy: true }),
    },
  };
}
