'use client';

import { useEffect } from 'react';

import useFinance1 from '@/lib/hooks/finance1/useFinance1';
import { useGlobalState } from '@/lib/store';

import { TopupReqsTable } from './topup-reqs-table';
import { columns } from './topup-reqs-table/columns';

export default function Finance1Page() {
  const gState = useGlobalState();
  const { getTopupRequests } = useFinance1();

  useEffect(() => {
    getTopupRequests();
  }, [
    gState.finance1.pagination.topupReqs.pageSize,
    gState.finance1.pagination.topupReqs.page,
    gState.finance1.pagination.topupReqs.search,
    gState.finance1.pagination.topupReqs.startDate,
    gState.finance1.pagination.topupReqs.endDate,
  ]);

  return (
    <div className="p-8">
      <TopupReqsTable
        data={gState.finance1.topupReqs.data.get({ noproxy: true })}
        columns={columns}
        meta={gState.finance1.topupReqs['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
