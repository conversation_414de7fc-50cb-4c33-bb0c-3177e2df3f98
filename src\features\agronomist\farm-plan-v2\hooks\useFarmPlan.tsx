'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { IFarmPlan, IFarmPlanResponse } from '../types/farm-plan.types';

// State
const initialState = {
  page: 1,
  pageSize: 10,
  search: '',
};

const farmPlanState = hookstate(initialState, devtools({ key: 'farmPlanState' }));

// Fetcher
const fetchFarmPlans = async () => {
  const { data } = await axios.get(`/agronomist/farmplan/viewAll`, {
    params: {
      page: farmPlanState.page.value,
      pageSize: farmPlanState.pageSize.value,
      search: farmPlanState.search.value,
    },
  });
  return data.data as IFarmPlanResponse;
};

// Hooks
function useFarmPlan() {
  const state = useHookstate(farmPlanState);
  const gStateP = useGlobalStatePersist();

  const searchDebounce = useHookstateDebounce(state.search, 500);

  const farmPlansQuery = useQuery({
    queryKey: [
      'farmPlans',
      {
        email: gStateP.user.user.email.value,
        page: state.page.value,
        pageSize: state.pageSize.value,
        search: searchDebounce.value,
      },
    ],
    queryFn: fetchFarmPlans,
    enabled: !!gStateP.user.value,
  });

  return {
    farmPlansQuery,
    state,
  };
}

export { farmPlanState };
export default useFarmPlan;
