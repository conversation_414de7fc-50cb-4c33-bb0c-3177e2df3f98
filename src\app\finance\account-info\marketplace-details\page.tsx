'use client';

import { useHookstate } from '@hookstate/core';
import {
  Timeline,
  TimelineConnector,
  TimelineHeader,
  TimelineIcon,
  TimelineItem,
  Typography,
} from '@material-tailwind/react';
import { format } from 'date-fns';
import { BellIcon, PackageCheckIcon, PackageIcon, PackageSearchIcon, PackageXIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useRef } from 'react';
import { FaMoneyBillWave, FaWallet } from 'react-icons/fa';
import { useReactToPrint } from 'react-to-print';
import { toast } from 'sonner';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>rollA<PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import {
  FulfillmentTypeLabels,
  PaymentMethodType,
  PRODUCT_TYPE_ACCESSOR,
} from '@/app/admin/marketplace/_components/Enums';
import { isSoaEnabled } from '@/lib/config/features';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { useSoaStore } from '@/lib/store/soa-store';
import { ISoaData } from '@/lib/store/soa-store.types';
import { cn } from '@/lib/utils';

import { IOrderInfo } from '../_components/order-info.types';
import PrintSoa from '../_components/print-soa';

export default function OrderDetailsPage() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const data = useHookstate(null as IOrderInfo);
  const { getSoa, soa } = useSoaStore();

  const contentRef = useRef<HTMLDivElement>(null);
  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: `SOA_${soa?.formatted?.farmerName.value.replaceAll(' ', '')}_${soa?.formatted?.referenceNumber.value}_${format(new Date(), 'yyyy-MM-dd')}`,
    onPrintError: (error) => {
      toast.error('Oops! Something went wrong', {
        description: 'Failed to export SOA. Please try again later.',
      });
    },
  });

  const address = data.value && data['shipping_address'].value ? JSON.parse(data['shipping_address'].value) : {};
  const paymentMethod = useHookstate({
    cash: {
      checked: false,
      amount: 0,
    },
    ewallet: {
      checked: false,
      amount: 0,
    },
  });

  useEffect(() => {
    if (gStateP.finance.accountInfo['marketplaceDetails'].value) {
      const info = gStateP.finance.accountInfo['marketplaceDetails'].get({ noproxy: true }) as IOrderInfo;
      data.set(info);

      if (isSoaEnabled) {
        getSoa(info.id);
      }

      const totalPrice = gStateP.finance.accountInfo['marketplaceDetails']['total_price'].value;
      const walletAllocation = gStateP.finance.accountInfo['marketplaceDetails']['wallet_allocation'].value;
      const paymentMethodType = gStateP.finance.accountInfo['marketplaceDetails']['payment_method'].value;

      paymentMethod.set({
        cash: {
          checked: paymentMethodType === PaymentMethodType.CASH || paymentMethodType === PaymentMethodType.MULTIPLE,
          amount:
            paymentMethodType === PaymentMethodType.MULTIPLE
              ? totalPrice - walletAllocation
              : paymentMethodType === PaymentMethodType.CASH
                ? totalPrice
                : 0,
        },
        ewallet: {
          checked: paymentMethodType === PaymentMethodType.EWALLET || paymentMethodType === PaymentMethodType.MULTIPLE,
          amount:
            paymentMethodType === PaymentMethodType.EWALLET
              ? totalPrice
              : paymentMethodType === PaymentMethodType.MULTIPLE
                ? walletAllocation
                : 0,
        },
      });
    }
  }, [gStateP.finance.accountInfo['marketplaceDetails']]);

  return (
    <div>
      {data.value && (
        <div className="space-y-6 p-6 xl:p-12">
          <div className="flex justify-between">
            <div className="">
              <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Order Information</h1>
              <Breadcrumb className="mt-2">
                <BreadcrumbList>
                  <BreadcrumbItem className="cursor-pointer">
                    <BreadcrumbLink onClick={() => router.back()}>Account Information</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Order Information</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {isSoaEnabled && (
              <>
                {soa.value ? (
                  <div>
                    <Button className="px-10" onClick={() => reactToPrintFn()}>
                      Print
                    </Button>
                    <PrintSoa contentRef={contentRef} orderInfo={soa.get({ noproxy: true }) as ISoaData} />
                  </div>
                ) : (
                  <Skeleton className="h-10 w-32" />
                )}
              </>
            )}
          </div>

          <div className="font-dmSans text-xl font-bold text-primary">Order Details</div>
          <div className="grid items-start gap-6 border-b-2 border-dashed border-gray-300 pb-6 md:grid-cols-2">
            {/* Left */}
            <div className="grid gap-3">
              <div className="grid sm:grid-cols-3 sm:gap-6">
                <div className="text-sm text-primary/60 sm:text-base">Order ID</div>
                <div className="text-primary sm:col-span-2">{data['reference_number'].value}</div>
              </div>
              <div className="grid sm:grid-cols-3 sm:gap-6">
                <div className="text-sm text-primary/60 sm:text-base">Buyer Name</div>
                <div className="text-primary sm:col-span-2">{`${data['customer']['farmer']['first_name'].value} ${data['customer']['farmer']['last_name'].value}`}</div>
              </div>
              <div className="grid sm:grid-cols-3 sm:gap-6">
                <div className="text-sm text-primary/60 sm:text-base">Address</div>
                <div className="text-primary sm:col-span-2">
                  {`${address.addressHouseNumber ?? ''} 
                ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
                ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
                ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
                ${address.addressZipCode || ''}`}
                </div>
              </div>
              <div className="grid sm:grid-cols-3 sm:gap-6">
                <div className="text-sm text-primary/60 sm:text-base">Contact No.</div>
                <div className="text-primary sm:col-span-2">{`${data['customer']['farmer']['mobile_number'].value}`}</div>
              </div>
            </div>

            {/* Right */}
            <div className="grid gap-3">
              <div className="grid sm:grid-cols-3 sm:gap-6">
                <div className="text-sm text-primary/60 sm:text-base">
                  <span>Date & Time Ordered</span>
                </div>
                <div className="text-primary sm:col-span-2">
                  <div>
                    {new Date(data['created_at'].value).toLocaleString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      hourCycle: 'h12',
                    })}
                  </div>
                </div>
              </div>
              <div className="grid sm:grid-cols-3 sm:gap-6">
                <div className="text-sm text-primary/60 sm:text-base">Service Type</div>
                <div className="text-primary sm:col-span-2">{`${FulfillmentTypeLabels[data['fulfillment_type'].value]}`}</div>
              </div>
              <div className="grid sm:grid-cols-3 sm:gap-6">
                <div className="text-sm text-primary/60 sm:text-base">
                  <span>Pick up Date</span>
                </div>
                <div className="text-primary sm:col-span-2">
                  <div>
                    {new Date(data['shipping_date'].value).toLocaleString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: '2-digit',
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Status History */}
          <div className="">
            <div className="py-6 font-dmSans text-xl font-bold text-primary">Status History</div>

            <Timeline>
              {data['statusHistory'].get({ noproxy: true }).map((item, index) => {
                const status = {
                  0: {
                    text: 'Pending',
                    color: 'yellow',
                    icon: <BellIcon className="size-5" />,
                  },
                  1: {
                    text: 'Preparing',
                    color: 'purple',
                    icon: <PackageSearchIcon className="size-5" />,
                  },
                  2: {
                    text: 'Order Ready',
                    color: 'blue',
                    icon: <PackageIcon className="size-5" />,
                  },
                  3: {
                    text: 'Completed',
                    color: 'green',
                    icon: <PackageCheckIcon className="size-5" />,
                  },
                  999: {
                    text: 'Cancelled',
                    color: 'red',
                    icon: <PackageXIcon className="size-5" />,
                  },
                };

                if (index === data['statusHistory'].length - 1) {
                  return (
                    <TimelineItem key={index} className={cn('h-28', index !== 0 ? 'opacity-50' : '')}>
                      <TimelineHeader className="relative rounded-xl border border-blue-gray-50 bg-white py-3 pl-4 pr-8 shadow-lg shadow-blue-gray-900/5">
                        <TimelineIcon className="p-3" variant="ghost" color={status[item.status_type].color}>
                          {status[item.status_type].icon}
                        </TimelineIcon>
                        <div className="flex flex-col gap-1">
                          <Typography variant="h6" color="blue-gray" placeholder={undefined}>
                            {item.message}
                          </Typography>
                          <Typography variant="small" color="gray" className="font-normal" placeholder={undefined}>
                            {new Date(item.updated_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: true,
                            })}
                          </Typography>
                        </div>
                      </TimelineHeader>
                    </TimelineItem>
                  );
                }

                return (
                  <TimelineItem key={index} className={cn('h-28', index !== 0 ? 'opacity-50' : '')}>
                    <TimelineConnector className="!w-[78px]" />
                    <TimelineHeader className="relative rounded-xl border border-blue-gray-50 bg-white py-3 pl-4 pr-8 shadow-lg shadow-blue-gray-900/5">
                      <TimelineIcon className="p-3" variant="ghost" color={status[item.status_type].color}>
                        {status[item.status_type].icon}
                      </TimelineIcon>
                      <div className="flex flex-col gap-1">
                        <Typography variant="h6" color="blue-gray" placeholder={undefined}>
                          {item.message}
                        </Typography>
                        <Typography variant="small" color="gray" className="font-normal" placeholder={undefined}>
                          {new Date(item.updated_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true,
                          })}
                        </Typography>
                      </div>
                    </TimelineHeader>
                  </TimelineItem>
                );
              })}
            </Timeline>
          </div>

          <div className="font-dmSans text-xl font-bold text-primary">Order List</div>
          <ScrollArea className="max-w-[calc(100vw-48px)] rounded-xl lg:max-w-[calc(100vw-338px)] xl:max-w-none">
            <div className="w-max overflow-hidden rounded-xl border xl:w-full">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-100">
                    <TableHead className="font-bold">Item Code</TableHead>
                    <TableHead className="font-bold">Item Name</TableHead>
                    <TableHead className="font-bold">Quantity</TableHead>
                    <TableHead className="font-bold">Unit Price</TableHead>
                    <TableHead className="font-bold">Total Amount</TableHead>
                    <TableHead className="font-bold">UOM</TableHead>
                    <TableHead className="font-bold">Vat Code</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data['marketplaceProductOrders'].get({ noproxy: true }).map((invoice, index) => {
                    const accessor = PRODUCT_TYPE_ACCESSOR[Number(invoice.marketplaceProduct.product_type)];
                    const product = invoice.marketplaceProduct[accessor];

                    return (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{invoice.marketplaceProduct.code}</TableCell>
                        <TableCell>{product.name}</TableCell>
                        <TableCell>{invoice.quantity}</TableCell>
                        <TableCell>
                          {invoice.price.toLocaleString('en-US', { style: 'currency', currency: 'PHP' })}
                        </TableCell>
                        <TableCell>
                          {Number(invoice.price * invoice.quantity).toLocaleString('en-US', {
                            style: 'currency',
                            currency: 'PHP',
                          })}
                        </TableCell>
                        <TableCell>{invoice.marketplaceProduct.unit}</TableCell>
                        <TableCell>{invoice.marketplaceProduct.vatable}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
                <TableFooter>
                  <TableRow>
                    <TableCell colSpan={2}>Total</TableCell>
                    <TableCell>
                      {data['marketplaceProductOrders']
                        .get({ noproxy: true })
                        .map((invoice) => invoice.quantity)
                        .reduce((a, b) => a + b, 0)}
                    </TableCell>
                    <TableCell></TableCell>
                    <TableCell>
                      {data['total_price'].value.toLocaleString('en-US', {
                        style: 'currency',
                        currency: 'PHP',
                      })}
                    </TableCell>
                    <TableCell colSpan={2}></TableCell>
                  </TableRow>
                </TableFooter>
              </Table>
            </div>

            <ScrollBar orientation="horizontal" />
          </ScrollArea>

          <div className="grid gap-6 px-4 md:grid-cols-2">
            {/* Left */}
            <div>
              <div className="pt-6 font-dmSans text-xl font-bold text-primary">Order Summary</div>

              <div className="max-w-sm space-y-2 border-b border-primary py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-primary/60">Subtotal</div>
                  <div className="text-primary">
                    {data['total_price'].value.toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-primary/60">Shipping Fee</div>
                  <div className="text-primary">
                    {Number(data['shipping_fee'].value).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}
                  </div>
                </div>
              </div>

              <div className="max-w-sm pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-primary/60">Total Amount (Vat Incl.)</div>
                  <div className="font-bold text-primary">
                    {Number(data['shipping_fee'].value + data['total_price'].value).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}
                  </div>
                </div>
              </div>
            </div>

            {/* Right */}
            <div>
              <div className="pt-6 font-dmSans text-xl font-bold text-primary">Payment Method</div>
              <div className="pt-4">
                <div className="grid gap-3">
                  <div className="flex items-start gap-3">
                    <div
                      className={cn(
                        'w-[200px] flex items-center gap-3 rounded-md border border-primary px-4 py-3 text-primary',
                        paymentMethod.cash.checked.value ? '' : 'opacity-50',
                      )}
                    >
                      <Checkbox
                        disabled
                        checked={paymentMethod.cash.checked.value}
                        onCheckedChange={(e: boolean) => {
                          if (!e) {
                            paymentMethod.cash.amount.set(0);
                          }
                          paymentMethod.cash.checked.set(e);
                        }}
                      />
                      <Label htmlFor="r1">Cash Payment</Label>
                      <FaMoneyBillWave className="size-4" />
                    </div>

                    {paymentMethod.cash.checked.value && (
                      <div className="flex-1">
                        <Input
                          readOnly
                          className="w-full"
                          type="number"
                          min={0}
                          value={paymentMethod.cash.amount.value}
                          onChange={(e) => {
                            let value = e.target.value;

                            // If the value is '0' and the user types another number, remove the leading zero
                            if (value.length > 1 && value.startsWith('0')) {
                              e.target.value = value.replace(/^0+/, '');
                            }

                            paymentMethod.cash.amount.set(Number(value));
                          }}
                        />
                      </div>
                    )}
                  </div>

                  <div className="flex items-start gap-3">
                    <div
                      className={cn(
                        'w-[200px] flex items-center gap-3 rounded-md border border-primary px-4 py-3 text-primary',
                        paymentMethod.ewallet.checked.value ? '' : 'opacity-50',
                      )}
                    >
                      <Checkbox
                        disabled
                        checked={paymentMethod.ewallet.checked.value}
                        onCheckedChange={(e: boolean) => {
                          if (!e) {
                            paymentMethod.ewallet.amount.set(0);
                          }
                          paymentMethod.ewallet.checked.set(e);
                        }}
                      />
                      <div className="flex flex-col">
                        <p className="text-xs">e-Wallet Load</p>
                      </div>
                      <FaWallet className="size-4" />
                    </div>

                    {paymentMethod.ewallet.checked.value && (
                      <div className="flex-1">
                        <Input
                          readOnly
                          className="w-full"
                          type="number"
                          value={paymentMethod.ewallet.amount.value}
                          onChange={(e) => {
                            let value = e.target.value;

                            // If the value is '0' and the user types another number, remove the leading zero
                            if (value.length > 1 && value.startsWith('0')) {
                              e.target.value = value.replace(/^0+/, '');
                            }

                            paymentMethod.ewallet.amount.set(Number(value));
                          }}
                        />
                      </div>
                    )}
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-[200px] text-primary/60">Total Payment Amount</div>
                    <div className="pl-3 font-bold text-primary">
                      {Number(paymentMethod.cash.amount.value + paymentMethod.ewallet.amount.value).toLocaleString(
                        'en-US',
                        {
                          style: 'currency',
                          currency: 'PHP',
                        },
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Button className="px-8" variant="outline" type="button" onClick={() => router.back()}>
                Back
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
