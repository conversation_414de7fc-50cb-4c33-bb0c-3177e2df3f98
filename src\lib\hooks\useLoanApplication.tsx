'use client';

import { toast } from 'sonner';

import axios from '@/lib/api';

import { getUserType } from '../constants';
import { useGlobalStatePersist } from '../store/persist';

export default function useLoanApplication() {
  const gStateP = useGlobalStatePersist();

  const loanApplicationAction = async (action: 'approve' | 'reject', data) => {
    try {
      toast.loading(action === 'approve' ? 'Approving loan application...' : 'Rejecting loan application...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post(
          `/${getUserType(gStateP['user']['user']['user_type'].value)}/user/farmers/loanapplicant/${action}`,
          data,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        )
        .then((res) => res.data);
      console.log('loanApplicationAction: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: `${action === 'approve' ? 'Approve' : 'Reject'} loan application successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('loanApplicationAction: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { loanApplicationAction };
}
