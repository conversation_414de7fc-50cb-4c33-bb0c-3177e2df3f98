'use client';

// import ImgurUploaderInit from 'ckeditor5-imgur-uploader';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import React from 'react';
import { toast } from 'sonner';

import config from '@/lib/config';

// const ImgurUploader = ImgurUploaderInit({ clientID: config.setting.imgur });

const classicConfiguration = {
  //   extraPlugins: [ImgurUploader],
  removePlugins: ['EasyImage', 'ImageUpload'],
};

export default function CustomEditor({ data, onChange }) {
  return (
    <div className="">
      <CKEditor
        editor={ClassicEditor}
        config={classicConfiguration}
        data={data}
        onChange={(event, editor) => {
          const _data = editor.getData();
          // console.log({ event, editor, _data });
          onChange(_data);
        }}
        onError={(error) => {
          console.error('Custom Editor: ', error);
          toast.error('Opps!', { description: error.message });
        }}
      />
    </div>
  );
}
