'use client';

import { none, useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { SlidersHorizontalIcon, UploadIcon } from 'lucide-react';
import { CSVLink } from 'react-csv';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

import { PaymentMethodLabels } from '@/app/admin/marketplace/_components/Enums';
import { marketplaceReportsState } from '@/lib/store/marketplace-reports-store';
import { IData } from '@/lib/store/marketplace-reports-store.types';
import regions from '@/public/address/region.json';

const getRegionByCode = (region_code: string) => {
  const region = regions.find((region) => region.region_code === region_code);
  return region;
};

export const ReportsColumns = [
  {
    id: 'region',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Region" />,
    cell: ({ row }) => {
      const data: IData = row.original;
      const addressJSON = data.customer.farmer.address_province;
      const address = JSON.parse(addressJSON);
      const region = getRegionByCode(address?.region_code);
      return <div className="w-max">{region?.region_name || '-'}</div>;
    },
    accessorFn: (row) => {
      const data: IData = row;
      const addressJSON = data.customer.farmer.address_province;
      const address = JSON.parse(addressJSON);
      const region = getRegionByCode(address?.region_code);
      return region?.region_name || '-';
    },
  },
  {
    id: 'province',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Province" />,
    cell: ({ row }) => {
      const data: IData = row.original;
      const addressJSON = data.customer.farmer.address_province;
      const address = JSON.parse(addressJSON);
      return <div className="w-max">{address?.province_name || '-'}</div>;
    },
    accessorFn: (row) => {
      const data: IData = row;
      const addressJSON = data.customer.farmer.address_province;
      const address = JSON.parse(addressJSON);
      return address?.province_name || '-';
    },
  },
  {
    id: 'farmer_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Farmer ID" />,
    cell: ({ row }) => {
      const data: IData = row.original;
      return <div className="min-w-max">ID{data.customer.id.toString().padStart(9, '0')}</div>;
    },
    accessorFn: (row) => {
      const data: IData = row;
      return data.customer.id;
    },
  },
  {
    id: 'farmer_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Farmer Name" />,
    cell: ({ row }) => {
      const data: IData = row.original;
      const farmer = data.customer.farmer;
      return <div className="min-w-max capitalize">{`${farmer.first_name} ${farmer.last_name}`}</div>;
    },
    accessorFn: (row) => {
      const data: IData = row;
      const farmer = data.customer.farmer;
      return `${farmer.first_name} ${farmer.last_name}`;
    },
  },
  {
    id: 'order_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Order ID" />,
    cell: ({ row }) => {
      const data: IData = row.original;
      return <div className="min-w-max">{data.reference_number}</div>;
    },
    accessorFn: (row) => {
      const data: IData = row;
      return data.reference_number;
    },
  },
  {
    id: 'order_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Order Date" />,
    cell: ({ row }) => {
      const data: IData = row.original;
      return <div className="min-w-max">{format(new Date(data.created_at), 'MMM dd, yyyy | hh:mm a')}</div>;
    },
    accessorFn: (row) => {
      const data: IData = row;
      return format(new Date(data.created_at), 'MMM dd, yyyy | hh:mm a');
    },
  },
  {
    id: 'payment_method',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Method" />,
    cell: ({ row }) => {
      const data: IData = row.original;
      return <div className="min-w-max">{PaymentMethodLabels[data.payment_method]}</div>;
    },
    accessorFn: (row) => {
      const data: IData = row;
      return PaymentMethodLabels[data.payment_method];
    },
  },
];

export function ReportsFilter() {
  const state = useHookstate(marketplaceReportsState);

  const onReset = async () => {
    state.query.regions.set([]);
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button className="h-8" variant="outline" size="sm">
          <SlidersHorizontalIcon className="mr-2 size-3" />
          Filter
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col p-0">
        <ScrollArea className="h-1 flex-1 px-6">
          <SheetHeader className="pt-6">
            <div className="flex items-center justify-between pt-4">
              <SheetTitle>Filter</SheetTitle>
              <SheetTitle>
                <Button onClick={onReset} className="text-base text-kitaph-primary" variant="ghost">
                  Reset All
                </Button>
              </SheetTitle>
            </div>
          </SheetHeader>

          <div className="pb-6">
            <div className="mb-2 mt-4 text-sm text-gray-500">Credit Rating</div>

            {regions.map((region) => {
              return (
                <div key={region.id} className="flex items-center gap-2 py-2">
                  <Checkbox
                    id={`region-${region.region_code}`}
                    checked={state.query.regions.value.includes(region.region_code)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        state.query.regions.merge([region.region_code]);
                      } else {
                        const findIndex = state.query.regions.value.findIndex(
                          (regionCode) => regionCode === region.region_code,
                        );
                        if (findIndex !== -1) {
                          state.query.regions[findIndex].set(none);
                        }
                      }
                    }}
                  />
                  <label
                    htmlFor={`region-${region.region_code}`}
                    className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {region.region_name}
                  </label>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}

const exportHeaders = [
  { label: 'Region', key: 'region' },
  { label: 'Province', key: 'province' },
  { label: 'Farmer ID', key: 'farmerId' },
  { label: 'Farmer Name', key: 'farmerName' },
  { label: 'Order ID', key: 'orderId' },
  { label: 'Order Date', key: 'orderDate' },
  { label: 'Payment Method', key: 'paymentMethod' },
];

export function ReportsExport() {
  const state = useHookstate(marketplaceReportsState);

  const getData = () => {
    const data = state.report.data.get({ noproxy: true });
    return data.map((item) => {
      const addressJSON = item.customer.farmer.address_province;
      const address = JSON.parse(addressJSON);
      const region = getRegionByCode(address?.region_code);

      return {
        region: region?.region_name || '-',
        province: address?.province_name || '-',
        farmerId: item.customer.farmer.id,
        farmerName: `${item.customer.farmer.first_name} ${item.customer.farmer.last_name}`,
        orderId: item.reference_number,
        orderDate: format(new Date(item.created_at), 'MMM dd, yyyy | hh:mm a'),
        paymentMethod: PaymentMethodLabels[item.payment_method],
      };
    });
  };

  return (
    <div>
      <Button size="sm" className="h-8" asChild>
        <CSVLink
          data={getData()}
          headers={exportHeaders}
          filename={`marketplace-reports-${format(new Date(), 'yyyy-MM-dd')}.csv`}
          target="_blank"
        >
          <UploadIcon className="mr-2 size-3" />
          Export
        </CSVLink>
      </Button>
    </div>
  );
}
