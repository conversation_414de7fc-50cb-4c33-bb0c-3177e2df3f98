'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect, useRef } from 'react';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import { useGroupPriceStore } from '@/lib/store/admin/trading-app/group-price-store';

import CardInfo from './card-info';

interface GroupInfoCardProps {
  groupId: number;
  userType: UserType.NONFARMER | UserType.FARMER;
}

export default function GroupInfoCard({ groupId, userType }: GroupInfoCardProps) {
  const { getGroup, state } = useGroupPriceStore();
  const groupInfo = useHookstate(state[userType].selectedGroup);
  const loading = useHookstate(true);
  const isMounted = useRef(true);

  useEffect(() => {
    isMounted.current = true;

    (async () => {
      try {
        loading.set(true);
        await getGroup(groupId, userType);
      } catch (e) {
        console.error(e);
      } finally {
        if (isMounted.current) {
          loading.set(false); // Only set loading to false if still mounted
        }
      }
    })();

    return () => {
      isMounted.current = false; // Set to false when unmounted
    };
  }, [userType, groupId]);

  return (
    <div className="card grid gap-6">
      <div className="flex flex-wrap items-start gap-12">
        <CardInfo loading={loading.value} title="Group Name" value={groupInfo?.name?.value} />
        <CardInfo loading={loading.value} title="Percentage Rate (%)" value={groupInfo?.percentage_rate?.value} />
        <CardInfo loading={loading.value} title="Logistic Rate per kilo (₱)" value={groupInfo?.logistic_rate?.value} />
      </div>
      <CardInfo loading={loading.value} title="Remarks" value={groupInfo?.remarks?.value} />
    </div>
  );
}
