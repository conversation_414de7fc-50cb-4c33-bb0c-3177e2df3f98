'use client';

/* eslint-disable @next/next/no-img-element */
import { useHookstate } from '@hookstate/core';
import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon } from '@radix-ui/react-icons';
import { format, parse } from 'date-fns';
import { CheckCircle, UploadCloud, XCircle } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { BsFillTrashFill } from 'react-icons/bs';
import { CSVReader } from 'react-papaparse';
import { toast } from 'sonner';

import { DataTableFacetedFilter } from '@/components/layout/table/normal/table-faceted-filter';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

export default function UserCsv() {
  const gState = useGlobalState();

  const filteredData = useHookstate([]);
  const sData = useHookstate([]);
  const sDataPaginated = useHookstate({
    data: [],
  });

  const page = useHookstate(1);
  const pageSize = useHookstate(10);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const reset = () => {
    sData.set([]);
    filteredData.set([]);
    sDataPaginated.set({
      data: [],
    });
  };

  const handleOnDrop = (data, file) => {
    const parsedData = uploadHandler(data);
    sData.set(parsedData);
    filteredData.set(parsedData);
    gState.admin.usersUploadFile.set(file);
  };

  const handleOnError = (err, file, inputElem, reason) => {
    toast.error('Oops!', {
      description: err.message,
    });
  };

  const handleOnRemoveFile = (data) => {
    sData.set([]);
    filteredData.set([]);
    sDataPaginated.set({
      data: [],
    });
  };

  const uploadHandler = (data) => {
    const tmp = [];

    if (data.length === 0) {
      toast.error('Oops!', {
        description: `No data found!`,
      });
      return tmp;
    }

    data = data.filter((v) => v.errors.length === 0);

    const hasSurname = data[0].data.includes('Surname');
    const hasGivenName = data[0].data.includes('Given Name');
    const hasMiddleName = data[0].data.includes('Middle Name');
    const hasBirthdate = data[0].data.includes('Birthdate (MM-DD-YYYY)');
    const hasBirthplace = data[0].data.includes('Birthplace');
    const hasSex = data[0].data.includes('Sex (at Birth) MALE/FEMALE');
    const hasRsbsa = data[0].data.includes('RSBSA Number');
    const hasFarmArea = data[0].data.includes('Farm Area (# of hecterage)');
    const hasFarmPlan = data[0].data.includes('Farm Plan / PoP');
    const hasMobileNumber = data[0].data.includes('Mobile Number');

    if (
      !hasSurname ||
      !hasGivenName ||
      !hasMiddleName ||
      !hasBirthdate ||
      !hasBirthplace ||
      !hasSex ||
      !hasRsbsa ||
      !hasFarmArea ||
      !hasFarmPlan ||
      !hasMobileNumber
    ) {
      toast.error('Oops!', {
        description: `Invalid Data`,
      });
      return tmp;
    }

    data.forEach((el, index) => {
      // Skip header and template
      if (index < 1) return;
      // console.log(`row ${index}`, el.data);

      // Skip row if all fields are empty or whitespace
      const isRowEmpty = el.data.every((cell) => cell.trim() === '');

      console.log(`row ${index}`, isRowEmpty);
      if (isRowEmpty) return;

      tmp.push({ ...el.data });
    });

    console.log({ tmp });

    return tmp;
  };

  const paginator = (items, current_page, per_page_items) => {
    let page = current_page || 1,
      per_page = per_page_items || 10,
      offset = (page - 1) * per_page,
      paginatedItems = items.slice(offset).slice(0, per_page_items),
      total_pages = Math.ceil(items.length / per_page);

    return {
      page: page,
      per_page: per_page,
      pre_page: page - 1 ? page - 1 : null,
      next_page: total_pages > page ? page + 1 : null,
      total: items.length,
      total_pages: total_pages,
      data: paginatedItems,
    };
  };

  const formatDate = (dateStr: string) => {
    try {
      const parsedDate = parse(dateStr, 'MM-dd-yyyy', new Date());
      return format(parsedDate, 'yyyy-MM-dd');
    } catch (error) {
      console.error('Invalid date:', error);
      return '';
    }
  };

  useEffect(() => {
    console.log({
      sData: sData.get({ noproxy: true }).map((x) => ({
        lastName: x[0],
        firstName: x[1],
        middleName: x[2],
        birthDate: formatDate(x[3]),
        placeOfBirth: x[4],
        gender: x[5],
        rsbsaNumber: x[6],
        farmArea: x[7],
        farmPlan: x[8],
        mobileNumber: x[9],
        nationality: 'Filipino',
      })),
    });
    gState.admin.usersUpload.set(
      sData.get({ noproxy: true }).map((x) => ({
        lastName: x[0],
        firstName: x[1],
        middleName: x[2],
        birthDate: formatDate(x[3]),
        placeOfBirth: x[4],
        gender: x[5],
        rsbsaNumber: x[6],
        farmArea: x[7],
        farmPlan: x[8],
        mobileNumber: x[9],
        nationality: 'Filipino',
      })),
    );
  }, [sData]);

  useEffect(() => {
    sDataPaginated.set(paginator(filteredData.get({ noproxy: true }), page.value, pageSize.value));
  }, [page, pageSize, filteredData]);

  useEffect(() => {
    reset();
  }, []);

  return (
    <div className="space-y-6 px-6">
      <div className="">
        <div className={cn(sData.length > 0 && 'hidden')}>
          <CSVReader
            config={{ header: false }}
            onDrop={handleOnDrop}
            onError={handleOnError}
            addRemoveButton
            removeButtonColor="#ef4444"
            onRemoveFile={handleOnRemoveFile}
            style={{
              dropFile: {
                width: 150,
                height: 120,
              },
              fileNameInfo: {
                color: '#4b5563',
              },
              fileSizeInfo: {
                color: '#6366f1',
              },
            }}
          >
            <UploadCloud className="size-12" />
            <div className="mt-2 text-gray-500">Drag & drop file here</div>
            <div className="text-gray-500">Or</div>
            <div className="font-bold text-primary">Browser</div>
          </CSVReader>
        </div>

        {sData.value.length === 0 && (
          <div className="mt-3 text-center text-sm text-gray-500">
            Only CSV files are supported
            <div className="">
              Use the template provided here:
              <a
                className="ml-1 italic text-blue-500 hover:underline hover:underline-offset-2"
                href="/template/Bulk_User.csv"
                target="_blank"
              >
                Template
              </a>
            </div>
          </div>
        )}

        {sData.length > 0 && (
          <div className="grid">
            <ScrollArea className="max-h-[36vh] whitespace-nowrap rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Surname</TableHead>
                    <TableHead>Given Name</TableHead>
                    <TableHead>Middle Name</TableHead>
                    <TableHead>Birthdate</TableHead>
                    <TableHead>Birthplace</TableHead>
                    <TableHead>Sex (at Birth)</TableHead>
                    <TableHead>RSBSA Number</TableHead>
                    <TableHead>Farm Area (# of hecterage)</TableHead>
                    <TableHead>Farm Plan / PoP</TableHead>
                    <TableHead>Mobile Number</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sDataPaginated['data'].value.map((col, index) => (
                    <TableRow key={index}>
                      <TableCell>{col[0]}</TableCell>
                      <TableCell>{col[1]}</TableCell>
                      <TableCell>{col[2]}</TableCell>
                      <TableCell>{col[3]}</TableCell>
                      <TableCell>{col[4]}</TableCell>
                      <TableCell>{col[5]}</TableCell>
                      <TableCell>{col[6]}</TableCell>
                      <TableCell>{col[7]}</TableCell>
                      <TableCell>{col[8]}</TableCell>
                      <TableCell>{col[9]}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <ScrollBar orientation="horizontal" />
            </ScrollArea>

            {/* pagination */}
            <div className="mt-4 space-y-2 px-2">
              <div className="flex items-center justify-center gap-4">
                <div className="text-sm text-gray-500">Total of {filteredData.length} row(s)</div>

                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium">Rows per page</p>
                  <Select
                    value={`${pageSize.value}`}
                    onValueChange={(value) => {
                      pageSize.set(Number(value));
                    }}
                  >
                    <SelectTrigger className="h-8 w-[70px]">
                      <SelectValue placeholder={pageSize.value} />
                    </SelectTrigger>
                    <SelectContent side="top">
                      {[10, 20, 30, 40, 50].map((pageS) => (
                        <SelectItem key={pageS} value={`${pageS}`}>
                          {pageS}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex flex-col items-center gap-2">
                <div className="flex items-center gap-2">
                  <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                    Page {page.value} of {sDataPaginated['total_pages'].value}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      className="hidden size-8 p-0 lg:flex"
                      onClick={() => page.set(1)}
                      disabled={page.value == 1}
                    >
                      <span className="sr-only">Go to first page</span>
                      <DoubleArrowLeftIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="size-8 p-0"
                      onClick={() => {
                        if (page.value > 1) {
                          page.set((v) => v - 1);
                        }
                      }}
                      disabled={page.value == 1}
                    >
                      <span className="sr-only">Go to previous page</span>
                      <ChevronLeftIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="size-8 p-0"
                      onClick={() => {
                        if (page.value < sDataPaginated['total_pages'].value) {
                          page.set((v) => v + 1);
                        }
                      }}
                      disabled={sDataPaginated.data.length > 0 && !sDataPaginated['next_page'].value}
                    >
                      <span className="sr-only">Go to next page</span>
                      <ChevronRightIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="hidden size-8 p-0 lg:flex"
                      onClick={() => {
                        page.set(sDataPaginated['total_pages'].get({ noproxy: true }));
                      }}
                      disabled={sDataPaginated.data.length > 0 && !sDataPaginated['next_page'].value}
                    >
                      <span className="sr-only">Go to last page</span>
                      <DoubleArrowRightIcon className="size-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
