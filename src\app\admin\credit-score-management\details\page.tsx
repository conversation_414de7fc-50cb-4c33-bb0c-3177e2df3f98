'use client';

import { useHookstate } from '@hookstate/core';
import { useRouter } from 'next/navigation';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useEffect } from 'react';
import { toast } from 'sonner';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

import { CREDIT_MGT_TAB } from '@/lib/constants';
import useCreditScoring from '@/lib/hooks/admin/useCreditScoring';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import AfterLoan from './_components/after-loan';
import BeforeLoan from './_components/before-loan';
import DuringLoan from './_components/during-loan';

export default function CreditScoreManagementDetails() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const { getLoanRules } = useCreditScoring();
  const [creditScoreGroupId, setCreditScoreGroupId] = useQueryState('id', parseAsInteger);

  const selected = useHookstate(gStateP.selected['creditScoreGroup']);
  const creditTab = useHookstate(gState.creditTab);

  useEffect(() => {
    if (creditScoreGroupId === null || creditScoreGroupId <= 0) {
      toast.error('Not Found!', {
        description: 'Credit score group not found!',
      });
      router.back();
      return;
    }

    getLoanRules(creditScoreGroupId);
    // getRules(creditScoreGroupId);
  }, [creditScoreGroupId, creditTab]);

  return (
    <div className="p-8">
      {/* Title */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">{selected.description.value || ''}</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink onClick={() => router.back()}>Credit Score Group</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{selected.name.value || ''}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Tabs */}
      <ScrollArea className="">
        <div className="flex w-max items-center gap-8">
          {CREDIT_MGT_TAB.map((tab, index) => {
            const isSelected = tab.value === creditTab.value;

            return (
              <button
                key={tab.value}
                className={cn(
                  'transition-all duration-300 ease-in-out whitespace-nowrap my-3',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => {
                  creditTab.set(tab.value);
                }}
              >
                {tab.name}
              </button>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      <div className="mt-4">
        {creditTab.value === 'before_loan' && <BeforeLoan />}
        {creditTab.value === 'during_loan' && <DuringLoan />}
        {creditTab.value === 'after_loan' && <AfterLoan />}
      </div>
    </div>
  );
}
