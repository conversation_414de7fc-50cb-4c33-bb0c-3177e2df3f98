'use client';

import { Controller, UseFormReturn } from 'react-hook-form';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import useFarmer from '../../hooks/useFarmer';
import { ICreateFarmPlan } from '../../types/create-farm-plan.types';
import { IUserInfo } from '../../types/farmer.types';
import { getDisplayName } from '../../utils';
import FarmerSearchInput from './farmer-search-input';

interface IFarmerInfoProps {
  form: UseFormReturn<ICreateFarmPlan>;
}

export default function FarmerInfo({ form }: IFarmerInfoProps) {
  const { farmersQuery, state } = useFarmer();

  // Address
  const address = state.selected.value ? JSON.parse(state.selected.value.farmer.address) : undefined;
  const province = address ? JSON.parse(address?.addressProvince ?? '{}') : undefined;
  const city = address ? JSON.parse(address?.addressCity ?? '{}') : undefined;
  const barangay = address ? JSON.parse(address?.addressBarangay ?? '{}') : undefined;
  const houseNumber = address ? address?.addressHouseNumber : undefined;
  const completeAddress = address
    ? `${houseNumber}, ${barangay?.brgy_name}, ${city?.city_name}, ${province?.province_name}`
    : '';

  return (
    <div className="">
      <div>
        <Controller
          name="userId"
          control={form.control}
          rules={{
            required: `Farmer is required for farm planning`,
            validate: {
              hasSelected: (v) => v > 0 || `Farmer is required for farm planning`,
            },
          }}
          render={({ field }) => (
            <FarmerSearchInput
              label="Farmer Name"
              value={state.params.search.value}
              onChange={(value) => {
                state.params.search.set(value);
                // Only reset field value if user is clearing the input or typing something different from selected farmer
                if (!value || (state.selected.value && value !== getDisplayName(state.selected.value as IUserInfo))) {
                  field.onChange(0);
                }
              }}
              farmers={farmersQuery.data || []}
              isLoading={farmersQuery.isFetching || farmersQuery.isLoading}
              isError={!!form.formState.errors.userId}
              selectedFarmer={state.selected.value as IUserInfo}
              onFarmerSelect={(f) => {
                state.selected.set(f);
                field.onChange(f?.id ?? 0);
              }}
            />
          )}
        />
        {form.formState.errors.userId && <p className="form-error">{form.formState.errors.userId.message}</p>}
      </div>

      <div className="">
        <Label htmlFor="address">Home Address</Label>
        <Input id="address" value={completeAddress} disabled />
      </div>

      <div className="">
        <Label htmlFor="farmAreaSize">Farm Area Size (ha)</Label>
        <Input
          id="farmAreaSize"
          value={state.selected.value ? state.selected.value.farmer.farmerInfo.farm_area : 0}
          disabled
        />
      </div>
    </div>
  );
}
