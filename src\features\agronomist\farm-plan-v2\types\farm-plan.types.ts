import { IMeta } from '@/lib/types';

export interface IFarmPlanResponse {
  meta: IMeta;
  data: IFarmPlan[];
}

export interface IFarmPlan {
  id: number;
  user_id: number;
  crop_id: number;
  reference_number: string;
  cropping_type: string;
  agronomist_user_id: number;
  total_amount: number;
  agronomist_name: string;
  agronomist_prc_number: string;
  agronomist_valid_until: string;
  head_agronomist_name: string;
  head_agronomist_prc_number: string;
  head_agronomist_valid_until: string;
  created_at: string;
  updated_at: string;
  farmPlanItems: FarmPlanItems[];
  user: User;
}

export interface FarmPlanItems {
  id: number;
  farm_plan_id: number;
  name: string;
  slug: string;
  type: string;
  total_amount: number | null;
  created_at: string;
  updated_at: string;
  farmPlanSubItems: FarmPlanSubItems[] | unknown[];
}

export interface FarmPlanSubItems {
  id: number;
  farm_plan_item_id: number;
  farm_plan_id: number;
  expected_date: string;
  item_name: string;
  unit: string;
  quantity: number;
  unit_cost: number;
  total_amount: number;
  notes: string;
  marketplace_product_id: number;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: string;
  remember_me_token: null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null;
  farmer: Farmer;
}

export interface Farmer {
  id: number;
  user_id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  birth_date: string;
  place_of_birth: string;
  religion: string;
  gender: string;
  civil_status: string;
  height: number;
  weight: number;
  mobile_number: string;
  address: string;
  address_house_number: string;
  address_province: string;
  address_city: string;
  address_barangay: string;
  address_zip_code: string;
  educational_attainment: string;
  educational_is_graduate: number;
  educational_degree: null;
  occupation: string;
  occupation_status: string;
  occupation_employer_name: string;
  occupation_employer_address: string;
  occupation_business_name: null;
  occupation_business_address: null;
  occupation_business_contact: null;
  occupation_annual_income: string;
  skills_farming: null;
  skills_fishing: number;
  skills_livestock: null;
  skills_construction: null;
  skills_processing: null;
  skills_servicing: null;
  skills_craft: null;
  skills_others: null;
  vehicle_owned: string;
  status: number;
  created_at: string;
  updated_at: string;
  has_biometric: number;
  qr_code: string;
  has_loan: number;
  has_submitted_loan_application: number;
  permanent_address: null;
  permanent_address_house_number: null;
  permanent_address_province: null;
  permanent_address_city: null;
  permanent_address_barangay: null;
  permanent_address_zip_code: null;
  permanent_address_length_of_stay: null;
  address_length_of_stay: null;
  occupation_title: null;
  source_of_funds: null;
  landbank_accounts: null;
  farmerInfo: FarmerInfo;
}

export interface FarmerInfo {
  id: number;
  farmer_id: number;
  farm_address: string;
  farm_area: number;
  farm_ownership: number;
  created_at: string;
  updated_at: string;
  nationality: string;
  other_mobile_number: null;
  year_residing: number;
  residence_ownership: string;
}
