'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { Pencil } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge, BadgeProps } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import useChemicals, { ModeChemSchema, UpdateModeChemSchema } from '@/lib/hooks/useChemicals';
import { cn } from '@/lib/utils';

export const modeColumns = [
  {
    id: 'mode_action',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Mode of Action" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.name}</div>;
    },
    accessorFn: (row) => row.name,
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const statusMessage = ['Disabled', 'Active'];
      const variant = ['error', 'success'] as BadgeProps;

      return (
        <div className="min-w-max">
          <Badge variant={variant[data.status]}>{statusMessage[data.status]}</Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'updated_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Updated At" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) =>
      `${new Date(row.updated_at).toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })}`,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

export const ModeActionHeader = () => {
  const { addMode } = useChemicals();
  const dialogInput = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(ModeChemSchema),
    defaultValues: {
      name: '',
    },
  });

  return (
    <div className="flex items-center justify-end gap-2">
      <Button
        className="h-8 px-2 lg:px-3"
        size="sm"
        onClick={() => {
          dialogInput.set(true);
        }}
      >
        Add Mode of Action
      </Button>

      {/* Add via input */}
      <Dialog open={dialogInput.value} onOpenChange={dialogInput.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-lg">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Mode of Action</DialogTitle>
            <DialogDescription>{`Fill the form below to add new mode of action`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(addMode)} className="">
            <div className="mt-3 grid gap-4 px-6 pb-6">
              <div className="relative grid w-full items-center gap-1.5">
                <Label htmlFor="name" className="pb-1 font-normal">
                  Mode of Action Name
                </Label>
                <div className="flex items-center gap-4">
                  <Input
                    {...register('name')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.name && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter active ingredient name"
                  />
                </div>
                {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
              </div>
            </div>

            <div className="flex justify-between gap-2 p-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              <Button className="px-12" type="submit">
                Add
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Action = ({ row }) => {
  const data = row.original;
  const updateDialog = useHookstate(false);
  const { updateMode } = useChemicals();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(UpdateModeChemSchema),
    defaultValues: {
      chemicalModeOfActionId: `${data.id}`,
      name: data.name,
      status: `${data.status}`,
    },
  });

  return (
    <>
      <div className="flex justify-end gap-2">
        <div className="flex justify-end gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="h-8 px-2 lg:px-3"
                variant="outline"
                size="sm"
                onClick={() => {
                  updateDialog.set(true);
                }}
              >
                <Pencil className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit Mode of Action</p>
            </TooltipContent>
          </Tooltip>

          {/* Update Dialog */}
          <Dialog open={updateDialog.value} onOpenChange={updateDialog.set}>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-primary">Update Mode of Action</DialogTitle>
                <DialogDescription>{`Fill the form below to update mode of action`}</DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit(updateMode)}>
                <div className="space-y-6">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="chemicalModeOfActionId" className="pb-1 font-normal">
                      Mode of Action ID
                    </Label>
                    <Input
                      {...register('chemicalModeOfActionId')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.chemicalModeOfActionId && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter mode of action id"
                      disabled
                    />
                    {errors.chemicalModeOfActionId && (
                      <p className="form-error">{`${errors.chemicalModeOfActionId.message}`}</p>
                    )}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="name" className="pb-1 font-normal">
                      Mode of Action Name
                    </Label>
                    <Input
                      {...register('name')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.name && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter mode of action name"
                    />
                    {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="status" className="pb-1 font-normal">
                      Status
                    </Label>
                    <Controller
                      control={control}
                      name="status"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="0">Disabled</SelectItem>
                              <SelectItem value="1">Active</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
                  </div>
                </div>

                <div className="flex justify-between gap-2 pt-6">
                  <DialogClose asChild>
                    <Button className="px-12" variant="outline" type="button">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button className="px-12" type="submit">
                    Update
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  );
};
