'use client';

import React from 'react';

const Page1 = ({ data }) => {
  const address = data && data['address'] ? JSON.parse(data['address']) : {};

  const now = new Date();
  const day = now.getDate();
  const month = now.toLocaleString('default', { month: 'long' });
  const year = now.getFullYear() % 100;

  return (
    <div className="relative flex h-[11in] w-[8.5in] flex-col border bg-[url(/assets/forms/farm-land-lease-agreement-form/1.jpg)] bg-contain bg-top bg-no-repeat capitalize print:border-none">
      {/* Date */}
      <div className="absolute right-[290px] top-[120px] text-xs font-medium capitalize">{day}</div>
      <div className="absolute right-[175px] top-[120px] text-xs font-medium capitalize">{month}</div>
      <div className="absolute left-[170px] top-[140px] text-xs font-medium capitalize">{year}</div>

      {/* Name */}
      <div className="absolute left-[420px] top-[285px] text-xs font-medium capitalize">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</div>

      {/* Address */}
      <div className="absolute left-[320px] top-[320px] text-xs font-medium capitalize">
        {`${address?.addressHouseNumber}, ${address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}, ${address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ''}, ${address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''}`}
      </div>
    </div>
  );
};

export default Page1;
