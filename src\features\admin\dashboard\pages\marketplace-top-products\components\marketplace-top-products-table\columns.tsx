'use client';

import { ColumnDef } from '@tanstack/react-table';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

import { IMarketplaceTopProducts } from '@/features/admin/dashboard/types';
import { toCurrency } from '@/lib/utils';

export const columnsMarketplaceTopProducts: ColumnDef<IMarketplaceTopProducts>[] = [
  {
    accessorKey: 'product_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="#" />,
  },
  {
    accessorKey: 'product_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Product Name" />,
    cell: ({ row }) => {
      return <div className="w-max">{row.original.product_name}</div>;
    },
  },
  {
    accessorKey: 'product_quantity',
    header: ({ column }) => <DataTableColumnHeader className="justify-end" column={column} title="Quantity" />,
    cell: ({ row }) => {
      return <div className="text-right">{row.getValue('product_quantity')}</div>;
    },
  },
  {
    accessorKey: 'product_unit',
    header: ({ column }) => <DataTableColumnHeader column={column} title="OUM" />,
  },
  {
    accessorKey: 'product_price',
    header: ({ column }) => <DataTableColumnHeader className="justify-end" column={column} title="Price per pc" />,
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('product_price'))}</div>;
    },
  },
  {
    accessorKey: 'product_sales',
    header: ({ column }) => <DataTableColumnHeader className="justify-end" column={column} title="Sales" />,
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('product_sales'))}</div>;
    },
  },
];
