'use client';

import { toast } from 'sonner';
import { z } from 'zod';

import axios from '@/lib/api';

import { useGlobalState } from '../store';

const passwordSchema = z
  .string()
  .min(8, 'At least 8 characters - the longer, the better.')
  .regex(/[A-Z]/, 'At least one uppercase letter')
  .regex(/[a-z]/, 'At least one lowercase letter')
  .regex(/\d/, 'At least one number')
  .regex(/[!@#$%^&*(),.?":{}|<>]/, 'At least one special character');

export const ChangePassSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: passwordSchema,
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'], // This shows where the error will be attached to
  });
export type ChangePassType = z.infer<typeof ChangePassSchema>;

export const checkPasswordStrength = (password) => {
  const requirements = [
    { regex: /.{8,}/, message: 'At least 8 characters - the longer, the better.' },
    { regex: /[a-z]/, message: 'At least one lowercase letter' },
    { regex: /[A-Z]/, message: 'At least one uppercase letter' },
    { regex: /\d/, message: 'At least one number' },
    { regex: /[!@#$%^&*(),.?":{}|<>]/, message: 'At least one special character' },
  ];

  return requirements.map((req) => ({
    message: req.message,
    passed: req.regex.test(password),
  }));
};

export default function useAuthenticated() {
  const gState = useGlobalState();

  const updatePassword = async (data) => {
    try {
      const res = await axios.post(`/account/update/password`, data).then((res) => res.data);
      console.log('updatePassword: ', res);

      toast.success('Success', {
        description: 'Password updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updatePassword: ', error);

      toast.error('Unable to Change Password', {
        description: error,
      });
    }
  };

  return { updatePassword };
}
