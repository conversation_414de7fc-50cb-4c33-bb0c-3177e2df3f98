import { HomeIcon, UserPenIcon, Users2Icon } from 'lucide-react';

export const paths = {
  dashboard: {
    root: '/field-relation-officer',
    get kyc() {
      return {
        root: `${this.root}/kyc` as const,
      };
    },
    get farmers() {
      return {
        root: `${this.root}/farmers` as const,
        view: (id: string) => `${this.root}/farmers?id=${id}`,
      };
    },
  },
};

export const MENU = [
  {
    id: 0,
    name: 'Dashboard',
    icon: <HomeIcon className="size-5" />,
    href: paths.dashboard.root,
  },
  {
    id: 1,
    name: 'KYC',
    icon: <UserPenIcon className="size-5" />,
    href: paths.dashboard.kyc.root,
  },
  {
    id: 2,
    name: 'Farmers',
    icon: <Users2Icon className="size-5" />,
    href: paths.dashboard.farmers.root,
  },
] as const;
