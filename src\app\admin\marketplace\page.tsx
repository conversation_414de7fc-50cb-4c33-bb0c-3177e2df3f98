'use client';

import { useGlobalStatePersist } from '@/lib/store/persist';

import { TAB_MARKETPLACE } from '../layout';
import EWalletPage from './_components/pages/EWallet';
import OrdersPage from './_components/pages/Orders';
import PaymentsPage from './_components/pages/payments/Payments';
import ProductsPage from './_components/pages/Products';
import ReportsPage from './_components/pages/reports';

export default function Marketplace() {
  const gStateP = useGlobalStatePersist();

  return (
    <div className="">
      {gStateP.tabsMarketplace.value === TAB_MARKETPLACE.ORDERS && <OrdersPage />}
      {gStateP.tabsMarketplace.value === TAB_MARKETPLACE.PRODUCTS && <ProductsPage />}
      {gStateP.tabsMarketplace.value === TAB_MARKETPLACE.EWALLET && <EWalletPage />}
      {gStateP.tabsMarketplace.value === TAB_MARKETPLACE.PAYMENTS && <PaymentsPage />}
      {gStateP.tabsMarketplace.value === TAB_MARKETPLACE.REPORTS && <ReportsPage />}
    </div>
  );
}
