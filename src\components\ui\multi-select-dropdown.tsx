'use client';

import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';

import { cn } from '@/lib/utils';

import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { ScrollArea } from './scroll-area';

interface Option {
  value: string;
  label: string;
}

interface MultiSelectProps {
  options: Option[];
  onChange?: (selectedValues: string[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  triggerClassName?: string;
}

export default function MultiSelectDropdown({
  options,
  onChange,
  placeholder = 'Select options',
  searchPlaceholder = 'Search Crop Name',
  triggerClassName = '',
}: MultiSelectProps) {
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter options based on search query
  const filteredOptions = options.filter((option) => option.label.toLowerCase().includes(searchQuery.toLowerCase()));

  // Toggle selection of an option
  const toggleOption = (value: string) => {
    const newSelectedValues = selectedValues.includes(value)
      ? selectedValues.filter((val) => val !== value)
      : [...selectedValues, value];

    setSelectedValues(newSelectedValues);
    onChange?.(newSelectedValues);
  };

  return (
    <Popover>
      {/* Dropdown trigger button */}
      <PopoverTrigger asChild>
        <button
          type="button"
          className={cn(
            'flex w-full items-center justify-between rounded-md border border-gray-200 bg-white px-4 py-3 text-left text-gray-700 shadow-sm',
            triggerClassName,
          )}
        >
          <span>{selectedValues.length > 0 ? `${selectedValues.length} Selected` : placeholder}</span>
          <ChevronDown className="size-4 text-gray-500" />
        </button>
      </PopoverTrigger>

      {/* Dropdown content */}
      <PopoverContent className="p-0">
        {/* Search input */}
        <div className="p-4 pb-1">
          <Input
            type="text"
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>

        {/* Options list */}
        <div className="flex flex-col">
          <ScrollArea className="h-2 flex-1">
            <div className="max-h-60 p-2">
              {filteredOptions.map((option) => {
                const isChecked = selectedValues.includes(option.value);

                return (
                  <div key={option.value} className="flex items-center space-x-2 rounded p-2 hover:bg-gray-50">
                    <div
                      className="flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleOption(option.value);
                      }}
                    >
                      <Checkbox
                        id={option.value}
                        checked={isChecked}
                        onCheckedChange={() => {
                          toggleOption(option.value);
                        }}
                        className={isChecked ? '' : ''}
                      />
                    </div>
                    <label htmlFor={option.value} className="w-full cursor-pointer text-gray-700">
                      {option.label}
                    </label>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      </PopoverContent>
    </Popover>
  );
}
