'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { ChevronLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { getUserType } from '@/lib/constants';
import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

export default function FarmerList({ columns, data }) {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const plateNum = useSearchParams().get('plateNum');
  const { assignVehicle } = useFarmer();
  const isEncoder = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'encoder';
  const isFinance = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'finance';
  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';
  const isAdminTopup = useHookstate(false);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  useEffect(() => {
    isAdminTopup.set(window.location.pathname.includes('/admin/marketplace/topup/') && isAdmin);
  }, []);

  return (
    <div>
      {/* Search */}
      <div className="flex items-center">
        <div className="w-[15%]">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ChevronLeft className="size-4" />
          </Button>
        </div>

        <div className="w-[70%]">
          <Input
            className="h-11 focus-visible:ring-primary"
            type="text"
            placeholder="Search by Name, Account ID"
            onChange={(event) => {
              table.setGlobalFilter(event.target.value);
            }}
          />
        </div>
      </div>

      <div className="font-dmSans mt-6 font-medium leading-loose text-primary md:text-xl lg:text-2xl">
        {isEncoder
          ? 'Select account to credit the transaction.'
          : isFinance || isAdminTopup.value
            ? 'Select account to add Top up request.'
            : 'Select account to add order transaction.'}
      </div>

      <div className="mt-6 space-y-12">
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row, index) => {
            const item = row.original as any;

            return (
              <div
                key={index}
                className="font-dmSans cursor-pointer rounded-md border border-transparent bg-white p-6 shadow-xl shadow-slate-300 transition duration-300 ease-in-out hover:scale-110 hover:border-primary/50"
                onClick={async () => {
                  if (isFinance) {
                    gStateP.finance1.topup['farmer'].set(item);
                    router.push('/finance/topup/request');
                    return;
                  }

                  if (isAdminTopup.value) {
                    gStateP.finance1.topup['farmer'].set(item);
                    router.push('/admin/marketplace/topup/request');
                    return;
                  }

                  // admin
                  gStateP.admin.orders.set({
                    customer: null as any,
                    data: [],
                    total: 0,
                    shippingDate: '',
                    fulfillmentType: '1',
                  });
                  gStateP.admin.orders['customer'].set(item);
                  router.push('/admin/marketplace/order/products');
                }}
              >
                <div className="grid grid-cols-4">
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Farmer Name</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{`${item.farmer.first_name} ${item.farmer.last_name}`}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Account ID</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{`${item.farmer.id
                      .toString()
                      .padStart(9, '0')}`}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Birthdate</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{` ${
                      item.farmer.birth_date
                        ? new Date(item.farmer.birth_date).toLocaleString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: '2-digit',
                          })
                        : 'N/A'
                    }`}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Contact No.</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{`${item.farmer.mobile_number}`}</div>
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center text-lg text-slate-400">No Result.</div>
        )}
      </div>

      <div className="mt-12">
        <DataTablePagination table={table} />
      </div>
    </div>
  );
}
