import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import React from 'react';

import GenerateForm from '@/components/form-generate/GenerateForm';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

import { useGlobalState } from '@/lib/store';
import { formatAddress, formatId } from '@/lib/utils';

import ProfileEditTabs from './ProfileEditTabs';
import ProfileImage from './ProfileImage';

const AccountProfile = () => {
  const gState = useGlobalState();
  const router = useRouter();
  const data = useHookstate(gState.fro.farmers.edit).value;

  return (
    <div className="p-6 md:p-8">
      <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between sm:gap-0">
        <div className="flex flex-1 justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Registered Accounts</h1>
            <Breadcrumb className="mt-2">
              <BreadcrumbList>
                <BreadcrumbItem className="cursor-pointer">
                  <BreadcrumbLink onClick={() => router.back()}>Back</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Registered Accounts</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <GenerateForm
            data={gState.fro.farmers.edit.get({ noproxy: true })}
            farmPlan={gState.fro.farmers.farmPlan.get({ noproxy: true })}
          />
        </div>
      </div>
      {data && (
        <div key={JSON.stringify(data)}>
          <div className="mt-8 flex flex-col gap-6 md:flex-row">
            <div className="card w-full">
              <h1 className="text-center text-xl font-bold text-kitaph-primary 2xl:text-left">Account Information</h1>

              <div className="mt-6 flex flex-col gap-8 2xl:flex-row">
                <ProfileImage data={data} />

                <div className="flex-1">
                  <dl className="grid gap-4 font-dmSans xl:grid-cols-2">
                    <div>
                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${data.farmer.first_name} ${data.farmer.last_name}`}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`ID${data.id.toString().padStart(9, '0')}`}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {data.farmer.mobile_number}
                        </dd>
                      </div>
                    </div>

                    <div>
                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {formatAddress(data.farmer.address)}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Birthday</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {format(data.farmer.birth_date, 'MMMM dd, yyyy')}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Email Address</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{data.email}</dd>
                      </div>
                    </div>
                  </dl>
                </div>
              </div>
            </div>
          </div>
          <ProfileEditTabs />
        </div>
      )}
    </div>
  );
};

export default AccountProfile;
