'use client';

import { useEffect } from 'react';

import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';

import { CreditScoreGroupTable } from './_components/group-table';
import { creditScoreGroupColumns } from './_components/group-table/columns';

export default function CreditScoreManagementPage() {
  const { groups, getGroups } = useCreditScoreMgt();

  useEffect(() => {
    getGroups();
  }, []);

  return (
    <div className="p-6 sm:p-8" key={JSON.stringify(groups.get({ noproxy: true }))}>
      <CreditScoreGroupTable columns={creditScoreGroupColumns} data={groups.get({ noproxy: true })} />
    </div>
  );
}
