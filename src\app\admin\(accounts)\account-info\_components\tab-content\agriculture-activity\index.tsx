'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import useFinance from '@/lib/hooks/useFinance';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { SaveConfirmation } from './save-confirmation';

// 0 = false, 1 = true
const checkOptions = [false, true];

export default function AgricultureActivityTabContent() {
  const gState = useGlobalState();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const { updateCreditScore } = useFinance();
  const creditScore = useHookstate(gState.selected.accountInfo['info']['farmer']['farmerCreditScore']);
  const credImg = creditScore?.vouch_by_leaders_attachment?.get({ noproxy: true }) || '';
  const credImgSplit = credImg?.split('/');
  const credName = credImgSplit[credImgSplit.length - 1];
  const radioYieldAchivement = useHookstate('');

  const saveConfirmation = useHookstate(false);
  const formData = useHookstate({
    geoTagging: creditScore.value ? creditScore['geo_tagging'].value : 0,
    geoTaggingRemarks: creditScore.value ? creditScore['geo_tagging_remarks'].value : '',
    landPreparation: creditScore.value ? creditScore['land_preparation'].value : 0,
    landPreparationRemarks: creditScore.value ? creditScore['land_preparation_remarks'].value : '',
    followedSeedsPerArea: creditScore.value ? creditScore['followed_seeds_per_area'].value : 0,
    fertilizationScheduleFirstVisit: creditScore.value ? creditScore['fertilization_schedule_first_visit'].value : 0,
    fertilizationScheduleFirstVisitRemarks: creditScore.value
      ? creditScore['fertilization_schedule_first_visit_remarks'].value
      : '',
    fertilizationScheduleSecondVisit: creditScore.value ? creditScore['fertilization_schedule_second_visit'].value : 0,
    fertilizationScheduleSecondVisitRemarks: creditScore.value
      ? creditScore['fertilization_schedule_second_visit_remarks'].value
      : '',
    fertilizationScheduleThirdVisit: creditScore.value ? creditScore['fertilization_schedule_third_visit'].value : 0,
    fertilizationScheduleThirdVisitRemarks: creditScore.value
      ? creditScore['fertilization_schedule_third_visit_remarks'].value
      : '',
    fertilizationVolumeFirstVisit: creditScore.value ? creditScore['fertilization_volume_first_visit'].value : 0,
    fertilizationVolumeFirstVisitRemarks: creditScore.value
      ? creditScore['fertilization_volume_first_visit_remarks'].value
      : '',
    fertilizationVolumeSecondVisit: creditScore.value ? creditScore['fertilization_volume_second_visit'].value : 0,
    fertilizationVolumeSecondVisitRemarks: creditScore.value
      ? creditScore['fertilization_volume_second_visit_remarks'].value
      : '',
    fertilizationVolumeThirdVisit: creditScore.value ? creditScore['fertilization_volume_third_visit'].value : 0,
    fertilizationVolumeThirdVisitRemarks: creditScore.value
      ? creditScore['fertilization_volume_third_visit_remarks'].value
      : '',
    cropProtectionScheduleFirstVisit: creditScore.value ? creditScore['crop_protection_schedule_first_visit'].value : 0,
    cropProtectionScheduleFirstVisitRemarks: creditScore.value
      ? creditScore['crop_protection_schedule_first_visit_remarks'].value
      : '',
    cropProtectionScheduleSecondVisit: creditScore.value
      ? creditScore['crop_protection_schedule_second_visit'].value
      : 0,
    cropProtectionScheduleSecondVisitRemarks: creditScore.value
      ? creditScore['crop_protection_schedule_second_visit_remarks'].value
      : '',
    cropProtectionScheduleThirdVisit: creditScore.value ? creditScore['crop_protection_schedule_third_visit'].value : 0,
    cropProtectionScheduleThirdVisitRemarks: creditScore.value
      ? creditScore['crop_protection_schedule_third_visit_remarks'].value
      : '',
    cropProtectionVolumeFirstVisit: creditScore.value ? creditScore['crop_protection_volume_first_visit'].value : 0,
    cropProtectionVolumeFirstVisitRemarks: creditScore.value
      ? creditScore['crop_protection_volume_first_visit_remarks'].value
      : '',
    cropProtectionVolumeSecondVisit: creditScore.value ? creditScore['crop_protection_volume_second_visit'].value : 0,
    cropProtectionVolumeSecondVisitRemarks: creditScore.value
      ? creditScore['crop_protection_volume_second_visit_remarks'].value
      : '',
    cropProtectionVolumeThirdVisit: creditScore.value ? creditScore['crop_protection_volume_third_visit'].value : 0,
    cropProtectionVolumeThirdVisitRemarks: creditScore.value
      ? creditScore['crop_protection_volume_third_visit_remarks'].value
      : '',
    harvestForecast: creditScore.value ? creditScore['harvest_forecast'].value : 0,
    harvestForecastRemarks: creditScore.value ? creditScore['harvest_forecast_remarks'].value : '',
    yieldAchievementOptionOne: creditScore.value ? creditScore['yield_achievement_option_one'].value : 0,
    yieldAchievementOptionOneRemarks: creditScore.value
      ? creditScore['yield_achievement_option_one_remarks'].value
      : '',
    yieldAchievementOptionTwo: creditScore.value ? creditScore['yield_achievement_option_two'].value : 0,
    yieldAchievementOptionTwoRemarks: creditScore.value
      ? creditScore['yield_achievement_option_two_remarks'].value
      : '',
    yieldAchievementOptionThree: creditScore.value ? creditScore['yield_achievement_option_three'].value : 0,
    yieldAchievementOptionThreeRemarks: creditScore.value
      ? creditScore['yield_achievement_option_three_remarks'].value
      : '',
    gapCompliance: creditScore.value ? creditScore['gap_compliance'].value : 0,
  });

  const onSubmit = () => {
    updateCreditScore({
      ...formData.get({ noproxy: true }),
      farmerId: data.farmer.id.value,
      userId: data.id.value,
    });
  };

  useEffect(() => {
    if (creditScore.value) {
      const options = {
        yield_achievement_option_one: creditScore['yield_achievement_option_one'].value,
        yield_achievement_option_two: creditScore['yield_achievement_option_two'].value,
        yield_achievement_option_three: creditScore['yield_achievement_option_three'].value,
      };

      const selectedOptions = Object.keys(options).find((key) => options[key] == 1);
      radioYieldAchivement.set(selectedOptions);

      if (selectedOptions === 'yield_achievement_option_one') {
        formData['yieldAchievementOptionTwoRemarks'].set('');
        formData['yieldAchievementOptionThreeRemarks'].set('');
      } else if (selectedOptions === 'yield_achievement_option_two') {
        formData['yieldAchievementOptionOneRemarks'].set('');
        formData['yieldAchievementOptionThreeRemarks'].set('');
      } else if (selectedOptions === 'yield_achievement_option_three') {
        formData['yieldAchievementOptionOneRemarks'].set('');
        formData['yieldAchievementOptionTwoRemarks'].set('');
      }
    }
  }, [
    creditScore['yield_achievement_option_one'],
    creditScore['yield_achievement_option_two'],
    creditScore['yield_achievement_option_three'],
  ]);

  return (
    <div className="mt-6">
      <div className="flex justify-end">
        <Button onClick={() => saveConfirmation.set(true)}>Save Changes</Button>
        <SaveConfirmation state={saveConfirmation} onSave={onSubmit} />
      </div>

      <div className="mt-6 grid gap-6 rounded-xl border-2 border-dashed bg-white p-6">
        {/* Farm Geo-Tagging */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="farm-geo-tagging"
            checked={checkOptions[formData['geoTagging'].value]}
            onCheckedChange={(v) => {
              formData['geoTagging'].set(v ? 1 : 0);
            }}
          />
          <label
            htmlFor="farm-geo-tagging"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Farm Geo-Tagging
          </label>
        </div>
        <div className="pl-6">
          <Input
            id="farm-geo-tagging-remarks"
            type="text"
            placeholder="Enter Remarks here"
            value={formData['geoTaggingRemarks'].value}
            onChange={(e) => {
              formData['geoTaggingRemarks'].set(e.target.value);
            }}
          />
        </div>

        {/* Land Preparation */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="land-preparation"
            checked={checkOptions[formData['landPreparation'].value]}
            onCheckedChange={(v) => {
              formData['landPreparation'].set(v ? 1 : 0);
            }}
          />
          <label
            htmlFor="land-preparation"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Land Preparation
          </label>
        </div>
        <div className="pl-6">
          <Input
            id="land-preparation-remarks"
            type="text"
            placeholder="Enter Remarks here"
            value={formData['landPreparationRemarks'].value}
            onChange={(e) => {
              formData['landPreparationRemarks'].set(e.target.value);
            }}
          />
        </div>

        {/* Followed Recommended No. of seeds per Area */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="followed-seeds-per-area"
            checked={checkOptions[formData['followedSeedsPerArea'].value]}
            onCheckedChange={(v) => {
              formData['followedSeedsPerArea'].set(v ? 1 : 0);
            }}
          />
          <label
            htmlFor="followed-seeds-per-area"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Followed Recommended No. of seeds per Area
          </label>
        </div>

        {/* Good Agricultural Practices (GAP) compliance */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="gap-compliance"
            checked={checkOptions[formData['gapCompliance'].value]}
            onCheckedChange={(v) => {
              formData['gapCompliance'].set(v ? 1 : 0);
            }}
          />
          <label
            htmlFor="gap-compliance"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Good Agricultural Practices (GAP) compliance
          </label>
        </div>

        {/* Fertilization Schedule */}
        <div className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          Fertilization Schedule
        </div>
        <div className="grid gap-2 pl-8">
          {/* 1st Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="fer-sched-1st-visit"
                checked={checkOptions[formData['fertilizationScheduleFirstVisit'].value]}
                onCheckedChange={(v) => {
                  formData['fertilizationScheduleFirstVisit'].set(v ? 1 : 0);
                }}
                disabled={checkOptions[formData['fertilizationScheduleSecondVisit'].value]}
              />
              <label
                htmlFor="fer-sched-1st-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                1st Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['fertilizationScheduleFirstVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['fertilizationScheduleFirstVisitRemarks'].value}
                onChange={(e) => {
                  formData['fertilizationScheduleFirstVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 2nd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="fer-sched-2nd-visit"
                checked={checkOptions[formData['fertilizationScheduleSecondVisit'].value]}
                onCheckedChange={(v) => {
                  formData['fertilizationScheduleSecondVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  checkOptions[
                    (formData['fertilizationScheduleFirstVisit'].value,
                    formData['fertilizationScheduleThirdVisit'].value)
                  ] || !formData['fertilizationScheduleFirstVisit'].value
                }
              />
              <label
                htmlFor="fer-sched-2nd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                2nd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['fertilizationScheduleSecondVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['fertilizationScheduleSecondVisitRemarks'].value}
                onChange={(e) => {
                  formData['fertilizationScheduleSecondVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 3rd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="fer-sched-3rd-visit"
                checked={checkOptions[formData['fertilizationScheduleThirdVisit'].value]}
                onCheckedChange={(v) => {
                  formData['fertilizationScheduleThirdVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  (formData['fertilizationScheduleFirstVisit'].value &&
                    !formData['fertilizationScheduleSecondVisit'].value) ||
                  !formData['fertilizationScheduleFirstVisit'].value
                }
              />
              <label
                htmlFor="fer-sched-3rd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                3rd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['fertilizationScheduleThirdVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['fertilizationScheduleThirdVisitRemarks'].value}
                onChange={(e) => {
                  formData['fertilizationScheduleThirdVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>
        </div>

        {/* Fertilization Volume */}
        <div className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          Fertilization Volume
        </div>
        <div className="grid gap-2 pl-8">
          {/* 1st Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="fer-vol-1st-visit"
                checked={checkOptions[formData['fertilizationVolumeFirstVisit'].value]}
                onCheckedChange={(v) => {
                  formData['fertilizationVolumeFirstVisit'].set(v ? 1 : 0);
                }}
                disabled={checkOptions[formData['fertilizationVolumeSecondVisit'].value]}
              />
              <label
                htmlFor="fer-vol-1st-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                1st Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['fertilizationVolumeFirstVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['fertilizationVolumeFirstVisitRemarks'].value}
                onChange={(e) => {
                  formData['fertilizationVolumeFirstVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 2nd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="fer-vol-2nd-visit"
                checked={checkOptions[formData['fertilizationVolumeSecondVisit'].value]}
                onCheckedChange={(v) => {
                  formData['fertilizationVolumeSecondVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  checkOptions[
                    (formData['fertilizationVolumeFirstVisit'].value, formData['fertilizationVolumeThirdVisit'].value)
                  ] || !formData['fertilizationVolumeFirstVisit'].value
                }
              />
              <label
                htmlFor="fer-vol-2nd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                2nd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['fertilizationVolumeSecondVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['fertilizationVolumeSecondVisitRemarks'].value}
                onChange={(e) => {
                  formData['fertilizationVolumeSecondVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 3rd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="fer-vol-3rd-visit"
                checked={checkOptions[formData['fertilizationVolumeThirdVisit'].value]}
                onCheckedChange={(v) => {
                  formData['fertilizationVolumeThirdVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  (formData['fertilizationVolumeFirstVisit'].value &&
                    !formData['fertilizationVolumeSecondVisit'].value) ||
                  !formData['fertilizationVolumeFirstVisit'].value
                }
              />
              <label
                htmlFor="fer-vol-3rd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                3rd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['fertilizationVolumeThirdVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['fertilizationVolumeThirdVisitRemarks'].value}
                onChange={(e) => {
                  formData['fertilizationVolumeThirdVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>
        </div>

        {/* Crop Protection Schedule */}
        <div className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          Crop Protection Schedule
        </div>
        <div className="grid gap-2 pl-8">
          {/* 1st Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="crop-sched-1st-visit"
                checked={checkOptions[formData['cropProtectionScheduleFirstVisit'].value]}
                onCheckedChange={(v) => {
                  formData['cropProtectionScheduleFirstVisit'].set(v ? 1 : 0);
                }}
                disabled={checkOptions[formData['cropProtectionScheduleSecondVisit'].value]}
              />
              <label
                htmlFor="crop-sched-1st-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                1st Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['cropProtectionScheduleFirstVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['cropProtectionScheduleFirstVisitRemarks'].value}
                onChange={(e) => {
                  formData['cropProtectionScheduleFirstVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 2nd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="crop-sched-2nd-visit"
                checked={checkOptions[formData['cropProtectionScheduleSecondVisit'].value]}
                onCheckedChange={(v) => {
                  formData['cropProtectionScheduleSecondVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  checkOptions[
                    (formData['cropProtectionScheduleFirstVisit'].value,
                    formData['cropProtectionScheduleThirdVisit'].value)
                  ] || !formData['cropProtectionScheduleFirstVisit'].value
                }
              />
              <label
                htmlFor="crop-sched-2nd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                2nd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['cropProtectionScheduleSecondVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['cropProtectionScheduleSecondVisitRemarks'].value}
                onChange={(e) => {
                  formData['cropProtectionScheduleSecondVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 3rd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="crop-sched-3rd-visit"
                checked={checkOptions[formData['cropProtectionScheduleThirdVisit'].value]}
                onCheckedChange={(v) => {
                  formData['cropProtectionScheduleThirdVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  (formData['cropProtectionScheduleFirstVisit'].value &&
                    !formData['cropProtectionScheduleSecondVisit'].value) ||
                  !formData['cropProtectionScheduleFirstVisit'].value
                }
              />
              <label
                htmlFor="crop-sched-3rd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                3rd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['cropProtectionScheduleThirdVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['cropProtectionScheduleThirdVisitRemarks'].value}
                onChange={(e) => {
                  formData['cropProtectionScheduleThirdVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>
        </div>

        {/* Crop Protection Volume */}
        <div className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          Crop Protection Volume
        </div>
        <div className="grid gap-2 pl-8">
          {/* 1st Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="crop-vol-1st-visit"
                checked={checkOptions[formData['cropProtectionVolumeFirstVisit'].value]}
                onCheckedChange={(v) => {
                  formData['cropProtectionVolumeFirstVisit'].set(v ? 1 : 0);
                }}
                disabled={checkOptions[formData['cropProtectionVolumeSecondVisit'].value]}
              />
              <label
                htmlFor="crop-vol-1st-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                1st Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['cropProtectionVolumeFirstVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['cropProtectionVolumeFirstVisitRemarks'].value}
                onChange={(e) => {
                  formData['cropProtectionVolumeFirstVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 2nd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="crop-vol-2nd-visit"
                checked={checkOptions[formData['cropProtectionVolumeSecondVisit'].value]}
                onCheckedChange={(v) => {
                  formData['cropProtectionVolumeSecondVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  checkOptions[
                    (formData['cropProtectionVolumeFirstVisit'].value, formData['cropProtectionVolumeThirdVisit'].value)
                  ] || !formData['cropProtectionVolumeFirstVisit'].value
                }
              />
              <label
                htmlFor="crop-vol-2nd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                2nd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['cropProtectionVolumeSecondVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['cropProtectionVolumeSecondVisitRemarks'].value}
                onChange={(e) => {
                  formData['cropProtectionVolumeSecondVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>

          {/* 3rd Visit */}
          <div className="flex items-center gap-8">
            <div className="flex w-36 items-center space-x-2">
              <Checkbox
                id="crop-vol-3rd-visit"
                checked={checkOptions[formData['cropProtectionVolumeThirdVisit'].value]}
                onCheckedChange={(v) => {
                  formData['cropProtectionVolumeThirdVisit'].set(v ? 1 : 0);
                }}
                disabled={
                  (formData['cropProtectionVolumeFirstVisit'].value &&
                    !formData['cropProtectionVolumeSecondVisit'].value) ||
                  !formData['fertilizationVolumeFirstVisit'].value
                }
              />
              <label
                htmlFor="crop-vol-3rd-visit"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                3rd Visit
              </label>
            </div>
            <div
              className={cn(
                'flex-1',
                checkOptions[formData['cropProtectionVolumeThirdVisit'].value] ? 'visible' : 'invisible',
              )}
            >
              <Input
                type="text"
                placeholder="Enter Remarks here"
                value={formData['cropProtectionVolumeThirdVisitRemarks'].value}
                onChange={(e) => {
                  formData['cropProtectionVolumeThirdVisitRemarks'].set(e.target.value);
                }}
              />
            </div>
          </div>
        </div>

        {/* Harvest Projection */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="harvest-forecast"
            checked={checkOptions[formData['harvestForecast'].value]}
            onCheckedChange={(v) => {
              formData['harvestForecast'].set(v ? 1 : 0);
            }}
          />
          <label
            htmlFor="harvest-forecast"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Harvest Projection
          </label>
        </div>
        <div className="pl-6">
          <Input
            id="harvest-forecast-remarks"
            type="text"
            placeholder="Enter Remarks here"
            value={formData['harvestForecastRemarks'].value}
            onChange={(e) => {
              formData['harvestForecastRemarks'].set(e.target.value);
            }}
          />
        </div>

        {/* Target Yield Achievement */}
        <div className="flex items-center space-x-2">
          <Checkbox
            id="leader-vouch"
            defaultChecked={checkOptions[creditScore?.yield_achievement?.value ?? 0]}
            disabled
          />
          <label
            htmlFor="leader-vouch"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Target Yield Achievement
          </label>
        </div>
        <div className="pl-6">
          <RadioGroup
            value={radioYieldAchivement.value}
            onValueChange={(v) => {
              radioYieldAchivement.set(v);

              formData['yieldAchievementOptionOne'].set(v === 'yield_achievement_option_one' ? 1 : 0);
              formData['yieldAchievementOptionTwo'].set(v === 'yield_achievement_option_two' ? 1 : 0);
              formData['yieldAchievementOptionThree'].set(v === 'yield_achievement_option_three' ? 1 : 0);
            }}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yield_achievement_option_one" id="yield_achievement_option_one" />
              <Label htmlFor="yield_achievement_option_one">80%-100%</Label>
            </div>
            {radioYieldAchivement.value === 'yield_achievement_option_one' && (
              <div className="py-2 pl-6">
                <Input
                  id="harvest-forecast-remarks"
                  type="text"
                  placeholder="Enter Remarks here"
                  value={formData['yieldAchievementOptionOneRemarks'].value}
                  onChange={(e) => {
                    formData['yieldAchievementOptionOneRemarks'].set(e.target.value);
                  }}
                />
              </div>
            )}
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yield_achievement_option_two" id="yield_achievement_option_two" />
              <Label htmlFor="yield_achievement_option_two">71%-79%</Label>
            </div>
            {radioYieldAchivement.value === 'yield_achievement_option_two' && (
              <div className="py-2 pl-6">
                <Input
                  id="harvest-forecast-remarks"
                  type="text"
                  placeholder="Enter Remarks here"
                  value={formData['yieldAchievementOptionTwoRemarks'].value}
                  onChange={(e) => {
                    formData['yieldAchievementOptionTwoRemarks'].set(e.target.value);
                  }}
                />
              </div>
            )}
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yield_achievement_option_three" id="yield_achievement_option_three" />
              <Label htmlFor="yield_achievement_option_three">70% and below</Label>
            </div>
            {radioYieldAchivement.value === 'yield_achievement_option_three' && (
              <div className="py-2 pl-6">
                <Input
                  id="harvest-forecast-remarks"
                  type="text"
                  placeholder="Enter Remarks here"
                  value={formData['yieldAchievementOptionThreeRemarks'].value}
                  onChange={(e) => {
                    formData['yieldAchievementOptionThreeRemarks'].set(e.target.value);
                  }}
                />
              </div>
            )}
          </RadioGroup>
        </div>
      </div>
    </div>
  );
}
