'use client';

import { useHookstate } from '@hookstate/core';

import axios from '@/lib/api';
import { useGlobalState } from '@/lib/store';

export default function useMember() {
  const gState = useGlobalState();

  const nonLoanHolderDashboard = useHookstate(gState.admin.members.nonLoanHolderDashboard);
  const nonLoanHolders = useHookstate(gState.admin.members.nonLoanHolder);
  const pagination = useHookstate(gState.admin.pagination.nonLoanHolder);

  const loanHolderDashboard = useHookstate(gState.admin.members.loanHolderDashboard);
  const loanHolders = useHookstate(gState.admin.members.loanHolder);
  const paginationLoanHolder = useHookstate(gState.admin.pagination.loanHolder);

  const loanApplicants = useHookstate(gState.admin.members.loanApplicants);
  const loanApplicantsDashboard = useHookstate(gState.admin.members.loanApplicantsDashboard);
  const paginationLoanApplicants = useHookstate(gState.admin.pagination.loanApplicants);

  const getNonLoanHolders = async () => {
    try {
      const _res = await axios
        .get(`/admin/user/farmers/nonloanholder`, {
          params: {
            ...gState.admin.pagination.nonLoanHolder.get({ noproxy: true }),
          },
        })
        .then((res) => res.data.data);
      console.log('getNonLoanHolders: ', _res);
      nonLoanHolders.set(_res);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getNonLoanHolders: ', error);
    }
  };

  const getNonLoanHoldersDashboard = async () => {
    try {
      const _res = await axios
        .get(`/admin/user/farmers/nonloanholder/dashboard`, {
          params: {
            creditScoreGroupId: pagination.creditScoreGroupId.value,
            totalTradingpostTransactionStart: pagination.totalTradingpostTransactionStart.value,
            totalTradingpostTransactionEnd: pagination.totalTradingpostTransactionEnd.value,
            totalMarketplaceTransactionStart: pagination.totalMarketplaceTransactionStart.value,
            totalMarketplaceTransactionEnd: pagination.totalMarketplaceTransactionEnd.value,
            totalSalesTransactionStart: pagination.totalSalesTransactionStart.value,
            totalSalesTransactionEnd: pagination.totalSalesTransactionEnd.value,
            lastTransactionDays: pagination.lastTransactionDays.value,
          },
        })
        .then((res) => res.data.data);
      console.log('getNonLoanHoldersDashboard: ', _res);
      nonLoanHolderDashboard.set(_res);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getNonLoanHoldersDashboard: ', error);
    }
  };

  const getLoanHolders = async () => {
    try {
      const _res = await axios
        .get(`/admin/user/farmers/loanholder`, {
          params: {
            ...gState.admin.pagination.loanHolder.get({ noproxy: true }),
          },
        })
        .then((res) => res.data.data);
      console.log('getLoanHolders: ', _res);
      loanHolders.set(_res);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getLoanHolders: ', error);
    }
  };

  const getLoanHoldersDashboard = async () => {
    try {
      const _res = await axios
        .get(`/admin/user/farmers/loanholder/dashboard`, {
          params: {
            creditScoreGroupIds: paginationLoanHolder.creditScoreGroupIds.value,
            loanTerm: paginationLoanHolder.loanTerm.value,
            loanBalanceStart: paginationLoanHolder.loanBalanceStart.value,
            loanBalanceEnd: paginationLoanHolder.loanBalanceEnd.value,
            loanAmountStart: paginationLoanHolder.loanAmountStart.value,
            loanAmountEnd: paginationLoanHolder.loanAmountEnd.value,
            startDate: paginationLoanHolder.startDate.value,
            endDate: paginationLoanHolder.endDate.value,
          },
        })
        .then((res) => res.data.data);
      console.log('getLoanHoldersDashboard: ', _res);
      loanHolderDashboard.set(_res);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getLoanHoldersDashboard: ', error);
    }
  };

  const getLoanApplicants = async () => {
    try {
      const _res = await axios
        .get(`/admin/user/farmers/loanapplicant`, {
          params: {
            ...gState.admin.pagination.loanApplicants.get({ noproxy: true }),
          },
        })
        .then((res) => res.data.data);
      console.log('getLoanApplicants: ', _res);
      loanApplicants.set(_res);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getLoanApplicants: ', error);
    }
  };

  const getLoanApplicantsDashboard = async () => {
    try {
      const _res = await axios
        .get(`/admin/user/farmers/loanapplicant/dashboard`, {
          params: {
            ...gState.admin.pagination.loanApplicants.get({ noproxy: true }),
          },
        })
        .then((res) => res.data.data);
      console.log('getLoanApplicantsDashboard: ', _res);
      loanApplicantsDashboard.set(_res);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getLoanApplicantsDashboard: ', error);
    }
  };

  return {
    getNonLoanHolders,
    nonLoanHolders,
    pagination,
    getNonLoanHoldersDashboard,
    nonLoanHolderDashboard,
    getLoanHolders,
    loanHolders,
    paginationLoanHolder,
    getLoanHoldersDashboard,
    loanHolderDashboard,
    loanApplicants,
    loanApplicantsDashboard,
    paginationLoanApplicants,
    getLoanApplicants,
    getLoanApplicantsDashboard,
  };
}
