'use client';

import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { LoanRequestStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { getUserType } from '@/lib/constants';
import { cn } from '@/lib/utils';

export const columns = [
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          <Badge className={cn(LoanRequestStatusLabels[data.status].color)}>
            {LoanRequestStatusLabels[data.status].label}
          </Badge>
        </div>
      );
    },
    accessorFn: (row) => row.status,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const farmer = row.original.user.farmer;
      return <div className="min-w-max capitalize">{`${farmer.first_name} ${farmer.last_name}`}</div>;
    },
    accessorFn: (row) => `${row.user.farmer.first_name} ${row.user.farmer.last_name}`,
  },
  {
    id: 'account_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">ID{data.user.id.toString().padStart(9, '0')}</div>;
    },
    accessorFn: (row) => row.user.id.toString().padStart(9, '0'),
  },
  {
    id: 'payment_amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Amount" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {`${Number(data.amount).toLocaleString('en-US', {
            style: 'currency',
            currency: 'PHP',
          })}`}
        </div>
      );
    },
    accessorFn: (row) => `${Number(row.amount)}`,
  },
  {
    id: 'penalty_charge',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Penalty Amount" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {`${Number(data.penalty_amount).toLocaleString('en-US', {
            style: 'currency',
            currency: 'PHP',
          })}`}
        </div>
      );
    },
    accessorFn: (row) => `${Number(row.penalty_amount)}`,
  },
  {
    id: 'payment_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{format(new Date(data.payment_date), 'MMM dd, yyyy')}</div>;
    },
    accessorFn: (row) => {
      return `${format(new Date(row.payment_date), 'MMM dd, yyyy | hh:mm a')}`;
    },
  },
  {
    id: 'date_processed',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Processed" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{format(new Date(data.created_at), 'MMM dd, yyyy | hh:mm a')}</div>;
    },
    accessorFn: (row) => {
      return `${format(new Date(row.created_at), 'MMM dd, yyyy | hh:mm a')}`;
    },
  },
  {
    id: 'transaction_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment ID" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{`${data.reference_number}`}</div>;
    },
    accessorFn: (row) => {
      return `${row.reference_number}`;
    },
  },
  {
    id: 'mode_payment',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Mode of Payment" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.mode_of_payment}</div>;
    },
    accessorFn: (row) => {
      return `${row.mode_of_payment}`;
    },
  },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    cell: ({ row }) => {
      const data = row.original.processedBy;
      const user = data[getUserType(data.user_type)];
      return <div className="min-w-max">{user ? `${user.first_name} ${user.last_name}` : data.email}</div>;
    },
    accessorFn: (row) => {
      const data = row.processedBy;
      const user = data[getUserType(data.user_type)];
      return user ? `${user.first_name} ${user.last_name}` : data.email;
    },
  },
  // {
  //   id: 'amount',
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" />,
  //   cell: ({ row }) => {
  //     const data = row.original;
  //     return (
  //       <div className="min-w-max">{`${Number(data.total_price).toLocaleString('en-US', {
  //         style: 'currency',
  //         currency: 'PHP',
  //       })}`}</div>
  //     );
  //   },
  //   accessorFn: (row) => {
  //     return `${row.total_price}`;
  //   },
  // },
  // {
  //   id: 'total_items',
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Total Items" />,
  //   cell: ({ row }) => {
  //     const data = row.original;
  //     return (
  //       <div className="min-w-max">
  //         {data.marketplaceProductOrders.map((invoice) => invoice.quantity).reduce((a, b) => a + b, 0)}
  //       </div>
  //     );
  //   },
  //   accessorFn: (row) => {
  //     return `${row.marketplaceProductOrders.map((invoice) => invoice.quantity).reduce((a, b) => a + b, 0)}`;
  //   },
  // },
];
