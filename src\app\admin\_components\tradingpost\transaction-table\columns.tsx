'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { getUserType } from '@/lib/constants';
import { calculateDuration } from '@/lib/utils';

export const columns = [
  {
    id: 'plate_no',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Plate No." />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max uppercase">{data.vehicle_plate_number}</div>;
    },
    accessorFn: (row) => `${row.vehicle_plate_number}`,
  },
  {
    id: 'farmer_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Farmer Name" />,
    accessorFn: (row) => {
      const type = getUserType(row.user_type);
      return `${row[type]?.first_name} ${row[type]?.last_name}`;
    },
  },
  {
    id: 'transaction_value',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Value" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.gross_sales.toLocaleString('en-US', {
            style: 'currency',
            currency: 'PHP',
          })}
        </div>
      );
    },
    accessorFn: (row) => `${row.gross_sales}`,
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const variant = {
        0: 'info',
        1: 'success',
        2: 'orange',
        3: 'error',
      };
      const desc = ['Entry', 'Complete', 'Pending Exit', 'Expired'];

      return (
        <div className="flex min-w-max items-center gap-3">
          <Badge variant={variant[data.status]}>
            <span>{desc[data.status]}</span>
          </Badge>
        </div>
      );
    },
    accessorFn: (row) => {
      const desc = ['Entry', 'Complete', 'Pending Exit', 'Expired'];
      return desc[row.status];
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'entry_time',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Entry Time" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${new Date(data.entry_time ?? data.created_at).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        })}`}</div>
      );
    },
    accessorFn: (row) =>
      `${new Date(row.entry_time ?? row.created_at).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })}`,
  },
  {
    id: 'entry_weight',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Entry Weight" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{Number(data.entry_weight).toLocaleString()} kg</div>;
    },
    accessorFn: (row) => Number(row.entry_weight).toLocaleString(),
  },
  {
    id: 'exit_time',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Exit Time" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${
          data.exit_time
            ? new Date(data.exit_time).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
              })
            : 'N/A'
        }`}</div>
      );
    },
    accessorFn: (row) =>
      `${
        row.exit_time
          ? new Date(row.exit_time).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
            })
          : 'N/A'
      }`,
  },
  {
    id: 'exit_weight',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Exit Weight" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{Number(data.exit_weight).toLocaleString()} kg</div>;
    },
    accessorFn: (row) => Number(row.exit_weight).toLocaleString(),
  },
  {
    id: 'total_hours',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total Hours" />,
    cell: ({ row }) => {
      const data = row.original;
      const start = new Date(data.entry_time ?? data.created_at);
      const end = data.exit_time ? new Date(data.exit_time) : new Date();
      const { hours, minutes } = calculateDuration(start, end);

      return (
        <div className="min-w-max" key={`${hours} ${minutes}`}>
          {hours > 0 ? `${hours} hrs & ${minutes} mins` : `${minutes} mins`}
        </div>
      );
    },
    accessorFn: (row) => {
      const start = new Date(row.entry_time ?? row.created_at);
      const end = row.exit_time ? new Date(row.exit_time) : new Date();
      const { hours, minutes } = calculateDuration(start, end);
      return hours > 0 ? `${hours} hrs & ${minutes} mins` : `${minutes} mins`;
    },
  },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.processedBy && getUserType(data.processedBy.user_type) === 'encoder'
            ? data.processedBy.encoder
              ? `${data.processedBy.encoder.first_name} ${data.processedBy.encoder.last_name}`
              : data.processedBy.email
            : data.processedBy.admin
              ? `${data.processedBy.admin.first_name} ${data.processedBy.admin.last_name}`
              : data.processedBy.email}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.processedBy && getUserType(row.processedBy.user_type) === 'encoder'
        ? row.processedBy.encoder
          ? `${row.processedBy.encoder.first_name} ${row.processedBy.encoder.last_name}`
          : row.processedBy.email
        : row.processedBy.admin
          ? `${row.processedBy.admin.first_name} ${row.processedBy.admin.last_name}`
          : row.processedBy.email;
    },
  },
];
