'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { DashboardStat } from '@/app/admin/marketplace/_components/orders-table';
import { TAB_LOAN_HOLDER, TabLoanHolderEnum } from '@/lib/constants/enums';
import useMember from '@/lib/hooks/admin/useMember';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function LoanHolderTable({ columns, data, metadata = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const { loanHolderDashboard, paginationLoanHolder, loanApplicants, loanApplicantsDashboard } = useMember();

  const tabLoanHolder = useHookstate(gStateP.tabsLoanHolder);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);

        if (tabLoanHolder.value === TabLoanHolderEnum.LOAN_HOLDER) {
          router.push(`/admin/account-info/?id=${data.user.id}`);
        } else {
          console.log('TEST: ', data);
          gStateP.selected.loanApplicationDetails.set(data);
          router.push(`./loan-application-details/`);
        }
      },
    },
  });

  return (
    <div className="space-y-4">
      {tabLoanHolder.value === TabLoanHolderEnum.LOAN_HOLDER && (
        <div className="grid grid-cols-1 gap-4 pb-4 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-5">
          {/* All Loan Holder */}
          <DashboardStat
            value={loanHolderDashboard.allLoan.value}
            label="All Loan Holder"
            className="bg-loanHolder-all focus:shadow-xl focus:shadow-loanHolder-all/60"
            img="/assets/loan-holders/all.png"
            onClick={() => {
              paginationLoanHolder.paymentStatuses.set([]);
            }}
          />

          {/* Active */}
          <DashboardStat
            value={loanHolderDashboard.active.value}
            label="Active"
            className="bg-loanHolder-active focus:shadow-xl focus:shadow-loanHolder-active/60"
            img="/assets/loan-holders/active.png"
            onClick={() => paginationLoanHolder.paymentStatuses.set(['ACTIVE'])}
          />

          {/* Paid Off */}
          <DashboardStat
            value={loanHolderDashboard.paid.value}
            label="Paid Off"
            className="bg-loanHolder-paidOff focus:shadow-xl focus:shadow-loanHolder-paidOff/60"
            img="/assets/loan-holders/paid-off.png"
            onClick={() => paginationLoanHolder.paymentStatuses.set(['PAID'])}
          />

          {/* In Grace Period */}
          <DashboardStat
            value={loanHolderDashboard.grace.value}
            label="In Grace Period"
            className="bg-loanHolder-gracePeriod focus:shadow-xl focus:shadow-loanHolder-gracePeriod/60"
            img="/assets/loan-holders/grace-period.png"
            onClick={() => paginationLoanHolder.paymentStatuses.set(['GRACE'])}
          />

          {/* Overdue */}
          <DashboardStat
            value={loanHolderDashboard.overdue.value}
            label="Overdue"
            className="bg-loanHolder-overdue focus:shadow-xl focus:shadow-loanHolder-overdue/60"
            img="/assets/loan-holders/overdue.png"
            onClick={() => paginationLoanHolder.paymentStatuses.set(['OVERDUE'])}
          />
        </div>
      )}
      {tabLoanHolder.value === TabLoanHolderEnum.LOAN_APPLICANTS && (
        <div className="grid grid-cols-1 gap-4 pb-4 sm:grid-cols-2 md:grid-cols-4">
          {/* Loan Applicants */}
          <DashboardStat
            value={loanApplicantsDashboard.all.value}
            label="Loan Applicants"
            className="bg-loanApplicants-all focus:shadow-xl focus:shadow-loanApplicants-all/60"
            img="/assets/loan-applicants/loan-applicants.png"
          />

          {/* Approved */}
          <DashboardStat
            value={loanApplicantsDashboard.approved.value}
            label="Approved"
            className="bg-loanApplicants-approved focus:shadow-xl focus:shadow-loanApplicants-approved/60"
            img="/assets/loan-applicants/approved.png"
          />

          {/* Pending */}
          <DashboardStat
            value={loanApplicantsDashboard.pending.value}
            label="Pending"
            className="bg-loanApplicants-pending focus:shadow-xl focus:shadow-loanApplicants-pending/60"
            img="/assets/loan-applicants/pending.png"
          />

          {/* Rejected */}
          <DashboardStat
            value={loanApplicantsDashboard.rejected.value}
            label="Rejected"
            className="bg-loanApplicants-rejected focus:shadow-xl focus:shadow-loanApplicants-rejected/60"
            img="/assets/loan-applicants/rejected.png"
          />
        </div>
      )}

      {/* Tabs */}
      <HorizontalScrollBar>
        <div className="flex w-max items-center gap-8">
          {TAB_LOAN_HOLDER.map((tab, index) => {
            const isSelected = tab === tabLoanHolder.value;

            return (
              <button
                key={tab}
                className={cn(
                  'transition-all duration-300 ease-in-out whitespace-nowrap my-3 capitalize',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => {
                  tabLoanHolder.set(tab);
                }}
              >
                {tab.replace('-', ' ')}
              </button>
            );
          })}
        </div>
      </HorizontalScrollBar>

      <DataTableToolbar
        id={tabLoanHolder.value === TabLoanHolderEnum.LOAN_HOLDER ? 'account-loan' : 'account-loan-applicants'}
        table={table}
        meta={metadata}
      />

      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      if ((event.target as HTMLElement).tagName.toLowerCase() !== 'button')
                        table.options.meta?.getRowClicked?.(row);
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {metadata ? (
        <DataTablePaginationMeta
          table={table}
          meta={metadata}
          onChangePageSize={(pageSize) => {
            gState.admin.pagination.loanHolder.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            gState.admin.pagination.loanHolder.page.set(page);
          }}
          all={metadata.total}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}
