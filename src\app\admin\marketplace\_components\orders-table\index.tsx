'use client';

import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { Scroll } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import { OrderStatusType } from '../Enums';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function OrdersTable({ columns, data, meta = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);

        gStateP.admin['viewOrder'].set(data);
        router.push('/admin/marketplace/order/details?id=' + data.id);
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4 pb-4 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-6">
        {/* Total Orders */}
        <DashboardStat
          value={gState.admin.marketplace.dashboard.totalOrders.value}
          label="Total Orders"
          className="bg-dashboard-red focus:shadow-xl focus:shadow-dashboard-red/60"
          img="/assets/icon/total_orders.png"
          onClick={() => gState.admin.pagination.orders.status.set([])}
        />

        {/* Pending */}
        <DashboardStat
          value={gState.admin.marketplace.dashboard.pendingOrder.value}
          label="Pending"
          className="bg-dashboard-yellow focus:shadow-xl focus:shadow-dashboard-yellow/60"
          img="/assets/icon/pending.png"
          onClick={() => gState.admin.pagination.orders.status.set([OrderStatusType.PENDING])}
        />

        {/* Preparing */}
        <DashboardStat
          value={gState.admin.marketplace.dashboard.preparingOrder.value}
          label="Preparing"
          className="bg-dashboard-purple focus:shadow-xl focus:shadow-dashboard-purple/60"
          img="/assets/icon/preparing.png"
          onClick={() => gState.admin.pagination.orders.status.set([OrderStatusType.PREPARING])}
        />

        {/* Order Ready */}
        <DashboardStat
          value={gState.admin.marketplace.dashboard.orderReadyOrder.value}
          label="Order Ready"
          className="bg-dashboard-blue focus:shadow-xl focus:shadow-dashboard-blue/60"
          img="/assets/icon/order_ready.svg"
          onClick={() => gState.admin.pagination.orders.status.set([OrderStatusType.ORDER_READY])}
        />

        {/* Completed */}
        <DashboardStat
          value={gState.admin.marketplace.dashboard.completedOrder.value}
          label="Completed"
          className="bg-dashboard-green focus:shadow-xl focus:shadow-dashboard-green/60"
          img="/assets/icon/completed.png"
          onClick={() => gState.admin.pagination.orders.status.set([OrderStatusType.COMPLETED])}
        />

        {/* Cancelled */}
        <DashboardStat
          value={gState.admin.marketplace.dashboard.cancelledOrder.value}
          label="Cancelled"
          className="bg-dashboard-red-dark focus:shadow-xl focus:shadow-dashboard-red-dark/60"
          img="/assets/icon/cancelled.png"
          onClick={() => gState.admin.pagination.orders.status.set([OrderStatusType.CANCELLED])}
        />
      </div>

      <DataTableToolbar id="marketplace-orders" table={table} meta={meta} />

      <div className="flex rounded-md border bg-white">
        <ScrollArea className="w-1 flex-1">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      const target = event.target as HTMLElement;
                      const isButton = target.tagName.toLowerCase() === 'button';
                      const isP = target.tagName.toLowerCase() === 'p';
                      const isHeading = target.tagName.toLowerCase() === 'h2';
                      const isOpen = target.dataset.state === 'open';
                      const isDisabled = target.classList.contains('flex');

                      if (!isButton && !isP && !isHeading && !isOpen && !isDisabled) {
                        table.options.meta?.getRowClicked?.(row);
                      }
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      {meta ? (
        <DataTablePaginationMeta
          table={table}
          meta={meta}
          onChangePageSize={(pageSize) => {
            gState.admin.pagination.orders.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            gState.admin.pagination.orders.page.set(page);
          }}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}

export const DashboardStat = ({ value, label, img, onClick = () => {}, className = '' }) => {
  return (
    <button
      className={cn(
        'flex items-center justify-start pl-3 rounded-xl gap-3 font-poppins py-6 cursor-pointer',
        'transition-all duration-300 ease-in-out',
        className,
      )}
      onClick={onClick}
    >
      <div>
        <img className="h-[2.6rem]" src={img} alt={label} />
      </div>
      <div className="text-left text-[#151D48]">
        <div className="min-w-max font-medium">{label}</div>
        <div className="text-3xl font-bold">{value}</div>
      </div>
    </button>
  );
};
