'use client';

import { useEffect } from 'react';

import useLoanPayment from '@/lib/hooks/useLoanPayment';
import { useGlobalState } from '@/lib/store';

import { LoanPaymentTable } from './loan-payment-table';
import { columns } from './loan-payment-table/columns';

export default function Finance2PaymentPage() {
  const gState = useGlobalState();
  const { fetchRequests } = useLoanPayment();

  useEffect(() => {
    Promise.all([fetchRequests()]);
  }, [
    gState.finance.loanPayments.pagination.requests.startDate,
    gState.finance.loanPayments.pagination.requests.endDate,
    gState.finance.loanPayments.pagination.requests.page,
    gState.finance.loanPayments.pagination.requests.pageSize,
    gState.finance.loanPayments.pagination.requests.status[0],
  ]);

  return (
    <div className="p-8">
      <LoanPaymentTable
        columns={columns}
        data={gState.finance.loanPayments.requests.data.get({ noproxy: true })}
        meta={gState.finance.loanPayments.requests['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
