'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

import { isSoaEnabled } from '@/lib/config/features';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import { TRANSACTION_TAB } from '../../constants';
import { MarketplaceTable } from '../../marketplace-table';
import { columns as marketplaceColumns, selectColumn } from '../../marketplace-table/columns';
import { SalesTransactionTable } from '../../sales-transaction';
import { columns as salesTransactionColumn } from '../../sales-transaction/columns';
import { TransactionDetailsTable } from '../../transaction-details-table';
import { columns } from '../../transaction-details-table/columns';

export default function TransactionsTabContent() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const transTab = useHookstate(gState.selected.accountInfo.tabs.transaction);
  const data = useHookstate(gState.selected.accountInfo['info']);
  const lastTransaction = useHookstate('');

  useEffect(() => {
    const latestCreatedAt = [
      ...(gState.selected.accountInfo['tradingPost']['data'].length > 0
        ? [gState.selected.accountInfo['tradingPost']['data'][0]['created_at'].get({ noproxy: true })]
        : []),
      ...(gState.selected.accountInfo['marketplace']['data'].length > 0
        ? [gState.selected.accountInfo['marketplace']['data'][0]['created_at'].get({ noproxy: true })]
        : []),
      ...(gState.selected.accountInfo['sales']['data'].length > 0
        ? [gState.selected.accountInfo['sales']['data'][0]['created_at'].get({ noproxy: true })]
        : []),
    ].sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
    if (latestCreatedAt.length > 0) {
      lastTransaction.set(latestCreatedAt[0]);
    } else {
      lastTransaction.set('');
    }
  }, [
    gState.selected.accountInfo['tradingPost'],
    gState.selected.accountInfo['marketplace'],
    gState.selected.accountInfo['sales'],
  ]);

  return (
    <div>
      {/* Stats Card */}
      <div className="grid gap-4 pt-8 sm:grid-cols-2 2xl:grid-cols-3">
        <div className="card space-y-2 text-center">
          <div className="text-2xl font-bold text-primary">
            {data.value
              ? Number(data.wallet.value ? data.wallet.balance.value : 0).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })
              : 0}
          </div>
          <div className="text-sm font-bold text-[#A3AED0]">E-Wallet Load</div>
        </div>

        <div className="card space-y-2 text-center">
          <div className="text-2xl font-bold text-primary">
            {lastTransaction.value ? format(new Date(lastTransaction.value), 'MMM dd, yyyy') : 'N/A'}
          </div>
          <div className="text-sm font-bold text-[#A3AED0]">Date of Last Transaction</div>
        </div>
      </div>

      {transTab.value === 'sales_transaction' && (
        <div className="mt-8 grid place-items-end">
          <Button
            type="button"
            onClick={() => {
              gStateP.selected['farmer'].set(gState.selected.accountInfo['info'].get({ noproxy: true }));
              router.push('./add-transaction/');
            }}
          >
            Add Sales Transaction
          </Button>
        </div>
      )}

      <ScrollArea className="my-8 w-[calc(100vw-48px)] sm:w-auto">
        <div className="mr-6 flex w-max items-center gap-8 pb-1">
          {TRANSACTION_TAB.map((tab) => {
            const isSelected = tab.value === transTab.value;

            return (
              <button
                key={tab.value}
                className={cn(
                  'transition-all duration-300 ease-in-out min-w-max',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => transTab.set(tab.value)}
              >
                {tab.name}
              </button>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {transTab.value === 'trading_post' && (
        <TransactionDetailsTable
          columns={columns}
          data={gState.selected.accountInfo.tradingPost.data.get({ noproxy: true })}
          metadata={gState.selected.accountInfo.tradingPost['meta'].get({ noproxy: true })}
        />
      )}

      {transTab.value === 'marketplace' && (
        <MarketplaceTable
          columns={isSoaEnabled ? [selectColumn, ...marketplaceColumns] : marketplaceColumns}
          data={gState.selected.accountInfo.marketplace.data.get({ noproxy: true })}
          metadata={gState.selected.accountInfo.marketplace['meta'].get({ noproxy: true })}
        />
      )}

      {transTab.value === 'sales_transaction' && (
        <SalesTransactionTable
          columns={salesTransactionColumn}
          data={gState.selected.accountInfo.sales.data.get({ noproxy: true })}
          metadata={gState.selected.accountInfo.sales['meta'].get({ noproxy: true })}
        />
      )}
    </div>
  );
}
