'use client';

import { toast } from 'sonner';
import { z } from 'zod';

import axios from '@/lib/api';

import { useGlobalState } from '../store';

export const SeedSchema = z.object({
  seeds: z.array(
    z.object({
      name: z
        .string()
        .min(2, 'Seed name is required')
        .max(50, 'Seed name is too long')
        .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
          message: 'Seed name must contain only letters and spaces',
        }),
      seedSubcategoryId: z.string(),
      seedBreedId: z.string().optional(),
      seedVarietyId: z.string().optional(),
    }),
  ),
});
export type SeedType = z.infer<typeof SeedSchema>;

export const UpdateSeedSchema = z.object({
  seedId: z.string().min(1, 'Seed ID is required'),
  name: z
    .string()
    .min(2, 'Seed name is required')
    .max(50, 'Seed name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Seed name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Status is required'),
  seedSubcategoryId: z.string().min(1, 'Subcategory is required'),
  seedBreedId: z.string().optional(),
  seedVarietyId: z.string().optional(),
});
export type UpdateSeedType = z.infer<typeof UpdateSeedSchema>;

// Seed Subcategory
export const SubcatSeedSchema = z.object({
  name: z
    .string()
    .min(2, 'Subcategory name is required')
    .max(50, 'Subcategory name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Subcategory name must contain only letters and spaces',
    }),
});
export type SubcatSeedType = z.infer<typeof SubcatSeedSchema>;

export const UpdateSubcatSeedSchema = z.object({
  seedSubcategoryId: z.string().min(1, 'Subcategory is required'),
  name: z
    .string()
    .min(2, 'Subcategory name is required')
    .max(50, 'Subcategory name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Subcategory name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Status is required'),
});
export type UpdateSubcatSeedType = z.infer<typeof UpdateSubcatSeedSchema>;

// Seed Variety
export const VarietySeedSchema = z.object({
  name: z
    .string()
    .min(2, 'Variety name is required')
    .max(50, 'Variety name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Variety name must contain only letters and spaces',
    }),
});
export type VarietySeedType = z.infer<typeof VarietySeedSchema>;

export const UpdateVarietySeedSchema = z.object({
  seedVarietyId: z.string().min(1, 'Variety is required'),
  name: z
    .string()
    .min(2, 'Variety name is required')
    .max(50, 'Variety name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Variety name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Status is required'),
});
export type UpdateVarietySeedType = z.infer<typeof UpdateVarietySeedSchema>;

// Seed Breed
export const BreedSeedSchema = z.object({
  name: z
    .string()
    .min(2, 'Breed name is required')
    .max(50, 'Breed name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Breed name must contain only letters and spaces',
    }),
});
export type BreedSeedType = z.infer<typeof BreedSeedSchema>;

export const UpdateBreedSeedSchema = z.object({
  seedBreedId: z.string().min(1, 'Breed is required'),
  name: z
    .string()
    .min(2, 'Breed name is required')
    .max(50, 'Breed name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Breed name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Status is required'),
});
export type UpdateBreedSeedType = z.infer<typeof UpdateBreedSeedSchema>;

export default function useSeeds() {
  const gState = useGlobalState();

  const getSeeds = async () => {
    try {
      const _crops = await axios.get('/admin/seeds/viewAll').then((res) => res.data.data);
      console.log('getSeeds: ', _crops);
      gState.admin.seeds.data.set(_crops);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getSeeds: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getSubcategory = async () => {
    try {
      const _chems = await axios.get('/admin/seeds/subcategory/viewAll').then((res) => res.data.data);
      console.log('getSubcategory: ', _chems);
      gState.admin.seeds.subcategory.set(_chems);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getSubcategory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addSeed = async (data) => {
    console.log('addSeed: ', data);

    try {
      const _data = await axios.post('/admin/seeds/create', data).then((res) => res.data);
      console.log('addSeed: ', _data);
      await getSeeds();

      toast.success('Success', {
        description: 'Seed has been added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addSeed: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateSeed = async (data) => {
    console.log('updateSeed: ', data);

    try {
      const _data = await axios.post('/admin/seeds/update', data).then((res) => res.data);
      console.log('updateSeed: ', _data);
      await getSeeds();

      toast.success('Success', {
        description: 'Seed updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateSeed: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateSubcategory = async (data) => {
    console.log('updateSubcategory: ', data);

    try {
      const _data = await axios.post('/admin/seeds/subcategory/update', data).then((res) => res.data);
      console.log('updateSubcategory: ', _data);
      await getSubcategory();

      toast.success('Success', {
        description: 'Subcategory updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateSubcategory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addSubcategory = async (data) => {
    console.log('addSubcategory: ', data);

    try {
      const _data = await axios.post('/admin/seeds/subcategory/create', data).then((res) => res.data);
      console.log('addSubcategory: ', _data);
      await getSubcategory();

      toast.success('Success', {
        description: 'Subcategory has been added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addSubcategory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getVariety = async () => {
    try {
      const _chems = await axios.get('/admin/seeds/variety/viewAll').then((res) => res.data.data);
      console.log('getVariety: ', _chems);
      gState.admin.seeds.variety.set(_chems);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getVariety: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addVariety = async (data) => {
    console.log('addVariety: ', data);

    try {
      const _data = await axios.post('/admin/seeds/variety/create', data).then((res) => res.data);
      console.log('addVariety: ', _data);
      await getVariety();

      toast.success('New Variety', {
        description: `${data.name} has been added`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addVariety: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateVariety = async (data) => {
    console.log('updateVariety: ', data);

    try {
      const _data = await axios.post('/admin/seeds/variety/update', data).then((res) => res.data);
      console.log('updateVariety: ', _data);
      await getVariety();

      toast.success('Update Variety', {
        description: `${data.name} updated successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateVariety: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getBreed = async () => {
    try {
      const _chems = await axios.get('/admin/seeds/breed/viewAll').then((res) => res.data.data);
      console.log('getBreed: ', _chems);
      gState.admin.seeds.breed.set(_chems);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getBreed: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addBreed = async (data) => {
    console.log('addBreed: ', data);

    try {
      const _data = await axios.post('/admin/seeds/breed/create', data).then((res) => res.data);
      console.log('addBreed: ', _data);
      await getBreed();

      toast.success('New Breed', {
        description: `${data.name} has been added`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addBreed: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateBreed = async (data) => {
    console.log('updateBreed: ', data);

    try {
      const _data = await axios.post('/admin/seeds/breed/update', data).then((res) => res.data);
      console.log('updateBreed: ', _data);
      await getBreed();

      toast.success('Update Breed', {
        description: `${data.name} updated successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateBreed: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    getSeeds,
    getSubcategory,
    addSeed,
    updateSeed,
    updateSubcategory,
    addSubcategory,
    getVariety,
    addVariety,
    updateVariety,
    getBreed,
    addBreed,
    updateBreed,
  };
}
