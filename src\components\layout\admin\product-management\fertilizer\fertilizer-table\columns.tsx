'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ColumnDef } from '@tanstack/react-table';
import { ChevronDown, Pencil, Plus, TextCursorInput, UploadCloud, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge, BadgeProps } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import { FertilizerTypeLabel } from '@/lib/constants/enums';
import useFertlizer, { FertilizerSchema, UpdateFertilizerType, UpdateFertlizerSchema } from '@/lib/hooks/useFertlizer';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import FertilizerCsv from '../FertilizerCsv';
import { IFertilizer } from './fertilizer.types';

export const columns: ColumnDef<IFertilizer>[] = [
  {
    id: 'fertilizer_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Fertilizer Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.name}</div>;
    },
    accessorFn: (row) => row.name,
  },
  {
    id: 'grade',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Grade" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.grade ?? 'N/A'}</div>;
    },
    accessorFn: (row) => row.grade ?? 'N/A',
  },
  {
    id: 'type',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Type" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.type !== null ? FertilizerTypeLabel[data.type] : 'N/A'}</div>;
    },
    accessorFn: (row) => (row.type !== null ? FertilizerTypeLabel[row.type] : 'N/A'),
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const statusMessage = ['Disabled', 'Active'];
      const variant = ['error', 'success'] as BadgeProps;

      return (
        <div className="min-w-max">
          <Badge variant={variant[data.status]}>{statusMessage[data.status]}</Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'updated_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Updated At" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) =>
      `${new Date(row.updated_at).toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })}`,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

export const ActionHeader = () => {
  const router = useRouter();
  const gState = useGlobalState();
  const { addFertilizer } = useFertlizer();
  const dialogInput = useHookstate(false);
  const dialogUpload = useHookstate(false);
  const dropdown = useHookstate(false);
  const loading = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(FertilizerSchema),
    defaultValues: {
      fertilizers: [
        {
          name: '',
          grade: '',
          type: '',
        },
      ],
    },
  });
  const { fields, append, remove } = useFieldArray({
    name: 'fertilizers',
    control,
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      await addFertilizer(data);

      dialogInput.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div className="flex items-center justify-end gap-2">
      <DropdownMenu onOpenChange={dropdown.set}>
        <DropdownMenuTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" size="sm">
            Add Fertilizer via
            <ChevronDown
              className={cn('ml-2 h-4 w-4 transition duration-300 ease-in-out', dropdown.value && 'rotate-180')}
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => dialogInput.set(true)}>
            <TextCursorInput className="mr-2 size-4" />
            Input
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              dialogUpload.set(true);
            }}
          >
            <UploadCloud className="mr-2 size-4" />
            Upload
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Add via input */}
      <Dialog open={dialogInput.value} onOpenChange={dialogInput.set}>
        <DialogContent className="p-0 sm:max-w-xl">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Fertilizer</DialogTitle>
            <DialogDescription>
              {`Add new fertilizer to your list. You can add multiple fertilizer at a time.`}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="">
            <div className="mt-3 grid gap-4 px-6 pb-6">
              {fields.map((field, index) => {
                const errorForField = errors?.fertilizers?.[index]?.name;
                const fieldType = errors?.fertilizers?.[index]?.type;
                const fieldGrade = errors?.fertilizers?.[index]?.grade;

                return (
                  <div key={field.id} className="flex items-start gap-3">
                    <div className="relative grid w-full items-center gap-1.5">
                      <Label htmlFor="name" className="pb-1 font-normal">
                        Fertilizer Name
                      </Label>

                      <Input
                        {...register(`fertilizers.${index}.name` as const)}
                        className={cn(
                          'focus-visible:ring-primary',
                          errorForField && 'border-red-500 focus-visible:ring-red-500',
                        )}
                        type="text"
                        placeholder="Enter fertilizer name"
                      />
                      {errorForField && <p className="form-error">{`${errorForField.message}`}</p>}
                    </div>

                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor={`fertilizers.${index}.type`} className="pb-1 font-normal">
                        Type
                      </Label>
                      <Controller
                        control={control}
                        name={`fertilizers.${index}.type` as const}
                        render={({ field: { onChange, onBlur, value, ref } }) => (
                          <Select onValueChange={onChange} value={value}>
                            <SelectTrigger
                              className={cn(
                                'focus-visible:ring-primary',
                                fieldType && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                              )}
                            >
                              <SelectValue placeholder="Select fertilizer type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                {FertilizerTypeLabel.map((label, index) => (
                                  <SelectItem key={index} value={`${index}`}>
                                    {label}
                                  </SelectItem>
                                ))}
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {fieldType && <p className="form-error">{`${fieldType['message']}`}</p>}
                    </div>

                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor={`fertilizers.${index}.grade`} className="pb-1 font-normal">
                        Grade
                      </Label>
                      <div className="flex items-center gap-4">
                        <Input
                          {...register(`fertilizers.${index}.grade` as const)}
                          className={cn(
                            'focus-visible:ring-primary',
                            fieldGrade && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          type="text"
                          placeholder="00-00-00"
                        />
                        <div className={cn(index === 0 && 'invisible')}>
                          <Button
                            className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                            variant="outline"
                            size="icon"
                            onClick={() => remove(index)}
                            type="button"
                          >
                            <X className="size-5" />
                          </Button>
                        </div>
                      </div>
                      {fieldGrade && <p className="form-error">{`${fieldGrade.message}`}</p>}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end px-6 pt-2">
              <Button
                variant="outline"
                size="icon"
                className="border-slate-300"
                type="button"
                onClick={() => append({ name: '', type: '', grade: '' })}
              >
                <Plus className="size-5 text-primary" />
              </Button>
            </div>

            <div className="flex justify-between gap-2 p-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              {loading.value ? (
                <ButtonLoading />
              ) : (
                <Button className="px-12" type="submit">
                  Add
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add via upload */}
      <Dialog open={dialogUpload.value} onOpenChange={dialogUpload.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-lg">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Fertilizer</DialogTitle>
            <DialogDescription>{`Upload CSV file to add fertilizer`}</DialogDescription>
          </DialogHeader>

          <FertilizerCsv />

          <div className="flex justify-between gap-2 p-6">
            <DialogClose asChild>
              <Button className="px-12" variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="px-12" type="button" disabled={gState.admin.fertilizer.upload.length === 0}>
                  Add
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will add new fertilizer to your list.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    type="button"
                    onClick={async () => {
                      const fertilizers = {
                        fertilizers: gState.admin.fertilizer.upload.value,
                      };
                      await addFertilizer(JSON.parse(JSON.stringify(fertilizers)));
                      dialogUpload.set(false);
                    }}
                  >
                    Continue
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Action = ({ row }) => {
  const data: IFertilizer = row.original;
  const updateDialog = useHookstate(false);
  const loading = useHookstate(false);
  const { updateFertilizer } = useFertlizer();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(UpdateFertlizerSchema),
    defaultValues: {
      fertilizerId: `${data.id}`,
      name: data.name,
      status: `${data.status}`,
      type: typeof data.type === 'number' ? (data.type.toString() as UpdateFertilizerType['type']) : '',
      grade: `${data.grade ?? ''}`,
    },
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      await updateFertilizer(data);

      updateDialog.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <>
      <div className="flex justify-end gap-2">
        <div className="flex justify-end gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="h-8 px-2 lg:px-3"
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log('TEST: ', data);
                  updateDialog.set(true);
                }}
              >
                <Pencil className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit Fertilizer</p>
            </TooltipContent>
          </Tooltip>

          {/* Update Crops Dialog */}
          <Dialog open={updateDialog.value} onOpenChange={updateDialog.set}>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-primary">Update Fertilizer</DialogTitle>
                <DialogDescription>{`Fill the form below to update fertilizer`}</DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="space-y-6">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="fertilizerId" className="pb-1 font-normal">
                      Fertilizer ID
                    </Label>
                    <Input
                      {...register('fertilizerId')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.fertilizerId && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter crop id"
                      disabled
                    />
                    {errors.fertilizerId && <p className="form-error">{`${errors.fertilizerId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="name" className="pb-1 font-normal">
                      Fertilizer Name
                    </Label>
                    <Input
                      {...register('name')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.name && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter fertilizer name"
                    />
                    {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="type" className="pb-1 font-normal">
                      Type
                    </Label>
                    <Controller
                      control={control}
                      name="type"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.type && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {FertilizerTypeLabel.map((label, index) => (
                                <SelectItem key={index} value={`${index}`}>
                                  {label}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.type && <p className="form-error">{`${errors.type.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="grade" className="pb-1 font-normal">
                      Grade
                    </Label>
                    <Input
                      {...register('grade')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.grade && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="00-00-00"
                    />
                    {errors.grade && <p className="form-error">{`${errors.grade.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="status" className="pb-1 font-normal">
                      Status
                    </Label>
                    <Controller
                      control={control}
                      name="status"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="0">Disabled</SelectItem>
                              <SelectItem value="1">Active</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
                  </div>
                </div>

                <div className="flex justify-between gap-2 pt-6">
                  <DialogClose asChild>
                    <Button className="px-12" variant="outline" type="button">
                      Cancel
                    </Button>
                  </DialogClose>
                  {loading.value ? (
                    <ButtonLoading />
                  ) : (
                    <Button className="px-12" type="submit">
                      Update
                    </Button>
                  )}
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  );
};
