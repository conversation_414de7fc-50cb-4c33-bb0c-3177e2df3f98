'use client';

import { format } from 'date-fns';
import { CalendarIcon, ChevronDown, ChevronUp, Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';
import { cn } from '@/lib/utils';

import { ICreateFarmPlan } from '../../types/create-farm-plan.types';

interface ILaborRequirementsProps {
  form: UseFormReturn<ICreateFarmPlan>;
}

export default function LaborRequirements({ form }: ILaborRequirementsProps) {
  const state = useHeadAgronomistState();

  // Collapsible state
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Get form methods from props
  const { control, watch, setValue } = form;

  // Field array for managing multiple items
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items.6.subItems', // Target the first item (Seed Requirements) in the items array
  });

  // Watch for changes to calculate totals
  const watchOtherLaborCosts = watch('items.6.subItems');

  // Calculate total amounts when quantity or unit cost changes
  useEffect(() => {
    if (!watchOtherLaborCosts) return;

    watchOtherLaborCosts.forEach((item: any, index: number) => {
      const quantity = Number(item.quantity) || 0;
      const unitCost = Number(item.unitCost) || 0;
      const totalAmount = quantity * unitCost;

      if (totalAmount !== item.totalAmount) {
        setValue(`items.6.subItems.${index}.totalAmount`, totalAmount);
      }
    });
  }, [watchOtherLaborCosts, setValue]);

  // Calculate subtotal
  const subtotal =
    watchOtherLaborCosts?.reduce((sum: number, item: any) => sum + (Number(item.totalAmount) || 0), 0) || 0;

  // Add a new row
  const addRow = () => {
    append({
      farmPlanSubItemId: 0,
      expectedDate: new Date(),
      itemName: '',
      unit: '',
      quantity: 1,
      unitCost: 0,
      notes: '',
      marketplaceProductId: undefined,
      totalAmount: 0, // For UI calculation only
    });
  };

  useEffect(() => {
    state.section2.group.laborReq.set(subtotal);
  }, [subtotal]);

  return (
    <div className="mt-6">
      <div className="rounded-md border border-[#2E3B7C]">
        <div
          className="flex cursor-pointer items-center justify-between bg-[#2E3B7C] px-3 py-2 text-white md:px-4"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          <h3 className="text-sm font-semibold md:text-base">LABOR: Other Labor Costs</h3>
          <div className="flex items-center gap-2">
            <span
              className={cn('rounded-md bg-orange-500 px-2 py-1 text-xs font-medium', {
                hidden: fields.length === 0,
              })}
            >
              {fields.length} items
            </span>
            {isCollapsed ? <ChevronDown className="size-4 md:size-5" /> : <ChevronUp className="size-4 md:size-5" />}
          </div>
        </div>

        {!isCollapsed && (
          <>
            <div className="pt-3">
              <HorizontalScrollBar>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[150px]">Expected Date</TableHead>
                      <TableHead className="w-[200px]">Activity/Items</TableHead>
                      <TableHead className="w-[100px]">Unit</TableHead>
                      <TableHead className="w-[100px]">Quantity</TableHead>
                      <TableHead className="w-[120px]">Unit Cost</TableHead>
                      <TableHead className="w-[120px]">Total Amount</TableHead>
                      <TableHead className="w-[200px]">Notes</TableHead>
                      {/* <TableHead className="w-[80px]">Actions</TableHead> */}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fields.map((field, index) => (
                      <TableRow key={field.id}>
                        {/* Expected Date */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.6.subItems.${index}.expectedDate`}
                            render={({ field }) => (
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      'w-full justify-start text-left font-normal text-xs md:text-sm',
                                      !field.value && 'text-muted-foreground',
                                    )}
                                  >
                                    <CalendarIcon className="mr-1 size-3 md:mr-2 md:size-4" />
                                    {field.value ? (
                                      format(new Date(field.value as string | Date), 'MMM d, yyyy')
                                    ) : (
                                      <span>Pick a date</span>
                                    )}
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <Calendar
                                    mode="single"
                                    selected={field.value ? new Date(field.value as string | Date) : undefined}
                                    onSelect={field.onChange}
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                            )}
                          />
                        </TableCell>

                        {/* Items */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.6.subItems.${index}.itemName`}
                            rules={{
                              required: 'Item is required',
                              validate: {
                                hasSelected: (v) => v.length > 0 || 'Item is required',
                              },
                            }}
                            render={({ field }) => (
                              <Input
                                className={cn('text-xs md:text-sm', {
                                  'input-error':
                                    form.formState.errors.items &&
                                    form.formState.errors.items[6]?.subItems[index]?.itemName,
                                })}
                                type="text"
                                value={field.value}
                                onChange={field.onChange}
                              />
                            )}
                          />
                          {form.formState.errors.items &&
                            form.formState.errors?.items[6]?.subItems[index]?.itemName && (
                              <p className="form-error text-xs md:text-sm">
                                {form.formState.errors.items[6]?.subItems[index]?.itemName.message}
                              </p>
                            )}
                        </TableCell>

                        {/* Unit */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.6.subItems.${index}.unit`}
                            rules={{
                              required: `Unit is required`,
                            }}
                            render={({ field }) => (
                              <Input
                                className={cn('text-xs md:text-sm', {
                                  'input-error':
                                    form.formState.errors.items &&
                                    form.formState.errors.items[6]?.subItems[index]?.unit,
                                })}
                                value={field.value}
                                onChange={field.onChange}
                              />
                            )}
                          />
                          {form.formState.errors.items && form.formState.errors.items[6]?.subItems[index]?.unit && (
                            <p className="form-error text-xs md:text-sm">
                              {form.formState.errors.items[6]?.subItems[index]?.unit.message}
                            </p>
                          )}
                        </TableCell>

                        {/* Quantity */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.6.subItems.${index}.quantity`}
                            rules={{
                              required: `Quantity is required`,
                              validate: {
                                isNumber: (v) => !isNaN(Number(v)) || 'Quantity must be a number',
                                isPositive: (v) => Number(v) >= 0 || 'Quantity must be a positive number',
                              },
                            }}
                            render={({ field }) => (
                              <Input
                                className={cn('text-xs md:text-sm', {
                                  'input-error':
                                    form.formState.errors.items &&
                                    form.formState.errors.items[6]?.subItems[index]?.quantity,
                                })}
                                value={field.value as number}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? 1 : Number(e.target.value);

                                  field.onChange(value);
                                  form.setValue(
                                    `items.6.subItems.${index}.totalAmount`,
                                    value * Number(form.getValues(`items.6.subItems.${index}.unitCost`)),
                                  );
                                }}
                                type="number"
                                placeholder="0"
                                min={1}
                              />
                            )}
                          />
                          {form.formState.errors.items && form.formState.errors.items[6]?.subItems[index]?.quantity && (
                            <p className="form-error text-xs md:text-sm">
                              {form.formState.errors.items[6]?.subItems[index]?.quantity.message}
                            </p>
                          )}
                        </TableCell>

                        {/* Unit Cost */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.6.subItems.${index}.unitCost`}
                            rules={{
                              required: `Unit cost is required`,
                              validate: {
                                isNumber: (v) => !isNaN(Number(v)) || 'Unit cost must be a number',
                                isPositive: (v) => Number(v) >= 0 || 'Unit cost must be a positive number',
                              },
                            }}
                            render={({ field }) => (
                              <Input
                                className={cn('text-xs md:text-sm', {
                                  'input-error':
                                    form.formState.errors.items &&
                                    form.formState.errors.items[6]?.subItems[index]?.unitCost,
                                })}
                                value={Number(field.value)}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? 0 : Number(e.target.value);
                                  if (isNaN(value)) return;

                                  form.setValue(
                                    `items.6.subItems.${index}.totalAmount`,
                                    value * Number(form.getValues(`items.6.subItems.${index}.quantity`)),
                                  );
                                  field.onChange(value);
                                }}
                                type="text"
                                placeholder="0"
                              />
                            )}
                          />
                          {form.formState.errors.items && form.formState.errors.items[6]?.subItems[index]?.unitCost && (
                            <p className="form-error text-xs md:text-sm">
                              {form.formState.errors.items[6]?.subItems[index]?.unitCost.message}
                            </p>
                          )}
                        </TableCell>

                        {/* Total Amount */}
                        <TableCell className="p-2 md:p-4">
                          <Input
                            value={watchOtherLaborCosts?.[index]?.totalAmount?.toLocaleString() || '0'}
                            disabled
                            className="w-full bg-gray-50 text-xs md:text-sm"
                          />
                        </TableCell>

                        {/* Notes */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.6.subItems.${index}.notes`}
                            render={({ field }) => (
                              <Input
                                value={field.value as string}
                                onChange={field.onChange}
                                placeholder="Note"
                                className="w-full text-xs md:text-sm"
                              />
                            )}
                          />
                        </TableCell>

                        {/* Actions */}
                        {/* <TableCell className="p-2 md:p-4">
                          <Button variant="ghost" size="icon" onClick={() => remove(index)} className="text-red-500">
                            <Trash2 className="size-3 md:size-4" />
                          </Button>
                        </TableCell> */}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </HorizontalScrollBar>
            </div>

            <div className="flex flex-col space-y-3 p-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:p-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-semibold md:text-base">Sub-Total</span>
                <span className="text-sm font-semibold text-blue-700 md:text-base">
                  PHP {subtotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              </div>
              {/* <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={addRow}
                className="flex items-center gap-1 text-xs text-blue-600 md:text-sm"
              >
                <Plus className="size-3 md:size-4" />
                Add Item
              </Button> */}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
