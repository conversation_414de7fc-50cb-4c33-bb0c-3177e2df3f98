'use client';

import { LogOut, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import useLogin from '@/lib/hooks/useLogin';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { urlify } from '@/lib/utils';

interface IUserProfileDropdownProps {}

export function UserProfileDropdown({}: IUserProfileDropdownProps) {
  const router = useRouter();

  const gStateP = useGlobalStatePersist();
  const { onLogout } = useLogin();

  const onSettings = async () => {
    router.push('/head-agronomist/settings');
  };

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'Q' && (e.metaKey || e.ctrlKey) && e.shiftKey) {
        e.preventDefault();
        onLogout();
      } else if (e.key === 's' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        onSettings();
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, [onLogout, onSettings]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative size-8 rounded-full">
          <Avatar className="size-8">
            <AvatarImage
              src={
                gStateP.user.value?.user?.user_img ? urlify(gStateP.user.value?.user?.user_img, 'users/profile') : null
              }
              alt={gStateP.user.value?.user?.email}
            />
            <AvatarFallback>{gStateP.user.value?.user?.email?.charAt(0).toUpperCase() || 'U'}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{gStateP.user.value?.user?.email}</p>
            <p className="text-xs leading-none text-muted-foreground">Head Agronomist</p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={onSettings}>
            <Settings className="mr-2 size-4" />
            <span>Settings</span>
            <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={onLogout}>
          <LogOut className="mr-2 size-4" />
          <span>Log out</span>
          <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
