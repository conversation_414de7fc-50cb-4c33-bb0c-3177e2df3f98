'use client';

import { useEffect } from 'react';

import useUsers from '@/lib/hooks/useUsers';
import { useGlobalState } from '@/lib/store';

import { UsersTable } from './users-table';
import { columns } from './users-table/columns';

export default function Users() {
  const gState = useGlobalState();
  const { getUsers } = useUsers();

  useEffect(() => {
    getUsers();
  }, []);

  console.log('users', gState.admin.users.data.get({ noproxy: true }));

  return (
    <div className="pt-4">
      <UsersTable
        columns={columns}
        data={gState.admin.users.data.get({ noproxy: true })}
        metadata={gState.admin.users['metadata'].get({ noproxy: true })}
      />
    </div>
  );
}
