'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { z } from 'zod';

import axios from '@/lib/api';

import { DEFAULT_ADMIN_ACTIVE_MENU } from '../constants';
import { useGlobalState } from '../store';
import { useGlobalStatePersist } from '../store/persist';

// Define a username validation function
export const usernameValidation = z
  .string()
  .min(3, 'Username must be at least 3 characters long')
  .regex(/^[a-zA-Z0-9_.]+$/, 'Username can only contain letters, numbers, underscores, and dots');

// Update the validation schema to allow both email and username
export const LoginSchema = z.object({
  email: z.union([z.string().email().min(1, 'Email is required'), usernameValidation]),
  password: z.string().min(8, 'Min. 8 characters'),
  keep_me_logged_in: z.boolean().optional(),
});
export type LoginType = z.infer<typeof LoginSchema>;

// Define user roles and their corresponding paths
export const roles = [
  'isSuperAdmin',
  'isAdmin',
  'isOperation1',
  'isOperation2',
  'isSale1',
  'isSale2',
  'isFinance1',
  'isFinance2',
  'isHeadAgronomist',
  'isAgronomist',
  'isFieldRelationOfficer',
];
export const paths = [
  '/admin',
  '/admin',
  '/operation',
  '/operation',
  '/sale',
  '/sale',
  '/finance',
  '/finance',
  '/head-agronomist',
  '/agronomist',
  '/field-relation-officer',
];
export const rolePaths = {
  isSuperAdmin: '/admin',
  isAdmin: '/admin',
  isOperation1: '/operation',
  isOperation2: '/operation',
  isSale1: '/sale',
  isSale2: '/sale',
  isFinance1: '/finance',
  isFinance2: '/finance',
  isHeadAgronomist: '/head-agronomist',
  isAgronomist: '/agronomist',
  isFieldRelationOfficer: '/field-relation-officer',
};

export default function useLogin() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const onLogin = async (data: LoginType) => {
    try {
      toast.loading('Logging in...', {
        description: 'Please wait...',
        duration: 10000,
      });

      // Set remember_me state if user opts to be remembered
      gStateP.keep_me_logged_in.set(data.keep_me_logged_in);

      // Set the user in the persisted global state to null initially
      gStateP['user'].set(null);

      // Make a POST request to the /login endpoint with the user's data
      const res = await axios.post('/login', data);
      const _res = res.data.data;
      console.log('onLogin: ', _res);

      const isAllFalse = roles.every((role) => !_res[role]);
      if (isAllFalse) {
        throw new Error('User is not authorized');
      }

      // Store the token returned from the server in local storage
      localStorage.setItem('token', _res.token);

      // If the user chose to be remembered, persist the user's data
      if (data.keep_me_logged_in) {
        gStateP.cred.set({
          email: data.email,
          password: data.password,
        });
      }

      // Set the user in the global state
      gStateP['user'].set(_res);

      // Redirect the user to a path that matches their role
      for (let i = 0; i < roles.length; i++) {
        if (_res[roles[i]]) {
          router.push(paths[i]);
          break;
        }
      }

      // Display a success message upon successful authentication
      toast.dismiss();
      toast.success('Authentication Success!', {
        description: 'You have successfully logged in.',
      });
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;

      // Display an error message to the user
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const onLogout = () => {
    gStateP['user'].set(null);
    gStateP.admin.activeMenu.set(DEFAULT_ADMIN_ACTIVE_MENU);

    localStorage.clear();
    router.replace('/');
  };

  return { onLogin, onLogout };
}
