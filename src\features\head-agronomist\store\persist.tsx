'use client';

import { extend, hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import merge from 'lodash.merge';

import { localstored } from '@/lib/store/plugins/localStored';

const initialState = {
  activeMenu: 1,
};

export const headAgronomistStatePersist = hookstate(
  initialState,
  extend(
    localstored({
      key: 'headAgronomistStatePersist',
      onRestored: (s) => {
        const restored = s.get({ noproxy: true });

        if (s.value) {
          const synced = merge({}, initialState, restored);

          console.log('restored state: ', synced);
          s.set(synced);
        } else {
          console.log('restored state: localstorage is empty');
        }
      },
    }),
    devtools({ key: 'headAgronomistStatePersist' }),
  ),
);

export const useHeadAgronomistStatePersist = () => useHookstate(headAgronomistStatePersist);
