'use client';

import { format } from 'date-fns';
import { CalendarIcon, ChevronDown, ChevronUp, Plus, Trash2 } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';

import { LoadingButton } from '@/components/common/loading/loading-button';
import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';
import { useProducts } from '@/features/head-agronomist/store/products.store';
import { EProducttype, IProductData } from '@/features/head-agronomist/types/product.types';
import { cn } from '@/lib/utils';

import { ICreateFarmPlan } from '../../types/create-farm-plan.types';

interface IOtherFarmMaterialsProps {
  form: UseFormReturn<ICreateFarmPlan>;
}

export default function OtherFarmMaterials({ form }: IOtherFarmMaterialsProps) {
  const state = useHeadAgronomistState();

  const { productsQuery: seeds } = useProducts(EProducttype.OTHERS);
  const selectedItem = useRef(new Map<number, IProductData>());

  // Collapsible state
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Get form methods from props
  const { control, watch, setValue } = form;

  // Field array for managing multiple items
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items.5.subItems', // Target the first item (Seed Requirements) in the items array
  });

  // Watch for changes to calculate totals
  const watchOtherFarmMaterials = watch('items.5.subItems');

  // Calculate total amounts when quantity or unit cost changes
  useEffect(() => {
    if (!watchOtherFarmMaterials) return;

    watchOtherFarmMaterials.forEach((item: any, index: number) => {
      const quantity = Number(item.quantity) || 0;
      const unitCost = Number(item.unitCost) || 0;
      const totalAmount = quantity * unitCost;

      if (totalAmount !== item.totalAmount) {
        setValue(`items.5.subItems.${index}.totalAmount`, totalAmount);
      }
    });
  }, [watchOtherFarmMaterials, setValue]);

  // Calculate subtotal
  const subtotal =
    watchOtherFarmMaterials?.reduce((sum: number, item: any) => sum + (Number(item.totalAmount) || 0), 0) || 0;

  // Add a new row
  const addRow = () => {
    append({
      farmPlanSubItemId: 0,
      expectedDate: new Date(),
      itemName: '',
      unit: '',
      quantity: 1,
      unitCost: 0,
      notes: '',
      marketplaceProductId: undefined,
      totalAmount: 0, // For UI calculation only
    });
  };

  useEffect(() => {
    state.section1.group.othersFarmMaterials.set(subtotal);
  }, [subtotal]);

  return (
    <div className="mt-6">
      <div className="rounded-md border border-[#2E3B7C]">
        <div
          className="flex cursor-pointer items-center justify-between bg-[#2E3B7C] px-3 py-2 text-white md:px-4"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          <h3 className="text-sm font-semibold md:text-base">OTHERS: Farm Materials, Consumables, etc.</h3>
          <div className="flex items-center gap-2">
            <span
              className={cn('rounded-md min-w-max bg-orange-500 px-2 py-1 text-xs font-medium', {
                hidden: fields.length === 0,
              })}
            >
              {fields.length} items
            </span>
            {isCollapsed ? <ChevronDown className="size-4 md:size-5" /> : <ChevronUp className="size-4 md:size-5" />}
          </div>
        </div>

        {!isCollapsed && (
          <>
            <div className="pt-3">
              <HorizontalScrollBar>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[150px]">Expected Date</TableHead>
                      <TableHead className="w-[200px]">Items</TableHead>
                      <TableHead className="w-[100px]">Unit</TableHead>
                      <TableHead className="w-[100px]">Quantity</TableHead>
                      <TableHead className="w-[120px]">Unit Cost</TableHead>
                      <TableHead className="w-[120px]">Total Amount</TableHead>
                      <TableHead className="w-[200px]">Notes</TableHead>
                      {/* <TableHead className="w-[80px]">Actions</TableHead> */}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fields.map((field, index) => (
                      <TableRow key={field.id}>
                        {/* Expected Date */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.5.subItems.${index}.expectedDate`}
                            render={({ field }) => (
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      'w-full justify-start text-left font-normal text-xs md:text-sm',
                                      !field.value && 'text-muted-foreground',
                                    )}
                                  >
                                    <CalendarIcon className="mr-1 size-3 md:mr-2 md:size-4" />
                                    {field.value ? (
                                      format(new Date(field.value as string | Date), 'MMM d, yyyy')
                                    ) : (
                                      <span>Pick a date</span>
                                    )}
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <Calendar
                                    mode="single"
                                    selected={field.value ? new Date(field.value as string | Date) : undefined}
                                    onSelect={field.onChange}
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                            )}
                          />
                        </TableCell>

                        {/* Items */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.5.subItems.${index}.itemName`}
                            rules={{
                              required: 'Item is required',
                              validate: {
                                hasSelected: (v) => v.length > 0 || 'Item is required',
                              },
                            }}
                            render={({ field }) => (
                              <Select
                                onValueChange={(v) => {
                                  field.onChange(v);

                                  const selected = seeds.data.pages.reduce((foundItem, page) => {
                                    if (foundItem) return foundItem;
                                    return page.data.find((item) => item.otherProduct.name === v);
                                  }, undefined) as IProductData;
                                  console.log('SELECTED: ', selected);
                                  selectedItem.current.set(index, selected);

                                  if (selected) {
                                    setValue(`items.5.subItems.${index}.unit`, selected.unit);
                                    setValue(`items.5.subItems.${index}.unitCost`, selected.price);
                                    setValue(`items.5.subItems.${index}.marketplaceProductId`, selected.id);

                                    setValue(`items.5.subItems.${index}.quantity`, 1);
                                    setValue(`items.5.subItems.${index}.totalAmount`, 1 * Number(selected.price));
                                  }
                                }}
                                value={field.value as string}
                              >
                                <SelectTrigger
                                  className={cn('w-full text-xs md:text-sm', {
                                    'input-error':
                                      form.formState.errors.items &&
                                      form.formState.errors.items[5]?.subItems[index]?.itemName,
                                  })}
                                >
                                  <SelectValue placeholder="Select Product" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectGroup>
                                    {seeds.isSuccess &&
                                      seeds.data.pages?.map((page) =>
                                        page.data.map((item) => (
                                          <SelectItem
                                            key={item.id}
                                            value={item.otherProduct.name}
                                            disabled={item.stocks === 0}
                                          >
                                            <div
                                              className={cn('flex items-center justify-between gap-1', {
                                                'text-red-500 font-semibold': item.stocks === 0,
                                              })}
                                            >
                                              <img
                                                className="mr-1 size-5 rounded-full md:size-6"
                                                src={item.image}
                                                alt=""
                                              />
                                              <div className="line-clamp-1 text-xs md:text-sm">
                                                {item.otherProduct.name}
                                              </div>
                                              <div className="text-xs md:text-sm">({item.stocks})</div>
                                            </div>
                                          </SelectItem>
                                        )),
                                      )}
                                    {seeds.hasNextPage && (
                                      <div>
                                        {seeds.isFetching || seeds.isFetchingNextPage ? (
                                          <LoadingButton variant="ghost" size="sm" className="h-8 w-full text-xs">
                                            Loading
                                          </LoadingButton>
                                        ) : (
                                          <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-full text-xs"
                                            onClick={() => seeds.fetchNextPage()}
                                          >
                                            Load More
                                          </Button>
                                        )}
                                      </div>
                                    )}
                                  </SelectGroup>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          {form.formState.errors.items && form.formState.errors.items[5]?.subItems[index]?.itemName && (
                            <p className="form-error text-xs md:text-sm">
                              {form.formState.errors.items[5]?.subItems[index]?.itemName.message}
                            </p>
                          )}
                        </TableCell>

                        {/* Unit */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.5.subItems.${index}.unit`}
                            render={({ field }) => (
                              <Input
                                value={field.value}
                                onChange={field.onChange}
                                disabled
                                className="text-xs md:text-sm"
                              />
                            )}
                          />
                        </TableCell>

                        {/* Quantity */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.5.subItems.${index}.quantity`}
                            render={({ field }) => (
                              <Input
                                value={field.value as number}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? 1 : Number(e.target.value);
                                  const max = selectedItem.current.get(index).stocks;

                                  if (value > max) return;

                                  field.onChange(value);
                                  form.setValue(
                                    `items.5.subItems.${index}.totalAmount`,
                                    value * Number(form.getValues(`items.5.subItems.${index}.unitCost`)),
                                  );
                                }}
                                type="number"
                                placeholder="0"
                                className="w-full text-xs md:text-sm"
                                min={1}
                              />
                            )}
                          />
                        </TableCell>

                        {/* Unit Cost */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.5.subItems.${index}.unitCost`}
                            render={({ field }) => (
                              <Input
                                value={Number(field.value).toLocaleString()}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? 0 : Number(e.target.value);
                                  field.onChange(value);
                                }}
                                disabled
                                type="text"
                                placeholder="0"
                                className="w-full text-xs md:text-sm"
                              />
                            )}
                          />
                        </TableCell>

                        {/* Total Amount */}
                        <TableCell className="p-2 md:p-4">
                          <Input
                            value={watchOtherFarmMaterials?.[index]?.totalAmount?.toLocaleString() || '0'}
                            disabled
                            className="w-full bg-gray-50 text-xs md:text-sm"
                          />
                        </TableCell>

                        {/* Notes */}
                        <TableCell className="p-2 md:p-4">
                          <Controller
                            control={control}
                            name={`items.5.subItems.${index}.notes`}
                            render={({ field }) => (
                              <Input
                                value={field.value as string}
                                onChange={field.onChange}
                                placeholder="Note"
                                className="w-full text-xs md:text-sm"
                              />
                            )}
                          />
                        </TableCell>

                        {/* Actions */}
                        {/* <TableCell className="p-2 md:p-4">
                          <Button variant="ghost" size="icon" onClick={() => remove(index)} className="text-red-500">
                            <Trash2 className="size-3 md:size-4" />
                          </Button>
                        </TableCell> */}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </HorizontalScrollBar>
            </div>

            <div className="flex flex-col space-y-3 p-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:p-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-semibold md:text-base">Sub-Total</span>
                <span className="text-sm font-semibold text-blue-700 md:text-base">
                  PHP {subtotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              </div>
              {/* <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={addRow}
                className="flex items-center gap-1 text-xs text-blue-600 md:text-sm"
              >
                <Plus className="size-3 md:size-4" />
                Add Item
              </Button> */}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
