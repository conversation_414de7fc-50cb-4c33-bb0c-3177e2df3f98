'use client';

import { useHookstate } from '@hookstate/core';
import { CheckIcon, XIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import useFinance2 from '@/lib/hooks/finance2/useFinance2';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { topDownState, useTopDownStore } from '@/lib/store/top-down-store';
import { cn } from '@/lib/utils';

export function TopdownActionDialog({ action = '' as 'approve' | 'reject', ...props }) {
  // use this if you want to control the alert dialog programatically
  const [open, setOpen] = useState(false);
  const { topdownAction } = useTopDownStore('finance');
  const loading = useHookstate(false);
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      remarks: '',
    },
  });

  const onSubmit = async (data: any) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        topdownRequestId: topDownState.reqDetails['id'].value,
      };
      console.log('onSubmit: ', updatedData);

      await topdownAction(action, updatedData);

      setOpen(false);
      router.push('/finance');
    } catch (error) {
      console.error(error);
    } finally {
      loading.set(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {action === 'approve' ? (
          <Button {...props} variant="success" disabled={loading.value}>
            <CheckIcon className="mr-2 size-4" />
            Approve
          </Button>
        ) : (
          <Button {...props} variant="destructive" disabled={loading.value}>
            <XIcon className="mr-2 size-4" />
            Reject
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="font-sans sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Confirmation</DialogTitle>
          <DialogDescription>{`Do you confirm to ${action} this request?`}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          {action === 'reject' && (
            <div className="grid gap-4 pb-6 pt-2">
              <div className="grid gap-2">
                <Label htmlFor="remarks">Remarks</Label>
                <Textarea
                  {...register('remarks', {
                    required: 'Remarks is required',
                  })}
                  className={cn(errors.remarks && 'border-red-500 focus-visible:ring-red-500')}
                  placeholder={`Enter the reason why you want to ${action} this request.`}
                />
                {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
              </div>
            </div>
          )}

          <DialogFooter className="gap-3 sm:justify-end sm:gap-0">
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Close
              </Button>
            </DialogClose>
            {loading.value ? <ButtonLoading /> : <Button type="submit">Submit</Button>}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
