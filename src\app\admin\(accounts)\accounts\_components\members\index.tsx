'use client';

import { useEffect } from 'react';

import useTradingPost from '@/lib/hooks/admin/useTradingPost';
import { useGlobalState } from '@/lib/store';

import { MembersTable } from './members-table';
import { columns } from './members-table/columns';

export default function MembersPage() {
  const gState = useGlobalState();
  const { fetchMembers } = useTradingPost();

  useEffect(() => {
    fetchMembers();
  }, []);

  return (
    <div className="p-6 lg:p-8">
      <MembersTable columns={columns} data={gState.admin.members.data.get({ noproxy: true })} />
    </div>
  );
}
