'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

import { PRODUCT_TYPE, PRODUCT_TYPE_ACCESSOR } from '@/app/admin/marketplace/_components/Enums';
import { capitalize } from '@/lib/utils';

export const columns = [
  {
    id: 'price',
    accessorFn: (row) => `${row.price}`,
  },
  {
    id: 'name',
    accessorFn: (row) => {
      const accessor = PRODUCT_TYPE_ACCESSOR[Number(row.product_type)];
      const product = row[accessor];
      const weight = row.weight || 0;
      const unit = row.unit || '';

      return `${product.name} - ${weight} ${unit}`;
    },
  },
  {
    id: 'type',
    accessorFn: (row) => {
      const accessor = PRODUCT_TYPE_ACCESSOR[Number(row.product_type)];
      const product = row[accessor];

      const isSeed = Number(row.product_type) === PRODUCT_TYPE.SEEDS;
      const isChemical = Number(row.product_type) === PRODUCT_TYPE.CHEMICALS;
      const hasSub = isSeed || isChemical;

      return `${accessor}${
        hasSub
          ? `, ${isSeed ? (product.seedSubcategory ? product.seedSubcategory.name : 'N/A') : product.chemicalSubcategory ? product.chemicalSubcategory.name : 'N/A'}`
          : ''
      }`;
    },
  },
  {
    id: 'stocks',
    accessorFn: (row) => {
      return `${row.stocks}`;
    },
  },
];
