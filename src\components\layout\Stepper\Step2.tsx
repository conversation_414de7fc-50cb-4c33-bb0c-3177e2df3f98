'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Plus, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { STEPPER_FORM } from '@/lib/constants';
import useFarmer, { FarmerSchema } from '@/lib/hooks/useFarmer';
import { UserSchema } from '@/lib/hooks/useUsers';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';
import { useStateContext } from '@/providers/app/state';

import { GovernmentIdentificationEnum } from './Enums';

export default function Step2() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { updateFarmer } = useFarmer();
  const { setState, state } = useStateContext();
  const data = gStateP.admin.members['details'].get({ noproxy: true });

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      password: '',
      password_confirmation: '',
      userImage: null,
      governmentIdentification: data.farmer.governmentIdentifications.map((item) => ({
        governmentIdType: item.government_id_type,
        governmentIdNumber: item.government_id_number,
        upload: null,
      })),
    },
  });
  const governId = useFieldArray({
    name: 'governmentIdentification',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      governmentIdentification: _data.governmentIdentification.map((item) => ({
        governmentIdNumber: item.governmentIdNumber,
        governmentIdType: item.governmentIdType,
      })),
      userImage: _data.userImage.length > 0 ? _data.userImage[0] : null,
      userId: data.farmer.user_id,
    };

    _data.governmentIdentification.map((item) => {
      if (item.upload) {
        updatedData = {
          ...updatedData,
          [`governmentIdentification_${item.governmentIdNumber}`]: item.upload[0],
        };
      }
    });

    console.log('Identification Docs: ', updatedData);
    updateFarmer(updatedData);
    // setState((v) => ({
    //   ...v,
    //   ..._data,
    //   userId: data.id,
    // }));
    // !gState.stepper.isLastStep.value && gState.stepper.activeStep.set((cur) => cur + 1);
  };

  return (
    <form id={STEPPER_FORM[2]} onSubmit={handleSubmit(onSubmit)}>
      <div className="font-dmSans text-xl font-bold text-primary">Educational Background</div>
      <div className="mt-6 space-y-4">
        {governId.fields.map((field, index) => {
          const errorForField = errors?.governmentIdentification?.[index];

          return (
            <div key={field.id} className="grid grid-cols-3 gap-4">
              {/* ID Type */}
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor={`governmentIdentification.${index}.governmentIdType`} className="pb-1 font-normal">
                  Government Identification Type
                  <span className="ml-1 font-bold text-red-500">*</span>
                </Label>
                <Controller
                  control={control}
                  name={`governmentIdentification.${index}.governmentIdType` as const}
                  rules={{ required: 'Government ID Type is required' }}
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <Select onValueChange={onChange} value={value}>
                      <SelectTrigger
                        className={cn(
                          'focus-visible:ring-primary',
                          errorForField?.governmentIdType &&
                            'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                        )}
                      >
                        <SelectValue placeholder="Select ID Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {Object.values(GovernmentIdentificationEnum).map((idType) => (
                            <SelectItem key={idType} value={idType}>
                              {idType}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errorForField?.governmentIdType && (
                  <p className="form-error">{`${errorForField?.governmentIdType?.message}`}</p>
                )}
              </div>

              {/* ID Number */}
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor={`governmentIdentification.${index}.governmentIdNumber`} className="pb-1 font-normal">
                  Government ID Number
                  <span className="ml-1 font-bold text-red-500">*</span>
                </Label>
                <Input
                  {...register(`governmentIdentification.${index}.governmentIdNumber` as const, {
                    required: 'Government ID Number is required',
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errorForField?.governmentIdNumber && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter ID Number"
                />
                {errorForField?.governmentIdNumber && (
                  <p className="form-error">{`${errorForField?.governmentIdNumber?.message}`}</p>
                )}
              </div>

              {/* Upload ID */}
              <div className="grid w-full max-w-sm items-center gap-1.5">
                <Label htmlFor={`governmentIdentification.${index}.upload`} className="pb-1 font-normal">
                  Upload ID Image
                  <span className="ml-1 font-bold text-red-500">*</span>
                </Label>
                <div className="flex items-center gap-4">
                  <Input
                    {...register(`governmentIdentification.${index}.upload` as const, {
                      required: 'Upload ID image is required',
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.governmentIdNumber && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="file"
                    placeholder="Upload ID"
                  />
                  <div className={cn(index === 0 && 'invisible')}>
                    <Button
                      className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                      variant="outline"
                      size="icon"
                      onClick={() => governId.remove(index)}
                    >
                      <X className="size-5" />
                    </Button>
                  </div>
                </div>
                {errorForField?.upload && <p className="form-error">{`${errorForField?.upload?.message}`}</p>}
              </div>
            </div>
          );
        })}

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              governId.append({
                governmentIdType: '',
                governmentIdNumber: '',
                upload: null,
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More ID info</span>
          </Button>
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Account Settings</div>
      <div className="mt-6 grid grid-cols-3 gap-4">
        {/* Password */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="password" className="pb-1 font-normal">
            <span className="">Password</span>
          </Label>
          <InputPassword
            {...register('password', {
              required: false,
              validate: {
                min: (value) => (value ? `${value}`.length >= 8 || `Min. 8 characters` : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.password && 'border-red-500 focus-visible:ring-red-500')}
            placeholder="Min. 8 characters"
          />
          {errors.password && <p className="form-error">{`${errors.password.message}`}</p>}
        </div>

        {/* Confirm Password */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="password_confirmation" className="pb-1 font-normal">
            Re-Type Password
          </Label>
          <InputPassword
            {...register('password_confirmation', {
              required: false,
              validate: {
                isMatch: (value) => (value ? value === watch('password') || `Passwords don't match` : true),
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.password_confirmation && 'border-red-500 focus-visible:ring-red-500',
            )}
            placeholder="Min. 8 characters"
          />
          {errors.password_confirmation && <p className="form-error">{`${errors.password_confirmation.message}`}</p>}
        </div>

        {/* Profile Picture */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="userImage" className="pb-1 font-normal">
            Profile Picture
          </Label>
          <Input
            type="file"
            {...register('userImage')}
            className={cn(
              'focus-visible:ring-primary',
              errors.userImage && 'border-red-500 focus-visible:ring-red-500',
            )}
            placeholder="Upload Profile Picture"
          />
          {errors.userImage && <p className="form-error">{`${errors.userImage.message}`}</p>}
        </div>
      </div>
    </form>
  );
}
