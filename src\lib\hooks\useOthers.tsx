'use client';

import { useHookstate } from '@hookstate/core';
import { toast } from 'sonner';
import { z } from 'zod';

import axios from '@/lib/api';

import { useGlobalState } from '../store';

export const OtherProductSchema = z.object({
  otherProducts: z.array(
    z.object({
      name: z.string().min(2, 'Product name is required'),
      brand: z.string().min(2, 'Brand is required'),
    }),
  ),
});
export type OtherProductType = z.infer<typeof OtherProductSchema>;

export const UpdateOtherProductSchema = z.object({
  otherProductId: z.string().min(1, 'Product ID is required'),
  name: z.string().min(2, 'Product name is required'),
  brand: z.string().min(2, 'Product brand is required'),
  status: z.string().min(1, 'Status is required'),
});
export type UpdateCropType = z.infer<typeof UpdateOtherProductSchema>;

export default function useOtherProduct() {
  const gState = useGlobalState();

  const otherProducts = useHookstate(gState.admin.otherProduct.data);

  const getOtherProduct = async () => {
    try {
      const _crops = await axios.get('/admin/otherproducts/viewAll').then((res) => res.data.data);
      console.log('getOthers: ', _crops);
      gState.admin.otherProduct.data.set(_crops);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getOthers: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addOtherProduct = async (data: OtherProductType) => {
    console.log('addOtherProduct: ', data);

    try {
      const _data = await axios.post('/admin/otherproducts/create', data).then((res) => res.data);
      console.log('addOtherProduct: ', _data);
      await getOtherProduct();

      toast.success('Sucess', {
        description: 'Product added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addOtherProduct: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateOtherProduct = async (data) => {
    console.log('updateOtherProduct: ', data);

    try {
      const _data = await axios.post('/admin/otherproducts/update', data).then((res) => res.data);
      console.log('updateOtherProduct: ', _data);
      await getOtherProduct();

      toast.success('Success', {
        description: 'Product updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateOtherProduct: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { otherProducts, getOtherProduct, addOtherProduct, updateOtherProduct };
}
