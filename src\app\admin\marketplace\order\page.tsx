'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { useGlobalStatePersist } from '@/lib/store/persist';

import { TAB_MARKETPLACE } from '../../layout';

export default function OrderPage() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();

  useEffect(() => {
    gStateP.tabsMarketplace.set(TAB_MARKETPLACE.ORDERS);
    router.push('/admin/marketplace/');
  }, []);

  return <div>Loading...</div>;
}
