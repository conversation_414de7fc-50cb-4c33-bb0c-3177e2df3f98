'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import { useGroupPriceStore } from '@/lib/store/admin/trading-app/group-price-store';
import { AddGroupSchema, AddGroupType } from '@/lib/store/admin/trading-app/group-price-store.types';
import { cn } from '@/lib/utils';

interface IAddGroupDialogProps {
  userType: UserType.NONFARMER | UserType.FARMER;
}

export default function AddGroupDialog({ userType }: IAddGroupDialogProps) {
  const createDialog = useHookstate(false);
  const { addGroup, state } = useGroupPriceStore();
  const loading = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    watch,
    setValue,
    setError,
    reset,
    trigger,
  } = useForm<Omit<AddGroupType, 'userType'>>({
    defaultValues: {
      name: '',
      remarks: '',
      percentageRate: 0,
      logisticRate: 0,
    },
  });

  const onSubmit = async (data: AddGroupType) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        userType,
      };
      await addGroup(updatedData);
      createDialog.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div>
      <Dialog
        open={createDialog.value}
        onOpenChange={(v) => {
          createDialog.set(v);
          if (v === false) reset();
        }}
      >
        <DialogTrigger asChild>
          <Button className="h-8 px-6">Add Group</Button>
        </DialogTrigger>

        <DialogContent className="">
          <DialogHeader>
            <DialogTitle className="text-primary">Add Group</DialogTitle>
            <DialogDescription>{`Fill up the forms to add a group. Click "Add" when you're ready.`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="mt-3 grid grid-cols-1 gap-4">
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="name" className="pb-1 font-normal">
                  Group Name
                </Label>
                <Input
                  {...register('name', {
                    required: 'Please fill in all required fields before submitting.',
                    validate: {
                      existingGroup: (value) => {
                        const groupAlradyExist = state[userType].data.data.value.find(
                          (item) => item.name.toLowerCase().trim() === value.toLowerCase().trim(),
                        );
                        if (groupAlradyExist) {
                          return 'Group name already taken. Please choose a different name.';
                        }
                        return true;
                      },
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.name && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter group name"
                />
                {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="percentageRate" className="pb-1 font-normal">
                  Percentage Rate (%)
                </Label>
                <Input
                  {...register('percentageRate', {
                    valueAsNumber: true,
                    required: 'Please fill in all required fields before submitting.',
                    validate: {
                      greaterThanZero: (v) => v >= 0 || 'Please enter a valid percentage rate (numeric values only).',
                      lessThan100: (v) => v <= 100 || 'Please enter a valid percentage rate (numeric values only).',
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.percentageRate && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="number"
                  placeholder="e.g 12"
                  step={0.01}
                />
                {errors.percentageRate && <p className="form-error">{`${errors.percentageRate.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="logisticRate" className="pb-1 font-normal">
                  Logistic Rate Per Kilo (₱)
                </Label>
                <Input
                  {...register('logisticRate', {
                    valueAsNumber: true,
                    required: 'Please fill in all required fields before submitting.',
                    validate: {
                      greaterThanZero: (v) => v >= 0 || 'Please enter a valid logistic rate (numeric values only).',
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.logisticRate && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="number"
                  placeholder="e.g 2"
                  step={0.01}
                />
                {errors.logisticRate && <p className="form-error">{`${errors.logisticRate.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="remarks" className="pb-1 font-normal">
                  Remarks
                </Label>
                <Textarea
                  {...register('remarks', {
                    required: 'Please fill in all required fields before submitting.',
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.remarks && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  placeholder="Enter Remarks"
                />
                {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
              </div>
            </div>

            <div className="flex justify-between gap-2 pt-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              {loading.value ? (
                <ButtonLoading text="Adding" />
              ) : (
                <Button className="px-12" type="submit">
                  Add
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
