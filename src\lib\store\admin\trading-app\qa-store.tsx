'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { catchError } from '@/lib/utils';

import { EditQaStaffType, IQa, QaStaffType } from './qa-store.types';

const initialState = {
  data: [] as IQa[],
};

export const qaState = hookstate(
  initialState,
  devtools({
    key: 'qaState',
  }),
);

export const useQaStore = (currentUser = 'admin') => {
  const state = useHookstate(qaState);

  const getStaff = async () => {
    try {
      const res: IQa[] = await axios.get(`/${currentUser}/tradingapp/staff/viewAll`).then((res) => res.data.data);
      state.data.set(res);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getStaff:', error);
    }
  };

  const updateStaff = async (data: EditQaStaffType & { userId: number | string }) => {
    try {
      toast.loading('Updating staff...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/staff/update`, data);
      await getStaff();

      toast.dismiss();
      toast.success('Success', {
        description: 'Staff updated successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'updateStaff');
    }
  };

  const createStaff = async (data: QaStaffType) => {
    try {
      toast.loading('Creating staff...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/staff/create`, data);
      await getStaff();

      toast.dismiss();
      toast.success('Success', {
        description: 'Staff created successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'createStaff');
    }
  };

  const toggleStaff = async (userId: string | number, status: 'deactivate' | 'activate') => {
    try {
      toast.loading(`Updating ${status} staff...`, {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post(`/${currentUser}/tradingapp/staff/${status}`, {
          userId,
        })
        .then((res) => res.data);

      console.log(`${status}Staff: `, _data);
      await getStaff();

      toast.dismiss();
      toast.success('Success', {
        description: _data.message,
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, `${status}Staff`);
    }
  };

  return { state, getStaff, updateStaff, createStaff, toggleStaff };
};
