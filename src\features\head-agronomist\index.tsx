'use client';

import { FarmPlanTemplatesTable } from './components/farm-plan-templates-table';
import { columns } from './components/farm-plan-templates-table/columns';
import { useFarmPlanTemplate } from './hooks/useFarmPlanTemplates';

export default function HeadAgronomist() {
  const { farmPlanTemplatesQuery } = useFarmPlanTemplate();

  return (
    <div className="p-6">
      <div className="">
        <FarmPlanTemplatesTable
          columns={columns}
          data={farmPlanTemplatesQuery.isSuccess ? farmPlanTemplatesQuery.data?.data?.data : []}
          metadata={farmPlanTemplatesQuery.data?.data?.meta}
          isLoading={farmPlanTemplatesQuery.isFetching}
        />
      </div>
    </div>
  );
}
