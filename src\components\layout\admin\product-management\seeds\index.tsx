'use client';

import { useEffect } from 'react';

import useSeeds from '@/lib/hooks/useSeeds';
import { useGlobalState } from '@/lib/store';

import useSeedsTab from './hooks/useSeedsTab';
import { SeedTable } from './seed-table';

export default function SeedPage() {
  const gState = useGlobalState();
  const { getSeeds, getSubcategory, getVariety, getBreed } = useSeeds();
  const seedsTab = useSeedsTab();

  useEffect(() => {
    Promise.all([getSeeds(), getSubcategory(), getVariety(), getBreed()]);
  }, []);

  return (
    <div className="px-6 py-8" key={JSON.stringify(seedsTab[gState.admin.seeds.tab.value].data)}>
      <SeedTable
        columns={seedsTab[gState.admin.seeds.tab.value].columns}
        data={seedsTab[gState.admin.seeds.tab.value].data}
      />
    </div>
  );
}
