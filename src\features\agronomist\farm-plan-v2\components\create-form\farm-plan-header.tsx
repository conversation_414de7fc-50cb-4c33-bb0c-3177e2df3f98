'use client';

import { useRouter } from 'next/navigation';

import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

/**
 * FarmPlanHeader component displays the page heading and breadcrumb navigation
 * for the Create Farm Plan page.
 */
export default function FarmPlanHeader() {
  const router = useRouter();

  return (
    <div>
      <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Create Farm Plan</h1>
      <Breadcrumb className="mt-2">
        <BreadcrumbList>
          <BreadcrumbItem className="cursor-pointer">
            <BreadcrumbLink onClick={() => router.back()}>Farm Plan</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Create Farm Plan</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
