'use client';

import { IconJarLogoIcon } from '@radix-ui/react-icons';
import { MapPinHouse, PiggyBankIcon, User } from 'lucide-react';
import React, { useEffect } from 'react';
import { MdPassword } from 'react-icons/md';

import Stepper from '@/components/ui/stepper';

import useFarmer from '@/lib/hooks/fro/useFarmer';
import { useGlobalState } from '@/lib/store';

import DataPrivacy from '../_components/Step1/DataPrivacy';
import PersonalInfo from '../_components/Step2/PersonalInfo';
import FarmInformation from '../_components/Step3/FarmInformation';
import BusinessInfo from '../_components/Step4/BusinessInfo';

const steps = [
  { number: 1, label: 'Consent & Data Privacy', icon: <MdPassword /> },
  { number: 2, label: 'Personal Information', icon: <User /> },
  { number: 3, label: 'Farm Information', icon: <MapPinHouse /> },
  { number: 4, label: 'Financial & Business Info', icon: <PiggyBankIcon /> },
];

export default function KYC() {
  const { fro: gStateFRO } = useGlobalState();
  const { addFarmer } = useFarmer();
  const step = gStateFRO.form.activeStep;
  const currentStep = step.value;

  const handleNext = () => step.set(currentStep + 1);

  const handlePrev = () => step.set(Math.max(currentStep - 1, 1));

  const clearAllForms = () => {
    gStateFRO.form.step1.set(null);
    gStateFRO.form.step2.set(null);
    gStateFRO.form.step3.set(null);
    gStateFRO.form.step4.set(null);
    step.set(1);
  };

  const handleSaveAll = async () => {
    const data = {
      ...gStateFRO.form.step1.value,
      ...gStateFRO.form.step2.value,
      ...gStateFRO.form.step3.value,
      ...gStateFRO.form.step4.value,
    };

    const governmentIdUploads: Record<string, File> = {};

    if (Array.isArray(data.governmentIdentification)) {
      data.governmentIdentification.forEach((row: any) => {
        const key = `governmentIdentification_${row.governmentIdNumber}`;

        if (row.upload && row.upload[0]) {
          governmentIdUploads[key] = row.upload[0];
        } else if (row.upload instanceof File) {
          governmentIdUploads[key] = row.upload;
        }
      });
    }

    const payload = {
      ...data,
      ...governmentIdUploads,
      // attachment: data.attachment[0] || data.attachment,
      userImage: data.userImage[0] || data.userImage,
      cropsPlanted: data.cropsPlanted.map((item) => item.split('-')[0].trim()),
    };
    console.log('attachment', data);
    console.log('all data:', payload);

    const res = await addFarmer(payload);
    if (res?.status === 1) {
      clearAllForms();
    }
  };

  const handleClearForms = () => {
    const confirmClear = window.confirm('Are you sure you want to clear all form data? This action cannot be undone.');

    if (!confirmClear) return;
    clearAllForms();
  };

  useEffect(() => {
    if (!currentStep) {
      step.set(1);
    }
  }, []);

  console.log('gStateFRO', gStateFRO.form.value);

  const renderStepForm = () => {
    switch (currentStep) {
      case 1:
        return <DataPrivacy gStateFRO={gStateFRO} onNext={handleNext} onClear={handleClearForms} />;
      case 2:
        return (
          <PersonalInfo gStateFRO={gStateFRO} onNext={handleNext} onPrevious={handlePrev} onClear={handleClearForms} />
        );
      case 3:
        return (
          <FarmInformation
            gStateFRO={gStateFRO}
            onNext={handleNext}
            onPrevious={handlePrev}
            onClear={handleClearForms}
          />
        );
      case 4:
        return (
          <BusinessInfo
            gStateFRO={gStateFRO}
            onPrevious={handlePrev}
            onClear={handleClearForms}
            onSave={handleSaveAll}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Stepper steps={steps} currentStep={currentStep} onStepClick={(v) => step.set(v)} />
      <div className="mt-6">{renderStepForm()}</div>
    </>
  );
}
