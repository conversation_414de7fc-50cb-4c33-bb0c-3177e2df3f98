'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { MoreHorizontal, Pencil, ToggleLeft, ToggleRight } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { useGroupPriceStore } from '@/lib/store/admin/trading-app/group-price-store';
import { EditGroupSchema, EditGroupType, IGroup } from '@/lib/store/admin/trading-app/group-price-store.types';
import { useQaStore } from '@/lib/store/admin/trading-app/qa-store';
import { EditQaStaffSchema, EditQaStaffType, IQa } from '@/lib/store/admin/trading-app/qa-store.types';
import { cn } from '@/lib/utils';

export const ColumnGroup = [
  {
    id: 'group_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Group Name" />,
    accessorFn: (row: IGroup) => `${row.name}`,
  },
  {
    id: 'percentage_rate',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Percentage Rate (%)" />,
    accessorFn: (row: IGroup) => `${row.percentage_rate}`,
  },
  {
    id: 'logistic_rate',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Logistic Rate per Kilo (₱)" />,
    accessorFn: (row: IGroup) => `${row.logistic_rate}`,
  },
  {
    id: 'remarks',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Remarks" />,
    cell: ({ row }) => <div className="line-clamp-2">{row.getValue('remarks')}</div>,
    accessorFn: (row: IGroup) => `${row.remarks}`,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader className="text-center" column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

const Action = ({ row }) => {
  const data: IGroup = row.original;
  const { updateGroup, toggleGroupStatus, state } = useGroupPriceStore();

  const deactivateConfirm = useHookstate(false);
  const activateConfirm = useHookstate(false);
  const editDialog = useHookstate(false);
  const updating = useHookstate(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(EditGroupSchema.omit({ tradingAppGroupPriceId: true })),
    defaultValues: {
      name: data.name,
      remarks: data.remarks,
      percentageRate: data.percentage_rate,
      logisticRate: data.logistic_rate,
    },
  });

  const onSubmit = async (_data: Omit<EditGroupType, 'tradingAppGroupPriceId'>) => {
    try {
      updating.set(true);

      const updatedData = {
        ..._data,
        tradingAppGroupPriceId: data.id,
      };

      await updateGroup(updatedData, data.user_type);
      editDialog.set(false);
    } catch (e) {
      console.error(e);
    } finally {
      updating.set(false);
    }
  };

  return (
    <>
      <div className="flex justify-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="size-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuItem
              className="flex items-center"
              onClick={() => {
                editDialog.set(true);
              }}
            >
              <Pencil className="mr-2 size-4" />
              <span>Edit</span>
            </DropdownMenuItem>

            {(data.status === 1 || data.status === null) && (
              <>
                <DropdownMenuItem className="flex items-center" onClick={() => deactivateConfirm.set(true)}>
                  <ToggleLeft className="mr-2 size-4" />
                  <span>Deactivate</span>
                </DropdownMenuItem>
              </>
            )}

            {(data.status === 0 || data.status === null) && (
              <>
                <DropdownMenuItem className="flex items-center" onClick={() => activateConfirm.set(true)}>
                  <ToggleRight className="mr-2 size-4" />
                  <span>Activate</span>
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Deactivate Confirmation */}
        <AlertDialog open={deactivateConfirm.value} onOpenChange={deactivateConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will deactivate {data.name}. This process cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  toggleGroupStatus({
                    userType: data.user_type,
                    tradingAppGroupPriceId: data.id,
                    status: 'deactivate',
                  });
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Activate Confirmation */}
        <AlertDialog open={activateConfirm.value} onOpenChange={activateConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will activate {data.name}. This process cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  toggleGroupStatus({
                    userType: data.user_type,
                    tradingAppGroupPriceId: data.id,
                    status: 'activate',
                  });
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Edit Dialog */}
        <Dialog open={editDialog.value} onOpenChange={editDialog.set}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-primary">Edit Group</DialogTitle>
              <DialogDescription>
                {`Fill up the forms to update a group. Click "Update" when you're ready.`}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="mt-3 grid grid-cols-1 gap-4">
                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="name" className="pb-1 font-normal">
                    Group Name
                  </Label>
                  <Input
                    {...register('name', {
                      required: 'Please fill in all required fields before submitting.',
                      validate: {
                        existingGroup: (value) => {
                          const groupAlradyExist = state[data.user_type].data.data.value.find(
                            (item) => item.name.toLowerCase().trim() === value.toLowerCase().trim(),
                          );
                          if (groupAlradyExist && groupAlradyExist.id !== data.id) {
                            return 'Group name already taken. Please choose a different name.';
                          }
                          return true;
                        },
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.name && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter group name"
                  />
                  {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="percentageRate" className="pb-1 font-normal">
                    Percentage Rate (%)
                  </Label>
                  <Input
                    {...register('percentageRate', {
                      valueAsNumber: true,
                      required: 'Please fill in all required fields before submitting.',
                      validate: {
                        greaterThanZero: (v) => v >= 0 || 'Please enter a valid percentage rate (numeric values only).',
                        lessThan100: (v) => v <= 100 || 'Please enter a valid percentage rate (numeric values only).',
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.percentageRate && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="number"
                    placeholder="e.g 12"
                    step={0.01}
                  />
                  {errors.percentageRate && <p className="form-error">{`${errors.percentageRate.message}`}</p>}
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="logisticRate" className="pb-1 font-normal">
                    Logistic Rate Per Kilo (₱)
                  </Label>
                  <Input
                    {...register('logisticRate', {
                      valueAsNumber: true,
                      required: 'Please fill in all required fields before submitting.',
                      validate: {
                        greaterThanZero: (v) => v >= 0 || 'Please enter a valid logistic rate (numeric values only).',
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.logisticRate && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="number"
                    placeholder="e.g 2"
                    step={0.01}
                  />
                  {errors.logisticRate && <p className="form-error">{`${errors.logisticRate.message}`}</p>}
                </div>

                <div className="grid w-full items-center gap-1.5">
                  <Label htmlFor="remarks" className="pb-1 font-normal">
                    Remarks
                  </Label>
                  <Textarea
                    {...register('remarks', {
                      required: 'Please fill in all required fields before submitting.',
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.remarks && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    placeholder="Enter Remarks"
                  />
                  {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
                </div>
              </div>

              <div className="flex justify-between gap-2 pt-6">
                <DialogClose asChild>
                  <Button className="px-12" variant="outline" type="button">
                    Cancel
                  </Button>
                </DialogClose>
                {updating.value ? (
                  <ButtonLoading text="Updating" className="px-12" />
                ) : (
                  <Button className="px-12" type="submit">
                    Update
                  </Button>
                )}
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};
