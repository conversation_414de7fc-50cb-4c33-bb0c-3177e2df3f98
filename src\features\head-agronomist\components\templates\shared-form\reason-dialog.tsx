'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface IReasonDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onConfirm: (reason: string) => Promise<void>;
  confirmText?: string;
  isLoading?: boolean;
}

interface IReasonForm {
  reason: string;
}

export default function ReasonDialog({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  confirmText = 'Confirm',
  isLoading = false,
}: IReasonDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<IReasonForm>({
    defaultValues: {
      reason: '',
    },
  });

  const handleSubmit = async (data: IReasonForm, event?: React.BaseSyntheticEvent) => {
    // Prevent event bubbling to parent forms
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (!data.reason.trim()) {
      form.setError('reason', {
        type: 'required',
        message: 'Reason is required',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await onConfirm(data.reason.trim());
      form.reset();
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Error in reason dialog:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="font-sans sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <form
          onSubmit={form.handleSubmit((data, event) => handleSubmit(data, event))}
          className="space-y-4"
          onKeyDown={(e) => {
            // Prevent Enter key from submitting parent form
            if (e.key === 'Enter' && e.target instanceof HTMLTextAreaElement) {
              e.stopPropagation();
            }
          }}
        >
          <div className="space-y-2">
            <Label htmlFor="reason">Reason for change *</Label>
            <Textarea
              id="reason"
              placeholder="Please provide a reason for this change..."
              className="min-h-[100px] resize-none"
              {...form.register('reason', {
                required: 'Reason is required',
                minLength: {
                  value: 3,
                  message: 'Reason must be at least 3 characters',
                },
                maxLength: {
                  value: 500,
                  message: 'Reason must not exceed 500 characters',
                },
              })}
            />
            {form.formState.errors.reason && (
              <p className="text-sm text-red-500">{form.formState.errors.reason.message}</p>
            )}
          </div>

          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={handleCancel} disabled={isSubmitting || isLoading}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              onClick={(e) => {
                // Additional safeguard to prevent event bubbling
                e.stopPropagation();
              }}
            >
              {isSubmitting || isLoading ? 'Processing...' : confirmText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
