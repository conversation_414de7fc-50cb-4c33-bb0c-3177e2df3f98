'use client';

import { useHookstate } from '@hookstate/core';

import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogFooter } from '@/components/ui/dialog';

export function DialogOutstandingBal({ state }) {
  // use this if you want to control the alert dialog programatically
  const dialogState = useHookstate(state);

  return (
    <Dialog open={dialogState.value} onOpenChange={dialogState.set}>
      <DialogContent className="font-sans sm:max-w-lg">
        <div className="text-center">
          <div>
            <img className="mx-auto" src="/assets/undraw/checklist.png" alt="" />
          </div>
          <div className="my-4 text-xl font-bold">Unable to process e-wallet top-up</div>
          <div>Outstanding loan balance detected.</div>
          <div>Please clear the loan balance to proceed.</div>
        </div>

        <DialogFooter className="mt-4 sm:justify-center">
          <DialogClose asChild>
            <Button type="button" size="lg" className="px-12">
              Okay
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
