'use client';

import { useHookstate } from '@hookstate/core';

import LoadingScreen from '@/components/LoadingScreen';
import { ScrollArea } from '@/components/ui/scroll-area';

import useProtected from '@/lib/hooks/utils/useProtected';
import { useGlobalStatePersist } from '@/lib/store/persist';

import Navbar from './components/navbar';
import DesktopSidebar from './components/sidebar/desktop-sidebar';
import MobileMenu from './components/sidebar/mobile-menu';
import { MENU } from './constants';

interface IAgronomistLayoutProps {
  children: React.ReactNode;
}

export default function AgronomistLayout({ children }: IAgronomistLayoutProps) {
  const gStateP = useGlobalStatePersist();
  const { loading } = useProtected();

  const mobileMenu = useHookstate(false);

  if (loading) {
    return <LoadingScreen />;
  }

  const activeMenu = MENU.find((item) => item.id === (gStateP.agronomist.activeMenu.value || 0));
  const menuTitle = activeMenu?.name || 'Dashboard';

  return (
    <ScrollArea className="h-screen">
      {gStateP.user.value && (
        <main>
          <Navbar menuTitle={menuTitle} onOpenMobileMenu={() => mobileMenu.set(true)} />

          <MobileMenu open={mobileMenu.value} onOpenChange={mobileMenu.set} />

          <div className="flex min-h-[calc(100vh-70px)] flex-col lg:min-h-full lg:flex-row">
            <DesktopSidebar />
            <section className="flex-1 border-l bg-gray-50">{children}</section>
          </div>
        </main>
      )}
    </ScrollArea>
  );
}
