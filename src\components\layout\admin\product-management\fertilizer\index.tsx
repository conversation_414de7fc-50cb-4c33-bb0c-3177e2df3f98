'use client';

import { useEffect } from 'react';

import useFertlizer from '@/lib/hooks/useFertlizer';
import { useGlobalState } from '@/lib/store';

import { FertilizerTable } from './fertilizer-table';
import { columns } from './fertilizer-table/columns';

export default function FertilizerPage() {
  const gState = useGlobalState();
  const { getFertilizer } = useFertlizer();

  useEffect(() => {
    getFertilizer();
  }, []);

  return (
    <div className="px-6 py-8" key={JSON.stringify(gState.admin.fertilizer.data.get({ noproxy: true }))}>
      <FertilizerTable columns={columns} data={gState.admin.fertilizer.data.get({ noproxy: true })} />
    </div>
  );
}
