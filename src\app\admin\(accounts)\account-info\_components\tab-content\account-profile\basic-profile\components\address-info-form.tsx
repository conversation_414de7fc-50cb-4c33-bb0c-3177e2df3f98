'use client';

import { State } from '@hookstate/core';
import {
  Control,
  Controller,
  FieldErrors,
  FieldValues,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
} from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { cn } from '@/lib/utils';

interface IAddressInfoFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
  address: any;
  addressPermanent: any;
  getValues: UseFormGetValues<FieldValues>;
  setValue: UseFormSetValue<FieldValues>;
  isSamePresent: State<boolean>;
}

export default function AddressInfoForm({
  register,
  control,
  errors,
  address,
  addressPermanent,
  getValues,
  setValue,
  isSamePresent,
}: IAddressInfoFormProps) {
  return (
    <div>
      {/* Present Address */}
      <div>
        <FormTitle title="Present Address" className="mt-6" />

        <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
          {/* Region */}
          <FormField key={JSON.stringify(address)} name="addressRegion" label="Region" errors={errors}>
            <Controller
              control={control}
              name="addressRegion"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') address.setRegionSelected(JSON.parse(v));
                  }}
                  value={value}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.addressRegion && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {address.regionList.map((region: any) => (
                        <SelectItem key={region.region_code} value={JSON.stringify(region)}>
                          {region.region_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* Province */}
          <FormField name="addressProvince" label="Province" errors={errors}>
            <Controller
              control={control}
              name="addressProvince"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') address.setProvinceSelected(JSON.parse(v));
                  }}
                  value={value}
                  disabled={address.provinceList.length === 0}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.addressProvince && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {address.provinceList.map((province: any) => (
                        <SelectItem key={province.province_code} value={JSON.stringify(province)}>
                          {province.province_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* City */}
          <FormField name="addressCity" label="City" errors={errors}>
            <Controller
              control={control}
              name="addressCity"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') address.setCitySelected(JSON.parse(v));
                  }}
                  value={value}
                  disabled={address.cityList.length === 0}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.addressCity && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select city" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {address.cityList.map((city: any) => (
                        <SelectItem key={city.city_code} value={JSON.stringify(city)}>
                          {city.city_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* Barangay */}
          <FormField name="addressBarangay" label="Barangay" errors={errors}>
            <Controller
              control={control}
              name="addressBarangay"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') address.setBarangaySelected(JSON.parse(v));
                  }}
                  value={value}
                  disabled={address.barangayList.length === 0}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.addressBarangay && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select Barangay" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {address.barangayList.map((barangay: any) => (
                        <SelectItem key={barangay.brgy_code} value={JSON.stringify(barangay)}>
                          {barangay.brgy_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* House Number */}
          <FormField name="addressHouseNumber" label="Street/House Number" errors={errors}>
            <Controller
              control={control}
              name="addressHouseNumber"
              rules={{ required: false }}
              render={({ field: { onChange, value } }) => (
                <Input
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.addressHouseNumber && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Street/House Number"
                  value={value}
                  onChange={(e) => {
                    onChange(e.target.value);
                    address.setStreet(e.target.value);
                  }}
                />
              )}
            />
          </FormField>

          {/* Zip Code */}
          <FormField name="addressZipCode" label="Zip Code" errors={errors}>
            <Controller
              control={control}
              name="addressZipCode"
              rules={{
                required: false,
                pattern: {
                  value: /^\d{4}$/, // validate zipcode to have exactly 4 digits
                  message: 'Invalid Zip Code format (e.g. 1234)',
                },
              }}
              render={({ field: { onChange, value } }) => (
                <Input
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.addressZipCode && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Zip Code"
                  value={value}
                  onChange={(e) => {
                    onChange(e.target.value);
                    address.setPostalCode(e.target.value);
                  }}
                />
              )}
            />
          </FormField>

          {/* Length of Stay */}
          <FormField name="addressLengthOfStay" label="Length of Stay" errors={errors}>
            <Input
              {...register('addressLengthOfStay', {
                required: false,
                validate: {
                  isGreaterThanZero: (v) => (v ? (v || 0) >= 0 || 'Must be greater or equal to 0' : true),
                },
              })}
              className={cn(
                'focus-visible:ring-primary',
                errors.addressLengthOfStay && 'border-red-500 focus-visible:ring-red-500',
              )}
              type="number"
              min={0}
              placeholder="e.g., 10 years"
            />
          </FormField>
        </div>
      </div>

      {/* Permanent Address */}
      <div>
        <FormTitle title="Permanent Address" className="mt-6" />

        <div className="my-6 flex items-center space-x-2">
          <Checkbox
            id="same-as-present"
            checked={isSamePresent.value}
            onCheckedChange={(checked: boolean) => isSamePresent.set(checked)}
          />
          <label
            htmlFor="same-as-present"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Same as Present Address
          </label>
        </div>

        <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
          {/* Region */}
          <FormField
            key={JSON.stringify(addressPermanent)}
            name="permanentAddressRegion"
            label="Region"
            errors={errors}
          >
            <Controller
              control={control}
              name="permanentAddressRegion"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') addressPermanent.setRegionSelected(JSON.parse(v));
                  }}
                  value={value}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.permanentAddressRegion &&
                        'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {addressPermanent.regionList.map((region: any) => (
                        <SelectItem key={region.region_code} value={JSON.stringify(region)}>
                          {region.region_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* Province */}
          <FormField name="permanentAddressProvince" label="Province" errors={errors}>
            <Controller
              control={control}
              name="permanentAddressProvince"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') addressPermanent.setProvinceSelected(JSON.parse(v));
                  }}
                  value={value}
                  disabled={addressPermanent.provinceList.length === 0}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.permanentAddressProvince &&
                        'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {addressPermanent.provinceList.map((province: any) => (
                        <SelectItem key={province.province_code} value={JSON.stringify(province)}>
                          {province.province_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* City */}
          <FormField name="permanentAddressCity" label="City" errors={errors}>
            <Controller
              control={control}
              name="permanentAddressCity"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') addressPermanent.setCitySelected(JSON.parse(v));
                  }}
                  value={value}
                  disabled={addressPermanent.cityList.length === 0}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.permanentAddressCity &&
                        'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select city" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {addressPermanent.cityList.map((city: any) => (
                        <SelectItem key={city.city_code} value={JSON.stringify(city)}>
                          {city.city_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* Barangay */}
          <FormField name="permanentAddressBarangay" label="Barangay" errors={errors}>
            <Controller
              control={control}
              name="permanentAddressBarangay"
              render={({ field: { onChange, value } }) => (
                <Select
                  onValueChange={(v) => {
                    onChange(v);
                    if (v !== '') addressPermanent.setBarangaySelected(JSON.parse(v));
                  }}
                  value={value}
                  disabled={addressPermanent.barangayList.length === 0}
                >
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.permanentAddressBarangay &&
                        'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Select Barangay" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {addressPermanent.barangayList.map((barangay: any) => (
                        <SelectItem key={barangay.brgy_code} value={JSON.stringify(barangay)}>
                          {barangay.brgy_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
          </FormField>

          {/* House Number */}
          <FormField name="permanentAddressHouseNumber" label="Street/House Number" errors={errors}>
            <Controller
              control={control}
              name="permanentAddressHouseNumber"
              rules={{ required: false }}
              render={({ field: { onChange, value } }) => (
                <Input
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.permanentAddressHouseNumber && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Street/House Number"
                  value={value}
                  onChange={(e) => {
                    onChange(e.target.value);
                    addressPermanent.setStreet(e.target.value);
                  }}
                />
              )}
            />
          </FormField>

          {/* Zip Code */}
          <FormField name="permanentAddressZipCode" label="Zip Code" errors={errors}>
            <Controller
              control={control}
              name="permanentAddressZipCode"
              rules={{
                required: false,
                pattern: {
                  value: /^\d{4}$/, // validate zipcode to have exactly 4 digits
                  message: 'Invalid Zip Code format (e.g. 1234)',
                },
              }}
              render={({ field: { onChange, value } }) => (
                <Input
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.permanentAddressZipCode && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Zip Code"
                  value={value}
                  onChange={(e) => {
                    onChange(e.target.value);
                    addressPermanent.setPostalCode(e.target.value);
                  }}
                />
              )}
            />
          </FormField>

          {/* Length of Stay */}
          <FormField name="permanentAddressLengthOfStay" label="Length of Stay" errors={errors}>
            <Input
              {...register('permanentAddressLengthOfStay', {
                required: false,
                validate: {
                  isGreaterThanZero: (v) => (v ? (v || 0) >= 0 || 'Must be greater or equal to 0' : true),
                },
              })}
              className={cn(
                'focus-visible:ring-primary',
                errors.permanentAddressLengthOfStay && 'border-red-500 focus-visible:ring-red-500',
              )}
              type="number"
              min={0}
              placeholder="e.g., 10 years"
            />
          </FormField>
        </div>
      </div>
    </div>
  );
}
