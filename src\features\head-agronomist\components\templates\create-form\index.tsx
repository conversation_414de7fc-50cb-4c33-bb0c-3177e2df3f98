'use client';

import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { useCreateFarmPlanTemplate } from '@/features/head-agronomist/hooks/useCreateFarmPlanTemplate';
import { ETemplateItemType } from '@/features/head-agronomist/types/farmplan-templates';

import CashReqSummary from '../shared-form/cash-requirements-summary';
import CostSummary from '../shared-form/cost-summary';
import CropInfo from '../shared-form/crop-info';
import FarmerEquitySummary from '../shared-form/farmer-equity-summary';
import InputCostSummary from '../shared-form/input-cost-summary';
import FoliarFertilization from '../shared-form/inputs-foliar-fert';
import PesticideApplication from '../shared-form/inputs-pesticide-application';
import SeedRequirements from '../shared-form/inputs-seed-requirements';
import SoilFertilizationSideDress from '../shared-form/inputs-soil-fert-side-dress';
import SoilFertilizationTopDress from '../shared-form/inputs-soil-fert-top-dress';
import KitaSubsidizedCosts from '../shared-form/kita-subsidized-costs';
import LaborRequirements from '../shared-form/labor-reqs';
import LoanRepaymentCalc from '../shared-form/loan-repayment-calculator';
import NonCashCosts from '../shared-form/non-cash-costs';
import NonKitaSubsidizedCosts from '../shared-form/nonkita-subsidized-costs';
import OtherFarmMaterials from '../shared-form/other-farm-materials';
import OtherProductionCosts from '../shared-form/other-production-costs';
import Header from './header';
import { ITemplateForm } from './types/template';

export default function CreateMasterTemplate() {
  // Use the create template hook
  const { submitTemplate, isSubmitting } = useCreateFarmPlanTemplate();

  // Initialize the form with the required structure
  const form = useForm<ITemplateForm>({
    defaultValues: {
      cropId: 0,
      versionNumber: '',
      location: '',
      contingencyForFluctuation: 0,
      interestRate: 0,
      numberOfMonthsPerTenor: 0,
      aorPerMonth: 0,
      items: [
        {
          name: 'Seed / Seedling Requirements (SE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Basal (Top-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Additional (Side-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Foliar Fertilization (Spray) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Pesticide Application (Spray / Spread) (CP)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Farm Materials, Consumables, etc.',
          type: ETemplateItemType.OTHERS,
          subItems: [],
        },
        {
          name: 'Labor Requirements',
          type: ETemplateItemType.LABOR,
          subItems: [],
        },
        {
          name: 'Other Production Costs',
          type: ETemplateItemType.OTHERS,
          subItems: [],
        },
        {
          name: 'Non-Cash Costs',
          type: ETemplateItemType.NON_CASH,
          subItems: [],
        },
        {
          name: 'KITA Subsidized Costs',
          type: ETemplateItemType.KITA_SUBSIDIZED,
          subItems: [],
        },
        {
          name: 'Non-KITA Subsidized Costs (Government Subsidy, Grants, Donations, etc.)',
          type: ETemplateItemType.NON_KITA_SUBSIDIZED,
          subItems: [],
        },
      ],
    },
  });

  // Handle form submission using the hook
  const onSubmit = form.handleSubmit(async (data) => {
    await submitTemplate(data);
  });

  return (
    <div className="p-6">
      <Header />

      <div className="my-6 rounded-md border border-blue-200 bg-blue-50 p-4">
        <p className="text-sm text-blue-700">
          Note: This master template is based on 1 hectare. For actual farm plans, input values are scaled according to
          the number of hectares.
        </p>
      </div>

      <form onSubmit={onSubmit}>
        <CropInfo form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARM INPUTS FOR DELIVERY / PICK-UP (Amount to be Held in favor of KITA)
        </div>

        <SeedRequirements form={form} />
        <SoilFertilizationTopDress form={form} />
        <SoilFertilizationSideDress form={form} />
        <FoliarFertilization form={form} />
        <PesticideApplication form={form} />
        <OtherFarmMaterials form={form} />

        {/* Estimated Farm Inputs Costs Section */}
        <InputCostSummary form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          CASH REQUIREMENTS (Labor, Overhead, Other Cash Costs, Etc.)
        </div>

        <LaborRequirements form={form} />
        <OtherProductionCosts form={form} />
        <CashReqSummary form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARMER&apos;S EQUITY (Other Non-Cash Costs, Subsidized Costs, etc.)
        </div>
        <NonCashCosts form={form} />
        <KitaSubsidizedCosts form={form} />
        <NonKitaSubsidizedCosts form={form} />
        <FarmerEquitySummary form={form} />

        <div className="mt-16 grid gap-4">
          <CostSummary />
          <LoanRepaymentCalc form={form} />
        </div>

        <div className="mt-16 flex justify-center">
          <Button type="submit" className="px-8" disabled={isSubmitting}>
            {isSubmitting ? 'Creating Template...' : 'Submit'}
          </Button>
        </div>
      </form>
    </div>
  );
}
