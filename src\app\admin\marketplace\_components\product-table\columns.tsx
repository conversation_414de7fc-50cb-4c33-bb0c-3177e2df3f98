'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

import { capitalize } from '@/lib/utils';

import { PRODUCT_TYPE, PRODUCT_TYPE_ACCESSOR } from '../Enums';

export const columns = [
  {
    id: 'price',
    accessorFn: (row) => `${row.price}`,
  },
  {
    id: 'name',
    accessorFn: (row) => {
      const accessor = PRODUCT_TYPE_ACCESSOR[Number(row.product_type)];
      const product = row[accessor];

      return `${product.name}`;
    },
  },
  {
    id: 'type',
    accessorFn: (row) => {
      const accessor = PRODUCT_TYPE_ACCESSOR[Number(row.product_type)];
      const product = row[accessor];

      const isSeed = Number(row.product_type) === PRODUCT_TYPE.SEEDS;
      const isChemical = Number(row.product_type) === PRODUCT_TYPE.CHEMICALS;
      const hasSub = isSeed || isChemical;

      return `${accessor}${
        hasSub
          ? `, ${isSeed ? (product.seedSubcategory ? product.seedSubcategory.name : 'N/A') : product.chemicalSubcategory ? product.chemicalSubcategory.name : 'N/A'}`
          : ''
      }`;
    },
  },
  {
    id: 'stocks',
    accessorFn: (row) => {
      return `${row.stocks}`;
    },
  },
  {
    id: 'isLow',
    accessorFn: (row) => {
      const isLowStocks = Number(row.stocks) < row.stocks_warning;

      return `${isLowStocks ? 'low' : 'normal'}`;
    },
  },
];
