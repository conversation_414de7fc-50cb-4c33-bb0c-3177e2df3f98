'use client';

import { format } from 'date-fns';
import { FC } from 'react';
import { CSVLink } from 'react-csv';

import { Button } from '@/components/ui/button';

import useTopTransactionsValue from '../hooks/useTopTransactionsValue';

const exportHeaders = [
  { label: '#', key: 'user_id' },
  { label: 'Name', key: 'name' },
  { label: 'Total Transactions', key: 'total_transactions' },
];

export const ExportTopTransactionsValue: FC = () => {
  const { topTransactionValueQuery } = useTopTransactionsValue();

  const getData = () =>
    topTransactionValueQuery.data.map((x) => ({
      user_id: x.user_id,
      name: `${x.first_name} ${x.last_name}`,
      total_transactions: x.total_transactions,
    })) || [];

  return (
    <div>
      <Button size="sm" className="h-8" asChild>
        <CSVLink
          data={getData()}
          headers={exportHeaders}
          filename={`top-transaction-value-${format(new Date(), 'yyyy-MM-dd')}.csv`}
          target="_blank"
        >
          Export
        </CSVLink>
      </Button>
    </div>
  );
};
