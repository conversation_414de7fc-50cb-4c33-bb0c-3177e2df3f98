'use client';

import Link from 'next/link';
import React from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { paths } from '@/features/field-relation-officer/layout/constants';

const Page = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Field Relation Officer Dashboard</h1>
        <p className="text-gray-500">Welcome to the Field Relation Officer Dashboard</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Link href={paths.dashboard.kyc.root}>
          <Card className="cursor-pointer transition-all hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Registration Form</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">KYC</div>
              <p className="text-xs text-muted-foreground">Collect and verify identification details for farmers</p>
            </CardContent>
          </Card>
        </Link>
        <Link href={paths.dashboard.farmers.root}>
          <Card className="cursor-pointer transition-all hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Records</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Registered Farmers</div>
              <p className="text-xs text-muted-foreground">Store information and profiles of all registered farmers</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
};

export default Page;
