'use client';

import { ColumnDef } from '@tanstack/react-table';

import { toCurrency } from '@/lib/utils';

import { IMarketplaceTopProducts } from '../../types';

export const columnsMarketplaceTopProducts: ColumnDef<IMarketplaceTopProducts>[] = [
  {
    accessorKey: 'product_id',
    header: ({ column }) => <div>#</div>,
  },
  {
    accessorKey: 'product_name',
    header: ({ column }) => <div className="min-w-max">Product Name</div>,
    cell: ({ row }) => {
      return <div className="w-max">{row.original.product_name}</div>;
    },
  },
  {
    accessorKey: 'product_quantity',
    header: ({ column }) => <div className="min-w-max text-right">Quantity</div>,
    cell: ({ row }) => {
      return <div className="text-right">{row.getValue('product_quantity')}</div>;
    },
  },
  {
    accessorKey: 'product_unit',
    header: ({ column }) => <div className="min-w-max">OUM</div>,
  },
  {
    accessorKey: 'product_price',
    header: ({ column }) => <div className="min-w-max text-right">Price per pc</div>,
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('product_price'))}</div>;
    },
  },
  {
    accessorKey: 'product_sales',
    header: ({ column }) => <div className="min-w-max text-right">Sales</div>,
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('product_sales'))}</div>;
    },
  },
];
