'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ChevronDown, Pencil, Plus, TextCursorInput, UploadCloud, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge, BadgeProps } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import useChemicals, { ChemicalSchema, UpdateChemicalSchema } from '@/lib/hooks/useChemicals';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import ChemicalsCsv from '../ChemicalsCsv';

export const columns = [
  {
    id: 'crop_protection_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Crop Protection Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.name}</div>;
    },
    accessorFn: (row) => row.name,
  },
  {
    id: 'brand',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Brand" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.brand}</div>;
    },
    accessorFn: (row) => row.brand,
  },
  {
    id: 'sub_category',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Sub Category" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.chemicalSubcategory ? data.chemicalSubcategory.name : 'N/A'}</div>;
    },
    accessorFn: (row) => row.chemicalSubcategory?.name ?? 'N/A',
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'active_ingredients',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Active Ingredients" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="">
          {data.chemicalActiveIngredients.map((ingredient) => ingredient.chemicalActiveIngredient.name).join(', ')}
        </div>
      );
    },
    accessorFn: (row) =>
      row.chemicalActiveIngredients.map((ingredient) => ingredient.chemicalActiveIngredient.name).join(', '),
  },
  {
    id: 'mode_action',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Mode of Action" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.chemicalModeOfAction ? data.chemicalModeOfAction.name : 'N/A'}</div>;
    },
    accessorFn: (row) => (row.chemicalModeOfAction ? row.chemicalModeOfAction.name : 'N/A'),
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const statusMessage = ['Disabled', 'Active'];
      const variant = ['error', 'success'] as BadgeProps;

      return (
        <div className="min-w-max">
          <Badge variant={variant[data.status]}>{statusMessage[data.status]}</Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'updated_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Updated At" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) =>
      `${new Date(row.updated_at).toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })}`,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

export const ActionHeader = () => {
  const router = useRouter();
  const gState = useGlobalState();
  const { addChemical, OPTIONS_ACTIVE_INGREDIENTS } = useChemicals();
  const dialogInput = useHookstate(false);
  const dialogUpload = useHookstate(false);
  const dropdown = useHookstate(false);
  const loading = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(ChemicalSchema),
    defaultValues: {
      chemicals: [
        {
          name: '',
          brand: '',
          chemicalSubcategoryId: '',
          chemicalModeOfActionId: '',
          chemicalActiveIngredients: [],
        },
      ],
    },
  });
  const { fields, append, remove } = useFieldArray({
    name: 'chemicals',
    control,
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        chemicals: data.chemicals.map((a) => ({
          ...a,
          chemicalActiveIngredients: a.chemicalActiveIngredients.map((b) => b.value),
        })),
      };
      await addChemical(updatedData);

      dialogInput.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div className="flex items-center justify-end gap-2">
      <DropdownMenu onOpenChange={dropdown.set}>
        <DropdownMenuTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" size="sm">
            Add Crop Protection via
            <ChevronDown
              className={cn('ml-2 h-4 w-4 transition duration-300 ease-in-out', dropdown.value && 'rotate-180')}
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => dialogInput.set(true)}>
            <TextCursorInput className="mr-2 size-4" />
            Input
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              dialogUpload.set(true);
            }}
          >
            <UploadCloud className="mr-2 size-4" />
            Upload
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Add via input */}
      <Dialog open={dialogInput.value} onOpenChange={dialogInput.set}>
        <DialogContent className="p-0 sm:max-w-[52rem]">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Crop Protection</DialogTitle>
            <DialogDescription>
              {`Add new Crop Protection to your list. You can add multiple Crop Protection at a time.`}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="">
            <div className="mt-3 grid gap-4 divide-y-2 divide-dashed px-6 pb-6">
              {fields.map((field, index) => {
                const fieldName = errors?.chemicals?.[index]?.name;
                const fieldBrand = errors?.chemicals?.[index]?.brand;
                const fieldCategory = errors?.chemicals?.[index]?.chemicalSubcategoryId;
                const fieldActiveIngredient = errors?.chemicals?.[index]?.chemicalActiveIngredients;
                const fieldModeOfAction = errors?.chemicals?.[index]?.chemicalModeOfActionId;

                return (
                  <div key={field.id} className={cn('grid gap-4', index === 0 ? '' : 'pt-6')}>
                    <div className="flex items-center gap-3">
                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor="name" className="pb-1 font-normal">
                          Crop Protection Name
                        </Label>
                        <div className="flex items-center gap-4">
                          <Input
                            {...register(`chemicals.${index}.name` as const)}
                            className={cn(
                              'focus-visible:ring-primary',
                              fieldName && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            type="text"
                            placeholder="Enter Crop Protection name"
                          />
                        </div>
                        {fieldName && <p className="form-error">{`${fieldName.message}`}</p>}
                      </div>

                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor="brand" className="pb-1 font-normal">
                          Brand
                        </Label>
                        <div className="flex items-center gap-4">
                          <Input
                            {...register(`chemicals.${index}.brand` as const)}
                            className={cn(
                              'focus-visible:ring-primary',
                              fieldBrand && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            type="text"
                            placeholder="Enter brand"
                          />
                        </div>
                        {fieldBrand && <p className="form-error">{`${fieldBrand.message}`}</p>}
                      </div>

                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor="chemicalSubcategoryId" className="pb-1 font-normal">
                          Sub Category
                        </Label>
                        <div className="flex items-center gap-4">
                          <Controller
                            control={control}
                            name={`chemicals.${index}.chemicalSubcategoryId`}
                            render={({ field: { onChange, onBlur, value, ref } }) => (
                              <Select onValueChange={onChange} value={value}>
                                <SelectTrigger
                                  className={cn(
                                    'focus-visible:ring-primary',
                                    fieldCategory &&
                                      'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                                  )}
                                >
                                  <SelectValue placeholder="Select subcategory" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectGroup>
                                    {gState.admin.chemicals.subcategory
                                      .get({ noproxy: true })
                                      .filter((x) => x.status === 1)
                                      .map((subcat, i) => (
                                        <SelectItem key={i} value={`${subcat.id}`}>
                                          {subcat.name}
                                        </SelectItem>
                                      ))}
                                  </SelectGroup>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          <div className={cn(index === 0 && 'invisible')}>
                            <Button
                              className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                              variant="outline"
                              size="icon"
                              onClick={() => remove(index)}
                              type="button"
                            >
                              <X className="size-5" />
                            </Button>
                          </div>
                        </div>
                        {fieldCategory && <p className="form-error">{`${fieldCategory.message}`}</p>}
                      </div>
                    </div>

                    <div className="flex items-start gap-3 pr-14">
                      <div className="grid w-full items-center gap-1.5">
                        <Label htmlFor={`chemicals.${index}.chemicalActiveIngredients`} className="pb-1 font-normal">
                          Active Ingredients
                        </Label>
                        <Controller
                          control={control}
                          name={`chemicals.${index}.chemicalActiveIngredients`}
                          render={({ field: { onChange, onBlur, value, ref } }) => (
                            <MultipleSelector
                              value={value}
                              onChange={onChange}
                              defaultOptions={OPTIONS_ACTIVE_INGREDIENTS.get({ noproxy: true }) as any}
                              placeholder="Select active ingredients"
                              emptyIndicator={
                                <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                                  No results found.
                                </p>
                              }
                            />
                          )}
                        />
                        {fieldActiveIngredient && <p className="form-error">{`${fieldActiveIngredient.message}`}</p>}
                      </div>

                      <div className="grid w-full items-center gap-1.5">
                        <Label htmlFor={`chemicals.${index}.chemicalModeOfActionId`} className="pb-1 font-normal">
                          Mode of Action
                        </Label>
                        <Controller
                          control={control}
                          name={`chemicals.${index}.chemicalModeOfActionId`}
                          render={({ field: { onChange, onBlur, value, ref } }) => (
                            <Select onValueChange={onChange} value={value}>
                              <SelectTrigger
                                className={cn(
                                  'focus-visible:ring-primary',
                                  fieldModeOfAction &&
                                    'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                                )}
                              >
                                <SelectValue placeholder="Select mode of action" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectGroup>
                                  {gState.admin.chemicals.mode
                                    .get({ noproxy: true })
                                    .filter((x) => x.status === 1)
                                    .map((subcat, i) => (
                                      <SelectItem key={i} value={`${subcat.id}`}>
                                        {subcat.name}
                                      </SelectItem>
                                    ))}
                                </SelectGroup>
                              </SelectContent>
                            </Select>
                          )}
                        />
                        {fieldModeOfAction && <p className="form-error">{`${fieldModeOfAction.message}`}</p>}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end px-6 pt-2">
              <Button
                variant="outline"
                size="icon"
                className="border-slate-300"
                type="button"
                onClick={() =>
                  append({
                    name: '',
                    brand: '',
                    chemicalSubcategoryId: '',
                    chemicalModeOfActionId: '',
                    chemicalActiveIngredients: [],
                  })
                }
              >
                <Plus className="size-5 text-primary" />
              </Button>
            </div>

            <div className="flex justify-between gap-2 p-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              {loading.value ? (
                <ButtonLoading />
              ) : (
                <Button className="px-12" type="submit">
                  Add
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add via upload */}
      <Dialog open={dialogUpload.value} onOpenChange={dialogUpload.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-[52rem]">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Crop Protection</DialogTitle>
            <DialogDescription>{`Upload CSV file to add Crop Protection`}</DialogDescription>
          </DialogHeader>

          <ChemicalsCsv />

          <div className="flex justify-between gap-2 p-6">
            <DialogClose asChild>
              <Button className="px-12" variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="px-12" type="button" disabled={gState.admin.chemicals.upload.length === 0}>
                  Add
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will add new chemicals to your list.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    type="button"
                    onClick={async () => {
                      const chemicals = {
                        chemicals: gState.admin.chemicals.upload.value,
                      };
                      await addChemical(JSON.parse(JSON.stringify(chemicals)));
                      dialogUpload.set(false);
                    }}
                  >
                    Continue
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Action = ({ row }) => {
  const data = row.original;
  const gState = useGlobalState();
  const updateDialog = useHookstate(false);
  const loading = useHookstate(false);
  const { updateChemical, OPTIONS_ACTIVE_INGREDIENTS } = useChemicals();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    resolver: zodResolver(UpdateChemicalSchema),
    defaultValues: {
      chemicalId: `${data.id}`,
      name: data.name,
      brand: data.brand,
      status: `${data.status}`,
      chemicalSubcategoryId: `${data.chemicalSubcategory?.id}`,
      chemicalModeOfActionId: `${data.chemicalModeOfAction?.id ?? ''}`,
      chemicalActiveIngredients: data.chemicalActiveIngredients.map((a) => ({
        label: a.chemicalActiveIngredient.name,
        value: a.chemicalActiveIngredient.id,
      })),
    },
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        chemicalActiveIngredients: data.chemicalActiveIngredients.map((a) => `${a.value}`),
      };
      await updateChemical(updatedData);

      updateDialog.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <>
      <div key={JSON.stringify(data)} className="flex justify-end gap-2">
        <div className="flex justify-end gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="h-8 px-2 lg:px-3"
                variant="outline"
                size="sm"
                onClick={() => {
                  updateDialog.set(true);
                }}
              >
                <Pencil className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit Crop Protection</p>
            </TooltipContent>
          </Tooltip>

          {/* Update Dialog */}
          <Dialog open={updateDialog.value} onOpenChange={updateDialog.set}>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-primary">Update Crop Protection</DialogTitle>
                <DialogDescription>{`Fill the form below to update Crop Protection`}</DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="space-y-6">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="chemicalId" className="pb-1 font-normal">
                      Crop Protection ID
                    </Label>
                    <Input
                      {...register('chemicalId')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.chemicalId && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter chemical id"
                      disabled
                    />
                    {errors.chemicalId && <p className="form-error">{`${errors.chemicalId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="name" className="pb-1 font-normal">
                      Crop Protection Name
                    </Label>
                    <Input
                      {...register('name')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.name && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Crop Protection name"
                    />
                    {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="brand" className="pb-1 font-normal">
                      Brand
                    </Label>
                    <Input
                      {...register('brand')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.brand && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter brand"
                    />
                    {errors.brand && <p className="form-error">{`${errors.brand.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="chemicalSubcategoryId" className="pb-1 font-normal">
                      Sub Category
                    </Label>
                    <Controller
                      control={control}
                      name="chemicalSubcategoryId"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.chemicalSubcategoryId &&
                                'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select sub category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {gState.admin.chemicals.subcategory
                                .get({ noproxy: true })
                                .filter((x) => x.status === 1)
                                .map((subcat, i) => (
                                  <SelectItem key={i} value={`${subcat.id}`}>
                                    {subcat.name}
                                  </SelectItem>
                                ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.chemicalSubcategoryId && (
                      <p className="form-error">{`${errors.chemicalSubcategoryId.message}`}</p>
                    )}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="chemicalActiveIngredients" className="pb-1 font-normal">
                      Active Ingredients
                    </Label>
                    <Controller
                      control={control}
                      name="chemicalActiveIngredients"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <MultipleSelector
                          value={value}
                          onChange={onChange}
                          defaultOptions={OPTIONS_ACTIVE_INGREDIENTS.get({ noproxy: true }) as any}
                          placeholder="Select active ingredients"
                          emptyIndicator={
                            <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                              No results found.
                            </p>
                          }
                        />
                      )}
                    />
                    {errors.chemicalActiveIngredients && (
                      <p className="form-error">{`${errors.chemicalActiveIngredients.message}`}</p>
                    )}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="chemicalModeOfActionId" className="pb-1 font-normal">
                      Mode of Action
                    </Label>
                    <Controller
                      control={control}
                      name="chemicalModeOfActionId"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.chemicalModeOfActionId &&
                                'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select mode of action" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {gState.admin.chemicals.mode
                                .get({ noproxy: true })
                                .filter((x) => x.status === 1)
                                .map((subcat, i) => (
                                  <SelectItem key={i} value={`${subcat.id}`}>
                                    {subcat.name}
                                  </SelectItem>
                                ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.chemicalModeOfActionId && (
                      <p className="form-error">{`${errors.chemicalModeOfActionId.message}`}</p>
                    )}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="status" className="pb-1 font-normal">
                      Status
                    </Label>
                    <Controller
                      control={control}
                      name="status"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="0">Disabled</SelectItem>
                              <SelectItem value="1">Active</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
                  </div>
                </div>

                <div className="flex justify-between gap-2 pt-6">
                  <DialogClose asChild>
                    <Button className="px-12" variant="outline" type="button">
                      Cancel
                    </Button>
                  </DialogClose>
                  {loading.value ? (
                    <ButtonLoading />
                  ) : (
                    <Button className="px-12" type="submit">
                      Update
                    </Button>
                  )}
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  );
};
