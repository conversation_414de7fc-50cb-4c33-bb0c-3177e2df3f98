'use client';

import { useHookstate } from '@hookstate/core';
import {
  Timeline,
  TimelineConnector,
  TimelineHeader,
  TimelineIcon,
  TimelineItem,
  Typography,
} from '@material-tailwind/react';
import { BellIcon, PackageCheckIcon, PackageIcon, PackageSearchIcon, PackageXIcon } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { FaMoneyBillWave, FaWallet } from 'react-icons/fa';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import useMarketplace from '@/lib/hooks/useMarketplace';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import { PaymentMethodType, PRODUCT_TYPE_ACCESSOR } from '../../_components/Enums';

export default function OrderDetailsPage() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const orderId = useSearchParams().get('id');

  const address = gStateP.admin['viewOrder']['shipping_address'].value
    ? JSON.parse(gStateP.admin['viewOrder']['shipping_address'].value)
    : {};
  const paymentMethod = useHookstate({
    cash: {
      checked: false,
      amount: 0,
    },
    ewallet: {
      checked: false,
      amount: 0,
    },
  });

  const { getOrderById } = useMarketplace();

  useEffect(() => {
    if (orderId) {
      getOrderById(orderId);
    } else {
      router.push('/admin/marketplace/order');
    }
  }, [orderId]);

  useEffect(() => {
    if (gStateP.admin['viewOrder'].value) {
      gStateP.admin.orders.shippingDate.set(gStateP.admin['viewOrder']['shipping_date'].value);

      const totalPrice = gStateP.admin['viewOrder']['total_price'].value;
      const walletAllocation = gStateP.admin['viewOrder']['wallet_allocation'].value;
      const paymentMethodType = gStateP.admin['viewOrder']['payment_method'].value;

      paymentMethod.set({
        cash: {
          checked: paymentMethodType === PaymentMethodType.CASH || paymentMethodType === PaymentMethodType.MULTIPLE,
          amount:
            paymentMethodType === PaymentMethodType.MULTIPLE
              ? totalPrice - walletAllocation
              : paymentMethodType === PaymentMethodType.CASH
                ? totalPrice
                : 0,
        },
        ewallet: {
          checked: paymentMethodType === PaymentMethodType.EWALLET || paymentMethodType === PaymentMethodType.MULTIPLE,
          amount:
            paymentMethodType === PaymentMethodType.EWALLET
              ? totalPrice
              : paymentMethodType === PaymentMethodType.MULTIPLE
                ? walletAllocation
                : 0,
        },
      });
    }
  }, [gStateP.admin['viewOrder']]);

  return (
    <div className="space-y-6 p-6 md:p-12">
      <div className="font-dmSans text-xl font-bold text-primary">Customer Details</div>
      <div className="grid items-start gap-6 border-b-2 border-dashed border-gray-300 pb-6 md:grid-cols-2">
        {/* Left */}
        <div className="grid gap-3">
          <div className="grid grid-cols-3 gap-6">
            <div className="text-primary/60">Account ID</div>
            <div className="col-span-2 text-primary">
              {gStateP.admin['viewOrder']['customer']['id'].value.toString().padStart(9, '0')}
            </div>
          </div>
          <div className="grid grid-cols-3 gap-6">
            <div className="text-primary/60">Customer Name</div>
            <div className="col-span-2 text-primary">{`${gStateP.admin['viewOrder']['customer']['farmer']['first_name'].value} ${gStateP.admin['viewOrder']['customer']['farmer']['last_name'].value}`}</div>
          </div>
          <div className="grid grid-cols-3 gap-6">
            <div className="text-primary/60">Address</div>
            <div className="col-span-2 text-primary">
              {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
            </div>
          </div>
        </div>

        {/* Right */}
        <div className="grid gap-3">
          <div className="grid grid-cols-3 gap-6">
            <div className="text-primary/60">Contact No.</div>
            <div className="col-span-2 text-primary">
              {gStateP.admin['viewOrder']['customer']['farmer']['mobile_number'].value}
            </div>
          </div>
          <div className="grid grid-cols-3 gap-6">
            <div className="text-primary/60">
              <span>Pick up Date</span>
            </div>
            <div className="col-span-2 text-primary">
              <div>
                {new Date(gStateP.admin['viewOrder']['shipping_date'].value).toLocaleString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: '2-digit',
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Status History */}
      <div className="">
        <div className="py-6 font-dmSans text-xl font-bold text-primary">Status History</div>

        <Timeline>
          {gStateP.admin['viewOrder']['statusHistory'].get({ noproxy: true }).map((item, index) => {
            const status = {
              0: {
                text: 'Pending',
                color: 'yellow',
                icon: <BellIcon className="size-5" />,
              },
              1: {
                text: 'Preparing',
                color: 'purple',
                icon: <PackageSearchIcon className="size-5" />,
              },
              2: {
                text: 'Order Ready',
                color: 'blue',
                icon: <PackageIcon className="size-5" />,
              },
              3: {
                text: 'Completed',
                color: 'green',
                icon: <PackageCheckIcon className="size-5" />,
              },
              999: {
                text: 'Cancelled',
                color: 'red',
                icon: <PackageXIcon className="size-5" />,
              },
            };

            if (index === gStateP.admin['viewOrder']['statusHistory'].length - 1) {
              return (
                <TimelineItem key={index} className={cn('h-28', index !== 0 ? 'opacity-50' : '')}>
                  <TimelineHeader className="relative rounded-xl border border-blue-gray-50 bg-white py-3 pl-4 pr-8 shadow-lg shadow-blue-gray-900/5">
                    <TimelineIcon className="p-3" variant="ghost" color={status[item.status_type].color}>
                      {status[item.status_type].icon}
                    </TimelineIcon>
                    <div className="flex flex-col gap-1">
                      <Typography variant="h6" color="blue-gray" placeholder={undefined}>
                        {item.message}
                      </Typography>
                      <div className="flex gap-1">
                        <Typography variant="small" color="gray" className="font-normal" placeholder={undefined}>
                          {new Date(item.updated_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true,
                          })}
                          ,
                        </Typography>
                        <Typography variant="small" color="gray" className="font-normal" placeholder={undefined}>
                          Processed By: {`${item.processedBy.admin.first_name} ${item.processedBy.admin.last_name}`}
                        </Typography>
                      </div>
                    </div>
                  </TimelineHeader>
                </TimelineItem>
              );
            }

            return (
              <TimelineItem key={index} className={cn('h-28', index !== 0 ? 'opacity-50' : '')}>
                <TimelineConnector className="!w-[78px]" />
                <TimelineHeader className="relative rounded-xl border border-blue-gray-50 bg-white py-3 pl-4 pr-8 shadow-lg shadow-blue-gray-900/5">
                  <TimelineIcon className="p-3" variant="ghost" color={status[item.status_type].color}>
                    {status[item.status_type].icon}
                  </TimelineIcon>
                  <div className="flex flex-col gap-1">
                    <Typography variant="h6" color="blue-gray" placeholder={undefined}>
                      {item.message}
                    </Typography>
                    <div className="flex gap-1">
                      <Typography variant="small" color="gray" className="font-normal" placeholder={undefined}>
                        {new Date(item.updated_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true,
                        })}
                        ,
                      </Typography>
                      <Typography variant="small" color="gray" className="font-normal" placeholder={undefined}>
                        Processed By: {`${item.processedBy.admin.first_name} ${item.processedBy.admin.last_name}`}
                      </Typography>
                    </div>
                  </div>
                </TimelineHeader>
              </TimelineItem>
            );
          })}
        </Timeline>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Order List</div>

      <div className="flex">
        <ScrollArea type="always" className="w-1 flex-1 rounded-xl">
          <div className="overflow-hidden rounded-xl border">
            <Table className="w-full">
              <TableHeader>
                <TableRow className="bg-gray-100">
                  <TableHead className="font-bold">Item Code</TableHead>
                  <TableHead className="font-bold">Item Name</TableHead>
                  <TableHead className="font-bold">Quantity</TableHead>
                  <TableHead className="font-bold">Unit Price</TableHead>
                  <TableHead className="font-bold">Total Amount</TableHead>
                  <TableHead className="font-bold">UOM</TableHead>
                  <TableHead className="font-bold">Vat Code</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {gStateP.admin['viewOrder']['marketplaceProductOrders'].get({ noproxy: true }).map((invoice, index) => {
                  const accessor = PRODUCT_TYPE_ACCESSOR[Number(invoice.marketplaceProduct.product_type)];
                  const product = invoice.marketplaceProduct[accessor];

                  return (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{invoice.marketplaceProduct.code}</TableCell>
                      <TableCell>{product.name}</TableCell>
                      <TableCell>{invoice.quantity}</TableCell>
                      <TableCell>
                        {invoice.price.toLocaleString('en-US', { style: 'currency', currency: 'PHP' })}
                      </TableCell>
                      <TableCell>
                        {Number(invoice.price * invoice.quantity).toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                      </TableCell>
                      <TableCell>{invoice.marketplaceProduct.unit}</TableCell>
                      <TableCell>{invoice.marketplaceProduct.vatable}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
              <TableFooter>
                <TableRow>
                  <TableCell colSpan={2}>Total</TableCell>
                  <TableCell>
                    {gStateP.admin['viewOrder']['marketplaceProductOrders']
                      .get({ noproxy: true })
                      .map((invoice) => invoice.quantity)
                      .reduce((a, b) => a + b, 0)}
                  </TableCell>
                  <TableCell></TableCell>
                  <TableCell>
                    {gStateP.admin['viewOrder']['total_price'].value.toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}
                  </TableCell>
                  <TableCell colSpan={2}></TableCell>
                </TableRow>
              </TableFooter>
            </Table>
          </div>

          <ScrollBar orientation="horizontal" className="w-full" />
        </ScrollArea>
      </div>

      <div className="grid gap-6 px-4 md:grid-cols-2">
        {/* Left */}
        <div>
          <div className="pt-6 font-dmSans text-xl font-bold text-primary">Order Summary</div>

          <div className="max-w-sm space-y-2 border-b border-primary py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-primary/60">Subtotal</div>
              <div className="text-primary">
                {gStateP.admin['viewOrder']['total_price'].value.toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-primary/60">Shipping Fee</div>
              <div className="text-primary">
                {Number(gStateP.admin['viewOrder']['shipping_fee'].value).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
          </div>

          <div className="max-w-sm pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-primary/60">Total Amount (Vat Incl.)</div>
              <div className="font-bold text-primary">
                {Number(
                  gStateP.admin['viewOrder']['shipping_fee'].value + gStateP.admin['viewOrder']['total_price'].value,
                ).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Right */}
        <div>
          <div className="pt-6 font-dmSans text-xl font-bold text-primary">Payment Method</div>
          <div className="pt-4">
            <div className="grid gap-3">
              <div className="flex items-start gap-3">
                <div
                  className={cn(
                    'w-[200px] flex items-center gap-3 rounded-md border border-primary px-4 py-3 text-primary',
                    paymentMethod.cash.checked.value ? '' : 'opacity-50',
                  )}
                >
                  <Checkbox
                    disabled
                    checked={paymentMethod.cash.checked.value}
                    onCheckedChange={(e: boolean) => {
                      if (!e) {
                        paymentMethod.cash.amount.set(0);
                      }
                      paymentMethod.cash.checked.set(e);
                    }}
                  />
                  <Label htmlFor="r1">Cash Payment</Label>
                  <FaMoneyBillWave className="size-4" />
                </div>

                {paymentMethod.cash.checked.value && (
                  <div className="flex-1">
                    <Input
                      readOnly
                      className="w-full"
                      type="number"
                      min={0}
                      value={paymentMethod.cash.amount.value}
                      onChange={(e) => {
                        let value = e.target.value;

                        // If the value is '0' and the user types another number, remove the leading zero
                        if (value.length > 1 && value.startsWith('0')) {
                          e.target.value = value.replace(/^0+/, '');
                        }

                        paymentMethod.cash.amount.set(Number(value));
                      }}
                    />
                  </div>
                )}
              </div>

              <div className="flex items-start gap-3">
                <div
                  className={cn(
                    'w-[200px] flex items-center gap-3 rounded-md border border-primary px-4 py-3 text-primary',
                    paymentMethod.ewallet.checked.value ? '' : 'opacity-50',
                  )}
                >
                  <Checkbox
                    disabled
                    checked={paymentMethod.ewallet.checked.value}
                    onCheckedChange={(e: boolean) => {
                      if (!e) {
                        paymentMethod.ewallet.amount.set(0);
                      }
                      paymentMethod.ewallet.checked.set(e);
                    }}
                  />
                  <div className="flex flex-col">
                    <p className="text-xs">e-Wallet Load</p>
                  </div>
                  <FaWallet className="size-4" />
                </div>

                {paymentMethod.ewallet.checked.value && (
                  <div className="flex-1">
                    <Input
                      readOnly
                      className="w-full"
                      type="number"
                      value={paymentMethod.ewallet.amount.value}
                      onChange={(e) => {
                        let value = e.target.value;

                        // If the value is '0' and the user types another number, remove the leading zero
                        if (value.length > 1 && value.startsWith('0')) {
                          e.target.value = value.replace(/^0+/, '');
                        }

                        paymentMethod.ewallet.amount.set(Number(value));
                      }}
                    />
                  </div>
                )}
              </div>

              <div className="flex items-start gap-3">
                <div className="w-[200px] text-primary/60">Total Payment Amount</div>
                <div className="pl-3 font-bold text-primary">
                  {Number(paymentMethod.cash.amount.value + paymentMethod.ewallet.amount.value).toLocaleString(
                    'en-US',
                    {
                      style: 'currency',
                      currency: 'PHP',
                    },
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div>
          <Button className="px-8" variant="outline" type="button" onClick={() => router.back()}>
            Back
          </Button>
        </div>
      </div>
    </div>
  );
}
