'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { toast } from 'sonner';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import axios from '@/lib/api';
import { DEFAULT_GROUP_TAB } from '@/lib/constants';
import { catchError } from '@/lib/utils';

import { AddGroupType, EditGroupType, IGroup, IGroupData } from './group-price-store.types';

const initialState = {
  [UserType.NONFARMER]: {
    data: {
      meta: null,
      data: [],
    } as IGroupData,
    tab: DEFAULT_GROUP_TAB,
    selectedGroup: null as IGroup,
  },
  [UserType.FARMER]: {
    data: {
      meta: null,
      data: [],
    } as IGroupData,
    tab: DEFAULT_GROUP_TAB,
    selectedGroup: null as IGroup,
  },
};

export const groupPriceState = hookstate(
  initialState,
  devtools({
    key: 'groupPriceState',
  }),
);

export const useGroupPriceStore = (currentUser = 'admin') => {
  const state = useHookstate(groupPriceState);

  const getGroups = async (userType: UserType.NONFARMER | UserType.FARMER) => {
    try {
      const res = await axios
        .get(`/${currentUser}/tradingapp/group/price/viewAll`, {
          params: {
            userType,
          },
        })
        .then((res) => res.data.data);
      state[userType].data.set(res);
    } catch (e) {
      console.error(e);
    }
  };

  const getGroup = async (groupId: number, userType: UserType.NONFARMER | UserType.FARMER) => {
    try {
      const res = await axios
        .get(`/${currentUser}/tradingapp/group/price/view/${groupId}`)
        .then((res) => res.data.data);
      state[userType].selectedGroup.set(res);
    } catch (e) {
      console.error(e);
    }
  };

  const addGroup = async (data: AddGroupType) => {
    try {
      toast.loading('Adding group...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/group/price/create`, data);
      await getGroups(data.userType);

      toast.dismiss();
      toast.success('Success', {
        description: 'Group added successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'addGroup');
    }
  };

  const updateGroup = async (data: EditGroupType, userType: UserType.NONFARMER | UserType.FARMER) => {
    try {
      toast.loading('Updating group...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/group/price/update`, data);
      await getGroups(userType);

      toast.dismiss();
      toast.success('Success', {
        description: 'Group updated successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'updateGroup');
    }
  };

  const toggleGroupStatus = async ({
    userType,
    tradingAppGroupPriceId,
    status,
  }: {
    userType: UserType.NONFARMER | UserType.FARMER;
    tradingAppGroupPriceId: number;
    status: 'activate' | 'deactivate';
  }) => {
    try {
      toast.loading('Updating group status...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/group/price/update`, {
        tradingAppGroupPriceId,
        status: status === 'activate' ? 1 : 0,
      });
      await getGroups(userType);

      toast.dismiss();
      toast.success('Success', {
        description: 'Group status updated successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'toggleGroupStatus');
    }
  };

  return { getGroups, state, addGroup, updateGroup, toggleGroupStatus, getGroup };
};
