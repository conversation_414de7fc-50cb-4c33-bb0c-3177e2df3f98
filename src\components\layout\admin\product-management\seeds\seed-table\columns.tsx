'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ChevronDown, Pencil, Plus, TextCursorInput, UploadCloud, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge, BadgeProps } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import useSeeds, { SeedSchema, UpdateSeedSchema } from '@/lib/hooks/useSeeds';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import SeedCsv from '../SeedCsv';

export const columns = [
  {
    id: 'seed_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Seed Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.name}</div>;
    },
    accessorFn: (row) => row.name,
  },
  {
    id: 'variety',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Variety" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.seedVariety?.name ?? 'N/A'}</div>;
    },
    accessorFn: (row) => row.seedVariety?.name ?? 'N/A',
  },
  {
    id: 'crop_type',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Crop Type" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.seedSubcategory?.name}</div>;
    },
    accessorFn: (row) => row.seedSubcategory?.name,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'breed',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Breed" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.seedBreed?.name ?? 'N/A'}</div>;
    },
    accessorFn: (row) => row.seedBreed?.name ?? 'N/A',
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const statusMessage = ['Disabled', 'Active'];
      const variant = ['error', 'success'] as BadgeProps;

      return (
        <div className="min-w-max">
          <Badge variant={variant[data.status]}>{statusMessage[data.status]}</Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'updated_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Updated At" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) =>
      `${new Date(row.updated_at).toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })}`,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

export const ActionHeader = () => {
  const router = useRouter();
  const gState = useGlobalState();
  const { addSeed } = useSeeds();
  const dialogInput = useHookstate(false);
  const dialogUpload = useHookstate(false);
  const dropdown = useHookstate(false);
  const loading = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(SeedSchema),
    defaultValues: {
      seeds: [
        {
          name: '',
          seedSubcategoryId: '',
          seedBreedId: '',
          seedVarietyId: '',
        },
      ],
    },
  });
  const { fields, append, remove } = useFieldArray({
    name: 'seeds',
    control,
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      await addSeed(data);

      dialogInput.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div className="flex items-center justify-end gap-2">
      <DropdownMenu onOpenChange={dropdown.set}>
        <DropdownMenuTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" size="sm">
            Add Seed via
            <ChevronDown
              className={cn('ml-2 h-4 w-4 transition duration-300 ease-in-out', dropdown.value && 'rotate-180')}
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => dialogInput.set(true)}>
            <TextCursorInput className="mr-2 size-4" />
            Input
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              dialogUpload.set(true);
            }}
          >
            <UploadCloud className="mr-2 size-4" />
            Upload
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Add via input */}
      <Dialog open={dialogInput.value} onOpenChange={dialogInput.set}>
        <DialogContent className="p-0 sm:max-w-[52rem]">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Seed</DialogTitle>
            <DialogDescription>{`Add new seed to your list. You can add multiple seed at a time.`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="">
            <div className="mt-3 grid gap-6 divide-y-2 divide-dashed px-6 pb-6">
              {fields.map((field, index) => {
                const fieldName = errors?.seeds?.[index]?.name;
                const fieldCategory = errors?.seeds?.[index]?.seedSubcategoryId;
                const fieldVariety = errors?.seeds?.[index]?.seedVarietyId;
                const fieldBreed = errors?.seeds?.[index]?.seedBreedId;

                return (
                  <div key={field.id} className={cn('grid gap-4', index === 0 ? '' : 'pt-6')}>
                    <div className="flex items-center gap-3">
                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor="name" className="pb-1 font-normal">
                          Seed Name
                        </Label>
                        <div className="flex items-center gap-4">
                          <Input
                            {...register(`seeds.${index}.name` as const)}
                            className={cn(
                              'focus-visible:ring-primary',
                              fieldName && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            type="text"
                            placeholder="Enter Seed name"
                          />
                        </div>
                        {fieldName && <p className="form-error">{`${fieldName.message}`}</p>}
                      </div>

                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor="seedSubcategoryId" className="pb-1 font-normal">
                          Crop Type
                        </Label>
                        <div className="flex items-center gap-4">
                          <Controller
                            control={control}
                            name={`seeds.${index}.seedSubcategoryId`}
                            render={({ field: { onChange, onBlur, value, ref } }) => (
                              <Select onValueChange={onChange} value={value}>
                                <SelectTrigger
                                  className={cn(
                                    'focus-visible:ring-primary',
                                    fieldVariety &&
                                      'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                                  )}
                                >
                                  <SelectValue placeholder="Select crop type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectGroup>
                                    {gState.admin.seeds.subcategory.get({ noproxy: true }).map((subcat, i) => (
                                      <SelectItem key={i} value={`${subcat.id}`}>
                                        {subcat.name}
                                      </SelectItem>
                                    ))}
                                  </SelectGroup>
                                </SelectContent>
                              </Select>
                            )}
                          />
                          <div className={cn(index === 0 && 'invisible')}>
                            <Button
                              className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                              variant="outline"
                              size="icon"
                              onClick={() => remove(index)}
                              type="button"
                            >
                              <X className="size-5" />
                            </Button>
                          </div>
                        </div>
                        {fieldVariety && <p className="form-error">{`${fieldVariety.message}`}</p>}
                      </div>
                    </div>

                    <div className="flex items-center gap-3 pr-14">
                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor={`seeds.${index}.seedVarietyId`} className="pb-1 font-normal">
                          Variety
                        </Label>
                        <div className="flex items-center gap-4">
                          <Controller
                            control={control}
                            name={`seeds.${index}.seedVarietyId`}
                            render={({ field: { onChange, onBlur, value, ref } }) => (
                              <Select onValueChange={onChange} value={value}>
                                <SelectTrigger
                                  className={cn(
                                    'focus-visible:ring-primary',
                                    fieldCategory &&
                                      'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                                  )}
                                >
                                  <SelectValue placeholder="Select variety" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectGroup>
                                    {gState.admin.seeds.variety
                                      .get({ noproxy: true })
                                      .filter((a) => a.status === 1)
                                      .map((subcat, i) => (
                                        <SelectItem key={i} value={`${subcat.id}`}>
                                          {subcat.name}
                                        </SelectItem>
                                      ))}
                                  </SelectGroup>
                                </SelectContent>
                              </Select>
                            )}
                          />
                        </div>
                        {fieldCategory && <p className="form-error">{`${fieldCategory.message}`}</p>}
                      </div>

                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor={`seeds.${index}.seedBreedId`} className="pb-1 font-normal">
                          Breed
                        </Label>
                        <div className="flex items-center gap-4">
                          <Controller
                            control={control}
                            name={`seeds.${index}.seedBreedId`}
                            render={({ field: { onChange, onBlur, value, ref } }) => (
                              <Select onValueChange={onChange} value={value}>
                                <SelectTrigger
                                  className={cn(
                                    'focus-visible:ring-primary',
                                    fieldBreed && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                                  )}
                                >
                                  <SelectValue placeholder="Select breed" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectGroup>
                                    {gState.admin.seeds.breed
                                      .get({ noproxy: true })
                                      .filter((a) => a.status === 1)
                                      .map((subcat, i) => (
                                        <SelectItem key={i} value={`${subcat.id}`}>
                                          {subcat.name}
                                        </SelectItem>
                                      ))}
                                  </SelectGroup>
                                </SelectContent>
                              </Select>
                            )}
                          />
                        </div>
                        {fieldBreed && <p className="form-error">{`${fieldBreed.message}`}</p>}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end px-6 pt-2">
              <Button
                variant="outline"
                size="icon"
                className="border-slate-300"
                type="button"
                onClick={() => append({ name: '', seedSubcategoryId: '', seedVarietyId: '', seedBreedId: '' })}
              >
                <Plus className="size-5 text-primary" />
              </Button>
            </div>

            <div className="flex justify-between gap-2 p-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              {loading.value ? (
                <ButtonLoading />
              ) : (
                <Button className="px-12" type="submit">
                  Add
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add via upload */}
      <Dialog open={dialogUpload.value} onOpenChange={dialogUpload.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-[52rem]">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Seed</DialogTitle>
            <DialogDescription>{`Upload CSV file to add seed`}</DialogDescription>
          </DialogHeader>

          <SeedCsv />

          <div className="flex justify-between gap-2 p-6">
            <DialogClose asChild>
              <Button className="px-12" variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="px-12" type="button" disabled={gState.admin.seeds.upload.length === 0}>
                  Add
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will add new seeds to your list.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    type="button"
                    onClick={async () => {
                      const seeds = {
                        seeds: gState.admin.seeds.upload.value,
                      };
                      await addSeed(JSON.parse(JSON.stringify(seeds)));
                      dialogUpload.set(false);
                    }}
                  >
                    Continue
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Action = ({ row }) => {
  const data = row.original;
  const gState = useGlobalState();
  const updateDialog = useHookstate(false);
  const loading = useHookstate(false);
  const { updateSeed } = useSeeds();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(UpdateSeedSchema),
    defaultValues: {
      seedId: `${data.id}`,
      name: data.name,
      status: `${data.status}`,
      seedSubcategoryId: `${data.seedSubcategory?.id}`,
      seedBreedId: `${data.seed_breed_id ?? ''}`,
      seedVarietyId: `${data.seed_variety_id ?? ''}`,
    },
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      await updateSeed(data);

      updateDialog.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <>
      <div className="flex justify-end gap-2">
        <div className="flex justify-end gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="h-8 px-2 lg:px-3"
                variant="outline"
                size="sm"
                onClick={() => {
                  updateDialog.set(true);
                }}
              >
                <Pencil className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit Seed</p>
            </TooltipContent>
          </Tooltip>

          {/* Update Dialog */}
          <Dialog open={updateDialog.value} onOpenChange={updateDialog.set}>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-primary">Update Seed</DialogTitle>
                <DialogDescription>{`Fill the form below to update seed`}</DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="space-y-6">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="seedId" className="pb-1 font-normal">
                      Seed ID
                    </Label>
                    <Input
                      {...register('seedId')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.seedId && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter seed id"
                      disabled
                    />
                    {errors.seedId && <p className="form-error">{`${errors.seedId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="name" className="pb-1 font-normal">
                      Seed Name
                    </Label>
                    <Input
                      {...register('name')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.name && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter seed name"
                    />
                    {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="seedSubcategoryId" className="pb-1 font-normal">
                      Crop Type
                    </Label>
                    <Controller
                      control={control}
                      name="seedSubcategoryId"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.seedSubcategoryId &&
                                'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select crop type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {gState.admin.seeds.subcategory.get({ noproxy: true }).map((subcat, i) => (
                                <SelectItem key={i} value={`${subcat.id}`}>
                                  {subcat.name}
                                </SelectItem>
                              ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.seedSubcategoryId && <p className="form-error">{`${errors.seedSubcategoryId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="seedVarietyId" className="pb-1 font-normal">
                      Variety
                    </Label>
                    <Controller
                      control={control}
                      name="seedVarietyId"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.seedVarietyId &&
                                'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select variety" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {gState.admin.seeds.variety
                                .get({ noproxy: true })
                                .filter((v) => v.status === 1)
                                .map((subcat, i) => (
                                  <SelectItem key={i} value={`${subcat.id}`}>
                                    {subcat.name}
                                  </SelectItem>
                                ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.seedVarietyId && <p className="form-error">{`${errors.seedVarietyId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="seedBreedId" className="pb-1 font-normal">
                      Breed
                    </Label>
                    <Controller
                      control={control}
                      name="seedBreedId"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.seedBreedId &&
                                'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select breed" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {gState.admin.seeds.breed
                                .get({ noproxy: true })
                                .filter((v) => v.status === 1)
                                .map((subcat, i) => (
                                  <SelectItem key={i} value={`${subcat.id}`}>
                                    {subcat.name}
                                  </SelectItem>
                                ))}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.seedBreedId && <p className="form-error">{`${errors.seedBreedId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="status" className="pb-1 font-normal">
                      Status
                    </Label>
                    <Controller
                      control={control}
                      name="status"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="0">Disabled</SelectItem>
                              <SelectItem value="1">Active</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
                  </div>
                </div>

                <div className="flex justify-between gap-2 pt-6">
                  <DialogClose asChild>
                    <Button className="px-12" variant="outline" type="button">
                      Cancel
                    </Button>
                  </DialogClose>
                  {loading.value ? (
                    <ButtonLoading />
                  ) : (
                    <Button className="px-12" type="submit">
                      Update
                    </Button>
                  )}
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  );
};
