'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { MoreHorizontal, Pencil, ToggleLeft, ToggleRight, UserPlus } from 'lucide-react';
import { Controller, useForm } from 'react-hook-form';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { getUserType, isSuperAdmin, ROLES } from '@/lib/constants';
import useUsers, { EditUserSchema, UserSchema } from '@/lib/hooks/useUsers';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

export const columns = [
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    accessorFn: (row) => {
      const { first_name, last_name } = row[getUserType(row.user_type)] || {};
      return `${first_name || ''} ${last_name || ''}`;
    },
  },
  {
    id: 'user_role',
    header: ({ column }) => <DataTableColumnHeader column={column} title="User Role" />,
    cell: ({ row }) => {
      const data = row.original;

      return <Badge variant={'outline'}>{ROLES[data.user_type]}</Badge>;
    },
    accessorFn: (row) => `${ROLES[row.user_type]}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'username',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Username" />,
    accessorFn: (row) => `${row.email}`,
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const variant = {
        1: 'success',
        0: 'error',
      };

      return (
        <div className="flex items-center gap-3">
          <div className={cn('rounded-full w-2 h-2', data.status === 1 ? 'bg-green-500' : 'bg-red-500')}></div>
          <div>{data.status === 1 ? 'Active' : 'Inactive'}</div>
        </div>
      );
    },
    // accessorFn: (row) => `${row.status === 1 ? 'Active' : 'Inactive'}`,
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'actions',
    header: ({ column }) => <ActionHeader />,
    cell: ({ row }) => <Action row={row} />,
  },
];

const ActionHeader = () => {
  const gStateP = useGlobalStatePersist();
  const { addUser } = useUsers();
  const _isSuperAdmin = isSuperAdmin(gStateP['user'].value ? gStateP['user']['user']['user_type'].value : 0);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(UserSchema),
    defaultValues: {
      userType: '',
      username: '',
      firstName: '',
      lastName: '',
      password: '',
      password_confirmation: '',
    },
  });

  return (
    <>
      {_isSuperAdmin && (
        <Dialog>
          <DialogTrigger asChild>
            <div className="flex justify-end">
              <Button className="h-8 px-2 lg:px-3" size="sm">
                <UserPlus className="mr-2 size-4" />
                Add User
              </Button>
            </div>
          </DialogTrigger>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-primary">Add User</DialogTitle>
              <DialogDescription>{`Fill up the forms to add a user. Click "Add" when you're ready.`}</DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(addUser)}>
              <div className="mt-3 grid grid-cols-2 gap-4">
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="email" className="pb-1 font-normal">
                    User Role
                  </Label>
                  <Controller
                    control={control}
                    name="userType"
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} defaultValue={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errors.userType && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select user role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="899">Field Relation Officer</SelectItem>
                            <SelectItem value="199">Encoder 1</SelectItem>
                            <SelectItem value="198">Encoder 2</SelectItem>
                            <SelectItem value="299">Operation 1</SelectItem>
                            <SelectItem value="298">Operation 2</SelectItem>
                            <SelectItem value="399">Sale 1</SelectItem>
                            <SelectItem value="398">Sale 2</SelectItem>
                            <SelectItem value="499">Finance 1</SelectItem>
                            <SelectItem value="498">Finance 2</SelectItem>
                            <SelectItem value="997">Product Manager</SelectItem>
                            <SelectItem value="996">Wallet Manager</SelectItem>
                            <SelectItem value="995">Invoice Manager</SelectItem>
                            <SelectItem value="59">Head Agronomist</SelectItem>
                            <SelectItem value="50">Agronomist</SelectItem>
                            {gStateP['user'].value && gStateP['user']['isSuperAdmin'].value && (
                              <SelectItem value="998">Admin</SelectItem>
                            )}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.userType && <p className="form-error">{`${errors.userType.message}`}</p>}
                </div>

                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="username" className="pb-1 font-normal">
                    Username
                  </Label>
                  <Input
                    {...register('username')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.username && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter username"
                  />
                  {errors.username && <p className="form-error">{`${errors.username.message}`}</p>}
                </div>

                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="firstName" className="pb-1 font-normal">
                    First Name
                  </Label>
                  <Input
                    {...register('firstName')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.firstName && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter firstname"
                  />
                  {errors.firstName && <p className="form-error">{`${errors.firstName.message}`}</p>}
                </div>

                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="lastName" className="pb-1 font-normal">
                    Last Name
                  </Label>
                  <Input
                    {...register('lastName')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.lastName && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter lastname"
                  />
                  {errors.lastName && <p className="form-error">{`${errors.lastName.message}`}</p>}
                </div>

                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="password" className="pb-1 font-normal">
                    Password
                  </Label>
                  <InputPassword
                    {...register('password')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.password && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    placeholder="Min. 8 characters"
                  />
                  {errors.password && <p className="form-error">{`${errors.password.message}`}</p>}
                </div>

                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="password_confirmation" className="pb-1 font-normal">
                    Re-Type Password
                  </Label>
                  <InputPassword
                    {...register('password_confirmation')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.password_confirmation && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    placeholder="Min. 8 characters"
                  />
                  {errors.password_confirmation && (
                    <p className="form-error">{`${errors.password_confirmation.message}`}</p>
                  )}
                </div>
              </div>

              <div className="flex justify-between gap-2 pt-6">
                <DialogClose asChild>
                  <Button className="px-12" variant="outline" type="button">
                    Cancel
                  </Button>
                </DialogClose>
                <Button className="px-12" type="submit">
                  Add
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

const Action = ({ row }) => {
  const data = row.original;
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { deactivateUser, activateUser, editUser } = useUsers();

  const _isSuperAdmin = isSuperAdmin(gStateP['user'].value ? gStateP['user']['user']['user_type'].value : 0);
  const deactivateConfirm = useHookstate(false);
  const activateConfirm = useHookstate(false);
  const editDialog = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(EditUserSchema),
    defaultValues: {
      userType: '',
      username: '',
      firstName: '',
      lastName: '',
      password: '',
      password_confirmation: '',
    },
  });

  return (
    <>
      {_isSuperAdmin && (
        <div className="flex justify-end gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="size-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />

              <DropdownMenuItem
                className="flex items-center"
                onClick={() => {
                  gState.admin.users['edit'].set(data);
                  editDialog.set(true);

                  const type = getUserType(data.user_type);

                  reset({
                    userType: `${data.user_type}`,
                    username: data.email,
                    firstName: data[type]?.first_name,
                    lastName: data[type]?.last_name,
                    password: '',
                    password_confirmation: '',
                  });
                }}
              >
                <Pencil className="mr-2 size-4" />
                <span>Edit</span>
              </DropdownMenuItem>

              {data.status === 1 && (
                <>
                  <DropdownMenuItem className="flex items-center" onClick={() => deactivateConfirm.set(true)}>
                    <ToggleLeft className="mr-2 size-4" />
                    <span>Deactivate</span>
                  </DropdownMenuItem>
                </>
              )}

              {data.status === 0 && (
                <>
                  <DropdownMenuItem className="flex items-center" onClick={() => activateConfirm.set(true)}>
                    <ToggleRight className="mr-2 size-4" />
                    <span>Activate</span>
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Deactivate Confirmation */}
          <AlertDialog open={deactivateConfirm.value} onOpenChange={deactivateConfirm.set}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action will deactivate the account associated with the username: {data.email}. This process
                  cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => {
                    deactivateUser(data.id);
                  }}
                >
                  Continue
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* Activate Confirmation */}
          <AlertDialog open={activateConfirm.value} onOpenChange={activateConfirm.set}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action will activate the account associated with the username: {data.email}. This process cannot
                  be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => {
                    activateUser(data.id);
                  }}
                >
                  Continue
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* Edit Dialog */}
          <Dialog open={editDialog.value} onOpenChange={editDialog.set}>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-primary">Edit User</DialogTitle>
                <DialogDescription>
                  {`Fill up the forms to update a user. Click "Update" when you're ready.`}
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit(editUser)}>
                <div className="mt-3 grid grid-cols-2 gap-4">
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="email" className="pb-1 font-normal">
                      User Role
                    </Label>
                    <Controller
                      control={control}
                      name="userType"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} defaultValue={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.userType && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="899">Field Operation Officer</SelectItem>
                              <SelectItem value="199">Encoder 1</SelectItem>
                              <SelectItem value="198">Encoder 2</SelectItem>
                              <SelectItem value="299">Operation 1</SelectItem>
                              <SelectItem value="298">Operation 2</SelectItem>
                              <SelectItem value="399">Sale 1</SelectItem>
                              <SelectItem value="398">Sale 2</SelectItem>
                              <SelectItem value="499">Finance 1</SelectItem>
                              <SelectItem value="498">Finance 2</SelectItem>
                              <SelectItem value="997">Product Manager</SelectItem>
                              <SelectItem value="996">Wallet Manager</SelectItem>
                              <SelectItem value="995">Invoice Manager</SelectItem>
                              <SelectItem value="59">Head Agronomist</SelectItem>
                              <SelectItem value="50">Agronomist</SelectItem>
                              {gStateP['user'].value && gStateP['user']['isSuperAdmin'].value && (
                                <SelectItem value="998">Admin</SelectItem>
                              )}
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.userType && <p className="form-error">{`${errors.userType.message}`}</p>}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="username" className="pb-1 font-normal">
                      Username
                    </Label>
                    <Input
                      {...register('username')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.username && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter username"
                    />
                    {errors.username && <p className="form-error">{`${errors.username.message}`}</p>}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="firstName" className="pb-1 font-normal">
                      First Name
                    </Label>
                    <Input
                      {...register('firstName')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.firstName && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter firstname"
                    />
                    {errors.firstName && <p className="form-error">{`${errors.firstName.message}`}</p>}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="lastName" className="pb-1 font-normal">
                      Last Name
                    </Label>
                    <Input
                      {...register('lastName')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.lastName && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter lastname"
                    />
                    {errors.lastName && <p className="form-error">{`${errors.lastName.message}`}</p>}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="password" className="pb-1 font-normal">
                      Password
                    </Label>
                    <InputPassword
                      {...register('password')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.password && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      placeholder="Min. 8 characters"
                    />
                    {errors.password && <p className="form-error">{`${errors.password.message}`}</p>}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor="password_confirmation" className="pb-1 font-normal">
                      Re-Type Password
                    </Label>
                    <InputPassword
                      {...register('password_confirmation')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.password_confirmation && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      placeholder="Min. 8 characters"
                    />
                    {errors.password_confirmation && (
                      <p className="form-error">{`${errors.password_confirmation.message}`}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-between gap-2 pt-6">
                  <DialogClose asChild>
                    <Button className="px-12" variant="outline" type="button">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button className="px-12" type="submit">
                    Update
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      )}
    </>
  );
};
