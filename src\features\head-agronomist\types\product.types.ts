import { IMeta } from '@/lib/types';

export enum EProducttype {
  CROPS = '0',
  SEEDS = '1',
  FERTILIZER = '2',
  CROP_PROTECTION = '3',
  OTHERS = '4',
}

export interface IProductSubcategory {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface ISeed {
  id: number;
  name: string;
  seed_subcategory_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  seed_variety_id: number | null;
  seed_breed_id: number | null;
  seedSubcategory: IProductSubcategory;
  seedBreed: any | null; // Assuming 'any' for now as structure is not provided
  seedVariety: any | null; // Assuming 'any' for now as structure is not provided
}

export interface IFertilizer {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
  grade: any | null;
  type: any | null;
}

export interface IChemical {
  id: number;
  name: string;
  brand: string;
  status: number;
  chemical_subcategory_id: number;
  created_at: string;
  updated_at: string;
  chemical_mode_of_action_id: null;
  chemicalActiveIngredients: unknown[];
  chemicalModeOfAction: null;
  chemicalSubcategory: IChemicalSubcategory;
}

export interface IChemicalSubcategory {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface IOtherProduct {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
  brand: string;
}

export interface IProductData {
  id: number;
  product_type: number;
  price: number;
  vatable: string;
  stocks: number;
  stocks_warning: number;
  code: string;
  weight: number;
  unit: string;
  image: string;
  description: string;
  seed_id: number;
  crop_id: number | null;
  fertilizer_id: number | null;
  chemical_id: number | null;
  status: number;
  created_at: string;
  updated_at: string;
  other_product_id: number | null;
  fertilizer: IFertilizer;
  otherProduct: IOtherProduct;
  crop: any | null; // Assuming 'any' for now as structure is not provided
  chemical: IChemical;
  seed: ISeed;
}

export interface IProductResponse {
  meta: IMeta;
  data: IProductData[];
}
