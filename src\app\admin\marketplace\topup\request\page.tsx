'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { PaperclipIcon, Trash2Icon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Controller, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useFinance from '@/lib/hooks/admin/useFinance';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn, urlify } from '@/lib/utils';

export default function RequestPage() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const { groups, getGroupsPublic } = useCreditScoreMgt();
  const data = useHookstate(gStateP.selected['farmer']);
  const address = data.value && data['farmer']['address'].value ? JSON.parse(data['farmer']['address'].value) : {};

  const loading = useHookstate(false);
  const { reqTopup } = useFinance();

  const [files, setFiles] = useState([]);
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc', '.docx'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.doc', '.docx'],
    },
    onDrop: (acceptedFiles) => {
      const newFiles = acceptedFiles.filter((file) => !files.some((f) => f.name === file.name));
      setFiles((v) => [...v, ...newFiles]);
    },
    multiple: true,
  });

  const removeFile = (file) => {
    const newFiles = [...files];
    newFiles.splice(newFiles.indexOf(file), 1);
    setFiles(newFiles);
  };

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    setValue,
    reset,
    watch,
  } = useForm({
    defaultValues: {
      // financing reference
      creditScoreGroupId: '',
      financeReferenceNumber: '',
      transferReferenceNumber: '',

      // loan amount summary
      totalLoanAmount: '',
      interestAmount: '',
      loanPrincipalAmount: '',
      financeCompanyProcessingFee: '0',
      netPrincipalAmount: '',

      // Net Principal Deductions
      cropInsuranceAmount: '0',
      advisoryServices: '0',

      // E-Wallet Top-Up (Post-Deductions)
      amount: '',

      // Loan Term Details
      loanTerm: '120',
      dueAt: '',
      remarks: '',

      applicationDocuments: '',
      affidavitDocuments: '',
      proofDocuments: '',
    },
  });
  const watchTotalLoanAmount = watch('totalLoanAmount');
  const watchInterestAmount = watch('interestAmount');
  const watchLoanPrincipalAmount = watch('loanPrincipalAmount');
  const watchFinanceCompanyProcessingFee = watch('financeCompanyProcessingFee', '0');
  const watchNetPrincipalAmount = watch('netPrincipalAmount');
  const watchCropInsuranceAmount = watch('cropInsuranceAmount');
  const watchAdvisoryServices = watch('advisoryServices');

  const onSubmit = async (data: any) => {
    try {
      loading.set(true);

      const formData = {
        ...data,
        userId: gStateP.selected['farmer']['id'].value,
        documents: files,
      };

      console.log('onSubmit: ', formData);
      await reqTopup(formData);

      router.push('/admin/marketplace');
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  // calculate loan principal
  useEffect(() => {
    const totalLoanAmount = Number(watchTotalLoanAmount);
    const interestAmount = Number(watchInterestAmount);

    if (!isNaN(totalLoanAmount) && !isNaN(interestAmount)) {
      setValue('loanPrincipalAmount', `${totalLoanAmount - interestAmount}`);
    }
  }, [watchTotalLoanAmount, watchInterestAmount]);

  // calculate net principal
  useEffect(() => {
    const loanPrincipalAmount = Number(watchLoanPrincipalAmount);
    const financeCompanyProcessingFee = Number(watchFinanceCompanyProcessingFee);

    if (!isNaN(loanPrincipalAmount) && !isNaN(financeCompanyProcessingFee)) {
      setValue('netPrincipalAmount', `${loanPrincipalAmount - financeCompanyProcessingFee}`);
    }
  }, [watchLoanPrincipalAmount, watchFinanceCompanyProcessingFee]);

  useEffect(() => {
    const cropInsuranceAmount = Number(watchCropInsuranceAmount);
    const advisoryServices = Number(watchAdvisoryServices) || 0;
    const netPricipalAmount = Number(watchNetPrincipalAmount);

    if (!isNaN(cropInsuranceAmount) && !isNaN(advisoryServices) && !isNaN(netPricipalAmount)) {
      setValue('amount', `${netPricipalAmount - (cropInsuranceAmount + advisoryServices)}`);
    }
  }, [watchCropInsuranceAmount, watchAdvisoryServices, watchNetPrincipalAmount]);

  useEffect(() => {
    getGroupsPublic();
  }, []);

  return (
    <div className="p-8">
      <div className="rounded-lg p-6 shadow-md shadow-slate-300">
        <div className="flex gap-8">
          <div>
            {/* Profile Image */}
            <div>
              <img
                className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                src={data.user_img.value ? urlify(data.user_img.value, 'users/profile') : '/assets/user-default.jpg'}
                alt=""
              />
            </div>

            <div className="mt-4 flex justify-center">
              <Button
                onClick={() => {
                  router.push(`/admin/account-info/?id=${data['farmer'].user_id.value}`);
                }}
              >
                View more info
              </Button>
            </div>
          </div>

          <div className="flex-1">
            <div className="text-xl font-bold leading-loose text-indigo-900">Account Information</div>
            <dl className="grid grid-cols-2 gap-4">
              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data.farmer.first_name.value} ${data.farmer.last_name.value}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`ID${data.id.value.toString().padStart(9, '0')}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data.farmer.mobile_number.value ?? ''}`}
                  </dd>
                </div>
              </div>

              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${new Date(data.farmer.birth_date.value).toLocaleString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data.email.value}`}</dd>
                </div>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <form id="request-form" className="mt-8 grid grid-cols-2 gap-8" onSubmit={handleSubmit(onSubmit)}>
        <div>
          <div className="text-xl font-bold leading-loose text-kitaph-primary">Financing References</div>
          <div className="my-6 grid gap-6">
            {/* Financing Group */}
            <div className="grid w-full items-center gap-3">
              <Label htmlFor="creditScoreGroupId" className="pb-1 font-normal">
                Financing Company <strong className="text-red-500">*</strong>
              </Label>
              <Controller
                control={control}
                name="creditScoreGroupId"
                rules={{ required: 'Financing company is required' }}
                render={({ field: { onChange, onBlur, value, ref } }) => (
                  <Select onValueChange={onChange} defaultValue={value}>
                    <SelectTrigger
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.creditScoreGroupId &&
                          'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                      )}
                    >
                      <SelectValue placeholder="Select financing company" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {groups.get({ noproxy: true }).map((group) => (
                          <SelectItem key={group.id} value={`${group.id}`}>
                            {group.name}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.creditScoreGroupId && <p className="form-error">{`${errors.creditScoreGroupId.message}`}</p>}
            </div>

            {/* Loan Ref No. */}
            <div className="grid gap-3">
              <Label htmlFor="financeReferenceNumber">
                Loan Reference No. <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('financeReferenceNumber', {
                  required: 'Loan reference no. is required',
                })}
                className={cn(errors.financeReferenceNumber && 'border-red-500 focus-visible:ring-red-500')}
                type="text"
                placeholder="Enter Reference No."
              />
              {errors.financeReferenceNumber && (
                <p className="form-error">{`${errors.financeReferenceNumber.message}`}</p>
              )}
            </div>

            {/* Transfer Ref No. */}
            <div className="grid gap-3">
              <Label htmlFor="transferReferenceNumber">Transfer Reference No.</Label>
              <Input
                {...register('transferReferenceNumber')}
                className={cn(errors.transferReferenceNumber && 'border-red-500 focus-visible:ring-red-500')}
                type="text"
                placeholder="Enter Reference No."
              />
              {errors.transferReferenceNumber && (
                <p className="form-error">{`${errors.transferReferenceNumber.message}`}</p>
              )}
            </div>

            <div className="text-xl font-bold leading-loose text-kitaph-primary">Loan Amount Summary</div>

            {/* Total Loan Amount */}
            <div className="grid gap-3">
              <Label htmlFor="totalLoanAmount">
                Total Loan Amount with interest at maturity <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('totalLoanAmount', {
                  required: 'Total Loan amount is required',
                  validate: {
                    isPositive: (v) => Number(v) > 0 || 'Amount must be greater than 0',
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.totalLoanAmount && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱1000.00"
              />
              {errors.totalLoanAmount && <p className="form-error">{`${errors.totalLoanAmount.message}`}</p>}
            </div>

            {/* Interest Amount */}
            <div className="grid gap-3">
              <Label htmlFor="interestAmount">
                Interest Amount <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('interestAmount', {
                  required: 'Total Loan amount is required',
                  validate: {
                    isPositive: (v) => Number(v) > 0 || 'Amount must be greater than 0',
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.interestAmount && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱500.00"
              />
              {errors.interestAmount && <p className="form-error">{`${errors.interestAmount.message}`}</p>}
            </div>

            {/* Loan Principal Amount */}
            <div className="grid gap-3">
              <Label htmlFor="loanPrincipalAmount">
                Loan Principal Amount <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('loanPrincipalAmount', {
                  required: 'Loan principal amount is required',
                  validate: {
                    isPositive: (v) => Number(v) > 0 || 'Amount must be greater than 0',
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.loanPrincipalAmount && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱500.00"
              />
              {errors.loanPrincipalAmount && <p className="form-error">{`${errors.loanPrincipalAmount.message}`}</p>}
            </div>

            {/* Financing Company Processing Fee */}
            <div className="grid gap-3">
              <Label htmlFor="financeCompanyProcessingFee">Financing Company Processing Fee</Label>
              <Input
                {...register('financeCompanyProcessingFee', {
                  required: false,
                  validate: {
                    isPositive: (v) => (v ? Number(v) > 0 || 'Amount must be greater than 0' : true),
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.financeCompanyProcessingFee && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱500.00"
              />
              {errors.financeCompanyProcessingFee && (
                <p className="form-error">{`${errors.financeCompanyProcessingFee.message}`}</p>
              )}
            </div>

            {/* Net Principal */}
            <div className="grid gap-3">
              <Label htmlFor="netPrincipalAmount">
                Net Principal <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('netPrincipalAmount', {
                  required: 'Net principal is required',
                  validate: {
                    isPositive: (v) => Number(v) > 0 || 'Amount must be greater than 0',
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.netPrincipalAmount && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱500.00"
              />
              {errors.netPrincipalAmount && <p className="form-error">{`${errors.netPrincipalAmount.message}`}</p>}
            </div>

            <div className="text-xl font-bold leading-loose text-kitaph-primary">Net Principal Deductions</div>
            {/* Crop Insurance Amount */}
            <div className="grid gap-3">
              <Label htmlFor="cropInsuranceAmount">
                Crop Insurance Amount <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('cropInsuranceAmount', {
                  required: 'Crop insurance amount is required',
                  validate: {
                    isPositive: (v) => Number(v) >= 0 || 'Amount must be non-negative',
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.cropInsuranceAmount && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱500.00"
              />
              {errors.cropInsuranceAmount && <p className="form-error">{`${errors.cropInsuranceAmount.message}`}</p>}
            </div>

            {/* Advisory Services */}
            <div className="grid gap-3">
              <Label htmlFor="advisoryServices">Advisory Services</Label>
              <Input
                {...register('advisoryServices', {
                  required: false,
                  validate: {
                    isPositive: (v) => (v ? Number(v) >= 0 || 'Amount must be greater than 0' : true),
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.advisoryServices && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱500.00"
              />
              {errors.advisoryServices && <p className="form-error">{`${errors.advisoryServices.message}`}</p>}
            </div>

            <div className="text-xl font-bold leading-loose text-kitaph-primary">E-Wallet Top-Up (Post-Deductions)</div>
            {/* E-Wallet Top up */}
            <div className="grid gap-3">
              <Label htmlFor="amount">
                E-Wallet Top up <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('amount', {
                  required: 'E-Wallet top up is required',
                  validate: {
                    isPositive: (v) => Number(v) > 0 || 'Amount must be greater than 0',
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.amount && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g ₱500.00"
              />
              {errors.amount && <p className="form-error">{`${errors.amount.message}`}</p>}
            </div>
          </div>
        </div>

        <div>
          <div className="text-xl font-bold leading-loose text-kitaph-primary">Loan Term Details</div>
          <div className="my-6 grid gap-6">
            {/* Loan Term */}
            <div className="grid gap-3">
              <Label htmlFor="loanTerm">
                Loan Term (in Days) <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('loanTerm', {
                  required: 'Loan Term is required',
                  validate: {
                    isPositive: (v) => Number(v) > 0 || 'Amount must be greater than 0',
                  },
                  valueAsNumber: true,
                })}
                className={cn(errors.loanTerm && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0}
                placeholder="e.g 120"
              />
              {errors.loanTerm && <p className="form-error">{`${errors.loanTerm.message}`}</p>}
            </div>

            {/* Due Date */}
            <div className="grid gap-3">
              <Label htmlFor="dueAt">
                Due Date <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('dueAt', {
                  required: 'Financing reference no. is required',
                })}
                className={cn(errors.dueAt && 'border-red-500 focus-visible:ring-red-500')}
                type="date"
                placeholder="Due Date"
                min={format(new Date(), 'yyyy-MM-dd')}
              />
              {errors.dueAt && <p className="form-error">{`${errors.dueAt.message}`}</p>}
            </div>

            <div>
              <div className="text-xl font-bold leading-loose text-kitaph-primary">Remarks</div>

              <div className="grid gap-3">
                {/* <Label htmlFor="remarks">Remarks</Label> */}
                <Textarea
                  {...register('remarks')}
                  className={cn(errors.remarks && 'border-red-500 focus-visible:ring-red-500')}
                  placeholder="Enter remarks here"
                />
                {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
              </div>
            </div>

            <div className="text-xl font-bold leading-loose text-kitaph-primary">Attachments</div>
            {/* Application Form */}
            <div className="grid gap-3">
              <Label htmlFor="applicationDocuments">
                Application Form <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('applicationDocuments', {
                  required: 'Application form is required',
                })}
                className={cn(errors.applicationDocuments && 'border-red-500 focus-visible:ring-red-500')}
                type="file"
              />
              {errors.applicationDocuments && <p className="form-error">{`${errors.applicationDocuments.message}`}</p>}
            </div>

            {/* Affidavit Form */}
            <div className="grid gap-3">
              <Label htmlFor="affidavitDocuments">
                Affidavit Form <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('affidavitDocuments', {
                  required: 'Affidavit form is required',
                })}
                className={cn(errors.affidavitDocuments && 'border-red-500 focus-visible:ring-red-500')}
                type="file"
              />
              {errors.affidavitDocuments && <p className="form-error">{`${errors.affidavitDocuments.message}`}</p>}
            </div>

            {/* Proof of Transfer */}
            <div className="grid gap-3">
              <Label htmlFor="proofDocuments">
                Proof of Transfer <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('proofDocuments', {
                  required: 'Proof of Transfer is required',
                })}
                className={cn(errors.proofDocuments && 'border-red-500 focus-visible:ring-red-500')}
                type="file"
              />
              {errors.proofDocuments && <p className="form-error">{`${errors.proofDocuments.message}`}</p>}
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Others</CardTitle>
              <CardDescription>Add your additional attachments here</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                {/* Image Dropzone */}
                <div className="pt-2">
                  <div className="border-spacing-4 cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                    <div {...getRootProps({ className: 'dropzone' })}>
                      <input {...getInputProps()} />

                      {/* eslint-disable-next-line jsx-a11y/alt-text */}
                      <PaperclipIcon className="mx-auto mb-2 size-12 text-gray-500" />
                      <p>{`Drag 'n' drop files here, or`}</p>

                      <div className="py-4 font-bold text-primary">Browse</div>
                    </div>
                  </div>
                </div>

                {files.length > 0 && (
                  <div className="grid gap-2">
                    {files.map((file) => {
                      return (
                        <div
                          key={file.name}
                          className="flex items-center justify-between rounded-md border border-green-500 px-4 py-1"
                        >
                          <div>{file.name}</div>

                          <Button
                            className="size-8 rounded-full bg-red-50 hover:bg-red-100"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeFile(file)}
                          >
                            <Trash2Icon className="size-4 text-red-500" />
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </form>

      <div className="mt-6 flex justify-end gap-4">
        <Button variant="outline" onClick={() => router.push('/admin/marketplace')}>
          Cancel
        </Button>
        {loading.value ? (
          <ButtonLoading />
        ) : (
          <Button type="submit" form="request-form">
            Submit Request
          </Button>
        )}
      </div>
    </div>
  );
}
