'use client';

import { ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

import Step0 from '@/components/layout/Stepper/Step0';
import Step1 from '@/components/layout/Stepper/Step1';
import Step2 from '@/components/layout/Stepper/Step2';
import Step3 from '@/components/layout/Stepper/Step3';
import Step4 from '@/components/layout/Stepper/Step4';
import Step5 from '@/components/layout/Stepper/Step5';
import Step6 from '@/components/layout/Stepper/Step6';
import UpdateFarmerStepper from '@/components/layout/Stepper/UpdateFarmerStepper';
import { Button } from '@/components/ui/button';

import { STEPPER_FORM } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';

export default function UpdateFarmerInfo() {
  const router = useRouter();
  const gState = useGlobalState();

  return (
    <div className="flex-1 p-12">
      <div className="">
        {/* Header */}
        <div className="">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ChevronLeft className="size-4" />
          </Button>

          <div className="pb-9 pt-6">
            <div className="font-dmSans font-medium text-primary md:text-xl lg:text-2xl">Update Information</div>
            <div className="font-dmSans text-gray-500">
              {`Simply choose the section, make your desired changes, and click "Submit" below.`}
            </div>
          </div>
        </div>

        <UpdateFarmerStepper />

        {gState.stepper.activeStep.value === 0 && <Step0 />}
        {gState.stepper.activeStep.value === 1 && <Step1 />}
        {gState.stepper.activeStep.value === 2 && <Step2 />}
        {gState.stepper.activeStep.value === 3 && <Step3 />}
        {gState.stepper.activeStep.value === 4 && <Step4 />}
        {gState.stepper.activeStep.value === 5 && <Step5 />}
        {gState.stepper.activeStep.value === 6 && <Step6 />}

        <div className="flex justify-center pt-12">
          <Button className="px-24" type="submit" size="lg" form={STEPPER_FORM[gState.stepper.activeStep.value]}>
            Submit
          </Button>
        </div>

        <div className="font-dmSans pt-12 text-center text-primary">
          All details submitted will be securely stored and will be used only by KitaPH system to provide you a better
          experience.
        </div>
      </div>
    </div>
  );
}
