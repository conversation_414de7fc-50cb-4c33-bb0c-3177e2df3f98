'use client';

import { useHookstate } from '@hookstate/core';
import { Cross2Icon } from '@radix-ui/react-icons';
import { endOfDay, format, startOfDay } from 'date-fns';
import { CalendarIcon, UploadCloud } from 'lucide-react';
import dynamic from 'next/dynamic';
import Link from 'next/link';

import { AdvancedDateRangePicker, DateRange } from '@/components/ui/advance-range-date-picker';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { LoanHolderActionHeader } from '@/app/admin/(accounts)/accounts/_components/loan-holders/loan-holder-table/columns';
import { NonLoanHolderActionHeader } from '@/app/admin/(accounts)/accounts/_components/non-loan-holders/non-loan-holder-table/columns';
import { BulkImportActionHeader } from '@/app/admin/(accounts)/accounts/_components/request/members-table/bulk-import-columns';
import { CreditScoreGroupHeader } from '@/app/admin/credit-score-management/_components/group-table/columns';
import { OrdersExport } from '@/app/admin/marketplace/_components/orders-table/columns';
import { ReportsExport, ReportsFilter } from '@/app/admin/marketplace/_components/pages/reports/reports-table/columns';
import { ExportMarketplaceTopProducts } from '@/features/admin/dashboard/pages/marketplace-top-products/components/export-marketplace-top-products';
import { marketplaceTopProductsState } from '@/features/admin/dashboard/pages/marketplace-top-products/hooks/useMarketplaceTopProducts';
import { ExportTopTransactionsValue } from '@/features/admin/dashboard/pages/top-transactions-value/components/export-top-transactions-value';
import { topTransactionsValueState } from '@/features/admin/dashboard/pages/top-transactions-value/hooks/useTopTransactionsValue';
import { ExportTradingPostTopCrops } from '@/features/admin/dashboard/pages/tradingpost-top-crops/components/export-tradingpost-top-crops';
import { tradingPostTopCropsState } from '@/features/admin/dashboard/pages/tradingpost-top-crops/hooks/useTradingPostTopCrops';
import { isSoaEnabled } from '@/lib/config/features';
import { RatingType } from '@/lib/constants/enums';
import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import { useGlobalState } from '@/lib/store';
import { marketplaceReportsState } from '@/lib/store/marketplace-reports-store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { topDownState } from '@/lib/store/top-down-store';
import { cn } from '@/lib/utils';

import buyerState from '../admin/pages/trading-app/buyer/hooks/useBuyer';
import { QaActionHeader } from '../admin/pages/trading-app/qa/components/staff-table/columns';
import { ActiveActionHeader } from '../admin/product-management/chemicals/chemicals-table/active-columns';
import { ActionHeader as ChemicalActionHeader } from '../admin/product-management/chemicals/chemicals-table/columns';
import { ModeActionHeader } from '../admin/product-management/chemicals/chemicals-table/mode-columns';
import { ActionHeader as SubcategoryActionHeader } from '../admin/product-management/chemicals/chemicals-table/subcategory-columns';
import { ActionHeader } from '../admin/product-management/crops/crops-table/columns';
import { ActionHeader as FertilizerActionHeader } from '../admin/product-management/fertilizer/fertilizer-table/columns';
import { OtherProductActionHeader } from '../admin/product-management/others/others-table/columns';
import { BreedActionHeader } from '../admin/product-management/seeds/seed-table/breed-columns';
import { ActionHeader as SeedActionHeader } from '../admin/product-management/seeds/seed-table/columns';
import { ActionHeader as SeedSubcategoryActionHeader } from '../admin/product-management/seeds/seed-table/subcategory-columns';
import { VarietyActionHeader } from '../admin/product-management/seeds/seed-table/variety-columns';
import { DataTableFacetedFilter } from './table-faceted-filter';
import { DataTableFacetedFilterControlled } from './table-faceted-filter-controlled';
import { DataTableViewOptions } from './table-view-option';

const ExportSelectedSOA = dynamic(
  () => import('@/app/admin/(accounts)/account-info/_components/marketplace-table/export-selected-soa'),
  { ssr: false },
);

export function DataTableToolbar({ id = '', table, meta = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const isFiltered = table.getState().columnFilters.length > 0;

  const { groups } = useCreditScoreMgt();

  const buyerListParams = useHookstate(buyerState.params);
  const marketplaceTopProducts = useHookstate(marketplaceTopProductsState);
  const tradingPostTopCrops = useHookstate(tradingPostTopCropsState);
  const topTransactionsValue = useHookstate(topTransactionsValueState);
  const usersBulkParams = useHookstate(gState.admin.pagination.usersBulk);

  const searchHandlers = {
    transactions: (value: string) => {
      gState.admin.pagination.transaction.page.set(1);
      gState.admin.pagination.transaction.search.set(value);
    },
    'finance1-topupreqs': (value: string) => {
      gState.finance1.pagination.topupReqs.page.set(1);
      gState.finance1.pagination.topupReqs.search.set(value);
    },
    'finance2-topupreqs': (value: string) => {
      gState.finance1.pagination.topupReqs.page.set(1);
      gState.finance1.pagination.topupReqs.search.set(value);
    },
    'buyer-list': (value: string, buyerListParams) => {
      buyerListParams.page.set(1);
      buyerListParams.search.set(value);
    },
    crops: (value: string) => {
      if (gState.admin.pagination.crops.page.value > 1) {
        gState.admin.pagination.crops.page.set(1);
      }
      gState.admin.pagination.crops.search.set(value);
    },
    'bulk-import': (value: string) => {
      if (usersBulkParams.page.value > 1) {
        usersBulkParams.page.set(1);
      }
      usersBulkParams.search.set(value);
    },
  };

  const handleSearch = (value: string, id: string, buyerListParams, table) => {
    const handler = searchHandlers[id];
    console.log({ handler, id, value, table, buyerListParams });
    if (handler) {
      handler(value, buyerListParams);
    } else {
      table.setGlobalFilter(value);
    }
  };

  const onChange = (date: DateRange) => {
    if (date && date?.from && date?.to) {
      gState.admin.pagination.orders.startDate.set(format(date.from, 'yyyy-MM-dd'));
      gState.admin.pagination.orders.endDate.set(format(date.to, 'yyyy-MM-dd'));

      gState.admin.pagination.loanHolder.startDate.set(format(date.from, 'yyyy-MM-dd'));
      gState.admin.pagination.loanHolder.endDate.set(format(date.to, 'yyyy-MM-dd'));

      gState.admin.pagination.loanApplicants.startDate.set(format(date.from, 'yyyy-MM-dd'));
      gState.admin.pagination.loanApplicants.endDate.set(format(date.to, 'yyyy-MM-dd'));

      gState.finance1.pagination.topupReqs.startDate.set(format(date.from, 'yyyy-MM-dd'));
      gState.finance1.pagination.topupReqs.endDate.set(format(date.to, 'yyyy-MM-dd'));

      gState.finance.loanPayments.pagination.dashboard.startDate.set(format(date.from, 'yyyy-MM-dd'));
      gState.finance.loanPayments.pagination.dashboard.endDate.set(format(date.to, 'yyyy-MM-dd'));

      gState.finance.loanPayments.pagination.requests.startDate.set(format(date.from, 'yyyy-MM-dd'));
      gState.finance.loanPayments.pagination.requests.endDate.set(format(date.to, 'yyyy-MM-dd'));

      topDownState.pagination.startDate.set(format(date.from, 'yyyy-MM-dd'));
      topDownState.pagination.endDate.set(format(date.to, 'yyyy-MM-dd'));
      topDownState.finance.pagination.startDate.set(format(date.from, 'yyyy-MM-dd'));
      topDownState.finance.pagination.endDate.set(format(date.to, 'yyyy-MM-dd'));

      marketplaceReportsState.query.startDate.set(format(date.from, 'yyyy-MM-dd'));
      marketplaceReportsState.query.endDate.set(format(date.to, 'yyyy-MM-dd'));

      marketplaceTopProducts.merge({
        startDate: startOfDay(date.from),
        endDate: endOfDay(date.to),
      });

      tradingPostTopCrops.merge({
        startDate: startOfDay(date.from),
        endDate: endOfDay(date.to),
      });

      topTransactionsValue.merge({
        startDate: startOfDay(date.from),
        endDate: endOfDay(date.to),
      });
    }
  };

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex flex-1 flex-wrap items-center gap-2">
        {id !== 'marketplace-reports' && (
          <Input
            placeholder="Search..."
            onChange={(event) => handleSearch(event.target.value, id, buyerListParams, table)}
            className="h-8 w-full sm:w-[150px] lg:w-[250px]"
          />
        )}

        {(id === 'users' ||
          id === 'fertilizer' ||
          id === 'chemicals' ||
          id === 'chemicals-subcategory' ||
          id === 'chemicals-active' ||
          id === 'chemicals-mode' ||
          id === 'seed' ||
          id === 'seed-variety' ||
          id === 'seed-breed' ||
          id === 'credit-score-mgt-group' ||
          id === 'otherProduct' ||
          id === 'qa-staff' ||
          id === 'seed-subcategory') && (
          <>
            {table.getColumn('status') && (
              <DataTableFacetedFilter
                column={table.getColumn('status')}
                title="Status"
                options={[
                  { label: 'Active', value: '1' },
                  { label: 'Inactive', value: '0' },
                ].map((item) => ({
                  label: item.label,
                  value: item.value,
                }))}
              />
            )}
          </>
        )}

        {id === 'crops' && (
          <>
            {table.getColumn('status') && (
              <DataTableFacetedFilterControlled
                column={table.getColumn('status')}
                title="Status"
                options={[
                  { label: 'Active', value: '1' },
                  { label: 'Inactive', value: '0' },
                ].map((item) => ({
                  label: item.label,
                  value: item.value,
                }))}
                state={gState.admin.pagination.crops.status}
              />
            )}
          </>
        )}

        {id === 'chemicals' && (
          <>
            {table.getColumn('sub_category') && (
              <DataTableFacetedFilter
                column={table.getColumn('sub_category')}
                title="Sub Category"
                options={JSON.parse(JSON.stringify(gState.admin.chemicals.subcategory.get({ noproxy: true }))).map(
                  (item) => ({
                    label: item.name,
                    value: item.name,
                  }),
                )}
              />
            )}
          </>
        )}
        {id === 'seed' && (
          <>
            {table.getColumn('sub_category') && (
              <DataTableFacetedFilter
                column={table.getColumn('sub_category')}
                title="Sub Category"
                options={JSON.parse(JSON.stringify(gState.admin.seeds.subcategory.get({ noproxy: true }))).map(
                  (item) => ({
                    label: item.name,
                    value: item.name,
                  }),
                )}
              />
            )}
          </>
        )}
        {id === 'users' && (
          <>
            {table.getColumn('user_role') && (
              <DataTableFacetedFilter
                column={table.getColumn('user_role')}
                title="User Role"
                options={[
                  { label: 'Encoder 1', value: 'Encoder 1' },
                  { label: 'Encoder 2', value: 'Encoder 2' },
                  { label: 'Admin', value: 'Admin' },
                  { label: 'Super Admin', value: 'Super Admin' },
                  { label: 'Product Manager', value: 'Product Manager' },
                  { label: 'Wallet Manager', value: 'Wallet Manager' },
                  { label: 'Invoice Manager', value: 'Invoice Manager' },
                  { label: 'Finance 1', value: 'Finance 1' },
                  { label: 'Finance 2', value: 'Finance 2' },
                  { label: 'Sale 1', value: 'Sale 1' },
                  { label: 'Sale 2', value: 'Sale 2' },
                  { label: 'Operation 1', value: 'Operation 1' },
                  { label: 'Operation 2', value: 'Operation 2' },
                ].map((item) => ({
                  label: item.label,
                  value: item.value,
                }))}
              />
            )}
          </>
        )}
        {id === 'transactions' && (
          <>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  size="sm"
                  className={cn(
                    'w-[184px] h-8 pl-3 text-left font-normal',
                    !gState.admin.pagination.transaction['calendar'].value && 'text-muted-foreground',
                  )}
                >
                  {gState.admin.pagination.transaction['calendar'].value ? (
                    format(gState.admin.pagination.transaction['calendar'].value, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto size-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={gState.admin.pagination.transaction['calendar'].value}
                  onSelect={gState.admin.pagination.transaction['calendar'].set}
                  disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            {table.getColumn('status') && (
              <DataTableFacetedFilter
                column={table.getColumn('status')}
                title="Status"
                options={[
                  { label: 'Entry', value: 'Entry' },
                  { label: 'Complete', value: 'Complete' },
                  { label: 'Pending Exit', value: 'Pending Exit' },
                  { label: 'Expired', value: 'Expired' },
                ].map((item) => ({
                  label: item.label,
                  value: item.value,
                }))}
              />
            )}
          </>
        )}
        {id === 'account-nonloan' && (
          <>
            <Select
              onValueChange={(e) => gState.admin.pagination.nonLoanHolder.creditScoreGroupId.set(Number(e))}
              value={gState.admin.pagination.nonLoanHolder.creditScoreGroupId.value.toString()}
            >
              <SelectTrigger className="h-8 w-[180px]">
                <SelectValue placeholder="Theme" />
              </SelectTrigger>
              <SelectContent>
                {groups
                  .get({ noproxy: true })
                  .filter((f) => f.status === 1)
                  .map((v) => {
                    return (
                      <SelectItem key={v.id} value={`${v.id}`}>
                        {v.name}
                      </SelectItem>
                    );
                  })}
              </SelectContent>
            </Select>

            {/* {table.getColumn('rating') && (
              <DataTableFacetedFilter
                column={table.getColumn('rating')}
                title="Rating"
                options={[
                  { label: 'Excellent', value: 'Excellent' },
                  { label: 'Very Good', value: 'Very Good' },
                  { label: 'Good', value: 'Good' },
                  { label: 'Fair', value: 'Fair' },
                  { label: 'Poor', value: 'Poor' },
                ].map((item) => ({
                  label: item.label,
                  value: item.value,
                }))}
              />
            )} */}
          </>
        )}

        {isFiltered && (
          <Button variant="destructive" onClick={() => table.resetColumnFilters()} className="h-8 px-2 lg:px-3">
            Reset
            <Cross2Icon className="ml-2 size-4" />
          </Button>
        )}

        {(id === 'marketplace-orders' ||
          id === 'finance1-topupreqs' ||
          id === 'finance2-topupreqs' ||
          id === 'admin-topupreqs' ||
          id === 'loan-request-table' ||
          id === 'admin-loan-payment-table' ||
          id === 'account-loan' ||
          id === 'account-loan-applicants' ||
          id === 'admin-top-down' ||
          id === 'finance2-top-down' ||
          id === 'marketplace-reports' ||
          id === 'marketplace-top-products' ||
          id === 'tradingpost-top-crops' ||
          id === 'top-transaction-value' ||
          id === 'finance2-loan-payment-table') && (
          <div>
            <AdvancedDateRangePicker onChange={onChange} />
          </div>
        )}

        {id === 'marketplace-reports' && <ReportsFilter />}
      </div>

      <div className="flex gap-2">
        {id === 'crops' && <ActionHeader />}
        {id === 'fertilizer' && <FertilizerActionHeader />}
        {id === 'seed' && <SeedActionHeader />}
        {id === 'seed-subcategory' && <SeedSubcategoryActionHeader />}
        {id === 'seed-variety' && <VarietyActionHeader />}
        {id === 'seed-breed' && <BreedActionHeader />}
        {id === 'chemicals' && <ChemicalActionHeader />}
        {id === 'chemicals-subcategory' && <SubcategoryActionHeader />}
        {id === 'chemicals-active' && <ActiveActionHeader />}
        {id === 'chemicals-mode' && <ModeActionHeader />}
        {id === 'credit-score-mgt-group' && <CreditScoreGroupHeader />}
        {id === 'otherProduct' && <OtherProductActionHeader />}
        {id === 'account-nonloan' && (
          <NonLoanHolderActionHeader
            column={table.getColumn('rating')}
            title="Filter"
            options={[
              { label: 'Excellent (86-100%)', value: `${RatingType.EXCELLENT}` },
              { label: 'Very Good (71-85%)', value: `${RatingType.VERY_GOOD}` },
              { label: 'Good (60-70%)', value: `${RatingType.GOOD}` },
              { label: 'Fair (16-59%)', value: `${RatingType.FAIR}` },
              { label: 'Poor (1-15%)', value: `${RatingType.POOR}` },
            ].map((item) => ({
              label: item.label,
              value: item.value,
            }))}
          />
        )}
        {id === 'account-loan' && <LoanHolderActionHeader />}
        {id === 'marketplace-reports' && <ReportsExport />}
        {id === 'qa-staff' && gStateP.user.value && gStateP.user.isSuperAdmin.value && <QaActionHeader />}
        {id === 'marketplace-orders' && <OrdersExport />}
        {id === 'account-marketplace' && isSoaEnabled && <ExportSelectedSOA table={table} />}
        {id === 'marketplace-top-products' && <ExportMarketplaceTopProducts />}
        {id === 'tradingpost-top-crops' && <ExportTradingPostTopCrops />}
        {id === 'top-transaction-value' && <ExportTopTransactionsValue />}

        {/* ! Deprecated: We now have add transaction to account transaction */}
        {/* <LinkButton id={id} from="transactions" href="/admin/select-user/" linkText="Add Transaction" /> */}
        <LinkButton id={id} from="marketplace-orders" href="/admin/select-user/" linkText="Add Transaction" />
        <LinkButton id={id} from="admin-topupreqs" href="/admin/select-user/" linkText="Request Top up" />
        <LinkButton id={id} from="finance1-topupreqs" href="/finance/select-user/" linkText="Request Top up" />
        <LinkButton id={id} from="loan-request-table" href="/finance/select-user/" linkText="Add Payment" />
        <LinkButton id={id} from="admin-loan-payment-table" href="/admin/select-user/" linkText="Add Payment" />
        <LinkButton id={id} from="admin-top-down" href="/admin/select-user/" linkText="Request Top-down" />

        {/* Uploading bulk users */}
        {id === 'bulk-import' && <BulkImportActionHeader />}

        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}

const LinkButton = ({ id, href, from, linkText }) => {
  const gStateP = useGlobalStatePersist();

  return (
    <>
      {id === from && (
        <div>
          <Button
            className="h-8"
            onClick={() => {
              gStateP.selected.from.set(from);
            }}
            asChild
          >
            <Link href={href}>{linkText}</Link>
          </Button>
        </div>
      )}
    </>
  );
};
