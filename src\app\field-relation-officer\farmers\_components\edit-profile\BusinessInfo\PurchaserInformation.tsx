import React from 'react';
import { Control, Controller, FieldErrors, UseFormRegister, UseFormWatch } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { YES_NO_OPTIONS } from '@/app/field-relation-officer/_components/constants';
import { TBusinessInfoSchema } from '@/app/field-relation-officer/_components/schemas';
import { cn } from '@/lib/utils';

interface IPurchaserInformationProps {
  register: UseFormRegister<TBusinessInfoSchema>;
  control: Control<TBusinessInfoSchema>;
  errors: FieldErrors<TBusinessInfoSchema>;
  watch: UseFormWatch<TBusinessInfoSchema>;
}

const PurchaserInformation = ({ register, control, errors, watch }: IPurchaserInformationProps) => {
  return (
    <div className="mt-6">
      <FormTitle title="Purchaser Information" />
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Selling Location for Harvested Crops */}
        <FormField name="purchaserSellingLocation" label="Selling Location for Harvested Crops" errors={errors}>
          <Input
            {...register('purchaserSellingLocation')}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserSellingLocation && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Selling Location for Harvested Crops"
          />
        </FormField>

        {/* Buyer's Full Name */}
        <FormField name="purchaserFullname" label="Buyer's Full Name" errors={errors}>
          <Input
            {...register('purchaserFullname')}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserFullname && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Full Name"
          />
        </FormField>

        {/* Buyer's Contact No. */}
        <FormField name="purchaserContactNumber" label="Buyer's Contact No." errors={errors}>
          <Input
            {...register('purchaserContactNumber', {
              required: false,
              validate: {
                isValidContactNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                      'Invalid contact number format (e.g. 09123456789)'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserContactNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Contact No."
          />
        </FormField>

        <FormField
          name="isInterestedToSellAtTradingPost"
          label="Are you interested to sell at the Trading Post?"
          errors={errors}
          required
        >
          <Controller
            control={control}
            name="isInterestedToSellAtTradingPost"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
      </div>
    </div>
  );
};

export default PurchaserInformation;
