{"name": "next13-tailwind", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "start-static": "npx serve@latest out", "lint": "next lint && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:fix": "next lint --fix && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "format": "pretty-quick && tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "format:staged": "pretty-quick --staged && tsc --noEmit --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepare": "husky", "clean": "rm -rf .next --force && rm -rf node_modules --force && rm -rf out --force && rm -rf yarn.lock && rm -rf tsconfig.tsbuildinfo --force"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "43.0.0", "@ckeditor/ckeditor5-react": "9.0.0", "@hookform/resolvers": "^3.3.4", "@hookstate/core": "^4.0.1", "@hookstate/devtools": "^4.0.1", "@hookstate/localstored": "^4.0.2", "@hookstate/subscribable": "^4.0.0", "@material-tailwind/react": "^2.1.9", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-visually-hidden": "^1.1.0", "@react-pdf/renderer": "^4.2.2", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-devtools": "^5.75.5", "@tanstack/react-table": "8.15.3", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "file-saver": "^2.0.5", "framer-motion": "^12.0.6", "geist": "^1.3.0", "jszip": "^3.10.1", "lodash.merge": "^4.6.2", "lucide-react": "^0.508.0", "next": "14.2.3", "next-themes": "^0.3.0", "nuqs": "^2.3.0", "pdf-lib": "1.17.1", "react": "18.2.0", "react-csv": "^2.2.2", "react-day-picker": "^8.10.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.2", "react-icons": "^5.5.0", "react-papaparse": "^3.17.2", "react-pdf-tailwind": "^2.3.0", "react-qr-code": "^2.0.15", "react-qrcode-logo": "^3.0.0", "react-signature-canvas": "^1.1.0-alpha.2", "react-to-print": "^3.0.1", "recharts": "^2.15.1", "select-philippines-address": "^1.0.6", "sonner": "^1.4.41", "tailwind-merge": "^2.2.2", "tailwindcss": "3.4.3", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vaul": "^0.9.9", "zod": "^3.24.4"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/node": "20.12.2", "@types/react": "18.2.38", "@types/react-dom": "18.2.23", "@typescript-eslint/parser": "^7.4.0", "autoprefixer": "10.4.21", "eslint": "8.57.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^10.1.3", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "postcss": "8.5.3", "prettier": "^3.5.3", "pretty-quick": "^4.1.1", "typescript": "5.5.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}