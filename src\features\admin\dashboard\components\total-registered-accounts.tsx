'use client';

import { FC } from 'react';

import { Skeleton } from '@/components/ui/skeleton';

import { cn, formatNumberWithCommas } from '@/lib/utils';

import useDashboard from '../hooks/useDashboard';
import { getRemarks } from './select-date/util';

export const TotalRegisteredAccounts: FC = () => {
  const { registeredFarmerQuery, state } = useDashboard();
  const remarks = getRemarks(
    state.selectDate.value,
    registeredFarmerQuery.data?.current_total_account || 0,
    registeredFarmerQuery.data?.previous_total_account || 0,
  );

  return (
    <div className="flex flex-1 items-center gap-4 rounded-xl bg-adminDashboard-purple-bg p-4">
      <div className="shrink-0">
        <img className="h-12 2xl:h-10 3xl:h-12" src="/assets/dashboard/registered.png" alt="" />
      </div>

      <div>
        {registeredFarmerQuery.isLoading ? (
          <Skeleton className="mb-2 h-5 w-20" />
        ) : (
          <div className="text-lg font-bold text-adminDashboard-title 2xl:text-base 3xl:text-lg">{`${formatNumberWithCommas(registeredFarmerQuery.data.current_total_account)}`}</div>
        )}
        <div className="mt-1 text-sm text-[#425166] 2xl:text-xs 3xl:text-sm">No. of Registered</div>
        <div
          className={cn(
            'mt-0.5 text-sm 2xl:text-[0.65rem] 3xl:text-sm',
            remarks.includes('-') ? 'text-[#F24822]' : 'text-[#4079ED]',
          )}
        >
          {registeredFarmerQuery.isLoading ? <Skeleton className="h-4 w-36" /> : remarks}
        </div>
      </div>
    </div>
  );
};
