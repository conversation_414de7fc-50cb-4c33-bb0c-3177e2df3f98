'use client';

import { format, startOfDay } from 'date-fns';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { useGlobalState } from '../../store';
import { useGlobalStatePersist } from '../../store/persist';

export default function useTradingPost() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const fetchTransactions = async () => {
    try {
      const _data = await axios
        .get(`/admin/tradingpost/viewAll`, {
          params: {
            search: gState.admin.pagination.transaction.search.value,
            page: gState.admin.pagination.transaction.page.value,
            pageSize: gState.admin.pagination.transaction.pageSize.value,
            // date: startOfDay(new Date(gState.admin.pagination.transaction['calendar'].value)),
            date: format(startOfDay(new Date(gState.admin.pagination.transaction['calendar'].value)), 'yyyy-MM-dd'),
          },
        })
        .then((res) => res.data.data);
      console.log('fetchTransactions: ', _data);

      _data.data = _data.data.map((item) => {
        let grossSales = 0;
        let grossProfit = 0;

        if (item.crops.length === 0) {
          return {
            ...item,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        } else {
          const updatedCrops = item.crops.map((crop) => {
            const weight = (item.entry_weight - item.exit_weight) * (crop.percentage / 100);

            let price = Number(crop.selling_price);
            let prodPrice = Number(crop.production_price);

            if (weight > 0) {
              grossSales += weight * price;
              grossProfit += weight * (price - prodPrice);
            }

            return {
              ...crop,
              gross_sales: weight > 0 ? weight * price : 0,
              gross_profit: weight > 0 ? weight * (price - prodPrice) : 0,
              weight,
              price,
              production_price: prodPrice,
            };
          });

          return {
            ...item,
            crops: updatedCrops,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        }
      });
      console.log('fetchTransactions: ', _data);

      gState.admin.transactions.set({
        data: _data.data,
        meta: _data.meta,
        details: null,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('fetchTransactions: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const fetchMembers = async () => {
    try {
      const _data = await axios
        .get(`/admin/user/farmers/viewAll`, {
          params: {
            status: ['1'],
          },
        })
        .then((res) => res.data.data);
      console.log('fetchMembers: ', _data);

      gState.admin.members.data.set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('fetchMembers: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const fetchMembersFiltered = async () => {
    try {
      const _data = await axios
        .get(`/admin/user/farmers/viewAll`, {
          params: {
            status: gState.admin.pagination.request.status.value,
          },
        })
        .then((res) => res.data.data);
      // console.log('fetchMembersFiltered: ', _data);

      gState.admin.members.data.set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      // console.error('fetchMembersFiltered: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const fetchMemberTransaction = async (memberId: string | number) => {
    try {
      const _data = await axios
        .get(`/admin/tradingpost/transaction/view/${memberId}`, {
          params: {
            page: gState.admin.pagination.transactionMember.page.value,
            pageSize: gState.admin.pagination.transactionMember.pageSize.value,
          },
        })
        .then((res) => res.data.data);

      _data.data = _data.data.map((item) => {
        let grossSales = 0;
        let grossProfit = 0;

        if (item.crops.length === 0) {
          return {
            ...item,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        } else {
          const updatedCrops = item.crops.map((crop) => {
            const weight = (item.entry_weight - item.exit_weight) * (crop.percentage / 100);

            let price = Number(crop.selling_price);
            let prodPrice = Number(crop.production_price);

            if (weight > 0) {
              grossSales += weight * price;
              grossProfit += weight * (price - prodPrice);
            }

            return {
              ...crop,
              gross_sales: weight > 0 ? weight * price : 0,
              gross_profit: weight > 0 ? weight * (price - prodPrice) : 0,
              weight,
              price,
              production_price: prodPrice,
            };
          });

          return {
            ...item,
            crops: updatedCrops,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        }
      });

      console.log('fetchMemberTransaction: ', _data);

      gState.admin.members.transaction.set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('fetchMemberTransaction: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addTransaction = async (data) => {
    try {
      toast.loading('Adding Transaction...', {
        description: 'Please wait...',
        duration: 90000,
      });

      const _data = await axios.post(`/admin/tradingpost/manual/entry`, data).then((res) => res.data.data);
      console.log('addTransaction: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: 'Transaction added successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addTransaction: ', error);

      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const action = async (action: 'approve' | 'reject', userId) => {
    try {
      toast.loading(action === 'approve' ? 'Approving Account...' : 'Rejecting Account...', {
        description: 'Please wait...',
        duration: 90000,
      });

      const _data = await axios
        .post(`/admin/user/farmer/${action}`, {
          userId,
        })
        .then((res) => res.data.data);
      console.log('action: ', _data);
      await fetchMembersFiltered();

      toast.dismiss();
      toast.success('Success', {
        description: `${action === 'approve' ? 'Approve' : 'Reject'} Account successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('action: ', error);

      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { fetchTransactions, fetchMembers, fetchMembersFiltered, fetchMemberTransaction, addTransaction, action };
}
