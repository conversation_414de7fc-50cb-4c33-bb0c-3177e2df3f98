'use client';

import { none } from '@hookstate/core';
import { Minus, Plus, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

import { useGlobalStatePersist } from '@/lib/store/persist';

export default function CartSheet() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const [open, setOpen] = useState(false);

  const calculateTotal = () => {
    let total = 0;
    gStateP.admin.orders.data.forEach((order) => {
      total += order['price'].value * order['quantity'].value;
    });

    gStateP.admin.orders.total.set(total);
  };

  useEffect(() => {
    calculateTotal();
  }, [gStateP.admin.orders.data]);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <Button
        className="h-8 px-6"
        variant="orange"
        onClick={() => {
          if (gStateP.admin.orders.data.length === 0) {
            toast.info('Cart is empty', {
              description: 'Please add items to your cart',
            });
            return;
          }
          setOpen(true);
        }}
      >
        {`View Cart (${gStateP.admin.orders.data.length})`}
      </Button>
      <SheetContent className="flex w-[36vw] !max-w-[50vw] flex-col px-0 font-inter">
        <SheetHeader className="px-6">
          <SheetTitle>Order Summary</SheetTitle>
          {/* <SheetDescription>{`Make changes to your profile here. Click save when you're done.`}</SheetDescription> */}
        </SheetHeader>

        <ScrollArea className="flex-1 px-6">
          <div className="flex flex-col gap-4 pb-4 pt-2">
            {gStateP.admin.orders.data.map((order, index) => {
              return (
                <div key={index}>
                  <div className="flex justify-between text-sm">
                    <div className="font-medium">{order['name'].value}</div>
                    <div>
                      {Number(order['price'].value).toLocaleString('en-US', {
                        style: 'currency',
                        currency: 'PHP',
                      })}
                    </div>
                  </div>

                  <div className="mt-2 flex justify-between">
                    <div className="flex gap-2">
                      {/* quantity */}
                      <div className="flex items-center rounded-lg bg-slate-200 px-1 py-px">
                        <Button
                          className="size-5"
                          variant="blue"
                          size="icon"
                          onClick={() => {
                            if (order['quantity'].value > 1) {
                              order['quantity'].set((v) => v - 1);
                            }
                          }}
                          disabled={order['quantity'].value === 1}
                        >
                          <Minus className="size-4" />
                        </Button>

                        <div className="px-4">{order['quantity'].value}</div>

                        <Button
                          className="size-5"
                          variant="blue"
                          size="icon"
                          onClick={() => {
                            order['quantity'].set((v) => v + 1);
                          }}
                          disabled={order['data'].stocks.value - order['quantity'].value === 0}
                        >
                          <Plus className="size-4" />
                        </Button>
                      </div>

                      {/* Remove */}
                      <div>
                        <Button
                          className="size-7"
                          variant="destructive"
                          size="icon"
                          onClick={() => {
                            if (gStateP.admin.orders.data.length === 1) {
                              setOpen(false);
                            }
                            gStateP.admin.orders.data[index].set(none);
                          }}
                        >
                          <X className="size-4" />
                        </Button>
                      </div>
                    </div>

                    {order['data'].stocks.value - order['quantity'].value === 0 ? (
                      <div className="text-xs font-bold text-red-500">Out of Stock</div>
                    ) : (
                      <div className="text-xs">{`Available: ${
                        order['data'].stocks.value - order['quantity'].value
                      }`}</div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>

        <SheetFooter className="px-6">
          <div className="w-full">
            <div className="mb-6 flex justify-between border-y border-gray-200 px-4 py-3">
              <div className="font-bold">Total Amount</div>
              <div className="font-bold">
                {Number(gStateP.admin.orders.total.value).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
            <div>
              <Button
                className="w-full rounded-full"
                variant="blue"
                onClick={() => {
                  setOpen(false);
                  router.push('/admin/marketplace/order/products/checkout/');
                }}
              >
                Proceed to Checkout
              </Button>
            </div>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
