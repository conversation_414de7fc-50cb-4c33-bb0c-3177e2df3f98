'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { useGlobalState } from '../store';
import { useGlobalStatePersist } from '../store/persist';

export default function useMarketplace() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const getProducts = async (status: string[] = ['1', '0']) => {
    try {
      const _products = await axios
        .get('/admin/marketplace/product/viewAll', {
          params: {
            page: gState.admin.pagination.products.page.value,
            pageSize: gState.admin.pagination.products.pageSize.value,
            productType: gState.admin.pagination.products.productType.value,
            search: gState.admin.pagination.products.search.value,
            productTypeCategory: gState.admin.pagination.products.productTypeCategory.value,
            status,
          },
        })
        .then((res) => res.data.data);

      gState.admin.marketplace.products.set(_products);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getProducts:', error);
    }
  };

  const getDashboard = async () => {
    try {
      const _dashboard = await axios
        .get('/admin/marketplace/order/dashboard', {
          params: {
            // page: gState.admin.pagination.products.page.value,
            // pageSize: gState.admin.pagination.products.pageSize.value,
            // productType: gState.admin.pagination.products.productType.value,
            startDate: gState.admin.pagination.orders.startDate.value,
            endDate: gState.admin.pagination.orders.endDate.value,
          },
        })
        .then((res) => res.data.data);

      gState.admin.marketplace.dashboard.set(_dashboard);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getDashboard:', error);
    }
  };

  const getProductById = async (id) => {
    try {
      const _products = await axios.get(`/admin/marketplace/product/view/${id}`).then((res) => res.data.data);
      console.log('getProductById:', _products);

      gState.admin.marketplace['editProduct'].set(_products);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getProductById:', error);
    }
  };

  const getOrders = async () => {
    try {
      const _orders = await axios
        .get('/admin/marketplace/order/viewAll', {
          params: {
            page: gState.admin.pagination.orders.page.value,
            pageSize: gState.admin.pagination.orders.pageSize.value,
            status: gState.admin.pagination.orders.status.value,
            startDate: gState.admin.pagination.orders.startDate.value,
            endDate: gState.admin.pagination.orders.endDate.value,
          },
        })
        .then((res) => res.data.data);

      const _ordersExport = await axios
        .get('/admin/marketplace/order/viewAll', {
          params: {
            page: 1,
            pageSize: _orders.meta.total,
            status: gState.admin.pagination.orders.status.value,
            startDate: gState.admin.pagination.orders.startDate.value,
            endDate: gState.admin.pagination.orders.endDate.value,
          },
        })
        .then((res) => res.data.data);

      gState.admin.marketplace.orders.set(_orders);
      gState.admin.marketplace.ordersExport.set(_ordersExport);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getOrders:', error);
    }
  };

  const getOrderById = async (id) => {
    try {
      const _products = await axios.get(`/admin/marketplace/order/view/${id}`).then((res) => res.data.data);
      console.log('getOrderById:', _products);

      gStateP.admin['viewOrder'].set(_products);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getOrderById:', error);
    }
  };

  const updateOrderStatus = async (data) => {
    try {
      const _data = await axios
        .post('/admin/marketplace/order/status/update', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('updateOrderStatus:', _data);
      await Promise.all([getDashboard(), getOrders()]);

      toast('Success', {
        description: _data.message,
      });
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('updateOrderStatus:', error);

      // Display an error message to the user
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addProduct = async (data) => {
    try {
      const _data = await axios
        .post('/admin/marketplace/product/create', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('addProduct:', _data);
      await getProducts();

      toast.success('Success', {
        description: 'Product added successfully',
      });
      router.back();
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('addProduct:', error);

      // Display an error message to the user
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateProduct = async (data) => {
    try {
      const _data = await axios
        .post('/admin/marketplace/product/update', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('updateProduct:', _data);
      await getProducts();

      toast.success('Success', {
        description: 'Product updated successfully',
      });
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('updateProduct:', error);

      // Display an error message to the user
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const createOrder = async (data) => {
    try {
      const _data = await axios
        .post('/admin/marketplace/order/create', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('createOrder:', _data);
      await Promise.all([getDashboard(), getProducts()]);

      toast.success('Success', {
        description: 'Order created successfully',
      });

      router.push('/admin/marketplace/');
      setTimeout(() => {
        gStateP.admin.orders.set({
          customer: null as any,
          data: [],
          total: 0,
          shippingDate: '',
          fulfillmentType: '1',
        });
      }, 3000);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('createOrder:', error);

      // Display an error message to the user
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    addProduct,
    getProducts,
    createOrder,
    getOrders,
    getProductById,
    updateProduct,
    getDashboard,
    getOrderById,
    updateOrderStatus,
  };
}
