'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { STEPPER_FORM } from '@/lib/constants';
import useFarmer, { FarmerSchema } from '@/lib/hooks/useFarmer';
import { UserSchema } from '@/lib/hooks/useUsers';
import useSelectAddress from '@/lib/hooks/utils/useSelectAddress';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';
import { useStateContext } from '@/providers/app/state';

export default function Step0() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { updateFarmer } = useFarmer();
  const { setState, state } = useStateContext();
  const data = gStateP.admin.members['details'].get({ noproxy: true });

  const address = useSelectAddress();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      firstName: data.farmer.first_name || '',
      lastName: data.farmer.last_name || '',
      middleName: data.farmer.middle_name || '',
      birthDate: data.farmer.birth_date || '',
      placeOfBirth: data.farmer.place_of_birth || '',
      gender: data.farmer.gender || '',
      religion: data.farmer.religion || '',
      civilStatus: data.farmer.civil_status || '',
      height: data.farmer.height || '',
      weight: data.farmer.weight || '',
      mobileNumber: data.farmer.mobile_number || '',
      addressRegion: '',
      addressProvince: '',
      addressCity: '',
      addressBarangay: '',
      addressHouseNumber: '',
      addressZipCode: '',
    },
  } as any);

  const onSubmit = (_data: any) => {
    console.log('Basic Info: ', _data);
    updateFarmer({
      ..._data,
      userId: data.farmer.user_id,
    });
    // setState((v) => ({
    //   ...v,
    //   ..._data,
    //   userId: data.id,
    // }));
    // !gState.stepper.isLastStep.value && gState.stepper.activeStep.set((cur) => cur + 1);
  };

  // Update Region
  useEffect(() => {
    if (address.regionList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address);
      const _province = JSON.parse(addressDetails?.addressProvince ?? '{}');

      if (_province?.region_code) {
        const _region = address.getRegionByCode(_province.region_code);
        address.setRegionSelected(_region);
        setValue('addressRegion', JSON.stringify(_region));
      }
    }
  }, [address.regionList]);

  // Update Province
  useEffect(() => {
    if (address.provinceList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address);
      const _province = JSON.parse(addressDetails?.addressProvince ?? '{}');

      if (_province?.province_code) {
        const __province = address.getProvinceByCode(_province.province_code);
        address.setProvinceSelected(__province);
        address.setStreet(addressDetails.addressHouseNumber);
        address.setPostalCode(addressDetails.addressZipCode);

        setValue('addressProvince', JSON.stringify(__province));
        setValue('addressHouseNumber', addressDetails.addressHouseNumber);
        setValue('addressZipCode', addressDetails.addressZipCode);
      }
    }
  }, [address.provinceList]);

  // Update City
  useEffect(() => {
    if (address.cityList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address);
      const _city = JSON.parse(addressDetails?.addressCity ?? '{}');

      if (_city?.city_code) {
        const __city = address.getCityByCode(_city.city_code);
        address.setCitySelected(__city);
        setValue('addressCity', JSON.stringify(_city));
      }
    }
  }, [address.cityList]);

  // Update Barangay
  useEffect(() => {
    if (address.barangayList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address);
      const _brgy = JSON.parse(addressDetails?.addressBarangay ?? '{}');

      if (_brgy?.brgy_code) {
        const __barangay = address.getBarangayByCode(_brgy.brgy_code);
        address.setBarangaySelected(__barangay);
        setValue('addressBarangay', JSON.stringify(__barangay));
      }
    }
  }, [address.barangayList]);

  return (
    <form id={STEPPER_FORM[0]} onSubmit={handleSubmit(onSubmit)}>
      <div className="mt-3 grid grid-cols-3 gap-4 space-y-4">
        {/* First Name */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="firstName" className="pb-1 font-normal">
            First Name
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Input
            {...register('firstName', { required: 'First Name is required' })}
            className={cn(
              'focus-visible:ring-primary',
              errors.firstName && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter First Name"
          />
          {errors.firstName && <p className="form-error">{`${errors.firstName.message}`}</p>}
        </div>

        {/* Middle Name */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="middleName" className="pb-1 font-normal">
            Middle Name
          </Label>
          <Input
            {...register('middleName')}
            className={cn(
              'focus-visible:ring-primary',
              errors.middleName && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Middle Name"
          />
          {errors.middleName && <p className="form-error">{`${errors.middleName.message}`}</p>}
        </div>

        {/* Last Name */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="lastName" className="pb-1 font-normal">
            Last Name
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Input
            {...register('lastName', { required: 'Last Name is required' })}
            className={cn('focus-visible:ring-primary', errors.lastName && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Last Name"
          />
          {errors.lastName && <p className="form-error">{`${errors.lastName.message}`}</p>}
        </div>

        {/* Birth Date */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="birthDate" className="pb-1 font-normal">
            Birth Date
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Input
            {...register('birthDate', { required: 'Birth Date is required' })}
            className={cn(
              'focus-visible:ring-primary',
              errors.birthDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Birth Date"
          />
          {errors.birthDate && <p className="form-error">{`${errors.birthDate.message}`}</p>}
        </div>

        {/* Place of Birth */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="placeOfBirth" className="pb-1 font-normal">
            Place of Birth
          </Label>
          <Input
            {...register('placeOfBirth')}
            className={cn(
              'focus-visible:ring-primary',
              errors.placeOfBirth && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Place of Birth"
          />
          {errors.placeOfBirth && <p className="form-error">{`${errors.placeOfBirth.message}`}</p>}
        </div>

        {/* Gender */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="gender" className="pb-1 font-normal">
            Gender
          </Label>
          <Controller
            control={control}
            name="gender"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.gender && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="MALE">Male</SelectItem>
                    <SelectItem value="FEMALE">Female</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.gender && <p className="form-error">{`${errors.gender.message}`}</p>}
        </div>

        {/* Religion */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="religion" className="pb-1 font-normal">
            Religion
          </Label>
          <Input
            {...register('religion')}
            className={cn('focus-visible:ring-primary', errors.religion && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Religion"
          />
          {errors.religion && <p className="form-error">{`${errors.religion.message}`}</p>}
        </div>

        {/* Height */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="height" className="pb-1 font-normal">
            Height (cm)
          </Label>
          <Input
            {...register('height', {
              required: false,
              validate: {
                isGreaterThanZero: (v) => (v ? (v || 0) > 0 || 'Height must be greater than 0 cm' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.height && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Height"
          />
          {errors.height && <p className="form-error">{`${errors.height.message}`}</p>}
        </div>

        {/* Weight */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="weight" className="pb-1 font-normal">
            Weight (cm)
          </Label>
          <Input
            {...register('weight', {
              required: false,
              validate: {
                isGreaterThanZero: (v) => (v ? (v || 0) > 0 || 'Weight must be greater than 0 kg' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.weight && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Weight"
          />
          {errors.weight && <p className="form-error">{`${errors.weight.message}`}</p>}
        </div>

        {/* Civil Status */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="civilStatus" className="pb-1 font-normal">
            Civil Status
          </Label>
          <Controller
            control={control}
            name="civilStatus"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.civilStatus && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select civil status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="SINGLE">Single</SelectItem>
                    <SelectItem value="MARRIED">Married</SelectItem>
                    <SelectItem value="WIDOWED">Widowed</SelectItem>
                    <SelectItem value="DIVORCED">Divorced</SelectItem>
                    <SelectItem value="SEPARATED">Separated</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.civilStatus && <p className="form-error">{`${errors.civilStatus.message}`}</p>}
        </div>

        {/* Mobile Number */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="mobileNumber" className="pb-1 font-normal">
            Mobile No.
          </Label>
          <Input
            {...register('mobileNumber', {
              required: false,
              validate: {
                isValidMobileNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                      'Invalid mobile number format (e.g. 09123456789)'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.mobileNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Mobile No."
          />
          {errors.mobileNumber && <p className="form-error">{`${errors.mobileNumber.message}`}</p>}
        </div>

        {/* Region */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="addressRegion" className="pb-1 font-normal">
            Region
          </Label>
          <Controller
            control={control}
            name="addressRegion"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  address.setRegionSelected(JSON.parse(v));
                }}
                value={value}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.addressRegion && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.regionList.map((region) => (
                      <SelectItem key={region.region_code} value={JSON.stringify(region)}>
                        {region.region_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.addressRegion && <p className="form-error">{`${errors.addressRegion.message}`}</p>}
        </div>

        {/* Province */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="addressProvince" className="pb-1 font-normal">
            Province
          </Label>
          <Controller
            control={control}
            name="addressProvince"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  address.setProvinceSelected(JSON.parse(v));
                }}
                value={value}
                disabled={address.provinceList.length === 0}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.addressProvince && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select province" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.provinceList.map((province) => (
                      <SelectItem key={province.province_code} value={JSON.stringify(province)}>
                        {province.province_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.addressProvince && <p className="form-error">{`${errors.addressProvince.message}`}</p>}
        </div>

        {/* City */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="addressCity" className="pb-1 font-normal">
            City
          </Label>
          <Controller
            control={control}
            name="addressCity"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  address.setCitySelected(JSON.parse(v));
                }}
                value={value}
                disabled={address.cityList.length === 0}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.addressCity && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select city" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.cityList.map((city) => (
                      <SelectItem key={city.city_code} value={JSON.stringify(city)}>
                        {city.city_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.addressCity && <p className="form-error">{`${errors.addressCity.message}`}</p>}
        </div>

        {/* Barangay */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="addressBarangay" className="pb-1 font-normal">
            Barangay
          </Label>
          <Controller
            control={control}
            name="addressBarangay"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select
                onValueChange={(v) => {
                  onChange(v);
                  address.setBarangaySelected(JSON.parse(v));
                }}
                value={value}
                disabled={address.barangayList.length === 0}
              >
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.addressBarangay && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Barangay" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {address.barangayList.map((barangay) => (
                      <SelectItem key={barangay.brgy_code} value={JSON.stringify(barangay)}>
                        {barangay.brgy_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.addressBarangay && <p className="form-error">{`${errors.addressBarangay.message}`}</p>}
        </div>

        {/* House Number */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="addressHouseNumber" className="pb-1 font-normal">
            Street/House Number
          </Label>
          <Controller
            control={control}
            name="addressHouseNumber"
            rules={{ required: false }}
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Input
                className={cn(
                  'focus-visible:ring-primary',
                  errors.addressHouseNumber && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Street/House Number"
                value={value}
                onChange={(e) => {
                  onChange(e.target.value);
                  address.setStreet(e.target.value);
                }}
              />
            )}
          />
          {errors.addressHouseNumber && <p className="form-error">{`${errors.addressHouseNumber.message}`}</p>}
        </div>

        {/* Zip Code */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="addressZipCode" className="pb-1 font-normal">
            Zip Code
          </Label>
          <Controller
            control={control}
            name="addressZipCode"
            rules={{
              required: false,
              pattern: {
                value: /^\d{4}$/, // validate zipcode to have exactly 4 digits
                message: 'Invalid Zip Code format (e.g. 1234)',
              },
            }}
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Input
                className={cn(
                  'focus-visible:ring-primary',
                  errors.addressZipCode && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Zip Code"
                value={value}
                onChange={(e) => {
                  onChange(e.target.value);
                  address.setPostalCode(e.target.value);
                }}
              />
            )}
          />
          {errors.addressZipCode && <p className="form-error">{`${errors.addressZipCode.message}`}</p>}
        </div>
      </div>
    </form>
  );
}
