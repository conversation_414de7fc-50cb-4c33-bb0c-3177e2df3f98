'use client';

import { useHookstate } from '@hookstate/core';

import { useGlobalState } from '@/lib/store';
import { useCreditScoreState } from '@/lib/store/creditScore';

export const BeforeAccessorAccountProfile = {
  'Family Profile': 'familyProfile',
  Biometrics: 'biometrics',
  'Vouch from farmer cooperatives leader': 'vouchByLeaders',
  'Vouch from Municipal Agriculture Office': 'mao',
  'Property Ownership': 'propertyOwnership',
  'Basic Information': 'basicInfo',
  'Identification Documents': 'identificationDocuments',
  'Farm Detail': 'farmDetails',
  'Career and Academic': 'careerAcademic',
};

export const BeforeAccessorAgricultureActivity = {
  'Good Agricultural Practices (GAP) compliance': 'gapCompliance',
  'Farm Geo-tagging': 'geoTagging',
};

export const BeforeAccessorTransactionRecords = {
  'Marketplace Transaction': 'marketplace',
  'Trading Post Transaction': 'tradingPost',
  'Sales of Harvest to Kita': 'sales',
};

export const SchedVolAccessor = {
  'If the farmer has completed first visit, assign': 'firstVisit',
  'If the farmer has completed second visit, assign': 'secondVisit',
  'If the farmer has completed third visit, assign': 'thirdVisit',
};

export const DuringAccessorAgricultureActivity = {
  'Land Preparation': 'landPreparation',
  'Followed Recommended Number of Seeds per Area': 'recommendedSeedsPerArea',
  'Fertilization Schedule': 'fertilizationSchedule',
  'Fertilization Volume': 'fertilizationVolume',
  'Crop Protection Schedule': 'cropProtectionSchedule',
  'Crop Protection Volume': 'cropProtectionVolume',
  'Harvest Projection': 'harvestProjection',
};

export const DuringAccessorTransactionRecords = {
  'Marketplace Transaction': 'marketplace',
  'Trading Post Transaction': 'tradingPost',
  'Sales of Harvest to Kita': 'sales',
};

export const AfterAccessorCreditHistory = {
  'Repayment Behavior': 'repaymentBehavior',
  'Number of loan cycles': 'loanCycle',
};

export default function useShuruUtils() {
  const gState = useGlobalState();
  const creditScore = useCreditScoreState();
  const rules = useHookstate(gState.shuruCreditScoring.rules);

  const accountProfileState = useHookstate(creditScore.before.accountProfile);
  const agricultureActivityState = useHookstate(creditScore.before.agricultureActivity);
  const transactionRecordsState = useHookstate(creditScore.before.transactionRecords);

  const duringAgricultureActivityState = useHookstate(creditScore.during.agricultureActivity);
  const duringTransactionRecordsState = useHookstate(creditScore.during.transactionRecords);

  const afterCreditHistoryState = useHookstate(creditScore.after.creditHistory);
  const afterLoanCyclesState = useHookstate(creditScore.after.loanCycles);
  const afterAgricultureActivityState = useHookstate(creditScore.after.agricultureActivity);

  /**
   *  BEFORE LOAN
   */

  // Account Profile
  const setupBeforeAccountProfile = () => {
    const accountProfile = rules['before']['Account Profile'].get({ noproxy: true });
    accountProfileState.set({
      basicInfo: accountProfile.basicInformation.max_score,
      careerAcademic: accountProfile.careerAcademics.parent_data.parent_score,
      identificationDocuments: accountProfile.governmentIds.max_score,
      familyProfile: accountProfile.familyProfile.max_score,
      biometrics: accountProfile.biometrics.max_score,
      propertyOwnership: accountProfile.propertyOwnership.max_score,
      farmDetails: accountProfile.farmDetails.parent_data.parent_score,
      vouchByLeaders: accountProfile.vouchByLeader.max_score,
      mao: accountProfile.vouchByMao.max_score,
    });

    accountProfile.propertyOwnership.levels.map((lvl) => {
      const pts = lvl['score'];
      const loan = lvl['upper_bound'] || lvl['lower_bound'];
      const order = lvl['order'];

      creditScore.before.propertyOwnership.conditions[order].set({ pts, loan });
    });
  };

  // Agriculture Activity
  const setupBeforeAgricultureActivity = () => {
    const agricultureActivity = rules['before']['Agriculture Activity'].get({ noproxy: true });
    agricultureActivityState.set({
      geoTagging: agricultureActivity.farmGeoTagging.max_score,
      gapCompliance: agricultureActivity.goodAgriculturalPractices.max_score,
    });
  };

  // Transaction Records
  const setupBeforeTransactionRecords = () => {
    const transactionRecords = rules['before']['Transaction Records'].get({ noproxy: true });
    transactionRecordsState.set({
      marketplace: transactionRecords.marketplaceTransaction.max_score,
      tradingPost: transactionRecords.tradingPostTransaction.max_score,
      sales: transactionRecords.saleOfHarvestToKita.max_score,
    });

    transactionRecords.marketplaceTransaction.levels.map((lvl) => {
      const pts = lvl['score'] || 0;
      const upto = lvl['upper_bound'] || lvl['lower_bound'];
      const from = lvl['lower_bound'];
      const order = lvl['order'];

      creditScore.before.marketplaceComputation[order].set({ pts, upto, from });
    });

    transactionRecords.tradingPostTransaction.levels.map((lvl) => {
      const pts = lvl['score'] || 0;
      const upto = lvl['upper_bound'] || lvl['lower_bound'];
      const from = lvl['lower_bound'];
      const order = lvl['order'];

      creditScore.before.tradingPostComputation[order].set({ pts, upto, from });
    });

    transactionRecords.saleOfHarvestToKita.levels.map((lvl) => {
      const pts = lvl['score'] || 0;
      const upto = lvl['upper_bound'] || lvl['lower_bound'];
      const from = lvl['lower_bound'];
      const order = lvl['order'];

      creditScore.before.salesComputation[order].set({ pts, upto, from });
    });
  };

  /**
   * DURING LOAN
   */

  // Agriculture Activity

  const setupDuringAgricultureActivity = () => {
    const agricultureActivity = rules['during']['Agriculture Activity'].get({ noproxy: true });
    duringAgricultureActivityState.set({
      landPreparation: agricultureActivity.landPreparation.max_score,
      recommendedSeedsPerArea: agricultureActivity.seedsPerArea.max_score,
      fertilizationSchedule: agricultureActivity.fertilizationSchedule.parent_data.parent_score,
      fertilizationVolume: agricultureActivity.fertilizationVolume.parent_data.parent_score,
      cropProtectionSchedule: agricultureActivity.cropProtectionSchedule.parent_data.parent_score,
      cropProtectionVolume: agricultureActivity.cropProtectionVolume.parent_data.parent_score,
      harvestProjection: agricultureActivity.harvestProjection.max_score,
    });
    creditScore.during.fertilizationSchedule.set({
      firstVisit: agricultureActivity.fertilizationSchedule.parent_data.child_rules[0].max_score,
      secondVisit: agricultureActivity.fertilizationSchedule.parent_data.child_rules[1].max_score,
      thirdVisit: agricultureActivity.fertilizationSchedule.parent_data.child_rules[2].max_score,
    });
    creditScore.during.fertilizationVolume.set({
      firstVisit: agricultureActivity.fertilizationVolume.parent_data.child_rules[0].max_score,
      secondVisit: agricultureActivity.fertilizationVolume.parent_data.child_rules[1].max_score,
      thirdVisit: agricultureActivity.fertilizationVolume.parent_data.child_rules[2].max_score,
    });
    creditScore.during.cropProtectionSchedule.set({
      firstVisit: agricultureActivity.cropProtectionSchedule.parent_data.child_rules[0].max_score,
      secondVisit: agricultureActivity.cropProtectionSchedule.parent_data.child_rules[1].max_score,
      thirdVisit: agricultureActivity.cropProtectionSchedule.parent_data.child_rules[2].max_score,
    });
    creditScore.during.cropProtectionVolume.set({
      firstVisit: agricultureActivity.cropProtectionVolume.parent_data.child_rules[0].max_score,
      secondVisit: agricultureActivity.cropProtectionVolume.parent_data.child_rules[1].max_score,
      thirdVisit: agricultureActivity.cropProtectionVolume.parent_data.child_rules[2].max_score,
    });
  };

  // Transaction Records
  const setupDuringTransactionRecords = () => {
    const transactionRecords = rules['during']['Transaction Records'].get({ noproxy: true });
    duringTransactionRecordsState.set({
      marketplace: transactionRecords.marketplaceTransaction.max_score,
      tradingPost: transactionRecords.tradingPostTransaction.max_score,
      sales: transactionRecords.saleOfHarvestToKita.max_score,
    });

    transactionRecords.marketplaceTransaction.levels.map((lvl) => {
      const pts = lvl['score'] || 0;
      const upto = lvl['upper_bound'] || lvl['lower_bound'];
      const from = lvl['lower_bound'];
      const order = lvl['order'];

      creditScore.during.marketplaceComputation[order].set({ pts, upto, from });
    });

    transactionRecords.tradingPostTransaction.levels.map((lvl) => {
      const pts = lvl['score'] || 0;
      const upto = lvl['upper_bound'] || lvl['lower_bound'];
      const from = lvl['lower_bound'];
      const order = lvl['order'];

      creditScore.during.tradingPostComputation[order].set({ pts, upto, from });
    });

    transactionRecords.saleOfHarvestToKita.levels.map((lvl) => {
      const pts = lvl['score'] || 0;
      const upto = lvl['upper_bound'] || lvl['lower_bound'];
      const from = lvl['lower_bound'];
      const order = lvl['order'];

      creditScore.during.salesComputation[order].set({ pts, upto, from });
    });
  };

  /**
   * AFTER LOAN
   */

  // Credit History & Loan Cycles
  const setupAfterCreditHistory = () => {
    const creditHistory = rules['after']['Credit History'].get({ noproxy: true });
    afterCreditHistoryState.set({
      repaymentBehavior: creditHistory.repaymentBehavior.max_score,
      paymentBeforeDue: creditHistory.repaymentBehavior.levels[0].score,
      paymentGracePeriod: creditHistory.repaymentBehavior.levels[1].score,
      paymentBeyondGracePeriod: creditHistory.repaymentBehavior.levels[2].score,
      gracePeriodDays: creditHistory.repaymentBehavior.levels[1].upper_bound,
    });
    afterLoanCyclesState.loanCycle.set(creditHistory.totalLoanCycles.max_score);
    creditHistory.totalLoanCycles.levels.map((lvl) => {
      const pts = lvl['score'] || 0;
      const loan = lvl['upper_bound'] || lvl['lower_bound'];
      const from = lvl['lower_bound'];
      const order = lvl['order'];

      afterLoanCyclesState.conditions[order].set({ pts, loan, from });
    });
  };

  // Agriculture Activity
  const setupAfterAgricultureActivity = () => {
    const agricultureActivity = rules['after']['Agriculture Activity'].get({ noproxy: true });
    afterAgricultureActivityState.targetYieldAchievement.set(agricultureActivity.targetYieldAchievement.max_score);
    creditScore.after.total.agricultureActivity.set(agricultureActivity.targetYieldAchievement.max_score);
    creditScore.after.targetYield.set({
      yield1: agricultureActivity.targetYieldAchievement.levels[0].score,
      yield2: agricultureActivity.targetYieldAchievement.levels[1].score,
      yield3: agricultureActivity.targetYieldAchievement.levels[2].score,
    });
  };

  return {
    rules,
    setupBeforeAccountProfile,
    setupBeforeAgricultureActivity,
    setupBeforeTransactionRecords,
    setupDuringAgricultureActivity,
    setupDuringTransactionRecords,
    setupAfterCreditHistory,
    setupAfterAgricultureActivity,
  };
}
