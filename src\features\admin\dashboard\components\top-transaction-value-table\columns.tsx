'use client';

import { ColumnDef } from '@tanstack/react-table';

import { toCurrency, urlify } from '@/lib/utils';

import { ITopTransactionValue } from '../../types';

export const columnsTopTransactionValue: ColumnDef<ITopTransactionValue>[] = [
  {
    accessorKey: 'user_id',
    header: ({ column }) => <div>#</div>,
  },
  {
    id: 'name',
    header: ({ column }) => <div className="min-w-max">Name</div>,
    cell: ({ row }) => {
      return (
        <div className="flex w-max items-center gap-2.5">
          <div>
            <img
              className="size-8 rounded-full"
              src={row.original.user_img ? urlify(row.original.user_img, 'users/profile') : '/assets/user-default.jpg'}
              alt="profile"
            />
          </div>
          <div>{`${row.original.first_name} ${row.original.last_name}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'total_transactions',
    header: ({ column }) => <div className="min-w-max"></div>,
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('total_transactions'))}</div>;
    },
  },
];
