import React from 'react';

const Page1 = ({ data }) => {
  const currentDate = new Date();
  const formattedDate = currentDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/letter-of-authorization-to-hold-funds-form/letter-of-authorization-to-hold-funds-form-1.jpg)] bg-contain bg-top bg-no-repeat capitalize">
      <div className="absolute top-[6.2rem] flex">
        {/* Date */}
        <div className="absolute left-36 top-3 w-[200px] text-sm">{formattedDate}</div>

        {/* Name */}
        <div className="absolute left-24 top-[38rem] w-[300px] uppercase">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</div>
      </div>
    </div>
  );
};

export default Page1;
