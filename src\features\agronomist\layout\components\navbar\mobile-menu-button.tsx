'use client';

import { Menu } from 'lucide-react';

import { Button } from '@/components/ui/button';

interface IMobileMenuButtonProps {
  onOpenMobileMenu: () => void;
}

export function MobileMenuButton({ onOpenMobileMenu }: IMobileMenuButtonProps) {
  return (
    <Button variant="outline" size="icon" className="shrink-0 lg:hidden" onClick={onOpenMobileMenu}>
      <Menu className="size-5" />
      <span className="sr-only">Toggle navigation menu</span>
    </Button>
  );
}
