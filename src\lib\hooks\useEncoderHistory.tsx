'use client';

import { toast } from 'sonner';

import axios from '@/lib/api';

import { useGlobalState } from '../store';
import { useGlobalStatePersist } from '../store/persist';

export default function useEncoderHistory() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const fetchTransactions = async () => {
    try {
      const _data = await axios
        .get(`/encoder/tradingpost/viewAll`, {
          params: {
            search: gState.admin.pagination.transaction.search.value,
            page: gState.admin.pagination.transaction.page.value,
            pageSize: gState.admin.pagination.transaction.pageSize.value,
            date: new Date(gState.admin.pagination.transaction['calendar'].value).toLocaleDateString('en-Us', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            }),
          },
        })
        .then((res) => res.data.data);
      console.log('fetchTransactions: ', _data);

      gState.admin.transactions.set({
        data: _data.data,
        meta: _data.meta,
        details: null,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('fetchTransactions: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { fetchTransactions };
}
