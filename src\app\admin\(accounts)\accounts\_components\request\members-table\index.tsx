'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useGlobalState } from '@/lib/store';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function MembersTable({ columns, data, metadata = null, tab = '0' }) {
  const gState = useGlobalState();
  const usersBulkParams = useHookstate(gState.admin.pagination.usersBulk);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);
      },
    },
  });

  return (
    <div className="space-y-4">
      <DataTableToolbar id={tab === '4' ? 'bulk-import' : 'account-request'} table={table} meta={metadata} />

      <HorizontalScrollBar>
        <Tabs
          defaultValue="crops"
          value={gState.admin.pagination.request.status[0].value}
          onValueChange={(v) => {
            gState.admin.pagination.request.status[0].set(v);
          }}
          className="w-max"
        >
          <TabsList className="">
            <TabsTrigger value="0">For Approval</TabsTrigger>
            <TabsTrigger value="1">Approved</TabsTrigger>
            <TabsTrigger value="3">Pending From Encoder</TabsTrigger>
            <TabsTrigger value="2">Rejected</TabsTrigger>
            <TabsTrigger value="4">Import Users</TabsTrigger>
          </TabsList>
        </Tabs>
      </HorizontalScrollBar>

      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      if ((event.target as HTMLElement).tagName.toLowerCase() !== 'button')
                        table.options.meta?.getRowClicked?.(row);
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {metadata ? (
        <DataTablePaginationMeta
          table={table}
          meta={metadata}
          onChangePageSize={(pageSize) => {
            if (tab === '4') {
              usersBulkParams.pageSize.set(pageSize);
            } else {
              gState.admin.pagination.transaction.pageSize.set(pageSize);
            }
          }}
          onChangePage={(page) => {
            if (tab === '4') {
              usersBulkParams.page.set(page);
            } else {
              gState.admin.pagination.transaction.page.set(page);
            }
          }}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}
