'use client';

import { useHookstate } from '@hookstate/core';
import { toast } from 'sonner';
import { z } from 'zod';

import { Option } from '@/components/ui/multiple-selector';

import axios from '@/lib/api';

import { useGlobalState } from '../store';

export const ChemicalSchema = z.object({
  chemicals: z.array(
    z.object({
      name: z
        .string()
        .min(2, 'Chemical name is required')
        .max(50, 'Chemical name is too long')
        .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
          message: 'Chemical name must contain only letters and spaces',
        }),
      brand: z.string().min(2, 'Brand is required'),
      chemicalSubcategoryId: z.string(),
      chemicalModeOfActionId: z.string().optional(),
      chemicalActiveIngredients: z.any().optional(),
    }),
  ),
});
export type ChemicalType = z.infer<typeof ChemicalSchema>;

export const UpdateChemicalSchema = z.object({
  chemicalId: z.string().min(1, 'Chemical ID is required'),
  name: z
    .string()
    .min(2, 'Chemical name is required')
    .max(50, 'Chemical name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Chemical name must contain only letters and spaces',
    }),
  brand: z.string().min(2, 'Brand is required'),
  status: z.string().min(1, 'Status is required'),
  chemicalSubcategoryId: z.string().min(1, 'Subcategory ID is required'),
  chemicalModeOfActionId: z.string().optional(),
  chemicalActiveIngredients: z.any().optional(),
});
export type UpdateChemicalType = z.infer<typeof UpdateChemicalSchema>;

// Subcategory
export const SubcatChemSchema = z.object({
  name: z
    .string()
    .min(2, 'Subcategory name is required')
    .max(50, 'Subcategory name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Subcategory name must contain only letters and spaces',
    }),
});
export type SubcatChemType = z.infer<typeof SubcatChemSchema>;

export const UpdateSubcatChemSchema = z.object({
  chemicalSubcategoryId: z.string().min(1, 'Subcategory ID is required'),
  name: z
    .string()
    .min(2, 'Subcategory name is required')
    .max(50, 'Subcategory name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Subcategory name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Status is required'),
});
export type UpdateSubcatChemType = z.infer<typeof UpdateSubcatChemSchema>;

// Active Ingredients
export const ActiveChemSchema = z.object({
  name: z
    .string()
    .min(2, 'Active Ingredient Name is required')
    .max(50, 'Active Ingredient Name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Active Ingredient Name must contain only letters and spaces',
    }),
});
export type ActiveChemType = z.infer<typeof ActiveChemSchema>;

export const UpdateActiveChemSchema = z.object({
  chemicalActiveIngredientId: z.string().min(1, 'Active Ingredient ID is required'),
  name: z
    .string()
    .min(2, 'Active Ingredient Name is required')
    .max(50, 'Active Ingredient Name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Active Ingredient Name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Status is required'),
});
export type UpdateUpdateChemType = z.infer<typeof UpdateActiveChemSchema>;

// Mode of Action
export const ModeChemSchema = z.object({
  name: z
    .string()
    .min(2, 'Mode of Action Name is required')
    .max(50, 'Mode of Action Name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Mode of Action Name must contain only letters and spaces',
    }),
});
export type ModeChemType = z.infer<typeof ModeChemSchema>;

export const UpdateModeChemSchema = z.object({
  chemicalModeOfActionId: z.string().min(1, 'Mode of Action ID is required'),
  name: z
    .string()
    .min(2, 'Mode of Action Name is required')
    .max(50, 'Mode of Action Name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Mode of Action Name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Status is required'),
});
export type UpdateModeChemType = z.infer<typeof UpdateModeChemSchema>;

export default function useChemicals() {
  const gState = useGlobalState();
  const OPTIONS_ACTIVE_INGREDIENTS = useHookstate<Option[]>(gState.admin.chemicals.activeOption);

  const getChemicals = async () => {
    try {
      const _chems = await axios.get('/admin/chemicals/viewAll').then((res) => res.data.data);
      console.log('getChemicals: ', _chems);
      gState.admin.chemicals.data.set(_chems);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getChemicals: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getSubcategory = async () => {
    try {
      const _chems = await axios.get('/admin/chemicals/subcategory/viewAll').then((res) => res.data.data);
      console.log('getSubcategory: ', _chems);
      gState.admin.chemicals.subcategory.set(_chems);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getSubcategory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addChemical = async (data) => {
    console.log('addChemical: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/create', data).then((res) => res.data);
      console.log('addChemical: ', _data);
      await getChemicals();

      toast.success('Success', {
        description: 'Chemical has been added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addChemical: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateChemical = async (data) => {
    console.log('updateChemical: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/update', data).then((res) => res.data);
      console.log('updateChemical: ', _data);
      await getChemicals();

      toast.success('Success', {
        description: 'Chemical updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateChemical: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateSubcategory = async (data) => {
    console.log('updateSubcategory: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/subcategory/update', data).then((res) => res.data);
      console.log('updateSubcategory: ', _data);
      await getSubcategory();

      toast.success('Success', {
        description: 'Subcategory updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateSubcategory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addSubcategory = async (data) => {
    console.log('addSubcategory: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/subcategory/create', data).then((res) => res.data);
      console.log('addSubcategory: ', _data);
      await getSubcategory();

      toast.success('Success', {
        description: 'Subcategory has been added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addSubcategory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getActiveIngredients = async () => {
    try {
      const _chems = await axios.get('/admin/chemicals/activeingredient/viewAll').then((res) => res.data.data);
      console.log('getActiveIngredients: ', _chems);

      OPTIONS_ACTIVE_INGREDIENTS.set(
        _chems.filter((chem) => chem.status === 1).map((chem) => ({ label: chem.name, value: `${chem.id}` })),
      );
      console.log('OPTIONS_ACTIVE_INGREDIENTS: ', OPTIONS_ACTIVE_INGREDIENTS.value);

      gState.admin.chemicals.active.set(_chems);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getActiveIngredients: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addActiveIngredient = async (data) => {
    console.log('addActiveIngredient: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/activeingredient/create', data).then((res) => res.data);
      console.log('addActiveIngredient: ', _data);
      await getActiveIngredients();

      toast.success('New Active Ingredient', {
        description: `${data.name} has been added as an active ingredient`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addActiveIngredient: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateActiveIngredient = async (data) => {
    console.log('updateActiveIngredient: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/activeingredient/update', data).then((res) => res.data);
      console.log('updateActiveIngredient: ', _data);
      await getActiveIngredients();

      toast.success('Success', {
        description: `${data.name} updated successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateActiveIngredient: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getMode = async () => {
    try {
      const _chems = await axios.get('/admin/chemicals/modeofaction/viewAll').then((res) => res.data.data);
      console.log('getMode: ', _chems);
      gState.admin.chemicals.mode.set(_chems);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getMode: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addMode = async (data) => {
    console.log('addMode: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/modeofaction/create', data).then((res) => res.data);
      console.log('addMode: ', _data);
      await getMode();

      toast.success('New Mode of Action', {
        description: `${data.name} has been added as a mode of action`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addMode: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateMode = async (data) => {
    console.log('updateMode: ', data);

    try {
      const _data = await axios.post('/admin/chemicals/modeofaction/update', data).then((res) => res.data);
      console.log('updateMode: ', _data);
      await getMode();

      toast.success('Success', {
        description: `${data.name} updated successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateMode: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    getChemicals,
    getSubcategory,
    updateChemical,
    addChemical,
    updateSubcategory,
    addSubcategory,
    getActiveIngredients,
    addActiveIngredient,
    updateActiveIngredient,
    getMode,
    addMode,
    updateMode,
    OPTIONS_ACTIVE_INGREDIENTS,
  };
}
