'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { CircleCheckIcon, CircleXIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import useAuthenticated, { ChangePassSchema, checkPasswordStrength } from '@/lib/hooks/useAuthenticated';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

export default function UpdatePassword() {
  const gState = useGlobalState();
  const loading = useHookstate(false);
  const { updatePassword } = useAuthenticated();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    resolver: zodResolver(ChangePassSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });
  const watchNewPassword = watch('newPassword');
  const passwordStrengthChecks = checkPasswordStrength(watchNewPassword || '');

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      console.log('onSubmit: ', data);
      await updatePassword(data);
      reset();
    } catch (e) {
      console.error('onSubmit');
    } finally {
      loading.set(false);
    }
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <CardTitle>Password</CardTitle>
        <CardDescription>Update your password here</CardDescription>
      </CardHeader>
      <CardContent>
        <form id="system-fee" onSubmit={handleSubmit(onSubmit)} className="grid items-start gap-6">
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="currentPassword">Current Password</Label>
            <InputPassword
              {...register('currentPassword')}
              className={cn(
                'focus-visible:ring-primary',
                errors.currentPassword && 'border-red-500 focus-visible:ring-red-500',
              )}
              placeholder="Min. 8 characters"
              type="password"
            />
            {errors.currentPassword && <p className="form-error">{`${errors.currentPassword.message}`}</p>}
          </div>

          <div className="grid items-start gap-4 sm:grid-cols-2">
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="newPassword">New Password</Label>
              <InputPassword
                {...register('newPassword')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.newPassword && 'border-red-500 focus-visible:ring-red-500',
                )}
                placeholder="Min. 8 characters"
                type="password"
              />

              {(watchNewPassword || errors.newPassword) && (
                <div className="mt-2">
                  {passwordStrengthChecks.map((check) => (
                    <div key={check.message} className="flex items-center gap-2">
                      {check.passed ? (
                        <CircleCheckIcon className="size-4 text-green-500" />
                      ) : (
                        <CircleXIcon className="size-4 text-red-500" />
                      )}
                      <span className="text-sm italic text-gray-500">{check.message}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="confirmPassword">Retype New Password</Label>
              <InputPassword
                {...register('confirmPassword')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.confirmPassword && 'border-red-500 focus-visible:ring-red-500',
                )}
                placeholder="Min. 8 characters"
                type="password"
              />
              {errors.confirmPassword && <p className="form-error">{`${errors.confirmPassword.message}`}</p>}
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter className="border-t bg-gray-50 px-6 py-4">
        {loading.value ? (
          <ButtonLoading />
        ) : (
          <Button type="submit" form="system-fee">
            Save
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
