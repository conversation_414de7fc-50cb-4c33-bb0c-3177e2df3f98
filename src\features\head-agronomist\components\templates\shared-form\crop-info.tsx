'use client';

import { Controller, UseFormReturn } from 'react-hook-form';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { useCrops } from '@/features/head-agronomist/store/crops-store';
import { cn } from '@/lib/utils';

import { ITemplateForm } from '../create-form/types/template';

interface ICropInfoProps {
  form: UseFormReturn<ITemplateForm>;
}

export default function CropInfo({ form }: ICropInfoProps) {
  const { cropsQuery } = useCrops();

  return (
    <div className="grid gap-4 sm:grid-cols-2">
      <div className="">
        <Label htmlFor="crop">Crop</Label>
        <Controller
          name="cropId"
          control={form.control}
          rules={{
            required: `Crop is required for farm planning`,
            validate: {
              min: (v) => v > 0 || `Crop is required for farm planning`,
            },
          }}
          render={({ field, fieldState }) => (
            <Select
              onValueChange={(v) => {
                field.onChange(Number(v));
              }}
              value={field.value?.toString()}
            >
              <SelectTrigger
                className={cn('w-full', {
                  'input-error': form.formState.errors.cropId,
                })}
                icon="down"
              >
                <SelectValue placeholder="Select crop" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="0" disabled>
                    Select crop
                  </SelectItem>

                  {cropsQuery.isSuccess &&
                    [...cropsQuery.data]
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((crop) => (
                        <SelectItem key={crop.id} value={`${crop.id}`}>
                          <div className="flex items-center gap-1">
                            <img className="mr-1 size-5 rounded-full md:size-6" src={crop.image} alt="" />
                            <div className="line-clamp-1 text-xs md:text-sm">{crop.name}</div>
                          </div>
                        </SelectItem>
                      ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          )}
        />
        {form.formState.errors.cropId && <p className="form-error">{form.formState.errors.cropId.message}</p>}
      </div>

      <div className="">
        <Label htmlFor="location">Location</Label>
        <Controller
          name="location"
          control={form.control}
          rules={{
            required: `Location is required for farm planning`,
            validate: {
              min: (v) => v.length >= 3 || `Invalid location`,
            },
          }}
          render={({ field, fieldState }) => (
            <Input
              value={field.value || ''}
              onChange={field.onChange}
              placeholder="Enter location"
              className={cn({
                'input-error': fieldState.error,
              })}
            />
          )}
        />

        {form.formState.errors.location && <p className="form-error">{form.formState.errors.location.message}</p>}
      </div>

      <div className="">
        <Label htmlFor="versionNumber">Version No.</Label>
        <Controller
          name="versionNumber"
          control={form.control}
          rules={{
            required: `Version number is required for farm planning`,
            validate: {
              min: (v) => v.length >= 1 || `Invalid version number`,
            },
          }}
          render={({ field, fieldState }) => (
            <Input
              value={field.value || ''}
              onChange={(e) => {
                const value = Number(e.target.value);
                if (isNaN(value) || e.target.value.length > 5) return;

                field.onChange(e.target.value.trim());
              }}
              placeholder="1"
              className={cn({
                'input-error': fieldState.error,
              })}
            />
          )}
        />

        {form.formState.errors.versionNumber && (
          <p className="form-error">{form.formState.errors.versionNumber.message}</p>
        )}
      </div>
    </div>
  );
}
