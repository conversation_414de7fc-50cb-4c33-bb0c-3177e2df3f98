'use client';

import { Control, Controller, FieldErrors, FieldValues } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import MultipleSelector from '@/components/ui/multiple-selector';

import {
  OPTIONS_CONSTRUCTION,
  OPTIONS_CRAFT,
  OPTIONS_FARMING,
  OPTIONS_FISHING,
  OPTIONS_LIVESTOCK,
  OPTIONS_PROCESSING,
  OPTIONS_SERVICING,
} from '../../Enums';

interface ISpecialSkillsFormProps {
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
}

export default function SpecialSkillsForm({ control, errors }: ISpecialSkillsFormProps) {
  return (
    <div>
      <div className="font-dmSans mt-6 text-xl font-bold text-primary">Special Skills</div>
      <div className="mt-6 grid items-start gap-4 sm:grid-cols-2 2xl:grid-cols-3">
        <FormField name="skillsFarming" label="Farming" errors={errors}>
          <Controller
            control={control}
            name="skillsFarming"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_FARMING}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        <FormField name="skillsFishing" label="Fishing" errors={errors}>
          <Controller
            control={control}
            name="skillsFishing"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_FISHING}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        <FormField name="skillsLivestock" label="Livestock" errors={errors}>
          <Controller
            control={control}
            name="skillsLivestock"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_LIVESTOCK}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        <FormField name="skillsConstruction" label="Construction" errors={errors}>
          <Controller
            control={control}
            name="skillsConstruction"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_CONSTRUCTION}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        <FormField name="skillsProcessing" label="Processing" errors={errors}>
          <Controller
            control={control}
            name="skillsProcessing"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_PROCESSING}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        <FormField name="skillsServicing" label="Servicing" errors={errors}>
          <Controller
            control={control}
            name="skillsServicing"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_SERVICING}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        {/* Crafting */}
        <FormField name="skillsCraft" label="Crafting" errors={errors}>
          <Controller
            control={control}
            name="skillsCraft"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_CRAFT}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>

        {/* Other Skills */}
        <FormField name="skillsOthers" label="Other Skills" errors={errors}>
          <Controller
            control={control}
            name="skillsOthers"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={[]}
                placeholder="Specify here..."
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>
      </div>
    </div>
  );
}
