'use client';

import { Option } from '@/components/ui/multiple-selector';

export enum EDUCATIONAL_ATTAINMENT {
  ELEMENTARY = 'ELEMENTARY',
  HIGH_SCHOOL = 'HIGH SCHOOL',
  COLLEGE = 'COLLEGE',
  VOCATIONAL = 'VOCATIONAL',
  POST_GRADUATE = 'POST GRADUATE',
  DOCTORATE = 'DOCTORATE',
  NONE = 'NONE',
}

export enum IS_GRADUATE {
  YES = '1',
  NO = '0',
}

export enum OCCUPATION {
  GOVERNMENT_EMPLOYEE = 'GOVERNMENT EMPLOYEE',
  PRIVATE_EMPLOYEE = 'PRIVATE EMPLOYEE',
  SELF_EMPLOYED_PRACTICING_PROFESSIONAL = 'SELF-EMPLOYED (PRACTICING PROFESSIONAL)',
  SELF_EMPLOYED_NON_PROFESSIONAL = 'SELF-EMPLOYED (NON-PROFESSIONAL)',
  BUSINESS_PERSON_ENTREPRENEUR = 'BUSINESS PERSON/ENTREPRENEUR',
  CHURCH_SERVANTS_WORKERS = 'CHURCH SERVANTS/WORKERS',
  OVERSEAS_FILIPINO_WORKER_OFW = 'OVERSEAS FILIPINO WORKER (OFW)',
  RETIREE_PENSIONER = 'RETIREE/PENSIONER',
  HOUSEWIFE_HOUSEHUSBAND = 'HOUSEWIFE/HOUSEHUSBAND',
  YOUTH_STUDENT = 'YOUTH/STUDENT',
  FARMER_FISHERFOLK = 'FARMER/FISHERFOLK',
  LABORER = 'LABORER',
}

export enum OCCUPATION_STATUS {
  EMPLOYED = 'EMPLOYED',
  UNEMPLOYED = 'UNEMPLOYED',
  SELF_EMPLOYED = 'SELF-EMPLOYED',
  STUDENT = 'STUDENT',
  RETIRED = 'RETIRED',
  HOMEMAKER = 'HOMEMAKER',
}

export enum GovernmentIdentificationEnum {
  DRIVER_LICENSE = 'DRIVER LICENSE',
  UMID = 'UMID',
  SSS = 'SSS',
  PHILSYS = 'PHILSYS',
  PHILHEALTH = 'PHILHEALTH',
  PHILIPPINE_PASSPORT = 'PHILIPPINE PASSPORT',
  POSTAL_ID = 'POSTAL ID',
  PRC = 'PRC',
  TIN = 'TIN',
  GSIS = 'GSIS',
  VOTERS_ID = 'VOTERS ID',
  SCHOOL_ID = 'SCHOOL ID',
  NBI_CLEARANCE = 'NBI CLEARANCE',
  COMPANY_ID = 'COMPANY ID',
  BARANGAY_CERTIFICATION = 'BARANGAY CERTIFICATION',
  CITY_HEALTH_CARD = 'CITY HEALTH CARD',
  DSWD_CERTIFICATION = 'DSWD CERTIFICATION',
  GOCC_ID = 'GOCC ID',
  OFW_ID = 'OFW ID',
  OWWA_ID = 'OWWA ID',
  PAGIBIG = 'PAGIBIG',
  SEAMANS_BOOK = 'SEAMANS BOOK',
  SENIOR_CITIZEN_CARD = 'SENIOR CITIZEN CARD',
  ACR = 'ACR',
}

export enum FamilyRelationshipEnums {
  SPOUSE = 'SPOUSE',
  SON = 'SON',
  DAUGHTER = 'DAUGHTER',
  SIBLING = 'SIBLING',
  EXTENDED_FAMILY = 'EXTENDED FAMILY',
}

export enum FarmerOccupationEnum {
  UNEMPLOYED = 'UNEMPLOYED',
  GOVERNMENT_EMPLOYEE = 'GOVERNMENT EMPLOYEE',
  PRIVATE_EMPLOYEE = 'PRIVATE EMPLOYEE',
  SELF_EMPLOYED_PROFESSIONAL = 'SELF EMPLOYED PROFESSIONAL',
  SELF_EMPLOYED_NONPROFESSIONAL = 'SELF EMPLOYED NONPROFESSIONAL',
  BUSINESS_PERSON = 'BUSINESS PERSON',
  CHURCH_SERVANTS = 'CHURCH SERVANTS',
  OFW = 'OFW',
  RETIREE = 'RETIREE',
  HOUSEWIFE = 'HOUSEWIFE',
  STUDENT = 'STUDENT',
  FARMER = 'FARMER',
  LABORER = 'LABORER',
}

export enum IFarmPropertyEnum {
  RESIDENTIAL = 'RESIDENTIAL',
  INDUSTRIAL = 'INDUSTRIAL',
  COMMERCIAL = 'COMMERCIAL',
  AGRICULTURAL = 'AGRICULTURAL',
}

// ------------------------

export const OPTIONS_FARMING: Option[] = [
  { label: 'PALAY', value: 'PALAY' },
  { label: 'CORN', value: 'CORN' },
  { label: 'VEGETABLE', value: 'VEGETABLE' },
];

export const OPTIONS_FISHING: Option[] = [{ label: 'FISHING', value: 'FISHING' }];

export const OPTIONS_LIVESTOCK: Option[] = [
  { label: 'SWINE', value: 'SWINE' },
  { label: 'POULTRY', value: 'POULTRY' },
];

export const OPTIONS_CONSTRUCTION: Option[] = [
  { label: 'MASONRY', value: 'MASONRY' },
  { label: 'CANPENTRY', value: 'CANPENTRY' },
  { label: 'PLUMBING', value: 'PLUMBING' },
  { label: 'ELECTRICAL', value: 'ELECTRICAL' },
  { label: 'PAINTING', value: 'PAINTING' },
];

export const OPTIONS_PROCESSING: Option[] = [
  { label: 'FOOD PROCESSING', value: 'FOOD PROCESSING' },
  { label: 'PRESERVATION', value: 'PRESERVATION' },
];

export const OPTIONS_SERVICING: Option[] = [
  { label: 'COSMETOLOGY', value: 'COSMETOLOGY' },
  { label: 'MASSAGE THERAPIST', value: 'MASSAGE THERAPIST' },
  { label: 'MECHANICAL REPAIR', value: 'MECHANICAL REPAIR' },
  { label: 'ELECTRONICS', value: 'ELECTRONICS' },
  { label: 'APPLIANCE REPAIR', value: 'APPLIANCE REPAIR' },
];

export const OPTIONS_CRAFT: Option[] = [
  { label: 'FURNITURE MAKING', value: 'FURNITURE MAKING' },
  { label: 'METAL WORK', value: 'METAL WORK' },
  { label: 'TAILORING', value: 'TAILORING' },
];
