'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { ChevronDownIcon, ShoppingBasket } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { GiChemicalTank, GiFertilizerBag, GiFruitBowl, GiGreenhouse, GiRopeCoil, GiSeedling } from 'react-icons/gi';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { Tab, TabItem, tabState } from '@/components/Tab';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';

import useHtmlToText from '@/lib/hooks/utils/useHtmlToText';
import { useGlobalState } from '@/lib/store';
import { capitalize, cn } from '@/lib/utils';

import { PRODUCT_TYPE, PRODUCT_TYPE_ACCESSOR } from '../Enums';

export default function ProductTable({ data, columns, meta = null }) {
  const gState = useGlobalState();
  const router = useRouter();
  const htmlToText = useHtmlToText();
  const subCat = useHookstate(gState.admin.chemicals.subcategory);
  const subCatSeed = useHookstate(gState.admin.seeds.subcategory);
  const cat = useHookstate(gState.admin.pagination.products.productTypeCategory);

  const cropTab = useHookstate(tabState);
  const isSeeds = useHookstate(false);
  const isCropProtection = useHookstate(false);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  return (
    <div>
      <div className="mb-6 flex">
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder="Search (e.g low, normal)"
            onChange={(event) => {
              if (meta) {
                gState.admin.pagination.products.page.set(1);
                gState.admin.pagination.products.search.set(event.target.value);
              } else {
                table.setGlobalFilter(event.target.value);
              }
            }}
            className="h-8 w-[150px] focus-visible:ring-primary lg:w-[250px]"
          />
        </div>

        <div>
          <Link href="/admin/marketplace/product/create">
            <Button className="h-8 px-6">Add Product</Button>
          </Link>
        </div>
      </div>

      {/* Tabs */}
      <HorizontalScrollBar>
        <div className="mr-4 grid gap-4 pb-6 pt-2">
          <Tab
            className="justify-center gap-4"
            defaultValue="all"
            onValueChange={(v) => {
              if (v === '3' || v === '1') {
                cat.set([]);
              }

              if (v === 'all') {
                // table.setGlobalFilter('');
                gState.admin.pagination.products.productType.set([]);
              } else {
                // table.setGlobalFilter(v);
                gState.admin.pagination.products.page.set(1);
                gState.admin.pagination.products.productType.set([v]);
              }
            }}
          >
            <TabItem value="all">
              <GiGreenhouse className="size-6" />
              <span className="text-lg">View All</span>
            </TabItem>
            <TabItem value="0">
              <GiFruitBowl className="size-6" />
              <span className="text-lg">Crops</span>
            </TabItem>
            <TabItem value="2">
              <GiFertilizerBag className="size-6" />
              <span className="text-lg">Fertilizers</span>
            </TabItem>
            <TabItem value="1">
              <DropdownMenu
                open={isSeeds.value}
                onOpenChange={(v) => {
                  if (cropTab.value === '1') {
                    isSeeds.set(v);
                  }
                }}
              >
                <DropdownMenuTrigger asChild>
                  <div className="flex items-center justify-between gap-2 outline-none">
                    <GiSeedling className="size-6" />
                    <span className="text-lg">Seeds</span>
                    <span>
                      <ChevronDownIcon className="size-6" />
                    </span>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="center">
                  <DropdownMenuRadioGroup
                    value={cat.length > 0 ? cat[0].value : 'all'}
                    onValueChange={(v) => {
                      if (v === 'all') {
                        cat.set([]);
                      } else {
                        cat.set([v]);
                      }
                    }}
                  >
                    <DropdownMenuRadioItem value="all">ALL</DropdownMenuRadioItem>
                    {subCatSeed
                      .get({ noproxy: true })
                      .filter((x) => x.status === 1)
                      .map((sub) => (
                        <DropdownMenuRadioItem key={sub.id} value={sub.id}>
                          {sub.name}
                        </DropdownMenuRadioItem>
                      ))}
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </TabItem>
            <TabItem value="3">
              <DropdownMenu
                open={isCropProtection.value}
                onOpenChange={(v) => {
                  if (cropTab.value === '3') {
                    isCropProtection.set(v);
                  }
                }}
              >
                <DropdownMenuTrigger asChild>
                  <div className="flex items-center justify-between gap-2 outline-none">
                    <GiChemicalTank className="size-6" />
                    <span className="text-lg">Crop Protection</span>
                    <span>
                      <ChevronDownIcon className="size-6" />
                    </span>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="center">
                  <DropdownMenuRadioGroup
                    value={cat.length > 0 ? cat[0].value : 'all'}
                    onValueChange={(v) => {
                      if (v === 'all') {
                        cat.set([]);
                      } else {
                        cat.set([v]);
                      }
                    }}
                  >
                    <DropdownMenuRadioItem value="all">ALL</DropdownMenuRadioItem>
                    {subCat
                      .get({ noproxy: true })
                      .filter((x) => x.status === 1)
                      .map((sub) => (
                        <DropdownMenuRadioItem key={sub.id} value={sub.id}>
                          {sub.name}
                        </DropdownMenuRadioItem>
                      ))}
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </TabItem>
            <TabItem value="4">
              <GiRopeCoil className="size-6" />
              <span className="text-lg">Others</span>
            </TabItem>
          </Tab>
        </div>
      </HorizontalScrollBar>

      <div
        className={cn(
          'grid mt-4',
          table.getRowModel().rows?.length
            ? 'sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-5 gap-8'
            : '',
        )}
      >
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row) => {
            const data = row.original as any;

            let accessor = PRODUCT_TYPE_ACCESSOR[Number(data.product_type)];
            const product = data[accessor];
            const description = htmlToText.convert(data.description);
            const weight = data.weight || 0;
            const unit = data.unit || '';
            const isActive = data.status === 1;

            const isSeed = Number(data.product_type) === PRODUCT_TYPE.SEEDS;
            const isChemical = Number(data.product_type) === PRODUCT_TYPE.CHEMICALS;
            const hasSub = isSeed || isChemical;
            const isLowStocks = Number(data.stocks) < data.stocks_warning;

            accessor = accessor === 'chemical' ? 'crop protection' : accessor;

            return (
              <div key={data.id} className="font-dmSans flex flex-col">
                <Dialog>
                  <DialogTrigger asChild>
                    <div className="cursor-pointer overflow-hidden rounded-2xl transition duration-300 ease-in-out hover:shadow-lg hover:shadow-amber-500/50">
                      <img
                        className="mx-auto h-48 object-cover object-center"
                        src={data.image || '/assets/default-product.png'}
                        alt={product.name}
                      />
                    </div>
                  </DialogTrigger>
                  <DialogContent className="p-0 sm:max-w-[525px]">
                    <ScrollArea className="h-[70vh]">
                      <div className="p-9">
                        <div className="flex justify-center">
                          <div className="overflow-hidden rounded-2xl">
                            <img
                              className="mx-auto h-48 object-cover object-center"
                              src={data.image || '/assets/default-product.png'}
                              alt={product.name}
                            />
                          </div>
                        </div>

                        <div className="my-4 flex flex-1 flex-col">
                          <div className="mt-3 line-clamp-2 font-semibold text-primary">
                            {`${product.name} - ${weight} ${unit}`}
                          </div>
                        </div>

                        {/* Category */}
                        <div className="flex items-center gap-2 pt-2">
                          <div>
                            <img src="/assets/icon/category.svg" alt="" />
                          </div>
                          <div className="flex text-sm capitalize text-[#A3AED0]">
                            <div>{`${accessor}`}</div>
                            <div>{`${
                              hasSub
                                ? `, ${
                                    isSeed
                                      ? capitalize(product.seedSubcategory ? product.seedSubcategory.name : 'N/A')
                                      : capitalize(
                                          product.chemicalSubcategory ? product.chemicalSubcategory.name : 'N/A',
                                        )
                                  }`
                                : ''
                            }`}</div>
                          </div>
                        </div>

                        <div className="mt-2 flex items-center justify-between">
                          {isLowStocks ? (
                            <div className="flex translate-x-[-6px] items-center gap-2 rounded-md bg-[#F24822] px-2 py-px text-white">
                              <ShoppingBasket className="size-6" />
                              <div className="">{`${data.stocks} pcs left`}</div>
                            </div>
                          ) : (
                            <div className="flex gap-2 text-[#A3AED0]">
                              <ShoppingBasket className="size-6" />
                              <div>{`${data.stocks} pcs`}</div>
                            </div>
                          )}

                          <div className="text-lg font-semibold text-[#ED6E11]">
                            {Number(data.price).toLocaleString('en-US', {
                              style: 'currency',
                              currency: 'PHP',
                            })}
                          </div>
                        </div>

                        <div className="mt-6">
                          <div className="flex capitalize text-[#A3AED0]">Product Description</div>
                          <div
                            className={cn(
                              'prose max-w-none mt-2',
                              'prose-figcaption:text-center',
                              'prose-figure:flex prose-figure:flex-col prose-figure:items-center',
                              'prose-img:object-cover',
                            )}
                            dangerouslySetInnerHTML={{
                              __html: data.description,
                            }}
                          />
                        </div>
                      </div>
                    </ScrollArea>
                  </DialogContent>
                </Dialog>

                <div className="flex flex-1 flex-col">
                  <div
                    className={cn(
                      'mt-3 line-clamp-2 min-h-[48px] cursor-pointer font-semibold text-primary hover:underline',
                      !isActive && 'text-red-500',
                    )}
                    onClick={() => {
                      router.push(`/admin/marketplace/product/update/?id=${data.id}`);
                    }}
                  >
                    {product.name}
                  </div>
                  <div className="line-clamp-2 min-h-[48px]">{description}</div>
                </div>

                {/* Category */}
                <div className="flex items-center gap-2 pt-2">
                  <div>
                    <img src="/assets/icon/category.svg" alt="" />
                  </div>
                  <div className="flex text-sm capitalize text-[#A3AED0]">
                    <div>{`${accessor}`}</div>
                    <div>{`${
                      hasSub
                        ? `, ${
                            isSeed
                              ? capitalize(product.seedSubcategory ? product.seedSubcategory.name : 'N/A')
                              : capitalize(product.chemicalSubcategory ? product.chemicalSubcategory.name : 'N/A')
                          }`
                        : ''
                    }`}</div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-2">
                  {isLowStocks ? (
                    <div className="flex translate-x-[-6px] items-center gap-2 rounded-md bg-[#F24822] px-2 py-px text-white">
                      <ShoppingBasket className="size-5" />
                      <div className="">{`${data.stocks} pcs left`}</div>
                    </div>
                  ) : (
                    <div className="flex gap-2 text-[#A3AED0]">
                      <ShoppingBasket className="size-6" />
                      <div>{`${data.stocks} pcs`}</div>
                    </div>
                  )}

                  <div className="font-semibold text-[#ED6E11]">
                    {Number(data.price).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div>
            <div className="flex h-24 items-center justify-center text-xl text-gray-500">No results.</div>
          </div>
        )}
      </div>

      <div className="mt-12">
        {meta ? (
          <DataTablePaginationMeta
            table={table}
            meta={meta}
            onChangePageSize={(pageSize) => {
              gState.admin.pagination.products.pageSize.set(pageSize);
            }}
            onChangePage={(page) => {
              gState.admin.pagination.products.page.set(page);
            }}
          />
        ) : (
          <DataTablePagination table={table} />
        )}
      </div>
    </div>
  );
}
