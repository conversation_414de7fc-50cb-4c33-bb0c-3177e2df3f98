'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import useFinance1 from '@/lib/hooks/finance1/useFinance1';
import useFinance from '@/lib/hooks/useFinance';
import { useGlobalState } from '@/lib/store';

export default function FetchAccountInfo() {
  const detailsId = useSearchParams().get('id');
  const { getAccountInfo, getFinance, getMarketplace, getTradingPost } = useFinance();
  const router = useRouter();
  const gState = useGlobalState();

  useEffect(() => {
    if (detailsId) {
      getAccountInfo(detailsId);
    } else {
      toast.error('Oops! Something went wrong', {
        description: 'Invalid ID, Please try again',
      });
      router.push('/finance');
    }
  }, [detailsId]);

  useEffect(() => {
    getTradingPost(detailsId);
  }, [
    gState.finance.accountInfo.pagination.tradingPost.page,
    gState.finance.accountInfo.pagination.tradingPost.pageSize,
  ]);

  useEffect(() => {
    getMarketplace(detailsId);
  }, [
    gState.finance.accountInfo.pagination.marketplace.page,
    gState.finance.accountInfo.pagination.marketplace.pageSize,
  ]);

  useEffect(() => {
    if (gState.selected.accountInfo['info'].value) {
      getFinance(detailsId);
    }
  }, [gState.finance.accountInfo.pagination.finance.page, gState.finance.accountInfo.pagination.finance.pageSize]);

  return null;
}
