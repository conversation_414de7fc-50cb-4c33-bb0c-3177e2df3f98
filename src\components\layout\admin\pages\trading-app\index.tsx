'use client';

import { TAB_TRADING_APP } from '@/app/admin/layout';
import { useGlobalStatePersist } from '@/lib/store/persist';

import BuyerPage from './buyer';
import QaPage from './qa';
import SellerPage from './seller';

export default function TradingAppPage() {
  const gStateP = useGlobalStatePersist();

  return (
    <div className="">
      {gStateP.tabsTradingApp.value === TAB_TRADING_APP.QA && <QaPage />}
      {gStateP.tabsTradingApp.value === TAB_TRADING_APP.BUYER && <BuyerPage />}
      {gStateP.tabsTradingApp.value === TAB_TRADING_APP.SELLER && <SellerPage />}
    </div>
  );
}
