'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

import { getUserType } from '@/lib/constants';

export const columns = [
  {
    id: 'farmer_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Farmer Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.farmer.first_name} {data.farmer.last_name}
        </div>
      );
    },
    accessorFn: (row) => {
      const type = getUserType(row.user_type);
      return `${row[type]?.first_name} ${row[type]?.last_name} | ${row.id}`;
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,
  },
  {
    id: 'plate_no',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Plate No." />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max uppercase">
          {data.farmer.farmerVehicles.map((v) => v.vehicle_plate_number).join(', ')}
        </div>
      );
    },
    accessorFn: (row) => `${row.farmer.farmerVehicles.map((v) => v.vehicle_plate_number).join(', ')}`,
  },
  {
    id: 'mobile_no',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Mobile No." />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.farmer.mobile_number}</div>;
    },
  },
  // {
  //   id: 'status',
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
  //   cell: ({ row }) => {
  //     const data = row.original;

  //     return (
  //       <div className="flex items-center gap-3">
  //         <Badge className={UserStatusLabels[data.status].color}>
  //           <span>{UserStatusLabels[data.status].label}</span>
  //         </Badge>
  //       </div>
  //     );
  //   },
  //   accessorFn: (row) => {
  //     return UserStatusLabels[row.status].label;
  //   },
  //   filterFn: (row, id, value) => {
  //     return value.includes(row.getValue(id));
  //   },
  // },
];
