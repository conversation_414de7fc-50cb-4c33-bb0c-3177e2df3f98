export interface ISoaData {
  raw: IRaw;
  formatted: IFormatted;
}

export interface IRaw {
  id: number;
  customer_id: number;
  payment_method: number;
  reference_number: string;
  total_price: number;
  order_status: number;
  payment_status: number;
  shipping_fee: number;
  shipping_address: string;
  shipping_date: string;
  fulfillment_type: number;
  created_at: string;
  updated_at: string;
  wallet_allocation: number;
  wallet_balance_before: number;
  wallet_balance_after: number;
  marketplaceProductOrders: IMarketplaceProductOrders[];
  customer: ICustomer;
}

export interface IMarketplaceProductOrders {
  id: number;
  marketplace_order_id: number;
  marketplace_product_id: number;
  price: number;
  quantity: number;
  vatable: string;
  created_at: string;
  updated_at: string;
  marketplaceProduct: IMarketplaceProduct;
}

export interface IMarketplaceProduct {
  id: number;
  product_type: number;
  price: number;
  code: string;
  weight: number;
  unit: string;
  seed_id: null;
  crop_id: number;
  fertilizer_id: null;
  chemical_id: null;
  other_product_id: null;
  fertilizer: null;
  crop: ICrop;
  seed: null;
  chemical: null;
  otherProduct: null;
}

export interface ICrop {
  id: number;
  name: string;
}

export interface ICustomer {
  id: number;
  email: string;
  username: string;
  user_type: number;
  farmer: IFarmer;
}

export interface IFarmer {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  mobile_number: string;
  address: string;
  farmerInfo: IFarmerInfo;
  cropsPlanted: ICropsPlanted[];
  seedSubcategories: ISeedSubcategories[];
  subCropsPlanted: ISubCropsPlanted[];
}

export interface IFarmerInfo {
  id: number;
  farmer_id: number;
  farm_area: number;
  farm_address: string;
}

export interface ICropsPlanted {
  id: number;
  farmer_id: number;
  crop_id: number;
  crop: ICrop;
}

export interface ISeedSubcategories {
  id: number;
  farmer_id: number;
  seed_subcategory_id: number;
  seedSubcategory: ISeedSubcategory;
}

export interface ISeedSubcategory {
  id: number;
  name: string;
}

export interface ISubCropsPlanted {
  id: number;
  farmer_id: number;
  crop_id: number;
  crop: ICrop;
}

export interface IFormatted {
  farmerName: string;
  farmAddress: string;
  farmArea: number;
  mainCropPlanted: string;
  subCropPlanted: string;
  cropType: string;
  cropProtection: IProduct[];
  fertilizer: IProduct[];
  miscellaneous: IProduct[];
  seeds: IProduct[];
  crops: IProduct[];
  referenceNumber: string;
  walletBalanceBefore: number;
  walletBalanceAfter: number;
  totalPrice: number;
  walletAllocation: number;
  cashAllocation?: number;
}

export interface IProduct {
  product: string;
  quantity: number;
  price: number;
  total: number;
}
