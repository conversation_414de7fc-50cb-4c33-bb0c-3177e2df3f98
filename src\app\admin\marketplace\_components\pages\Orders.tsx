'use client';

import Link from 'next/link';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';

import useMarketplace from '@/lib/hooks/useMarketplace';
import { useGlobalState } from '@/lib/store';

import { OrdersTable } from '../orders-table';
import { columns } from '../orders-table/columns';

export default function OrdersPage() {
  const gState = useGlobalState();
  const { getOrders, getDashboard } = useMarketplace();

  useEffect(() => {
    Promise.all([getOrders(), getDashboard()]);
  }, [
    gState.admin.pagination.orders.page,
    gState.admin.pagination.orders.pageSize,
    gState.admin.pagination.orders.status,
    gState.admin.pagination.orders.startDate,
    gState.admin.pagination.orders.endDate,
  ]);

  return (
    <div className="space-y-6 p-8">
      <OrdersTable
        data={gState.admin.marketplace.orders.data.get({ noproxy: true })}
        columns={columns}
        meta={gState.admin.marketplace.orders['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
