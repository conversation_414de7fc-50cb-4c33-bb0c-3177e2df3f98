'use client';

import { toast } from 'sonner';

import axios from '@/lib/api';

import { getUserType } from '../constants';
import { useGlobalState } from '../store';
import { useGlobalStatePersist } from '../store/persist';

export default function useLoanPayment() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const fetchDashboard = async () => {
    try {
      const _data = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanpayment/dashboard`, {
          params: {
            startDate: gState.finance.loanPayments.pagination.dashboard.startDate.value,
            endDate: gState.finance.loanPayments.pagination.dashboard.endDate.value,
          },
        })
        .then((res) => res.data.data);

      console.log('fetchDashboard: ', _data);
      gState.finance.loanPayments.dashboard.set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('fetchDashboard: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const fetchRequests = async () => {
    try {
      const _data = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanpayment/viewAll`, {
          params: {
            startDate: gState.finance.loanPayments.pagination.requests.startDate.value,
            endDate: gState.finance.loanPayments.pagination.requests.endDate.value,
            page: gState.finance.loanPayments.pagination.requests.page.value,
            pageSize: gState.finance.loanPayments.pagination.requests.pageSize.value,
            status: gState.finance.loanPayments.pagination.requests.status.value,
          },
        })
        .then((res) => res.data.data);

      console.log('fetchRequests: ', _data);
      gState.finance.loanPayments.requests.set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('fetchRequests: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const fetchRequest = async (id) => {
    try {
      const _data = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanpayment/view/${id}`)
        .then((res) => res.data.data);

      console.log('fetchRequest: ', _data);
      gStateP.selected['loanReq'].set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('fetchRequest: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const payLoan = async (data) => {
    try {
      const _data = await axios
        .post(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanpayment/request`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data.data);
      console.log('payLoan: ', _data);

      toast.success('Success', {
        description: 'Loan payment request submitted successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('payLoan: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const loanPaymentAction = async (action: 'approve' | 'reject', data) => {
    try {
      toast.loading(action === 'approve' ? 'Approving topup...' : 'Rejecting topup...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanpayment/${action}`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('loanPaymentAction: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: `${action === 'approve' ? 'Approve' : 'Reject'} loan payment successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('loanPaymentAction: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { fetchRequests, fetchDashboard, payLoan, fetchRequest, loanPaymentAction };
}
