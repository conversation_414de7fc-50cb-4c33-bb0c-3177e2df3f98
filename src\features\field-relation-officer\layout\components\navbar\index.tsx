'use client';

import { useHookstate } from '@hookstate/core';

import { useGlobalStatePersist } from '@/lib/store/persist';

import { Logo } from './logo';
import { MobileMenuButton } from './mobile-menu-button';
import { PageTitle } from './page-title';
import { FarmPlanCalcTabs } from './tabs';
import { UserProfileDropdown } from './user-profile-dropdown';

interface INavbarProps {
  onOpenMobileMenu: () => void;
  menuTitle: string;
}

export default function Navbar({ onOpenMobileMenu, menuTitle }: INavbarProps) {
  const gStateP = useGlobalStatePersist();
  const activeMenu = useHookstate(gStateP.fro.activeMenu);

  return (
    <nav className="flex border-b">
      <div className="w-auto p-4 pb-3 lg:w-[290px] lg:px-2 lg:pb-4">
        <MobileMenuButton onOpenMobileMenu={onOpenMobileMenu} />
        <Logo />
      </div>

      <div className="flex flex-1 items-end border-l px-6 pb-3">
        <div className="flex flex-1 items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <PageTitle title={menuTitle} />

            {/* Farm Plan & Crop Calculator */}
            {/* <FarmPlanCalcTabs isActive={activeMenu.value === 1} /> */}
          </div>

          <div className="flex items-center gap-4">
            <div className="flex gap-1">
              Hello, <span className="font-bold">{gStateP.user.user.fieldRelationOfficer.first_name.value}</span>
            </div>
            <UserProfileDropdown />
          </div>
        </div>
      </div>
    </nav>
  );
}
