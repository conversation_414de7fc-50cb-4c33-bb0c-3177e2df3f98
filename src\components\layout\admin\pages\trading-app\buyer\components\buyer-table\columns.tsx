'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, Pencil, StarIcon, ToggleLeft, ToggleRight, UserPlus } from 'lucide-react';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { FaStar } from 'react-icons/fa';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import { useQaStore } from '@/lib/store/admin/trading-app/qa-store';
import {
  EditQaStaffSchema,
  EditQaStaffType,
  IQa,
  QaStaffSchema,
  QaStaffType,
} from '@/lib/store/admin/trading-app/qa-store.types';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { capitalize, cn } from '@/lib/utils';

import { useBuyer } from '../../hooks/useBuyer';
import { EditBuyerSchema, EditBuyerType, IBuyer } from '../../hooks/useBuyer.types';

export const ColumnBuyer: ColumnDef<IBuyer>[] = [
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;

      return <div>{`${data.nonFarmer.first_name} ${data.nonFarmer.last_name}`}</div>;
    },
    accessorFn: (row) => `${row.nonFarmer.first_name} ${row.nonFarmer.last_name}`,
  },
  {
    id: 'username',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Username" />,
    accessorFn: (row) => `${row.email}`,
  },
  {
    id: 'user_rating',
    header: ({ column }) => <DataTableColumnHeader column={column} title="User Rating" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex items-center gap-1">
          {data.tradingAppUserRating ? (
            <>
              {Array.from({ length: 5 }, (_, index) => {
                const isFilled = index < Math.floor(data.tradingAppUserRating.rating);
                return (
                  <FaStar
                    key={index}
                    className={cn('size-4', {
                      'text-[#F5882C]': isFilled,
                      'text-gray-300': !isFilled,
                    })}
                  />
                );
              })}
            </>
          ) : (
            <>
              <FaStar className={cn('size-4 text-gray-300')} />
              <FaStar className={cn('size-4 text-gray-300')} />
              <FaStar className={cn('size-4 text-gray-300')} />
              <FaStar className={cn('size-4 text-gray-300')} />
              <FaStar className={cn('size-4 text-gray-300')} />
            </>
          )}
        </div>
      );
    },
    accessorFn: (row) =>
      row.tradingAppUserRating ? `${row.tradingAppUserRating.rating}/${row.tradingAppUserRating.total_rating}` : '0/5',
  },
  // {
  //   id: 'no_complaints',
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="No. of Complaints" />,
  //   accessorFn: (row) => `0`,
  // },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="flex items-center gap-3">
          <div className={cn('rounded-full w-2 h-2', data.status === 1 ? 'bg-green-500' : 'bg-red-500')}></div>
          <div>{data.status === 1 ? 'Active' : 'Inactive'}</div>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader className="text-center" column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

const Action = ({ row }) => {
  const data: IBuyer = row.original;
  const gStateP = useGlobalStatePersist();
  const { updateBuyer, toggleBuyer } = useBuyer();

  const deactivateConfirm = useHookstate(false);
  const activateConfirm = useHookstate(false);
  const editDialog = useHookstate(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(EditBuyerSchema),
    defaultValues: {
      password: '',
      password_confirmation: '',
    },
  });

  const onSubmit = async (_data: EditBuyerType) => {
    try {
      const updatedData = {
        ..._data,
        userId: data.id,
      };
      await updateBuyer(updatedData);
      editDialog.set(false);
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <>
      <div className="flex justify-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="size-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />

            {gStateP.user.value && gStateP.user.isSuperAdmin.value && (
              <DropdownMenuItem
                className="flex items-center"
                onClick={() => {
                  editDialog.set(true);
                }}
              >
                <Pencil className="mr-2 size-4" />
                <span>Edit</span>
              </DropdownMenuItem>
            )}

            {data.status === 1 && (
              <>
                <DropdownMenuItem className="flex items-center" onClick={() => deactivateConfirm.set(true)}>
                  <ToggleLeft className="mr-2 size-4" />
                  <span>Deactivate</span>
                </DropdownMenuItem>
              </>
            )}

            {data.status === 0 && (
              <>
                <DropdownMenuItem className="flex items-center" onClick={() => activateConfirm.set(true)}>
                  <ToggleRight className="mr-2 size-4" />
                  <span>Activate</span>
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Deactivate Confirmation */}
        <AlertDialog open={deactivateConfirm.value} onOpenChange={deactivateConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will deactivate the account associated with the username: {data.email}. This process cannot
                be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  toggleBuyer(data.id, 'deactivate');
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Activate Confirmation */}
        <AlertDialog open={activateConfirm.value} onOpenChange={activateConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will activate the account associated with the username: {data.email}. This process cannot be
                undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  toggleBuyer(data.id, 'activate');
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Edit Dialog */}
        <Dialog open={editDialog.value} onOpenChange={editDialog.set}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-primary">Edit User</DialogTitle>
              <DialogDescription>
                {`Fill up the forms to update a user. Click "Update" when you're ready.`}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="mt-3 grid grid-cols-2 gap-4">
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="password" className="pb-1 font-normal">
                    Password
                  </Label>
                  <InputPassword
                    {...register('password')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.password && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    placeholder="Min. 8 characters"
                  />
                  {errors.password && <p className="form-error">{`${errors.password.message}`}</p>}
                </div>

                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor="password_confirmation" className="pb-1 font-normal">
                    Re-Type Password
                  </Label>
                  <InputPassword
                    {...register('password_confirmation')}
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.password_confirmation && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    placeholder="Min. 8 characters"
                  />
                  {errors.password_confirmation && (
                    <p className="form-error">{`${errors.password_confirmation.message}`}</p>
                  )}
                </div>
              </div>

              <div className="flex justify-between gap-2 pt-6">
                <DialogClose asChild>
                  <Button className="px-12" variant="outline" type="button">
                    Cancel
                  </Button>
                </DialogClose>
                <Button className="px-12" type="submit">
                  Update
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};
