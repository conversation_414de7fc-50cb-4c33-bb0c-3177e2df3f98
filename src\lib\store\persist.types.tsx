import {
  IBarangay,
  IBusinessInfo,
  ICity,
  IDataPrivacy,
  IFarmInformation,
  IPersonalInformation,
  IProvince,
  IRegion,
} from '@/app/field-relation-officer/_components/types';

export interface ILogin {
  user: IUser;
  token: string;
  isSuperAdmin: boolean;
  isAdmin: boolean;
  isFarmer: boolean;
  isEncoder1: boolean;
  isEncoder2: boolean;
  isOperation1: boolean;
  isOperation2: boolean;
  isSale1: boolean;
  isSale2: boolean;
  isFinance1: boolean;
  isFinance2: boolean;
  isAgronomist?: boolean;
}

export interface IAgronomistState {
  activeMenu: number;
  tabs: {
    farmPlanCalc: TFarmPlanCalc;
  };
}

export interface IOption {
  value: any;
  label: string;
}

// interface IFROFormSteps {
//   activeStep: number;
//   step1: IDataPrivacy | null;
//   step2: IPersonalInformation | null;
//   step3: IFarmInformation | null;
//   step4: IBusinessInfo | null;
// }

interface IAddress {
  regions: IRegion[];
  provinces: IProvince[];
  cities: ICity[];
  barangays: IBarangay[];
}

interface IFROOptions {
  cropsPlanted: IOption[];
  address: IAddress;
}

interface IFROEdit {
  activeTab: string;
}
export interface IFROPersist {
  activeMenu: number;
  // form: IFROFormSteps;
  options: IFROOptions;
  edit: IFROEdit;
}

export type TFarmPlanCalc = 'farm-plan' | 'calc';

export interface IUser {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img?: null | string;
  remember_me_token: null | string;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null | string;
  admin: IAdmin;
  agronomist?: IAgronomist;
  fieldRelationOfficer?: IFieldRelationOfficer;
}

export interface IAdmin {
  id: number;
  first_name: string;
  last_name: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface IAgronomist {
  id: number;
  first_name: string;
  last_name: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface IFieldRelationOfficer {
  id: number;
  first_name: string;
  last_name: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}
