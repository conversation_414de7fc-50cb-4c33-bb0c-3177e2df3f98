// Tailwind CSS color palette (partial list - you can expand this)
const tailwindColors = {
  red: ['#ef4444', '#dc2626', '#b91c1c'],
  blue: ['#3b82f6', '#2563eb', '#1d4ed8'],
  green: ['#22c55e', '#16a34a', '#15803d'],
  yellow: ['#eab308', '#ca8a04', '#a16207'],
  purple: ['#a855f7', '#9333ea', '#7e22ce'],
  pink: ['#ec4899', '#db2777', '#be185d'],
  indigo: ['#6366f1', '#4f46e5', '#4338ca'],
  teal: ['#14b8a6', '#0d9488', '#0f766e'],
  orange: ['#f97316', '#ea580c', '#c2410c'],
  gray: ['#6b7280', '#4b5563', '#374151'],
  lime: ['#84cc16', '#65a30d', '#4d7c0f'],
  cyan: ['#22d3ee', '#06b6d4', '#0891b2'],
};

// Function to generate random color
export const generateRandomColor = () => {
  // Get all color classes into a single array
  const allColors = Object.values(tailwindColors).flat();
  // Pick random index
  const randomIndex = Math.floor(Math.random() * allColors.length);
  return allColors[randomIndex];
};
