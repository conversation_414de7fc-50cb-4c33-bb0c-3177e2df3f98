import '@/styles/globals.css';

import { GeistM<PERSON> } from 'geist/font/mono';
import { GeistSans } from 'geist/font/sans';
import { Metadata } from 'next';
import { NuqsAdapter } from 'nuqs/adapters/next/app';

import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';

import { isDevMode } from '@/lib/utils';
import { StateProvider } from '@/providers/app/state';
import { ThemeProvider } from '@/providers/lib/material-tailwind';
import ReactQueryProvider from '@/providers/lib/react-query';

export const metadata: Metadata = {
  metadataBase: new URL('https://kitaph-admin.vercel.app'),
  title: 'Admin | KitaPH',
  description: 'KitaPH - Admin Dashboard',
  keywords: ['kitaph', 'kita', 'admin', 'dashboard'],
  openGraph: {
    title: 'Admin | KitaPH',
    description: 'KitaPH - Admin Dashboard',
    images: 'kita-logo.png',
    type: 'website',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>{isDevMode && <script src="https://unpkg.com/react-scan/dist/auto.global.js" async />}</head>
      <body className={`${GeistSans.variable} ${GeistMono.variable}`}>
        <ReactQueryProvider>
          <ThemeProvider>
            <TooltipProvider delayDuration={100}>
              <StateProvider>
                <NuqsAdapter>{children}</NuqsAdapter>
              </StateProvider>
            </TooltipProvider>
          </ThemeProvider>
        </ReactQueryProvider>
        <Toaster closeButton />
      </body>
    </html>
  );
}
