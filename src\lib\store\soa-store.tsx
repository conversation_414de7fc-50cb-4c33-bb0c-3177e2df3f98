'use client';

import { useHookstate } from '@hookstate/core';

import axios from '@/lib/api';

import { isSoaEnabled } from '../config/features';
import { ISoaData } from './soa-store.types';

export const useSoaStore = (user = 'admin') => {
  const soa = useHookstate(null as ISoaData);
  const soaBulk = useHookstate([] as ISoaData[]);

  const getSoa = async (orderId: number) => {
    try {
      if (!isSoaEnabled) {
        console.warn('Soa is not enabled. Please check your environment variables.');
        return;
      }

      const { data } = await axios.get(`/${user}/marketplace/order/view/${orderId}/soa`);
      soa.set(data.data as ISoaData);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getSoa:', error);
    }
  };

  const getSoaBulk = async (orderIds: number[]) => {
    try {
      if (!isSoaEnabled) {
        console.warn('Soa is not enabled. Please check your environment variables.');
        return;
      }

      const { data } = await axios.get(`/${user}/marketplace/order/viewAll/soa`, {
        params: {
          marketplaceOrderIds: orderIds,
        },
      });

      console.log('getSoaBulk: ', data);
      soaBulk.set(data.data as ISoaData[]);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getSoaBulk:', error);
    }
  };

  return { getSoa, getSoaBulk, soa, soaBulk };
};
