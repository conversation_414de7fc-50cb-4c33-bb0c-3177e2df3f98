'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import Page1 from './Page1';
import Page2 from './Page2';

export default function LetterOfAuhorizationForm({
  isDialogOpen,
  setIsDialogOpen,
  data: farmerData,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
}) {
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: 'Letter Of Authorization to Hold Funds Form',
  });

  console.log('asdadasd', farmerData);

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Letter Of Authorization to Hold Funds Form</DialogTitle>
          </DialogHeader>
          <div className="mx-auto max-h-[50vh] max-w-4xl flex-1 flex-row justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div ref={contentRef}>
              <Page1 data={farmerData?.farmer} />
              <Page2 data={farmerData?.farmer} />
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
