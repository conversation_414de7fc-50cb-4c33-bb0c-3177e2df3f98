'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { ChevronLeft } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { DEFAULT_ADMIN_ACTIVE_MENU, getUserType } from '@/lib/constants';
import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { DialogNoLoan } from './DialogNoLoan';
import { DialogOutstandingBal } from './DialogOutstandingBal';

export default function FarmerList({ columns, data }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const plateNum = useSearchParams().get('plateNum');
  const { assignVehicle } = useFarmer();
  const from = gStateP.selected.from.get({ noproxy: true });
  const dialogOutstandingBal = useHookstate(false);
  const dialogNoLoan = useHookstate(false);

  const isEncoder = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'encoder';
  const isFinance = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'finance';
  const isFinanceTopupReq = isFinance && from === 'finance1-topupreqs';
  const isFinanceLoanReq = isFinance && from === 'loan-request-table';

  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';
  const isAdminTopup = isAdmin && from === 'admin-topupreqs';
  const isAdminTransactions = isAdmin && from === 'transactions';
  const isAdminMarkerplaceOrders = isAdmin && from === 'marketplace-orders';
  const isAdminLoanPayment = isAdmin && from === 'admin-loan-payment-table';
  const isAdminTopDown = isAdmin && from === 'admin-top-down';

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  return (
    <div>
      {/* Search */}
      <div className="flex items-center">
        <div className="w-[15%]">
          <Button
            variant="outline"
            size="icon"
            onClick={() => {
              if (isAdmin) {
                router.push('/admin');
                gStateP.admin.activeMenu.set(DEFAULT_ADMIN_ACTIVE_MENU);
              } else if (isFinance) {
                router.push('/finance');
              }
            }}
          >
            <ChevronLeft className="size-4" />
          </Button>
        </div>

        <div className="w-[70%]">
          <Input
            className="h-11 focus-visible:ring-primary"
            type="text"
            placeholder="Search by Name, Account ID"
            onChange={(event) => {
              table.setGlobalFilter(event.target.value);
            }}
          />
        </div>
      </div>

      <div className="font-dmSans mt-6 font-medium leading-loose text-primary md:text-xl lg:text-2xl">
        {isEncoder
          ? 'Select account to credit the transaction.'
          : isFinanceTopupReq || isAdminTopup
            ? 'Select account to add Top up request.'
            : isAdminTransactions
              ? 'Select account to add trading post transaction.'
              : isFinanceLoanReq || isAdminLoanPayment
                ? 'Select account to add loan payment request.'
                : isAdminMarkerplaceOrders
                  ? 'Select account to add order transaction.'
                  : isAdminTopDown
                    ? 'Select account to add top down request'
                    : ''}
      </div>

      <div className="mt-6 space-y-12">
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row, index) => {
            const item = row.original as any;

            return (
              <div
                key={index}
                className="font-dmSans cursor-pointer rounded-md border border-transparent bg-white p-6 shadow-xl shadow-slate-300 transition duration-300 ease-in-out hover:scale-110 hover:border-primary/50"
                onClick={async () => {
                  if (isFinanceLoanReq) {
                    const loanOutstandingBal = item.wallet?.credit || 0 - item.wallet?.payment || 0;
                    if (loanOutstandingBal === 0) {
                      dialogNoLoan.set(true);
                      return;
                    }

                    gStateP.selected['farmer'].set(item);
                    router.push('/finance/payment/request/');
                    return;
                  }

                  if (isFinanceTopupReq) {
                    const loanOutstandingBal = item.wallet?.credit || 0 - item.wallet?.payment || 0;
                    if (loanOutstandingBal > 0) {
                      dialogOutstandingBal.set(true);
                      return;
                    }

                    gStateP.selected['farmer'].set(item);
                    router.push('/finance/topup/request');
                    return;
                  }

                  if (isAdminLoanPayment) {
                    const loanOutstandingBal = item.wallet?.credit || 0 - item.wallet?.payment || 0;
                    if (loanOutstandingBal === 0) {
                      dialogNoLoan.set(true);
                      return;
                    }

                    gStateP.selected['farmer'].set(item);
                    router.push('/admin/marketplace/payment/request/');
                    return;
                  }

                  if (isAdminTopup) {
                    const loanOutstandingBal = item.wallet?.credit || 0 - item.wallet?.payment || 0;
                    if (loanOutstandingBal > 0) {
                      dialogOutstandingBal.set(true);
                      return;
                    }

                    gStateP.selected['farmer'].set(item);
                    router.push('/admin/marketplace/topup/request');
                    return;
                  }

                  if (isAdminTransactions) {
                    gStateP.selected['farmer'].set(item);
                    router.push('/admin/select-user/add-transaction/');
                    return;
                  }

                  if (isAdminTopDown) {
                    gStateP.selected['farmer'].set(item);
                    router.push('/admin/marketplace/topdown/request/');
                    return;
                  }

                  // admin
                  if (isAdminMarkerplaceOrders) {
                    gStateP.admin.orders.set({
                      customer: null as any,
                      data: [],
                      total: 0,
                      shippingDate: '',
                      fulfillmentType: '1',
                    });
                    gStateP.admin.orders['customer'].set(item);
                    router.push('/admin/marketplace/order/products');
                  }
                }}
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4">
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Farmer Name</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{`${item.farmer.first_name} ${item.farmer.last_name}`}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Account ID</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{`${item.id
                      .toString()
                      .padStart(9, '0')}`}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Birthdate</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{` ${
                      item.farmer.birth_date
                        ? new Date(item.farmer.birth_date).toLocaleString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: '2-digit',
                          })
                        : 'N/A'
                    }`}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-slate-400 lg:text-lg">Contact No.</div>
                    <div className="pt-1 font-medium text-primary lg:text-xl">{`${item.farmer.mobile_number}`}</div>
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center text-lg text-slate-400">No Result.</div>
        )}
      </div>

      <div className="mt-12">
        <DataTablePagination table={table} />
      </div>

      <DialogOutstandingBal state={dialogOutstandingBal} />
      <DialogNoLoan state={dialogNoLoan} />
    </div>
  );
}
