'use client';

import { toast } from 'sonner';
import { z } from 'zod';

import axios from '@/lib/api';

import { useGlobalState } from '../store';
import { IUserBulkUpload, IUserUpload } from '../types/user';
import { usernameValidation } from './useLogin';

export const UserSchema = z
  .object({
    userType: z.string().min(1, 'User role is required'),
    username: z.union([z.string().email().min(1, 'Email is required'), usernameValidation]),
    firstName: z.string().min(1, 'First Name is required'),
    lastName: z.string().min(1, 'Last Name is required'),
    password: z
      .string()
      .min(8, 'Min. 8 characters')
      .regex(
        /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).*$/,
        'Password must contain alphanumeric characters (A-Z, a-z, 0-9) and at least one special character (!@#$%^&*...)',
      ),
    password_confirmation: z.string().min(8, 'Min. 8 characters'),
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Passwords don't match",
    path: ['password_confirmation'], // This shows where the error will be attached to
  });
export type UserType = z.infer<typeof UserSchema>;

export const EditUserSchema = z
  .object({
    userType: z.string().min(1, 'User role is required'),
    username: z.union([z.string().email().min(1, 'Email is required'), usernameValidation]),
    firstName: z.string().min(1, 'First Name is required'),
    lastName: z.string().min(1, 'Last Name is required'),
    password: z
      .string()
      .regex(
        /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).*$/,
        'Password must contain alphanumeric characters (A-Z, a-z, 0-9) and at least one special character (!@#$%^&*...)',
      )
      .optional(),
    password_confirmation: z.string().optional(),
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Passwords don't match",
    path: ['password_confirmation'], // This shows where the error will be attached to
  });
export type EditUserType = z.infer<typeof EditUserSchema>;

export default function useUsers() {
  const gState = useGlobalState();

  const getUsers = async () => {
    try {
      const _users = await axios.get(`/admin/user/viewAll`).then((res) => res.data.data);
      console.log('users: ', _users);

      gState.admin.users.data.set(_users);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('users error: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addUser = async (data: UserType) => {
    console.log('addUser: ', data);

    try {
      const _data = await axios
        .post(`/admin/user/create`, {
          ...data,
          username: data.username.trim(),
          password: data.password.trim(),
          password_confirmation: data.password_confirmation.trim(),
        })
        .then((res) => res.data);
      console.log('addUser: ', _data);
      await getUsers();

      toast.success('Success', {
        description: 'User added successfully',
        duration: 5000,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addUser: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const editUser = async (data: EditUserType) => {
    console.log('editUser: ', data);

    try {
      const _data = await axios
        .post(`/admin/user/update`, {
          ...data,
          userId: gState.admin.users['edit']['id'].value,
          username: data.username.trim(),
          password: data.password.trim(),
          password_confirmation: data.password_confirmation.trim(),
        })
        .then((res) => res.data);
      console.log('editUser: ', _data);
      await getUsers();

      toast.success('Success', {
        description: 'User updated successfully',
        duration: 5000,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('editUser: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const deactivateUser = async (userId: string | number) => {
    try {
      const _data = await axios
        .post(`/admin/user/deactivate`, {
          userId,
        })
        .then((res) => res.data);
      console.log('deactivateUser: ', _data);
      await getUsers();

      toast.success('Success', {
        description: 'User deactivated successfully',
        duration: 5000,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('deactivateUser: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const activateUser = async (userId: string | number) => {
    try {
      const _data = await axios
        .post(`/admin/user/activate`, {
          userId,
        })
        .then((res) => res.data);
      console.log('activateUser: ', _data);
      await getUsers();

      toast.success('Success', {
        description: 'User activated successfully',
        duration: 5000,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('activateUser: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const bulkUploadUsers = async (data: IUserBulkUpload) => {
    try {
      if (!data.users.length)
        return toast.warning('Oops! Something went wrong', { description: 'No data to be uploaded.' });

      const _data = await axios
        .post('/admin/user/farmers/import', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);

      await getUsersBulkPaginated();

      toast.success('Success', {
        description: _data.message,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getUsersBulkPaginated = async () => {
    try {
      const _data = await axios
        .get(`/admin/user/farmers/imported/viewAll`, {
          params: {
            search: gState.admin.pagination.usersBulk.search.value,
            page: gState.admin.pagination.usersBulk.page.value,
            pageSize: gState.admin.pagination.usersBulk.pageSize.value,
          },
        })
        .then((res) => res.data.data);
      gState.admin.usersBulk.data.set(_data.data);
      gState.admin.usersBulk.metadata.set(_data.meta);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getUsersBulkPaginated: ', error);
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { getUsers, addUser, deactivateUser, activateUser, editUser, bulkUploadUsers, getUsersBulkPaginated };
}
