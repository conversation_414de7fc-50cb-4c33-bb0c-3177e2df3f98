'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import { cn } from '@/lib/utils';

import { Button } from './ui/button';

export const tabState = hookstate('');

export function TabItem({ children, value = '', className = '', ...props }) {
  const state = useHookstate(tabState);

  return (
    <Button
      className={cn(
        'flex items-center gap-2 border rounded-md cursor-pointer active:scale-110',
        'transition duration-300 ease-in-out',
        value === state.value
          ? 'bg-white shadow-md text-blue-500 border-blue-500 hover:bg-white hover:border-transparent hover:text-blue-500'
          : 'border-transparent hover:bg-white hover:shadow-md hover:text-blue-500 text-gray-600',
        className,
      )}
      variant="ghost"
      onClick={() => {
        state.set(value);
      }}
      {...props}
    >
      {children}
    </Button>
  );
}

export function Tab({ children, defaultValue = '', className = '', onValueChange = (value) => {}, ...props }) {
  const state = useHookstate(tabState);
  const prevValue = useHookstate('');

  useEffect(() => {
    state.set(defaultValue);
  }, []);

  useEffect(() => {
    if (prevValue.value === state.value) return;

    onValueChange(state.value);
    prevValue.set(state.value);
  }, [state, prevValue]);

  return (
    <div className={cn('flex gap-4', className)} {...props}>
      {children}
    </div>
  );
}
