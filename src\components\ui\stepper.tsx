import React from 'react';

import { cn } from '@/lib/utils';

interface IStep {
  number: number;
  label: string;
  icon?: React.ReactNode;
}

interface IStepperProps {
  steps: IStep[];
  currentStep: number;
  onStepClick?: (step: number) => void;
}

const Stepper = ({ steps, currentStep, onStepClick }: IStepperProps) => {
  return (
    <div className="flex w-full items-center px-8 pb-8 pt-12 ">
      {steps.map((val, idx, arr) => {
        const isActive = idx + 1 <= currentStep;

        return (
          <div
            key={idx}
            className={cn('flex w-full items-center cursor-pointer', idx + 1 === arr.length && 'w-auto flex-none')}
            onClick={() => onStepClick?.(idx + 1)}
          >
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  'relative z-10 flex-col w-[130px] h-full text-xs text-center p-4 items-center justify-center rounded-md font-bold bg-[#f1f1f1] border-2 ',
                  isActive ? 'border-[#919191]' : 'border-[#d9d9d9]',
                )}
              >
                {val.icon && <div className="mb-2 flex items-center justify-center text-lg">{val.icon}</div>}
                {val.label}
                <div
                  className={cn(
                    'mt-2 h-5 w-5 mx-auto rounded-full border-2 transition-colors',
                    isActive ? 'bg-green-500 border-green-600' : 'bg-white border-gray-400',
                  )}
                />
              </div>
            </div>
            {idx < arr.length - 1 && (
              <div className={cn('mx-2 h-1 flex-1', idx + 1 <= currentStep ? 'bg-primary' : 'bg-slate-200')} />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default Stepper;
