'use client';

import { useHookstate } from '@hookstate/core';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useEffect } from 'react';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Input, InputSign } from '@/components/ui/input';

import useCreditScoring from '@/lib/hooks/admin/useCreditScoring';
import { globalState, useGlobalState } from '@/lib/store';
import { catchError, cn } from '@/lib/utils';

export default function BeforeLoan() {
  const gState = useGlobalState();
  const { updateRules } = useCreditScoring();
  const rules = useHookstate(gState.creditScoring.rules);
  const beforeLoan = useHookstate(gState.creditScoring.beforeRules);
  const duringLoan = useHookstate(gState.creditScoring.duringRules);
  const afterLoan = useHookstate(gState.creditScoring.afterRules);
  const accountProfile = useHookstate(gState.creditScoring.accountProfile);
  const agricultureActivity = useHookstate(gState.creditScoring.agricultureActivity);
  const transactionRecords = useHookstate(gState.creditScoring.transactionRecords);

  const [creditScoreGroupId, setCreditScoreGroupId] = useQueryState('id', parseAsInteger);

  // Calculate total score whenever any rule score changes
  useEffect(() => {
    if (accountProfile['rules']?.length) {
      const totalScore = accountProfile['rules'].reduce((sum, rule) => {
        return sum + (rule.score?.get({ noproxy: true }) || 0);
      }, 0);
      accountProfile['score'].set(totalScore);
    }

    if (agricultureActivity['rules']?.length) {
      const totalScore = agricultureActivity['rules'].reduce((sum, rule) => {
        return sum + (rule.score?.get({ noproxy: true }) || 0);
      }, 0);
      agricultureActivity['score'].set(totalScore);
    }

    if (transactionRecords['rules']?.length) {
      const totalScore = transactionRecords['rules'].reduce((sum, rule) => {
        return sum + (rule.score?.get({ noproxy: true }) || 0);
      }, 0);
      transactionRecords['score'].set(totalScore);
    }

    // Update the total beforeLoan score
    const totalBeforeLoanScore =
      (accountProfile['score']?.get({ noproxy: true }) || 0) +
      (agricultureActivity['score']?.get({ noproxy: true }) || 0) +
      (transactionRecords['score']?.get({ noproxy: true }) || 0);

    // Initialize score property if it doesn't exist
    if (!beforeLoan['score']) {
      beforeLoan.merge({ score: totalBeforeLoanScore });
    } else {
      beforeLoan['score'].set(totalBeforeLoanScore);
    }
  }, [accountProfile['rules'], agricultureActivity['rules'], transactionRecords['rules']]);

  const handleUpdate = async () => {
    try {
      const data = {
        creditScoreGroupId: creditScoreGroupId,
        creditScoringConfig: {
          stages: [
            beforeLoan.get({ noproxy: true }),
            duringLoan.get({ noproxy: true }),
            afterLoan.get({ noproxy: true }),
          ],
        },
      };

      await updateRules(creditScoreGroupId, data);

      console.log('data: ', data);
    } catch (e) {
      catchError(e, 'handleUpdate');
    }
  };

  return (
    <div className="grid gap-3">
      <div className="max-w-5xl">
        <Accordion type="single" collapsible className="w-full">
          {/* Account Profile */}
          <AccordionItem value="item-1">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Account Profile</h1>
                <div className="font-bold text-primary">{`${accountProfile['score']?.value || 0}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              {accountProfile['rules']?.map((rule, index) => (
                <div key={index}>
                  <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                    <h1 className="font-medium">{rule.name?.get({ noproxy: true }) || ''}</h1>
                    <div className="flex shrink-0">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={rule.score?.get({ noproxy: true }) || 0}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          rule.score.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>

                  {rule.hasConditions?.get({ noproxy: true }) && (
                    <div className="ml-10 max-w-2xl py-4 sm:ml-20">
                      <div>
                        <div className="text-sm font-light">Points Computation Table</div>

                        {rule.conditions?.get({ noproxy: true })?.map((condition, condIndex) => (
                          <div key={condIndex} className="ml-5 mt-3 grid gap-3 sm:ml-10">
                            <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                              <div className="flex items-center gap-4">
                                {condition.order === 1 ? (
                                  <InputSign
                                    sign="PHP"
                                    signPosition="left"
                                    className="h-8 max-w-44 focus-visible:ring-primary"
                                    placeholder="0"
                                    min={0}
                                    type="number"
                                    value={condition.lowerBound}
                                    disabled
                                  />
                                ) : condition.upperBound !== null ? (
                                  <InputSign
                                    sign="PHP"
                                    signPosition="left"
                                    className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                                    placeholder="0"
                                    disabled
                                    value={condition.lowerBound}
                                  />
                                ) : (
                                  <>
                                    <div>Greater than or equal to</div>
                                    <InputSign
                                      sign="PHP"
                                      signPosition="left"
                                      className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                                      placeholder="0"
                                      disabled
                                      value={condition.lowerBound}
                                    />
                                  </>
                                )}

                                {condition.upperBound !== null && (
                                  <>
                                    <div>up to</div>
                                    <InputSign
                                      sign="PHP"
                                      signPosition="left"
                                      className="h-8 max-w-44 focus-visible:ring-primary"
                                      placeholder="0"
                                      min={0}
                                      type="number"
                                      value={condition.upperBound}
                                      onChange={(e) => {
                                        if (e.target.value.length > 3) return;
                                        rule.conditions[condIndex].upperBound.set(Number(e.target.value));
                                        if (condIndex < rule.conditions.length - 2) {
                                          rule.conditions[condIndex + 1].lowerBound.set(Number(e.target.value) + 1);
                                        }
                                      }}
                                    />
                                  </>
                                )}
                              </div>

                              <div className="flex">
                                <InputSign
                                  sign="pts"
                                  className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                                  type="number"
                                  min={0}
                                  placeholder="0"
                                  value={condition.score}
                                  onChange={(e) => {
                                    if (e.target.value.length > 3) return;
                                    rule.conditions[condIndex].score.set(Number(e.target.value));
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>

          {/* Agriculture Activity */}
          <AccordionItem value="item-2">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Agriculture Activity</h1>
                <div className="font-bold text-primary">{`${agricultureActivity['score']?.value || 0}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              {agricultureActivity['rules']?.map((rule, index) => (
                <div key={index}>
                  <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                    <h1 className="font-medium">{rule.name?.get({ noproxy: true }) || ''}</h1>
                    <div className="flex shrink-0">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={rule.score?.get({ noproxy: true }) || 0}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          rule.score.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>

          {/* Transaction Records */}
          <AccordionItem value="item-3">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Transaction Records</h1>
                <div className="font-bold text-primary">{`${transactionRecords['score']?.value || 0}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              {transactionRecords['rules']?.map((rule, index) => (
                <div key={index}>
                  <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                    <h1 className="font-medium">{rule.name?.get({ noproxy: true }) || ''}</h1>
                    <div className="flex shrink-0">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={rule.score?.get({ noproxy: true }) || 0}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          rule.score.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>

                  {rule.hasConditions?.get({ noproxy: true }) && (
                    <div className="ml-10 max-w-2xl py-4 sm:ml-20">
                      <div>
                        <div className="text-sm font-light">Points Computation Table</div>

                        {rule.conditions?.get({ noproxy: true })?.map((condition, condIndex) => (
                          <div key={condIndex} className="ml-5 mt-3 grid gap-3 sm:ml-10">
                            <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                              <div className="flex items-center gap-4">
                                {condition.order === 1 ? (
                                  <InputSign
                                    sign="PHP"
                                    signPosition="left"
                                    className="h-8 max-w-44 focus-visible:ring-primary"
                                    placeholder="0"
                                    min={0}
                                    type="number"
                                    value={condition.lowerBound}
                                    disabled
                                  />
                                ) : condition.upperBound !== null ? (
                                  <InputSign
                                    sign="PHP"
                                    signPosition="left"
                                    className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                                    placeholder="0"
                                    disabled
                                    value={condition.lowerBound}
                                  />
                                ) : (
                                  <>
                                    <div>Greater than or equal to</div>
                                    <InputSign
                                      sign="PHP"
                                      signPosition="left"
                                      className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                                      placeholder="0"
                                      disabled
                                      value={condition.lowerBound}
                                    />
                                  </>
                                )}

                                {condition.upperBound !== null && (
                                  <>
                                    <div>up to</div>
                                    <InputSign
                                      sign="PHP"
                                      signPosition="left"
                                      className="h-8 max-w-44 focus-visible:ring-primary"
                                      placeholder="0"
                                      min={0}
                                      type="number"
                                      value={condition.upperBound}
                                      onChange={(e) => {
                                        if (e.target.value.length > 3) return;
                                        rule.conditions[condIndex].upperBound.set(Number(e.target.value));
                                        if (condIndex < rule.conditions.length - 2) {
                                          rule.conditions[condIndex + 1].lowerBound.set(Number(e.target.value) + 1);
                                        }
                                      }}
                                    />
                                  </>
                                )}
                              </div>

                              <div className="flex">
                                <InputSign
                                  sign="pts"
                                  className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                                  type="number"
                                  min={0}
                                  placeholder="0"
                                  value={condition.score}
                                  onChange={(e) => {
                                    if (e.target.value.length > 3) return;
                                    rule.conditions[condIndex].score.set(Number(e.target.value));
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <div className="flex w-full max-w-5xl justify-between py-4">
          <h1 className="font-bold text-primary">Total</h1>
          <div className="font-bold text-primary">{`${beforeLoan['score']?.value}%` || 0}</div>
        </div>
      </div>

      <div className="mt-8">
        <Button type="button" className="px-12" onClick={handleUpdate}>
          Submit
        </Button>
      </div>
    </div>
  );
}
