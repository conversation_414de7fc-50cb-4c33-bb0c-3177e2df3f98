'use client';

import { toast } from 'sonner';
import { z } from 'zod';

import { Option } from '@/components/ui/multiple-selector';

import axios from '@/lib/api';

import { useGlobalState } from '../store';
import { ICropPriceRangeBacklogUpload } from '../types/crop.types';

export const CropSchema = z.object({
  crops: z.array(
    z.object({
      name: z
        .string()
        .min(2, 'Crop name is required')
        .max(50, 'Crop name is too long')
        .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
          message: 'Crop name must contain only letters and spaces',
        }),
      harvestDays: z
        .string()
        .min(1, 'Harvest days is required')
        .max(3, 'Harvest days is too long')
        .refine((val) => /^\d+$/.test(val) && parseInt(val) > 0, {
          message: 'Invalid Harvest days',
        }),
    }),
  ),
});
export type CropType = z.infer<typeof CropSchema>;

export const CropSingleSchema = z.object({
  name: z
    .string()
    .min(2, 'Crop name is required')
    .max(50, 'Crop name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Crop name must contain only letters and spaces',
    }),
  harvestDays: z
    .string()
    .min(1, 'Harvest days is required')
    .max(3, 'Harvest days is too long')
    .refine((val) => /^\d+$/.test(val) && parseInt(val) > 0, {
      message: 'Invalid Harvest days',
    }),
  keywords: z.array(z.object({ label: z.string(), value: z.string() })),
  sapItemCode: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (val === undefined || val === '') return true; // Allow empty/undefined
        if (val.trim() === '' && val.length > 0) return false; // Reject space-only strings
        return true; // Allow valid codes
      },
      {
        message: 'SAP Item Code cannot contain only spaces',
      },
    )
    .transform((val) => val?.trim() || undefined),
});
export type CropSingleType = z.infer<typeof CropSingleSchema>;

export const CropPriceSchema = z.object({
  cropsPrices: z.array(
    z.object({
      cropId: z.union([z.string(), z.number()]),
      sellingPrice: z.union([
        z
          .string()
          .min(1, 'Selling price is required')
          .regex(/^\d+(\.\d+)?$/, 'Invalid price format'),
        z.number().nonnegative('Price must be non-negative'),
      ]),
      productionPrice: z.union([
        z
          .string()
          .min(1, 'Production price is required')
          .regex(/^\d+(\.\d+)?$/, 'Invalid price format'),
        z.number().nonnegative('Price must be non-negative'),
      ]),
    }),
  ),
});
export type CropPriceType = z.infer<typeof CropPriceSchema>;

export const UpdateCropSchema = z.object({
  cropId: z.string().min(1, 'Crop ID is required'),
  name: z
    .string()
    .min(2, 'Crop name is required')
    .max(50, 'Crop name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Crop name must contain only letters and spaces',
    }),
  harvestDays: z
    .string()
    .min(1, 'Harvest days is required')
    .max(3, 'Harvest days is too long')
    .refine((val) => /^\d+$/.test(val) && parseInt(val) > 0, {
      message: 'Invalid Harvest days',
    }),
  keywords: z.array(z.object({ label: z.string(), value: z.string() })),
  status: z.string().min(1, 'Crop status is required'),
  sapItemCode: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (val === undefined || val === '') return true; // Allow empty/undefined
        if (val.trim() === '' && val.length > 0) return false; // Reject space-only strings
        return true; // Allow valid codes
      },
      {
        message: 'SAP Item Code cannot contain only spaces',
      },
    )
    .transform((val) => val?.trim() || undefined),
});
export type UpdateCropType = z.infer<typeof UpdateCropSchema>;

export const CropPriceRangeSchema = z.object({
  cropsPrices: z.array(
    z
      .object({
        cropId: z.coerce.number(),
        lowPrice: z.coerce.number().gte(0, { message: 'Low price must be non-negative' }),
        highPrice: z.coerce.number().gte(0, { message: 'High price must be non-negative' }),
        lowBaptcPrice: z.coerce.number().gte(0, { message: 'Low BAPTC price must be non-negative' }),
        highBaptcPrice: z.coerce.number().gte(0, { message: 'High BAPTC price must be non-negative' }),
        lowNvatPrice: z.coerce.number().gte(0, { message: 'Low N-VAT price must be non-negative' }),
        highNvatPrice: z.coerce.number().gte(0, { message: 'High N-VAT price must be non-negative' }),
      })
      .superRefine((val, ctx) => {
        // If one of the price has a value greater than 0, the other must have a value of greater than 0
        const { lowPrice, highPrice, lowBaptcPrice, highBaptcPrice, lowNvatPrice, highNvatPrice } = val;
        const isDirty =
          lowPrice > 0 ||
          highPrice > 0 ||
          lowBaptcPrice > 0 ||
          highBaptcPrice > 0 ||
          lowNvatPrice > 0 ||
          highNvatPrice > 0;

        if (isDirty) {
          // Low Price
          /* if (lowPrice === 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Low Price cannot be 0',
              path: ['lowPrice'],
            });
          } */
          if (lowPrice >= 0) {
            if (lowPrice > highPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low Price cannot be higher than High Price.',
                path: ['lowPrice'],
              });
            }
          }
          // High Price
          /* if (highPrice === 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'High Price cannot be 0',
              path: ['highPrice'],
            });
          } */
          if (highPrice >= 0) {
            if (lowPrice > highPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low Price cannot be higher than High Price.',
                path: ['lowPrice'],
              });
            }
            if (highPrice < lowPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low Price cannot be higher than High Price.',
                path: ['lowPrice'],
              });
            }
          }

          // Low BAPTC Price
          /* if (lowBaptcPrice === 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Low BAPTC Price cannot be 0',
              path: ['lowBaptcPrice'],
            });
          } */
          if (lowBaptcPrice >= 0) {
            if (lowBaptcPrice > highBaptcPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low BAPTC Price cannot be higher than High BAPTC Price.',
                path: ['lowBaptcPrice'],
              });
            }
          }
          // High BAPTC Price
          /* if (highBaptcPrice === 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'High BAPTC Price cannot be 0',
              path: ['highBaptcPrice'],
            });
          } */
          if (highBaptcPrice >= 0) {
            if (lowBaptcPrice > highBaptcPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low BAPTC Price cannot be higher than High BAPTC Price.',
                path: ['lowBaptcPrice'],
              });
            }
            if (highBaptcPrice < lowBaptcPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low BAPTC Price cannot be higher than High BAPTC Price.',
                path: ['lowBaptcPrice'],
              });
            }
          }

          // Low NVAT Price
          /* if (lowNvatPrice === 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Low NVAT Price cannot be 0',
              path: ['lowNvatPrice'],
            });
          } */
          if (lowNvatPrice >= 0) {
            if (lowNvatPrice > highNvatPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low NVAT Price cannot be higher than High NVAT Price.',
                path: ['lowNvatPrice'],
              });
            }
          }
          // High NVAT Price
          /* if (highNvatPrice === 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'High NVAT Price cannot be 0',
              path: ['highNvatPrice'],
            });
          } */
          if (highNvatPrice >= 0) {
            if (lowNvatPrice > highNvatPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low NVAT Price cannot be higher than High NVAT Price.',
                path: ['lowNvatPrice'],
              });
            }
            if (highNvatPrice < lowNvatPrice) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Low NVAT Price cannot be higher than High NVAT Price.',
                path: ['lowNvatPrice'],
              });
            }
          }
        }
      }),
  ),
});
export type CropPriceRangeType = z.infer<typeof CropPriceRangeSchema>;

export const SingleCropPriceRangeSchema = z.object({
  cropsPrices: z.array(
    z
      .object({
        cropId: z.coerce.number(),
        lowPrice: z.coerce.number().gte(0, { message: 'Low price must be non-negative' }),
        highPrice: z.coerce.number().gte(0, { message: 'High price must be non-negative' }),
        lowBaptcPrice: z.coerce.number().gte(0, { message: 'Low BAPTC price must be non-negative' }),
        highBaptcPrice: z.coerce.number().gte(0, { message: 'High BAPTC price must be non-negative' }),
        lowNvatPrice: z.coerce.number().gte(0, { message: 'Low N-VAT price must be non-negative' }),
        highNvatPrice: z.coerce.number().gte(0, { message: 'High N-VAT price must be non-negative' }),
      })
      .refine((data) => data.lowPrice <= data.highPrice, {
        message: 'Low Price cannot be higher than High Price.',
        path: ['lowPrice'],
      })
      .refine((data) => data.highPrice >= data.lowPrice, {
        message: 'High Price cannot be lower than Low Price.',
        path: ['highPrice'],
      })
      .refine((data) => data.lowBaptcPrice <= data.highBaptcPrice, {
        message: 'Low BAPTC Price cannot be higher than High BAPTC Price.',
        path: ['lowBaptcPrice'],
      })
      .refine((data) => data.highBaptcPrice >= data.lowBaptcPrice, {
        message: 'High BAPTC Price cannot be lower than Low BAPTC Price.',
        path: ['highBaptcPrice'],
      })
      .refine((data) => data.lowNvatPrice <= data.highNvatPrice, {
        message: 'Low N-VAT Price cannot be higher than High N-VAT Price.',
        path: ['lowNvatPrice'],
      })
      .refine((data) => data.highNvatPrice >= data.lowNvatPrice, {
        message: 'High N-VAT Price cannot be lower than Low N-VAT Price.',
        path: ['highNvatPrice'],
      }),
  ),
});
export type SingleCropPriceRangeType = z.infer<typeof SingleCropPriceRangeSchema>;

export default function useCrops() {
  const gState = useGlobalState();

  const getCrops = async () => {
    try {
      const _crops = await axios.get('/admin/crops/viewAll').then((res) => res.data.data);
      console.log('getCrops: ', _crops);
      gState.admin.crops.data.set(_crops);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getCrops: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getCropsPaginated = async () => {
    try {
      const _crops = await axios
        .get('/admin/crops/viewAllPaginated', {
          params: {
            page: gState.admin.pagination.crops.page.value,
            pageSize: gState.admin.pagination.crops.pageSize.value,
            search: gState.admin.pagination.crops.search.value,
            status: gState.admin.pagination.crops.status.value,
          },
        })
        .then((res) => res.data.data);
      console.log('getCropsPaginated: ', _crops);
      gState.admin.crops.paginated.set(_crops);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getCropsPaginated: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getCropsPublic = async () => {
    try {
      const _crops = await axios.get('/crops/viewAll').then((res) => res.data.data);
      console.log('getCrops: ', _crops);
      gState.admin.crops.data.set(_crops);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getCrops: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getCropPriceHistory = async (cropId) => {
    try {
      const _cropHistory = await axios.get(`/admin/crops/prices/history/${cropId}`).then((res) => res.data.data);
      console.log('getCropPriceHistory: ', _cropHistory);

      gState.admin.crops.history.set(_cropHistory);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getCropPriceHistory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getCropPriceRangeHistory = async (cropId) => {
    try {
      const _cropHistory = await axios
        .get(`/admin/crops/price-ranges/history/${cropId}`, {
          params: {
            ...gState.admin.pagination.cropPriceHistory.get({ noproxy: true }),
          },
        })
        .then((res) => res.data.data);
      console.log('getCropPriceRangeHistory: ', _cropHistory);

      gState.admin.crops.priceHistory.set(_cropHistory);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getCropPriceRangeHistory: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addCrops = async (data: CropType) => {
    console.log('addCrops: ', data);

    try {
      const _data = await axios.post('/admin/crops/create', data).then((res) => res.data);
      console.log('addCrops: ', _data);
      await getCrops();
      await getCropsPaginated();

      toast.success('Sucess', {
        description: 'Crops added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addCrops: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addCropSingle = async (data: any) => {
    console.log('addCropSingle: ', data);

    try {
      const _data = await axios
        .post('/admin/crops/create/single', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('addCropSingle: ', _data);
      await getCrops();
      await getCropsPaginated();

      toast.success('Sucess', {
        description: `${data.name} has been added`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addCropSingle: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateCrop = async (data) => {
    console.log('updateCrop: ', data);

    try {
      const _data = await axios
        .post('/admin/crops/update', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('updateCrop: ', _data);
      await getCrops();
      await getCropsPaginated();

      toast.success('Success', {
        description: 'Crops updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateCrop: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addPrice = async (data: CropPriceType) => {
    console.log('addPrice: ', data);
    try {
      const _data = await axios.post('/admin/crops/prices/update', data).then((res) => res.data);
      console.log('addPrice: ', _data);

      if (data.cropsPrices.length === 1) await getCropPriceRangeHistory(data.cropsPrices[0].cropId);

      toast.success('Success', {
        description: 'Price added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addPrice: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updatePriceRange = async (data: CropPriceRangeType) => {
    console.log('updatePriceRange: ', data);
    try {
      const _data = await axios.post('/admin/crops/price-ranges/update', data).then((res) => res.data);
      console.log('updatePriceRange: ', _data);
      await getCrops();
      await getCropsPaginated();

      if (data.cropsPrices.length === 1) await getCropPriceRangeHistory(data.cropsPrices[0].cropId);

      toast.success('Success', {
        description: _data.message,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updatePriceRange: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updatePriceRangeBacklog = async (data: ICropPriceRangeBacklogUpload) => {
    console.log('updatePriceRange: ', data);
    try {
      const _data = await axios.post('/admin/crops/price-ranges/update/backlog', data).then((res) => res.data);
      console.log('updatePriceRange: ', _data);
      await getCrops();
      await getCropsPaginated();

      if (data.cropsPrices.length === 1) await getCropPriceRangeHistory(data.cropsPrices[0].cropId);

      toast.success('Success', {
        description: _data.message,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updatePriceRange: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    getCrops,
    addCrops,
    getCropPriceHistory,
    addPrice,
    getCropsPublic,
    updateCrop,
    addCropSingle,
    updatePriceRange,
    getCropsPaginated,
    updatePriceRangeBacklog,
    getCropPriceRangeHistory,
  };
}
