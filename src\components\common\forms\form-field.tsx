'use client';

import { ReactNode } from 'react';
import { FieldErrors } from 'react-hook-form';

import { Label } from '@/components/ui/label';

import { cn } from '@/lib/utils';

interface IFormFieldProps {
  name: string;
  label: string;
  errors: FieldErrors;
  required?: boolean;
  children: ReactNode;
  className?: string;
}

export function FormField({ name, label, errors, required = false, children, className }: IFormFieldProps) {
  let fieldError: any = undefined;

  // Check if this is a nested field (name contains dots)
  if (name.includes('.')) {
    // For nested fields, extract just the last part of the name (e.g., "upload" from "governmentIdentification.0.upload")
    const fieldName = name.split('.').pop() || '';

    // For nested fields, the errors object is already the subset for this field
    fieldError = errors[fieldName];
  } else {
    // For top-level fields, access the error directly
    fieldError = errors[name];
  }

  return (
    <div className={cn('grid w-full max-w-sm items-center gap-1.5', className)}>
      <Label htmlFor={name} className="pb-1 font-normal">
        {label} {required && <span className="ml-1 font-bold text-red-500">*</span>}
      </Label>
      {children}
      {fieldError && <p className="form-error">{`${fieldError?.message}`}</p>}{' '}
    </div>
  );
}
