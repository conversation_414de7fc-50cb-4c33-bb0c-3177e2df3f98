'use client';

import { useHookstate } from '@hookstate/core';
import { PaperclipIcon } from 'lucide-react';
import { Control, Controller, FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';

import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

interface IBiometricInfoFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
}

export default function BiometricInfoForm({ register, control, errors }: IBiometricInfoFormProps) {
  const gState = useGlobalState();
  const data = useHookstate(gState.selected.accountInfo['info']);

  // Check if facial recognition file exists
  const facialRecognitionFile = data.farmer.facial_recognition?.value || '';
  const facialRecognitionSplit = facialRecognitionFile?.split('/');
  const facialRecognitionName = facialRecognitionSplit
    ? facialRecognitionSplit[facialRecognitionSplit.length - 1]
    : null;

  return (
    <div>
      <FormTitle title="Other Information" className="mt-6" />

      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        {/* Facial Biometric Upload */}
        <FormField name="facialRecognition" label="Facial Biometric" errors={errors} className="">
          <div className="flex items-center gap-4">
            <Input
              {...register('facialRecognition')}
              className={cn(
                'focus-visible:ring-primary',
                errors?.facialRecognition && 'border-red-500 focus-visible:ring-red-500',
              )}
              type="file"
              accept=".ply"
              placeholder="No file selected"
            />
          </div>

          {facialRecognitionFile && (
            <div className="mt-2">
              <a
                className="flex items-center gap-2 text-primary hover:underline"
                href={facialRecognitionFile}
                target="_blank"
              >
                <PaperclipIcon className="size-4" />
                {facialRecognitionName}
              </a>
            </div>
          )}
        </FormField>

        {/* Biometric */}
        <FormField name="hasBiometric" label="" errors={errors}>
          <Controller
            control={control}
            name="hasBiometric"
            render={({ field: { onChange, value } }) => (
              <div className="flex items-center gap-2">
                <Checkbox id="hasBiometric" checked={value} onCheckedChange={onChange} />
                <div className="grid pt-[2px] leading-none">
                  <label
                    htmlFor="hasBiometric"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Fingerprint Biometrics
                  </label>
                </div>
              </div>
            )}
          />
        </FormField>
      </div>
    </div>
  );
}
