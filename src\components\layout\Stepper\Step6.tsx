'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Plus, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { STEPPER_FORM } from '@/lib/constants';
import useFarmer, { FarmerSchema } from '@/lib/hooks/useFarmer';
import usePublic from '@/lib/hooks/usePublic';
import { UserSchema } from '@/lib/hooks/useUsers';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';
import { useStateContext } from '@/providers/app/state';

import {
  FamilyRelationshipEnums,
  FarmerOccupationEnum,
  GovernmentIdentificationEnum,
  IFarmPropertyEnum,
} from './Enums';

const OPTION_NATURAL_DISASTER = [
  {
    label: 'Excessive Rainfall',
    value: 'Excessive Rainfall',
  },
  {
    label: 'Excessive Windspeed',
    value: 'Excessive Windspeed',
  },
];

export default function Step6() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { updateFarmer } = useFarmer();
  const {
    getAllCrops,
    getSeeds,
    getFertilizer,
    getChemicals,
    OPTION_CROPS,
    OPTION_CHEMICAL,
    OPTION_FERTILIZER,
    OPTION_SEED,
    OPTION_CROPS_PLANTED,
  } = usePublic();
  const { setState, state } = useStateContext();
  const data = gStateP.admin.members['details'].get({ noproxy: true });

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      naturalDisasterCover: [],
      multiRiskCover: '',
      desiredAmountCover: '',
      additionalAmountCover: '',
      transplantingDate: '',
      harvestDate: '',
      sowingDate: '',
      seedbedding: [
        {
          option: '',
          quantity: '',
          cost: '',
        },
      ],
    },
  });
  const watchMultiRiskCover = watch('multiRiskCover');
  const watchDesiredAmountCover = watch('desiredAmountCover');
  const watchAdditionalAmountCover = watch('additionalAmountCover');
  const seedbedding = useFieldArray({
    name: 'seedbedding',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      userId: data.farmer.user_id,
    };

    console.log('Farm Ensurance: ', updatedData);
    // updateFarmer(updatedData);
    // setState((v) => ({
    //   ...v,
    //   ..._data,
    //   userId: data.id,
    // }));
    // !gState.stepper.isLastStep.value && gState.stepper.activeStep.set((cur) => cur + 1);
  };

  useEffect(() => {
    Promise.all([getAllCrops(), getSeeds(), getFertilizer(), getChemicals()]);
  }, []);

  useEffect(() => {
    if (gState.stepper.activeStep.value === 6) {
      reset((v) => ({
        ...v,
      }));
    }
  }, [gState.stepper.activeStep]);

  return (
    <form id={STEPPER_FORM[6]} onSubmit={handleSubmit(onSubmit)}>
      <div className="font-dmSans text-xl font-bold text-primary">Ensurance Information</div>
      <div className="my-6 grid grid-cols-3 items-start gap-4">
        {/* Natural Disaster Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="naturalDisasterCover" className="pb-1 font-normal">
            Natural Disaster Cover
          </Label>
          <Controller
            control={control}
            name="naturalDisasterCover"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTION_NATURAL_DISASTER}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.naturalDisasterCover && <p className="form-error">{`${errors.naturalDisasterCover.message}`}</p>}
        </div>

        {/* Multi Risk Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="multiRiskCover" className="pb-1 font-normal">
            Multi Risk Cover
          </Label>
          <Input
            {...register('multiRiskCover', {
              required: false,
              validate: {
                isGreaterThanZero: (v) =>
                  v ? (Number(v) || 0) > 0 || 'Multi Risk Cover must be greater than 0' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.multiRiskCover && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Multi Risk Cover"
          />
          {errors.multiRiskCover && <p className="form-error">{`${errors.multiRiskCover.message}`}</p>}
        </div>

        {/* Desired Amount Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="desiredAmountCover" className="pb-1 font-normal">
            Desired Amount Cover
          </Label>
          <Input
            {...register('desiredAmountCover', {
              required: false,
              validate: {
                isGreaterThanZero: (v) =>
                  v ? (Number(v) || 0) > 0 || 'Desired Amount Cover must be greater than 0' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.desiredAmountCover && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Desired Amount Cover"
          />
          {errors.desiredAmountCover && <p className="form-error">{`${errors.desiredAmountCover.message}`}</p>}
        </div>

        {/* Additional Amount Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="additionalAmountCover" className="pb-1 font-normal">
            Additional Amount Cover
          </Label>
          <Input
            {...register('additionalAmountCover', {
              required: false,
              validate: {
                isGreaterThanZero: (v) =>
                  v ? (Number(v) || 0) > 0 || 'Additional Amount Cover must be greater than 0' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.additionalAmountCover && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Additional Amount Cover"
          />
          {errors.additionalAmountCover && <p className="form-error">{`${errors.additionalAmountCover.message}`}</p>}
        </div>

        {/* Total Amount */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="additionalAmountCover" className="pb-1 font-normal">
            Total Amount
          </Label>
          <Input
            value={(
              Number(watchMultiRiskCover) +
              Number(watchDesiredAmountCover) +
              Number(watchAdditionalAmountCover)
            ).toLocaleString()}
            disabled
            className={cn('focus-visible:ring-primary')}
            type="text"
            placeholder="0"
          />
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Harvest Information</div>
      <div className="my-6 grid grid-cols-3 items-start gap-4">
        {/* Date of transplanting or direct seedling */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="transplantingDate" className="pb-1 font-normal">
            Date of transplanting or direct seedling
          </Label>
          <Input
            {...register('transplantingDate')}
            className={cn(
              'focus-visible:ring-primary',
              errors.transplantingDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Date of transplanting or direct seedling"
          />
          {errors.transplantingDate && <p className="form-error">{`${errors.transplantingDate.message}`}</p>}
        </div>

        {/* Harvest Date */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="harvestDate" className="pb-1 font-normal">
            Harvest Date
          </Label>
          <Input
            {...register('harvestDate')}
            className={cn(
              'focus-visible:ring-primary',
              errors.harvestDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Harvest Date"
          />
          {errors.harvestDate && <p className="form-error">{`${errors.harvestDate.message}`}</p>}
        </div>

        {/* Sowing Date */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="sowingDate" className="pb-1 font-normal">
            Sowing Date
          </Label>
          <Input
            {...register('sowingDate')}
            className={cn(
              'focus-visible:ring-primary',
              errors.sowingDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Sowing Date"
          />
          {errors.sowingDate && <p className="form-error">{`${errors.sowingDate.message}`}</p>}
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Farm & Plan Budget Activity</div>
    </form>
  );
}
