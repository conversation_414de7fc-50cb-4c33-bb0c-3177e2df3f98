'use client';

import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { RequestStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { cn } from '@/lib/utils';

export const columns = [
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max uppercase">
          <Badge className={cn(RequestStatusLabels[data.status].color, 'capitalize')}>
            {RequestStatusLabels[data.status].label}
          </Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.vehicle_plate_number}`,
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.user.farmer.first_name} {data.user.farmer.last_name}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.user.farmer.first_name} ${row.user.farmer.last_name}`;
    },
  },
  {
    id: 'account_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
    accessorFn: (row) => {
      return `ID${row.user_id.toString().padStart(9, '0')}`;
    },
  },
  {
    id: 'request_amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Request Amount" />,
    accessorFn: (row) => {
      return `${Number(row.amount).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      })}`;
    },
  },
  {
    id: 'financing_group',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Financing Group" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="">
          {data.creditScoreGroup ? (
            <>
              <div className="text-sm font-medium">{data.creditScoreGroup.name}</div>
              <div className="line-clamp-2 text-xs">{data.creditScoreGroup.description}</div>
            </>
          ) : (
            'N/A'
          )}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.creditScoreGroup?.name} | ${row.creditScoreGroup?.description}`;
    },
  },
  {
    id: 'reference_no',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Financing Reference No." />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="text-sm font-medium">{data.finance_reference_number}</div>;
    },
    accessorFn: (row) => {
      return `${row.finance_reference_number}`;
    },
  },
  {
    id: 'date_request',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Requested" />,
    accessorFn: (row) => {
      return `${new Date(row.created_at).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })}`;
    },
  },
  {
    id: 'loan_term',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Term (days)" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.loan_term}</div>;
    },
    accessorFn: (row) => {
      return `${row.loan_term}`;
    },
  },
  {
    id: 'due_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Due Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.due_at ? format(new Date(data.due_at), 'dd MMM yyyy') : 'N/A'}</div>;
    },
    accessorFn: (row) => {
      return `${format(new Date(row.due_at), 'dd MMM yyyy')}`;
    },
  },
  {
    id: 'transaction_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction ID" />,
    accessorFn: (row) => {
      return `${row.reference_number}`;
    },
  },
  {
    id: 'e_wallet_balance',
    header: ({ column }) => <DataTableColumnHeader column={column} title="E-Wallet Balance" />,
    accessorFn: (row) => {
      return `${Number(row.user.wallet ? row.user.wallet.balance : 0).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      })}`;
    },
  },
];
