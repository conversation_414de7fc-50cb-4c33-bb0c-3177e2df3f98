'use client';

import { useHookstate } from '@hookstate/core';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

export default function useCreditScoreMgt() {
  const gStateP = useGlobalStatePersist();
  const gState = useGlobalState();

  const groups = useHookstate(gState.admin.creditScoreMgt.groups.data);
  const group = useHookstate(gState.admin.creditScoreMgt.groups.selected);
  const selectedGroup = useHookstate(gStateP.selected['creditScoreGroup']);

  const getGroups = async () => {
    try {
      const _groups = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/creditscore/group/viewAll`)
        .then((res) => res.data.data);
      console.log('getGroups: ', _groups);
      groups.set(
        _groups.map((v) => ({
          ...v,
          checked: false,
          disabled: false,
        })),
      );
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getGroups: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getGroupsPublic = async () => {
    try {
      const _groups = await axios.get(`/creditscore/group/viewAll`).then((res) => res.data.data);
      console.log('getGroupsPublic: ', _groups);
      groups.set(
        _groups.map((v) => ({
          ...v,
          checked: false,
          disabled: false,
        })),
      );
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getGroupsPublic: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const createGroup = async (data) => {
    try {
      const _data = await axios.post(`/admin/creditscore/group/create`, data).then((res) => res.data);
      console.log('createGroup: ', _data);
      await getGroups();

      toast.success('Group Created!', {
        description: `${data.name} has been created`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('createGroup: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateGroup = async (data) => {
    try {
      const _data = await axios.post(`/admin/creditscore/group/update`, data).then((res) => res.data);
      console.log('updateGroup: ', _data);
      await getGroups();

      toast.success('Group Updated!', {
        description: `${data.name} has been updated`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateGroup: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateGroupSilent = async (data) => {
    try {
      const _data = await axios.post(`/admin/creditscore/group/update`, data).then((res) => res.data);
      console.log('updateGroup: ', _data);
      await getGroups();
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateGroup: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const duplicateGroup = async (data) => {
    try {
      const _data = await axios
        .post(`/admin/creditscore/group/create`, {
          ...data,
          name: `${data.name} (Copy)`,
        })
        .then((res) => res.data);
      console.log('duplicateGroup: ', _data);
      await getGroups();

      toast.success('Group Duplicated!', {
        description: `${data.name} has been duplicated`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('duplicateGroup: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const deactivateGroup = async (data) => {
    try {
      const _data = await axios
        .post(`/admin/creditscore/group/update`, {
          ...data,
          status: '0',
        })
        .then((res) => res.data);
      console.log('deactivateGroup: ', _data);
      await getGroups();

      toast.success('Group Deactivated!', {
        description: `${data.name} has been deactivated`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('deactivateGroup: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const activateGroup = async (data) => {
    try {
      const _data = await axios
        .post(`/admin/creditscore/group/update`, {
          ...data,
          status: '1',
        })
        .then((res) => res.data);
      console.log('activateGroup: ', _data);
      await getGroups();

      toast.success('Group Activated!', {
        description: `${data.name} has been Activated`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('activateGroup: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    groups,
    group,
    selectedGroup,
    getGroups,
    createGroup,
    updateGroup,
    updateGroupSilent,
    duplicateGroup,
    getGroupsPublic,
    deactivateGroup,
    activateGroup,
  };
}
