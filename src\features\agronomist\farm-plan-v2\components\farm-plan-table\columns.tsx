'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Eye, Pencil, PrinterIcon } from 'lucide-react';
import Link from 'next/link';
import { useRef } from 'react';
import { MdOutlineContentPasteSearch } from 'react-icons/md';
import { useReactToPrint } from 'react-to-print';
import { toast } from 'sonner';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import { useEditFarmPlan } from '../../hooks/useEditFarmPlan';
import { IFarmPlan } from '../../types/farm-plan.types';
import PrintFarmPlan from '../print-farm-plan';

export const columns: ColumnDef<IFarmPlan>[] = [
  {
    id: 'farmer_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Farmer Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{`${data.user.farmer.first_name} ${data.user.farmer.last_name}`}</div>;
    },
    accessorFn: (row) => `${row.user.farmer.first_name} ${row.user.farmer.last_name}`,
  },
  {
    id: 'farm_plan_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Farm Plan ID" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.reference_number}</div>;
    },
    accessorFn: (row) => row.reference_number,
  },
  {
    id: 'created_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Created by" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.agronomist_name}</div>;
    },
    accessorFn: (row) => row.agronomist_name,
  },
  {
    id: 'date_time_created',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Created" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.created_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) =>
      new Date(row.created_at).toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      }),
  },
  {
    id: 'total_production_cost',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total Production Cost" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">PHP {data?.total_amount?.toLocaleString() ?? 0}</div>;
    },
    accessorFn: (row) => row.total_amount,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader className="" column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

const Action = ({ row }) => {
  const data = row.original;
  const contentRef = useRef<HTMLDivElement>(null);
  const { farmPlanByIdQuery } = useEditFarmPlan(data.id);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: `FarmPlan_${data.user.farmer.first_name}_${data.user.farmer.last_name}_${data.reference_number}_${format(new Date(), 'yyyy-MM-dd')}`,
    onPrintError: () => {
      toast.error('Oops! Something went wrong', {
        description: 'Failed to print farm plan. Please try again later.',
      });
    },
  });

  const handlePrint = async () => {
    try {
      // Fetch the farm plan details if not already loaded
      if (!farmPlanByIdQuery.data) {
        await farmPlanByIdQuery.refetch();
      }

      // Wait a bit for the component to render
      setTimeout(() => {
        reactToPrintFn();
      }, 100);
    } catch (error) {
      toast.error('Failed to load farm plan details');
    }
  };

  return (
    <div className="flex justify-start gap-2">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" variant="outline" size="sm" asChild>
            <Link href={`/agronomist/farm-plan/view/?id=${data.id}`}>
              <Eye className="size-4" />
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>View</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className="h-8 px-2 lg:px-3"
            variant="outline"
            size="sm"
            onClick={handlePrint}
            disabled={farmPlanByIdQuery.isLoading}
          >
            <PrinterIcon className="size-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Print</p>
        </TooltipContent>
      </Tooltip>

      {/* Hidden Print Component */}
      {farmPlanByIdQuery.data && <PrintFarmPlan contentRef={contentRef} farmPlan={farmPlanByIdQuery.data} />}

      <Tooltip>
        <TooltipTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" variant="outline" size="sm" asChild>
            <Link href={`/agronomist/farm-plan/edit/?id=${data.id}`}>
              <Pencil className="size-4" />
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Edit</p>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};

export const ActionHeader = () => {
  return (
    <div className="flex items-center justify-end gap-2">
      <Button className="h-8" asChild>
        <Link href="/agronomist/farm-plan/create">Create Farm Plan</Link>
      </Button>
    </div>
  );
};
