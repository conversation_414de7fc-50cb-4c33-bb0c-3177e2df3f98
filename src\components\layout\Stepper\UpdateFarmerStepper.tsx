'use client';

import { Step, Stepper } from '@material-tailwind/react';
import { Check, Fingerprint, GraduationCap, ShieldCheck, Sprout, Trees, Truck, User, Users } from 'lucide-react';
import { GiFruitBowl } from 'react-icons/gi';
import { MdOutlineRealEstateAgent } from 'react-icons/md';

import { STEPPER_FORM } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

export default function UpdateFarmerStepper() {
  const gState = useGlobalState();

  const handleNext = () => !gState.stepper.isLastStep.value && gState.stepper.activeStep.set((cur) => cur + 1);
  const handlePrev = () => !gState.stepper.isFirstStep.value && gState.stepper.activeStep.set((cur) => cur - 1);

  return (
    <div className="w-full py-6">
      <Stepper
        activeStep={gState.stepper.activeStep.value}
        isLastStep={(value) => gState.stepper.isLastStep.set(value)}
        isFirstStep={(value) => gState.stepper.isFirstStep.set(value)}
        placeholder={undefined}
        className="select-none"
        lineClassName="bg-gray-500/50 h-[1.5px]"
        activeLineClassName="bg-green-500"
      >
        <CustomStep
          activeStep={gState.stepper.activeStep.value}
          position={0}
          icon={<User className="size-6" />}
          label={
            <div>
              <div>Basic</div>
              <div>Information</div>
            </div>
          }
          onClick={() => gState.stepper.activeStep.set(0)}
        />
        <CustomStep
          activeStep={gState.stepper.activeStep.value}
          position={1}
          icon={<GraduationCap className="size-6" />}
          label={
            <div>
              <div>Career &</div>
              <div>Academic</div>
            </div>
          }
          onClick={() => gState.stepper.activeStep.set(1)}
        />
        <CustomStep
          activeStep={gState.stepper.activeStep.value}
          position={2}
          icon={<Fingerprint className="size-6" />}
          label={
            <div>
              <div>Identification</div>
              <div>Documents</div>
            </div>
          }
          onClick={() => gState.stepper.activeStep.set(2)}
        />
        <CustomStep
          activeStep={gState.stepper.activeStep.value}
          position={3}
          icon={<Users className="size-6" />}
          label={
            <div>
              <div>Family</div>
              <div>Profile</div>
            </div>
          }
          onClick={() => gState.stepper.activeStep.set(3)}
        />
        <CustomStep
          activeStep={gState.stepper.activeStep.value}
          position={4}
          icon={<MdOutlineRealEstateAgent className="size-6" />}
          label={
            <div>
              <div>Property</div>
              <div>Ownership</div>
            </div>
          }
          onClick={() => gState.stepper.activeStep.set(4)}
        />
        <CustomStep
          activeStep={gState.stepper.activeStep.value}
          position={5}
          icon={<Sprout className="size-6" />}
          label={
            <div>
              <div>Farm</div>
              <div>Details</div>
            </div>
          }
          onClick={() => gState.stepper.activeStep.set(5)}
        />
        <CustomStep
          activeStep={gState.stepper.activeStep.value}
          position={6}
          icon={<ShieldCheck className="size-6" />}
          label={
            <div>
              <div>Farm</div>
              <div>Ensurance</div>
            </div>
          }
          onClick={() => gState.stepper.activeStep.set(6)}
        />
      </Stepper>
      {/* <div className="mt-16 flex justify-between">
                  <Button onClick={handlePrev} disabled={gState.stepper.isFirstStep.value}>
                    Prev
                  </Button>
                  <Button onClick={handleNext} disabled={gState.stepper.isLastStep.value}>
                    Next
                  </Button>
                </div> */}
    </div>
  );
}

const CustomStep = ({ activeStep = 0, position = 0, onClick = () => {}, icon = null, label = null }) => {
  return (
    <Step
      className={cn(
        '!bg-gray-100 border border-gray-300 rounded-lg h-auto w-auto cursor-pointer',
        activeStep === position && '!bg-white',
        activeStep > position && 'border-green-500 !bg-green-50',
      )}
      activeClassName="!bg-white"
      completedClassName="border-green-500 !bg-green-50"
      // onClick={() => gState.stepper.activeStep.set(0)}
      onClick={onClick}
      placeholder={undefined}
    >
      <div className="flex flex-col items-center px-6 py-3 font-normal text-slate-800">
        {icon}
        <div className="pt-2 text-center text-xs">
          {label}
          {activeStep > position && (
            <div className="pt-2">
              <div className="mx-auto flex size-5 items-center justify-center rounded-full bg-green-500 text-white">
                <Check className="size-3" />
              </div>
            </div>
          )}
          {activeStep == position && (
            <div className="pt-2">
              <div className="mx-auto flex size-5 items-center justify-center rounded-full border border-gray-400">
                <div className="size-3 rounded-full bg-green-500" />
              </div>
            </div>
          )}
          {activeStep !== position && activeStep < position && (
            <div className="pt-2">
              <div className="mx-auto flex size-5 items-center justify-center rounded-full border border-gray-400">
                <div className="invisible size-3 rounded-full bg-green-500" />
              </div>
            </div>
          )}
        </div>
      </div>
    </Step>
  );
};
