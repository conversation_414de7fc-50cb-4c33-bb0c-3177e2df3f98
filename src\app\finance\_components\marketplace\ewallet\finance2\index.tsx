'use client';

import { useEffect } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';

import { TAB_EWALLET, TabEWalletEnum } from '@/lib/constants/enums';
import useFinance2 from '@/lib/hooks/finance2/useFinance2';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { useTopDownStore } from '@/lib/store/top-down-store';
import { cn } from '@/lib/utils';

import { TopDownReqsTable } from './top-down/topdown-req-table';
import { columnsTopDownReq } from './top-down/topdown-req-table/columns';
import { TopupReqsTable } from './topup-reqs-table';
import { columns } from './topup-reqs-table/columns';

export default function Finance2Page() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { getTopupRequests } = useFinance2();
  const { data, meta } = useTopDownStore('finance');

  useEffect(() => {
    getTopupRequests();
  }, [
    gState.finance1.pagination.topupReqs.pageSize,
    gState.finance1.pagination.topupReqs.page,
    gState.finance1.pagination.topupReqs.search,
    gState.finance1.pagination.topupReqs.startDate,
    gState.finance1.pagination.topupReqs.endDate,
    gState.finance1.pagination.topupReqs.status[0],
  ]);

  return (
    <div className="grid gap-4 p-6 pt-3 md:p-8 md:pt-4">
      {/* Tabs */}
      <HorizontalScrollBar>
        <div className="flex w-max items-center gap-4">
          {TAB_EWALLET.map((tab, index) => {
            const isSelected = tab.value === gStateP.tabsEWallet.value;

            return (
              <button
                key={tab.value}
                className={cn(
                  'transition-all duration-300 ease-in-out whitespace-nowrap my-3 capitalize',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => {
                  gStateP.tabsEWallet.set(tab.value);
                }}
              >
                {tab.name}
              </button>
            );
          })}
        </div>
      </HorizontalScrollBar>

      {gStateP.tabsEWallet.value === TabEWalletEnum.TOP_UP && (
        <TopupReqsTable
          data={gState.finance1.topupReqs.data.get({ noproxy: true })}
          columns={columns}
          meta={gState.finance1.topupReqs['meta'].get({ noproxy: true })}
        />
      )}

      {gStateP.tabsEWallet.value === TabEWalletEnum.TOP_DOWN && (
        <TopDownReqsTable
          data={data.get({ noproxy: true })}
          columns={columnsTopDownReq}
          meta={meta.get({ noproxy: true })}
        />
      )}
    </div>
  );
}
