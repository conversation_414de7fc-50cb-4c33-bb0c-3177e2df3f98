'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

export const columns = [
  {
    id: 'farmer_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Farmer Name" />,
    accessorFn: (row) => `${row.farmer.first_name} ${row.farmer.last_name}`,
  },
  {
    id: 'account_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
    accessorFn: (row) => `${row.farmer.id.toString().padStart(9, '0')}`,
  },
  {
    id: 'birthdate',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Birthdate" />,
    accessorFn: (row) =>
      `${
        row.farmer.birth_date
          ? new Date(row.farmer.birth_date).toLocaleString('en-US', {
              year: 'numeric',
              month: 'short',
              day: '2-digit',
            })
          : 'N/A'
      }`,
  },
  {
    id: 'contact',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Contact No." />,
    accessorFn: (row) => `${row.farmer.mobile_number}`,
  },
];
