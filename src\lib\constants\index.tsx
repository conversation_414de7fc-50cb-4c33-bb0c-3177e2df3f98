export const DEFAULT_ADMIN_ACTIVE_MENU = 0;
export const DEFAULT_GROUP_TAB = 0;

export const ROLES = {
  1: 'Farmer',
  2: 'Demand',
  998: 'Admin',
  999: 'Super Admin',
  997: 'Product Manager',
  996: 'Wallet Manager',
  995: 'Invoice Manager',
  199: 'Encoder 1',
  198: 'Encoder 2',
  499: 'Finance 1',
  498: 'Finance 2',
  399: 'Sale 1',
  398: 'Sale 2',
  299: 'Operation 1',
  298: 'Operation 2',
  59: 'Head Agronomist',
  50: 'Agronomist',
  899: 'Field Relation Officer',
};

export const getUserType = (role: number) => {
  // admin
  if ([999, 998].includes(role)) {
    return 'admin';
  }
  // encoder
  if ([199, 198].includes(role)) {
    return 'encoder';
  }
  // finance
  if ([499, 498].includes(role)) {
    return 'finance';
  }
  // operation
  if ([299, 298].includes(role)) {
    return 'operation';
  }
  // sale
  if ([399, 398].includes(role)) {
    return 'sale';
  }
  // agronomist
  if ([50, 59].includes(role)) {
    return 'agronomist';
  }
  // fieldRelationOfficer
  if ([899].includes(role)) {
    return 'fieldRelationOfficer';
  }
  // farmer
  return 'farmer';
};
export const isSuperAdmin = (role: number) => {
  return [999].includes(role);
};

// -----------------------------

export enum LogStatus {
  ENTRY = 0,
  COMPLETE = 1,
  EXIT = 2,
}

export enum WorkCategory {
  FARMER = 0,
  DISPOSER = 1,
  BOTH = 2,
}

export const WorkCategoryLabels = ['Farmer', 'Disposer', 'Both'];

export enum FarmOwnership {
  OWNED = 0,
  LEASED = 1,
  BOTH = 2,
}

export const FarmOwnershipLabel = ['Owned', 'Leased', 'Owned & Leased'];

export enum VehicleOwnership {
  OWNED = 0,
  RENTED = 1,
}

export enum AgreeInputExpenses {
  TWENTY_FOURTY = 0,
  FOURTY_SIXTY = 1,
  SIXTY_EIGHTY = 2,
  EIGHTY_ABOVE = 3,
}

export const AgreeInputExpensesLabels = [
  'PHP 20,000 - PHP 40,000',
  'PHP 40,000 - PHP 60,000',
  'PHP 60,000 - PHP 80,000',
  'Above PHP 80,000',
];

export const STEPPER_FORM = [
  'basic-info',
  'career-academic',
  'identification-docs',
  'family-profile',
  'property-ownership',
  'farm-details',
  'farm-ensurance',
];

export const CREDIT_MGT_TAB = [
  {
    name: 'Before Loan',
    value: 'before_loan',
  },
  {
    name: 'During Loan',
    value: 'during_loan',
  },
  {
    name: 'After Loan',
    value: 'after_loan',
  },
];
