'use client';

/* eslint-disable @next/next/no-img-element */
import { useHookstate } from '@hookstate/core';
import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon } from '@radix-ui/react-icons';
import { format } from 'date-fns';
import { CheckCircle, UploadCloud, XCircle } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { BsFillTrashFill } from 'react-icons/bs';
import { CSVReader } from 'react-papaparse';
import { toast } from 'sonner';

import { DataTableFacetedFilter } from '@/components/layout/table/normal/table-faceted-filter';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { ScrollArea, ScrollBar } from '../../../../ui/scroll-area';

export default function NewPriceCSV() {
  const gState = useGlobalState();

  const filteredData = useHookstate([]);
  const sData = useHookstate([]);
  const sDataPaginated = useHookstate({
    data: [],
  });

  const page = useHookstate(1);
  const pageSize = useHookstate(10);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const reset = () => {
    sData.set([]);
    filteredData.set([]);
    sDataPaginated.set({
      data: [],
    });
  };

  const handleOnDrop = (data) => {
    const tmp = uploadHandler(data);

    sData.set(tmp);
    filteredData.set(tmp);
  };

  const handleOnError = (err, file, inputElem, reason) => {
    toast.error('Oops!', {
      description: err.message,
    });
  };

  const handleOnRemoveFile = (data) => {
    sData.set([]);
    filteredData.set([]);
    sDataPaginated.set({
      data: [],
    });
  };

  const uploadHandler = (data) => {
    const tmp = [];

    if (data.length === 0) {
      toast.error('Oops!', {
        description: `No data found!`,
      });
      return tmp;
    }

    data = data.filter((v) => v.errors.length === 0);

    const hasProductName = data[0].data.includes('Product Name');
    const hasTradingPostPrice = data[0].data.includes('Trading Post Price');
    const hasBaptcPrice = data[0].data.includes('BAPTC Price per kilo');
    const hasNVATPrice = data[0].data.includes('NVAT Price per kilo');

    const hasLow = data[1].data.includes('low');
    const hasHigh = data[1].data.includes('high');
    const hasDate = data[1].data.includes('Date');

    if (!hasProductName || !hasTradingPostPrice || !hasBaptcPrice || !hasNVATPrice || !hasLow || !hasHigh || !hasDate) {
      toast.error('Oops!', {
        description: `Invalid Data`,
      });
      return tmp;
    }

    data.forEach((el, index) => {
      // skip index 0 and 1 and empty product name
      if (index <= 1 || el.data[0].trim() === '') return;

      const find = gState.admin.crops.data
        .get({ noproxy: true })
        .find(
          (x) =>
            el.data[0].toLowerCase().includes(x.name.toLowerCase()) ||
            x.keywords?.toLowerCase().includes(el.data[0].toLowerCase()),
        );
      const existing = find !== undefined;

      tmp.push({
        ...el.data,
        Existing: existing,
        cropId: find?.id || 0,
      });
    });

    return tmp;
  };

  const paginator = (items, current_page, per_page_items) => {
    let page = current_page || 1,
      per_page = per_page_items || 10,
      offset = (page - 1) * per_page,
      paginatedItems = items.slice(offset).slice(0, per_page_items),
      total_pages = Math.ceil(items.length / per_page);

    return {
      page: page,
      per_page: per_page,
      pre_page: page - 1 ? page - 1 : null,
      next_page: total_pages > page ? page + 1 : null,
      total: items.length,
      total_pages: total_pages,
      data: paginatedItems,
    };
  };

  useEffect(() => {
    gState.admin.cropsUploadPrice.set(
      sData.get({ noproxy: true }).map((x) => ({
        cropId: x.cropId,
        name: x[0],
        lowPrice: x[1],
        highPrice: x[2],
        lowBaptcPrice: x[3],
        highBaptcPrice: x[4],
        lowNvatPrice: x[5],
        highNvatPrice: x[6],
        date: format(new Date(x[7]), 'yyyy-MM-dd'),
      })),
    );
  }, [sData]);

  useEffect(() => {
    sDataPaginated.set(paginator(filteredData.get({ noproxy: true }), page.value, pageSize.value));
  }, [page, pageSize, filteredData]);

  useEffect(() => {
    reset();
  }, []);

  return (
    <div className="space-y-6 px-6">
      <div className="">
        <div className={cn(sData.length > 0 && 'hidden')}>
          <CSVReader
            config={{ header: false }}
            onDrop={handleOnDrop}
            onError={handleOnError}
            addRemoveButton
            removeButtonColor="#ef4444"
            onRemoveFile={handleOnRemoveFile}
            style={{
              dropFile: {
                width: 150,
                height: 120,
              },
              fileNameInfo: {
                color: '#4b5563',
              },
              fileSizeInfo: {
                color: '#6366f1',
              },
            }}
          >
            <UploadCloud className="size-12" />
            <div className="mt-2 text-gray-500">Drag & drop file here</div>
            <div className="text-gray-500">Or</div>
            <div className="font-bold text-primary">Browser</div>
          </CSVReader>
        </div>

        {sData.value.length === 0 && (
          <div className="mt-3 text-center text-sm text-gray-500">
            Only CSV files are supported
            <div className="">
              Use the template provided here:
              <a
                className="ml-1 italic text-blue-500 hover:underline hover:underline-offset-2"
                href="/template/TradingAppPrices.csv"
                target="_blank"
              >
                Template
              </a>
            </div>
          </div>
        )}

        {sData.length > 0 && (
          <div className="grid">
            <div className="flex justify-between pb-4">
              <DataTableFacetedFilter
                data={sData}
                filteredData={filteredData}
                column="Existing"
                title="Existing"
                options={[
                  { label: 'Existing', value: 'true' },
                  { label: 'Non-Existing', value: 'false' },
                ].map((item) => ({
                  label: item.label,
                  value: item.value,
                }))}
              />
            </div>

            <ScrollArea className="max-h-[36vh] whitespace-nowrap rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="">Action</TableHead>
                    <TableHead className="">Existing</TableHead>
                    <TableHead>Product Name</TableHead>
                    <TableHead>Trading Post Low</TableHead>
                    <TableHead>Trading Post High</TableHead>
                    <TableHead>BAPTC Low</TableHead>
                    <TableHead>BAPTC High</TableHead>
                    <TableHead>NVAT Low</TableHead>
                    <TableHead>NVAT High</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sDataPaginated['data'].value.map((crop, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <button className="focus:outline-none active:scale-90">
                              <BsFillTrashFill className="size-5 text-red-500" />
                            </button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This action will delete the current crops from the list.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                type="button"
                                onClick={() => {
                                  const _filteredData = filteredData.get({ noproxy: true });
                                  const tmp = _filteredData.filter((x) => x[0] !== crop[0]);
                                  filteredData.set(tmp);

                                  const _sData = sData.get({ noproxy: true });
                                  const tmp2 = _sData.filter((x) => x[0] !== crop[0]);
                                  sData.set(tmp2);

                                  toast.success('Deleted!', {
                                    description: `Data has been deleted.`,
                                  });
                                }}
                              >
                                Continue
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                      <TableCell>
                        {crop.Existing ? (
                          <CheckCircle className="size-5 text-green-500" />
                        ) : (
                          <XCircle className="size-5 text-red-500" />
                        )}
                      </TableCell>
                      <TableCell>{crop[0]}</TableCell>
                      <TableCell>{crop[1]}</TableCell>
                      <TableCell>{crop[2]}</TableCell>
                      <TableCell>{crop[3]}</TableCell>
                      <TableCell>{crop[4]}</TableCell>
                      <TableCell>{crop[5]}</TableCell>
                      <TableCell>{crop[6]}</TableCell>
                      <TableCell>{crop[7]}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <ScrollBar orientation="horizontal" />
            </ScrollArea>

            {/* pagination */}
            <div className="mt-4 space-y-2 px-2">
              <div className="flex items-center justify-center gap-4">
                <div className="text-sm text-gray-500">Total of {filteredData.length} row(s)</div>

                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium">Rows per page</p>
                  <Select
                    value={`${pageSize.value}`}
                    onValueChange={(value) => {
                      pageSize.set(Number(value));
                    }}
                  >
                    <SelectTrigger className="h-8 w-[70px]">
                      <SelectValue placeholder={pageSize.value} />
                    </SelectTrigger>
                    <SelectContent side="top">
                      {[10, 20, 30, 40, 50].map((pageS) => (
                        <SelectItem key={pageS} value={`${pageS}`}>
                          {pageS}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex flex-col items-center gap-2">
                <div className="flex items-center gap-2">
                  <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                    Page {page.value} of {sDataPaginated['total_pages'].value}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      className="hidden size-8 p-0 lg:flex"
                      onClick={() => page.set(1)}
                      disabled={page.value == 1}
                    >
                      <span className="sr-only">Go to first page</span>
                      <DoubleArrowLeftIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="size-8 p-0"
                      onClick={() => {
                        if (page.value > 1) {
                          page.set((v) => v - 1);
                        }
                      }}
                      disabled={page.value == 1}
                    >
                      <span className="sr-only">Go to previous page</span>
                      <ChevronLeftIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="size-8 p-0"
                      onClick={() => {
                        if (page.value < sDataPaginated['total_pages'].value) {
                          page.set((v) => v + 1);
                        }
                      }}
                      disabled={sDataPaginated.data.length > 0 && !sDataPaginated['next_page'].value}
                    >
                      <span className="sr-only">Go to next page</span>
                      <ChevronRightIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="hidden size-8 p-0 lg:flex"
                      onClick={() => {
                        page.set(sDataPaginated['total_pages'].get({ noproxy: true }));
                      }}
                      disabled={sDataPaginated.data.length > 0 && !sDataPaginated['next_page'].value}
                    >
                      <span className="sr-only">Go to last page</span>
                      <DoubleArrowRightIcon className="size-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
