'use client';

import { useEffect } from 'react';

import useFarmers from '@/lib/hooks/fro/useFarmer';
import { useGlobalState } from '@/lib/store';

import FarmersTable from './table';
import { columns } from './table/columns';

export default function Farmers() {
  const gState = useGlobalState();
  const { getFarmers } = useFarmers();

  useEffect(() => {
    getFarmers();
  }, []);

  console.log('farmers', gState.fro.farmers.data.get({ noproxy: true }));

  return (
    <div className="pt-4">
      <FarmersTable
        columns={columns}
        data={gState.fro.farmers.data.get({ noproxy: true })}
        metadata={gState.fro.farmers['metadata'].get({ noproxy: true })}
      />
    </div>
  );
}
