'use client';

import { Search } from 'lucide-react';
import { useState } from 'react';

import { SvgSpinnersBarsRotateFade } from '@/components/common/icons/SvgSpinnersBarsRotateFade';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';

import { IFarmPlanTemplate } from '@/features/head-agronomist/types/farmplan-templates';
import { cn } from '@/lib/utils';

interface ITemplateSearchInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  isError?: boolean;
  isLoading?: boolean;
  templates?: IFarmPlanTemplate[];
  selectedTemplate?: IFarmPlanTemplate | undefined;
  onTemplateSelect?: (template: IFarmPlanTemplate | null) => void;
}

export default function TemplateSearchInput({
  value = '',
  onChange,
  label = 'Farm Plan Template',
  placeholder = 'Type to search farm plan templates...',
  isError = false,
  isLoading = false,
  templates = [],
  selectedTemplate,
  onTemplateSelect,
}: ITemplateSearchInputProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getTemplateDisplayName = (template: IFarmPlanTemplate) => {
    return `${template.crop.name}`;
  };

  const getTemplateSecondaryInfo = (template: IFarmPlanTemplate) => {
    return `${template.location} • v${template.version_number}`;
  };

  return (
    <div className="w-full">
      <Label htmlFor="template-search">{label}</Label>

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Input
              id="template-search"
              type="text"
              placeholder={placeholder}
              value={value}
              onChange={(e) => {
                onChange?.(e.target.value);
                if (!isOpen) {
                  setIsOpen(true);
                }
              }}
              className={cn('pr-10', isError && 'border-red-500 focus-visible:ring-red-500')}
              autoComplete="off"
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {isLoading ? (
                <SvgSpinnersBarsRotateFade className="size-4" />
              ) : (
                <Search className="size-4 text-gray-400" />
              )}
            </div>
          </div>
        </PopoverTrigger>

        <PopoverContent className="w-full max-w-none p-0" onOpenAutoFocus={(e) => e.preventDefault()}>
          <ScrollArea className="flex w-full flex-col p-1">
            {templates.length > 0 ? (
              <div className="max-h-60 flex-1">
                {templates.map((template) => (
                  <div
                    key={template.id}
                    className={cn(
                      'flex cursor-pointer items-center gap-3 rounded-md px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800',
                      {
                        'bg-gray-100 dark:bg-gray-800': selectedTemplate ? selectedTemplate.id === template.id : false,
                      },
                    )}
                    onClick={() => {
                      onTemplateSelect?.(template);
                      setIsOpen(false);
                      onChange?.(getTemplateDisplayName(template));
                    }}
                  >
                    <div className="flex size-8 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400">
                      {/* <span className="text-xs font-semibold">{template.crop.name.charAt(0).toUpperCase()}</span> */}
                      <img className="size-8 rounded-full" src={template.crop.image} alt="" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium">{getTemplateDisplayName(template)}</p>
                      <p className="truncate text-xs text-gray-500">{getTemplateSecondaryInfo(template)}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-sm text-gray-500">
                {value ? 'No templates found' : 'Start typing to search templates'}
              </div>
            )}
          </ScrollArea>
        </PopoverContent>
      </Popover>
    </div>
  );
}
