export interface IUserInfo {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: string | null;
  remember_me_token: string | null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: string | null;
  farmer: IFarmer;
}

export interface IFarmer {
  id: number;
  user_id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  birth_date: string;
  place_of_birth: string | null;
  religion: string;
  gender: string;
  civil_status: string;
  height: number;
  weight: number;
  mobile_number: string;
  address: string;
  address_house_number: string;
  address_province: string;
  address_city: string;
  address_barangay: string;
  address_zip_code: string;
  educational_attainment: string;
  educational_is_graduate: number;
  educational_degree: string;
  occupation: string;
  occupation_status: string;
  occupation_employer_name: string;
  occupation_employer_address: string;
  occupation_business_name: string | null;
  occupation_business_address: string | null;
  occupation_business_contact: string | null;
  occupation_annual_income: string | null;
  skills_farming: string;
  skills_fishing: number;
  skills_livestock: string;
  skills_construction: string;
  skills_processing: string;
  skills_servicing: string;
  skills_craft: string;
  skills_others: string;
  vehicle_owned: string;
  status: number;
  created_at: string;
  updated_at: string;
  has_biometric: number;
  qr_code: string;
  has_loan: number;
  has_submitted_loan_application: number;
  permanent_address: string;
  permanent_address_house_number: null;
  permanent_address_province: null;
  permanent_address_city: null;
  permanent_address_barangay: null;
  permanent_address_zip_code: null;
  permanent_address_length_of_stay: null;
  address_length_of_stay: null;
  occupation_title: string;
  source_of_funds: string;
  landbank_accounts: string;
  farmerInfo: IFarmerInfo;
  cropsPlanted: ICropsPlanted[];
  subCropsPlanted: ISubCropsPlanted[];
}

export interface IFarmerInfo {
  id: number;
  farmer_id: number;
  farm_address: string;
  farm_area: number;
  farm_ownership: number;
  created_at: string;
  updated_at: string;
  nationality: string;
  other_mobile_number: string;
  year_residing: number;
  residence_ownership: string;
}

export interface ICropsPlanted {
  id: number;
  farmer_id: number;
  crop_id: number;
  created_at: string;
  updated_at: string;
  crop: ICrop;
}

export interface ICrop {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
  harvest_days: number;
  is_sync: number;
  image: string;
  keywords: string | null;
  sap_item_code: string | null;
}

export interface ISubCropsPlanted {
  id: number;
  farmer_id: number;
  crop_id: number;
  created_at: string;
  updated_at: string;
  crop: ICrop;
}
