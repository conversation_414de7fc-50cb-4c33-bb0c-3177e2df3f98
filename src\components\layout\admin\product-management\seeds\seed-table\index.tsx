'use client';

import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

import useSeedsTab from '../hooks/useSeedsTab';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function SeedTable({ columns, data, metadata = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const seedsTab = useSeedsTab();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);
      },
    },
  });

  return (
    <div key={JSON.stringify(seedsTab[gState.admin.seeds.tab.value].data)} className="space-y-4">
      <DataTableToolbar id={seedsTab[gState.admin.seeds.tab.value].id} table={table} meta={metadata} />

      <HorizontalScrollBar>
        <Tabs
          defaultValue="all"
          value={gState.admin.seeds.tab.value}
          onValueChange={(v) => {
            gState.admin.seeds.tab.set(v);
          }}
          className=""
        >
          <TabsList className="">
            <TabsTrigger value="all">All Items</TabsTrigger>
            <TabsTrigger value="subcat">Crop Type</TabsTrigger>
            <TabsTrigger value="variety">Variety</TabsTrigger>
            <TabsTrigger value="breed">Breed</TabsTrigger>
          </TabsList>
        </Tabs>
      </HorizontalScrollBar>

      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      if ((event.target as HTMLElement).tagName.toLowerCase() !== 'button')
                        table.options.meta?.getRowClicked?.(row);
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {metadata ? (
        <DataTablePaginationMeta
          table={table}
          meta={metadata}
          onChangePageSize={(pageSize) => {
            gState.admin.pagination.transaction.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            gState.admin.pagination.transaction.page.set(page);
          }}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}
