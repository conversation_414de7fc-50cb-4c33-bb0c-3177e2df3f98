'use client';

import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';

import { ICreateFarmPlan } from '../../types/create-farm-plan.types';

interface ICashReqSummaryProps {
  form: UseFormReturn<ICreateFarmPlan>;
}

export default function CashReqSummary({ form }: ICashReqSummaryProps) {
  const state = useHeadAgronomistState();

  // Calculate Total Cash Requirements
  useEffect(() => {
    const total = state.section2.group.laborReq.value + state.section2.group.otherProductionCost.value;
    state.section2.totalCashRequirements.set(total);
  }, [state.section2.group.laborReq, state.section2.group.otherProductionCost]);

  return (
    <div className="mt-6 rounded-md bg-gray-100">
      <div className="divide-y">
        {/* Total Cash Requirements */}
        <div className="flex items-center justify-end p-2">
          <span className="text-sm font-medium text-gray-700">Total Cash Requirements</span>
          <span className="w-[200px] text-right text-sm font-semibold">
            {state.section2.totalCashRequirements.value.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </span>
        </div>
      </div>
    </div>
  );
}
