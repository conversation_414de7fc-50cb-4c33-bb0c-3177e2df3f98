'use client';

import { useHookstate } from '@hookstate/core';
import { useRouter } from 'next/navigation';

import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useGlobalStatePersist } from '@/lib/store/persist';
import { TFarmPlanCalc } from '@/lib/store/persist.types';

interface IFarmPlanCalcTabsProps {
  isActive: boolean;
}

export function FarmPlanCalcTabs({ isActive }: IFarmPlanCalcTabsProps) {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();
  const tabDFrmPlanCalc = useHookstate(gStateP.agronomist.tabs.farmPlanCalc);

  if (!isActive) return null;

  return (
    <>
      <div className="hidden lg:block">
        <Tabs
          defaultValue="members"
          value={tabDFrmPlanCalc.value}
          onValueChange={(v: TFarmPlanCalc) => {
            tabDFrmPlanCalc.set(v);
          }}
          onClick={() => {
            if (window.location.pathname !== '/agronomist/farm-plan/') {
              router.push('/agronomist/farm-plan/');
            }
          }}
          className=""
        >
          <TabsList className="">
            <TabsTrigger value="farm-plan">Farm Plan</TabsTrigger>
            <TabsTrigger value="calc">Crop Calculator</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="block lg:hidden">
        <Select
          defaultValue="members"
          value={tabDFrmPlanCalc.value}
          onValueChange={(v: TFarmPlanCalc) => {
            tabDFrmPlanCalc.set(v);
          }}
          onOpenChange={() => {
            if (window.location.pathname !== '/agronomist/farm-plan/') {
              router.push('/agronomist/farm-plan/');
            }
          }}
        >
          <SelectTrigger className="w-[180px]" icon="up-down">
            <SelectValue placeholder="Members" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="farm-plan">Farm Plan</SelectItem>
              <SelectItem value="calc">Crop Calculator</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </>
  );
}
