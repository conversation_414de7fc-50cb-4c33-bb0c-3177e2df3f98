'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { keepPreviousData, useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';

import { IFarmPlanTemplateResponse } from '../types/farmplan-templates';

// State
const initialState = {
  page: 1,
  pageSize: 10,
  search: '',
};

const farmPlanTemplateState = hookstate(initialState, devtools({ key: 'farmPlanTemplateState' }));

// fetcher
export const fetchFarmPlanTemplates = async () => {
  const { data } = await axios.get(`/agronomist/farmplan/template/viewAll`, {
    params: {
      page: farmPlanTemplateState.page.value,
      pageSize: farmPlanTemplateState.pageSize.value,
      search: farmPlanTemplateState.search.value,
    },
  });
  return data as IFarmPlanTemplateResponse;
};

// hooks
export const useFarmPlanTemplate = () => {
  const state = useHookstate(farmPlanTemplateState);
  const searchDebounce = useHookstateDebounce(state.search, 500);

  const farmPlanTemplatesQuery = useQuery({
    queryKey: [
      'farmPlanTemplates',
      {
        page: state.page.value,
        pageSize: state.pageSize.value,
        search: searchDebounce.value,
      },
    ],
    queryFn: fetchFarmPlanTemplates,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
  });

  return { farmPlanTemplatesQuery, state };
};

export { farmPlanTemplateState };
