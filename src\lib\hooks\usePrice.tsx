'use client';

import { toast } from 'sonner';

import { useGlobalStatePersist } from '../store/persist';

export default function usePrice() {
  const gStateP = useGlobalStatePersist();

  const AddCrop = async (data) => {
    console.log('AddCrop: ', data);
    // gStateP.sampleData.products.merge([data]);
    // toast({
    //   title: 'Crop Added',
    //   description: 'Crop added successfully',
    // });
  };

  const updateCrop = async (data) => {
    const foundProd = gStateP.sampleData.products
      .get({ noproxy: true })
      .findIndex((item) => item.itemName === data.itemName);

    if (foundProd !== -1) {
      gStateP.sampleData.products[foundProd].set(data);
      toast.success('Crop Updated', {
        description: 'Crop updated successfully',
      });
    }
  };

  return { AddCrop, updateCrop };
}
