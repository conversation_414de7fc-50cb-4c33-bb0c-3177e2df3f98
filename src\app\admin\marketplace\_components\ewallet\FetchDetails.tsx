'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import useFinance from '@/lib/hooks/admin/useFinance';
import useFinance1 from '@/lib/hooks/finance1/useFinance1';

export default function FetchDetails() {
  const detailsId = useSearchParams().get('id');
  const { getReqDetails } = useFinance();
  const router = useRouter();

  useEffect(() => {
    if (detailsId) {
      getReqDetails(detailsId);
    } else {
      toast.error('Oops! Something went wrong', {
        description: 'Invalid ID, Please try again',
      });
      router.push('/admin/marketplace');
    }
  }, [detailsId]);

  return null;
}
