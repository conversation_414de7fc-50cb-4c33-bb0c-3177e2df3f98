'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleUserSelector from '@/components/ui/multiple-user-selector';
import { Textarea } from '@/components/ui/textarea';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import { useGroupUserStore } from '@/lib/store/admin/trading-app/group-users-store';
import { AddGroupUserBulkSchema, AddGroupUserBulkType } from '@/lib/store/admin/trading-app/group-users-store.types';
import { cn } from '@/lib/utils';

interface IAddGroupDialogProps {
  userType: UserType.NONFARMER | UserType.FARMER;
  groupPriceId: number;
}

export default function AddGroupUserDialog({ userType, groupPriceId }: IAddGroupDialogProps) {
  const createDialog = useHookstate(false);
  const loading = useHookstate(false);
  const { searchUser, addUserBulk, getUsers, state: groupUserState } = useGroupUserStore();
  const selectedUsers = useHookstate<Array<{ id: number; username: string; description?: string }>>([]);
  // Use regular React state for cache to avoid circular references in Hookstate
  const [searchCache, setSearchCache] = React.useState<Record<string, Array<any>>>({});
  // Store IDs of users already added to the group
  const [existingUserIds, setExistingUserIds] = React.useState<number[]>([]);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    trigger,
    reset,
  } = useForm({
    resolver: zodResolver(
      AddGroupUserBulkSchema.omit({
        tradingAppGroupPriceId: true,
      }),
    ),
    defaultValues: {
      userIds: [],
      remarks: '',
    },
  });

  // Initialize selectedUsers when userIds changes - only run once on mount
  useEffect(() => {
    const userIds = control._formValues.userIds || [];
    if (userIds.length > 0 && selectedUsers.get().length === 0) {
      // If we have userIds but no selectedUsers, initialize with IDs as labels
      // This is a fallback for initial render
      const initialUsers = userIds.map((id) => ({
        id: Number(id),
        username: String(id), // Fallback to using ID as username
        description: '', // Empty description as fallback
      }));
      selectedUsers.set(initialUsers);
    }
  }, []);

  // Function to fetch existing users in the group
  const fetchExistingUsers = async (groupId: number) => {
    try {
      // Get all users in the group
      await getUsers(groupId);
      // Extract user IDs from the response
      const users = groupUserState.data.data.get({ noproxy: true });
      const userIds = users.map((user: any) => user.user.id);
      setExistingUserIds(userIds);
    } catch (e) {
      console.error('Error fetching existing users:', e);
    }
  };

  const onSubmit = async (data: AddGroupUserBulkType) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        tradingAppGroupPriceId: groupPriceId,
      };
      await addUserBulk(updatedData);
      reset();
      selectedUsers.set([]);
      // Clear search cache after submission
      setSearchCache({});
      // Refresh the list of existing users
      if (groupPriceId) {
        await fetchExistingUsers(groupPriceId);
      }
      createDialog.set(false);
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div>
      <Dialog
        open={createDialog.value}
        onOpenChange={(open) => {
          createDialog.set(open);
          if (open && groupPriceId) {
            // Fetch existing users when dialog opens
            fetchExistingUsers(groupPriceId);
          }
          if (!open) {
            reset();
            selectedUsers.set([]);
            // Clear search cache and existing users when dialog is closed
            setSearchCache({});
            setExistingUserIds([]);
          }
        }}
      >
        <DialogTrigger asChild>
          <Button className="h-8 px-6">Add Users</Button>
        </DialogTrigger>

        <DialogContent className="">
          <DialogHeader>
            <DialogTitle className="text-primary">Add Users</DialogTitle>
            <DialogDescription>{`Fill up the forms to add users. Click "Add" when you're ready.`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="mt-3 grid grid-cols-1 gap-4">
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="userIds" className="pb-1 font-normal">
                  User Name
                </Label>
                <Controller
                  control={control}
                  name="userIds"
                  render={({ field }) => (
                    <MultipleUserSelector
                      placeholder="Select users"
                      value={selectedUsers.get().map((user) => ({
                        value: String(user.id),
                        label: user.description ? `${user.username} - ${user.description}` : user.username,
                      }))}
                      onChange={(options) => {
                        // Update the selectedUsers state with the new selections
                        const newSelectedUsers = options.map((option) => {
                          // Extract username from the label (format: "username - first_name last_name")
                          const labelParts = option.label.split(' - ');
                          const username = labelParts[0];
                          // Clean up the description by removing any status indicators
                          const description =
                            labelParts[1]?.replace(' (Already selected)', '').replace(' (Already in group)', '') || '';

                          return {
                            id: Number(option.value),
                            username: username,
                            description: description,
                          };
                        });

                        // Ensure no duplicate users by filtering based on ID
                        const uniqueUsers = newSelectedUsers.filter(
                          (user, index, self) => index === self.findIndex((u) => u.id === user.id),
                        );

                        // Only update if the selection has actually changed
                        const currentIds = selectedUsers
                          .get()
                          .map((user) => user.id)
                          .sort()
                          .join(',');
                        const newIds = uniqueUsers
                          .map((user) => user.id)
                          .sort()
                          .join(',');

                        if (currentIds !== newIds) {
                          selectedUsers.set(uniqueUsers);

                          // Update the form value with just the IDs for submission
                          const userIds = uniqueUsers.map((user) => user.id);
                          setValue('userIds', userIds);
                          trigger('userIds');
                        }
                      }}
                      delay={500}
                      onSearch={async (query) => {
                        // Only search if query has at least 2 characters
                        if (query.length < 2) return [];

                        // Get current selected user IDs to prevent duplicates
                        const selectedIds = selectedUsers.get().map((user) => String(user.id));

                        // Check if we have cached results for this query
                        if (searchCache[query]) {
                          const cachedResults = searchCache[query];

                          // Process cached results to mark already selected items and existing users
                          return cachedResults.map((action) => {
                            const isAlreadySelected = selectedIds.includes(String(action.id));
                            const isExistingUser = existingUserIds.includes(Number(action.id));

                            return {
                              value: action.id,
                              label: isAlreadySelected
                                ? `${action.label} - ${action.description} (Already selected)`
                                : isExistingUser
                                  ? `${action.label} - ${action.description} (Already in group)`
                                  : `${action.label} - ${action.description}`,
                              description: action.description,
                              disable: isAlreadySelected || isExistingUser,
                            };
                          });
                        }

                        // If not cached, perform the search
                        const result = await searchUser(query, userType);

                        // Cache the raw results (limit to 20 entries to prevent memory issues)
                        const newCache = { ...searchCache };
                        newCache[query] = result.actions;

                        // Limit cache size by removing oldest entries if we exceed 20 entries
                        const cacheKeys = Object.keys(newCache);
                        if (cacheKeys.length > 20) {
                          // Remove the first (oldest) entries to keep only 20
                          const keysToRemove = cacheKeys.slice(0, cacheKeys.length - 20);
                          keysToRemove.forEach((key) => {
                            delete newCache[key];
                          });
                        }

                        setSearchCache(newCache);

                        return result.actions.map((action) => {
                          const isAlreadySelected = selectedIds.includes(String(action.id));
                          const isExistingUser = existingUserIds.includes(Number(action.id));

                          return {
                            value: action.id,
                            label: isAlreadySelected
                              ? `${action.label} - ${action.description} (Already selected)`
                              : isExistingUser
                                ? `${action.label} - ${action.description} (Already in group)`
                                : `${action.label} - ${action.description}`,
                            description: action.description,
                            disable: isAlreadySelected || isExistingUser,
                          };
                        });
                      }}
                      loadingIndicator={
                        <div className="flex items-center justify-center p-4">
                          <div className="size-6 animate-spin rounded-full border-b-2 border-primary"></div>
                        </div>
                      }
                      emptyIndicator={<p className="p-4 text-center text-sm text-gray-500">No users found</p>}
                    />
                  )}
                />
                {errors.userIds && <p className="form-error">{`${errors.userIds.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="remarks" className="pb-1 font-normal">
                  Remarks
                </Label>
                <Textarea
                  {...register('remarks')}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.remarks && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  placeholder="Enter Remarks"
                />
                {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
              </div>
            </div>

            <div className="flex justify-between gap-2 pt-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              {loading.value ? (
                <ButtonLoading text="Adding" />
              ) : (
                <Button className="px-12" type="submit">
                  Add
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
