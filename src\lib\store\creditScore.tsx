'use client';

import { extend, hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { subscribable } from '@hookstate/subscribable';

interface CreditScoreType {
  total: {
    before: number;
    during: number;
    after: number;
  };
  before: {
    total: {
      accountProfile: number;
      agricultureActivity: number;
      transactionRecords: number;
    };
    accountProfile: {
      basicInfo: number;
      careerAcademic: number;
      identificationDocuments: number;
      familyProfile: number;
      biometrics: number;
      propertyOwnership: number;
      farmDetails: number;
      vouchByLeaders: number;
      mao: number;
    };
    agricultureActivity: {
      geoTagging: number;
      gapCompliance: number;
    };
    transactionRecords: {
      marketplace: number;
      tradingPost: number;
      sales: number;
    };
    propertyOwnership: {
      conditions: {
        1: {
          loan: number;
          pts: number;
        };
        2: {
          loan: number;
          pts: number;
        };
        3: {
          loan: number;
          pts: number;
        };
        4: {
          loan: number;
          pts: number;
        };
        5: {
          loan: number;
          pts: number;
        };
      };
    };
    marketplaceComputation: {
      1: {
        from: number;
        upto: number;
        pts: number;
      };
      2: {
        from: number;
        upto: number;
        pts: number;
      };
      3: {
        from: number;
        upto: number;
        pts: number;
      };
      4: {
        from: number;
        upto: number;
        pts: number;
      };
      5: {
        from: number;
        upto: number;
        pts: number;
      };
    };
    tradingPostComputation: {
      1: {
        from: number;
        upto: number;
        pts: number;
      };
      2: {
        from: number;
        upto: number;
        pts: number;
      };
      3: {
        from: number;
        upto: number;
        pts: number;
      };
      4: {
        from: number;
        upto: number;
        pts: number;
      };
      5: {
        from: number;
        upto: number;
        pts: number;
      };
    };
    salesComputation: {
      1: {
        from: number;
        upto: number;
        pts: number;
      };
      2: {
        from: number;
        upto: number;
        pts: number;
      };
      3: {
        from: number;
        upto: number;
        pts: number;
      };
      4: {
        from: number;
        upto: number;
        pts: number;
      };
      5: {
        from: number;
        upto: number;
        pts: number;
      };
    };
  };
  during: {
    total: {
      agricultureActivity: number;
      transactionRecords: number;
    };
    agricultureActivity: {
      landPreparation: number;
      recommendedSeedsPerArea: number;
      fertilizationSchedule: number;
      fertilizationVolume: number;
      cropProtectionSchedule: number;
      cropProtectionVolume: number;
      harvestProjection: number;
    };
    transactionRecords: {
      marketplace: number;
      tradingPost: number;
      sales: number;
    };
    marketplaceComputation: {
      1: {
        from: number;
        upto: number;
        pts: number;
      };
      2: {
        from: number;
        upto: number;
        pts: number;
      };
      3: {
        from: number;
        upto: number;
        pts: number;
      };
      4: {
        from: number;
        upto: number;
        pts: number;
      };
      5: {
        from: number;
        upto: number;
        pts: number;
      };
    };
    tradingPostComputation: {
      1: {
        from: number;
        upto: number;
        pts: number;
      };
      2: {
        from: number;
        upto: number;
        pts: number;
      };
      3: {
        from: number;
        upto: number;
        pts: number;
      };
      4: {
        from: number;
        upto: number;
        pts: number;
      };
      5: {
        from: number;
        upto: number;
        pts: number;
      };
    };
    salesComputation: {
      1: {
        from: number;
        upto: number;
        pts: number;
      };
      2: {
        from: number;
        upto: number;
        pts: number;
      };
      3: {
        from: number;
        upto: number;
        pts: number;
      };
      4: {
        from: number;
        upto: number;
        pts: number;
      };
      5: {
        from: number;
        upto: number;
        pts: number;
      };
    };
    fertilizationSchedule: {
      firstVisit: number;
      secondVisit: number;
      thirdVisit: number;
    };
    fertilizationVolume: {
      firstVisit: number;
      secondVisit: number;
      thirdVisit: number;
    };
    cropProtectionSchedule: {
      firstVisit: number;
      secondVisit: number;
      thirdVisit: number;
    };
    cropProtectionVolume: {
      firstVisit: number;
      secondVisit: number;
      thirdVisit: number;
    };
  };
  after: {
    total: {
      creditHistory: number;
      loanCycles: number;
      agricultureActivity: number;
    };
    creditHistory: {
      repaymentBehavior: number;
      paymentBeforeDue: number;
      paymentGracePeriod: number;
      paymentBeyondGracePeriod: number;
      gracePeriodDays: number;
    };
    loanCycles: {
      loanCycle: number;
      conditions: {
        1: {
          from: number;
          loan: number;
          pts: number;
        };
        2: {
          from: number;
          loan: number;
          pts: number;
        };
        3: {
          from: number;
          loan: number;
          pts: number;
        };
        4: {
          from: number;
          loan: number;
          pts: number;
        };
      };
    };
    agricultureActivity: {
      targetYieldAchievement: number;
    };
    targetYield: {
      yield1: number;
      yield2: number;
      yield3: number;
    };
  };
}

export const creditScore = hookstate(
  {
    total: {
      before: 0,
      during: 0,
      after: 0,
    },
    before: {
      total: {
        accountProfile: 0,
        agricultureActivity: 0,
        transactionRecords: 0,
      },
      accountProfile: {
        basicInfo: 0,
        careerAcademic: 0,
        identificationDocuments: 0,
        familyProfile: 0,
        biometrics: 0,
        propertyOwnership: 0,
        farmDetails: 0,
        vouchByLeaders: 0,
        mao: 0,
      },
      agricultureActivity: {
        geoTagging: 0,
        gapCompliance: 0,
      },
      transactionRecords: {
        marketplace: 0,
        tradingPost: 0,
        sales: 0,
      },
      propertyOwnership: {
        conditions: {
          1: {
            loan: 2,
            pts: 0,
          },
          2: {
            loan: 4,
            pts: 0,
          },
          3: {
            loan: 6,
            pts: 0,
          },
          4: {
            loan: 8,
            pts: 0,
          },
          5: {
            loan: 9,
            pts: 0,
          },
        },
      },
      marketplaceComputation: {
        1: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        2: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        3: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        4: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        5: {
          from: 0,
          upto: 0,
          pts: 0,
        },
      },
      tradingPostComputation: {
        1: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        2: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        3: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        4: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        5: {
          from: 0,
          upto: 0,
          pts: 0,
        },
      },
      salesComputation: {
        1: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        2: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        3: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        4: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        5: {
          from: 0,
          upto: 0,
          pts: 0,
        },
      },
    },
    during: {
      total: {
        agricultureActivity: 0,
        transactionRecords: 0,
      },
      agricultureActivity: {
        landPreparation: 0,
        recommendedSeedsPerArea: 0,
        fertilizationSchedule: 0,
        fertilizationVolume: 0,
        cropProtectionSchedule: 0,
        cropProtectionVolume: 0,
        harvestProjection: 0,

        farmActivities: 0,
        harvestForecast: 0,
      },
      transactionRecords: {
        marketplace: 0,
        tradingPost: 0,
        sales: 0,
      },
      marketplaceComputation: {
        1: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        2: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        3: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        4: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        5: {
          from: 0,
          upto: 0,
          pts: 0,
        },
      },
      tradingPostComputation: {
        1: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        2: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        3: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        4: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        5: {
          from: 0,
          upto: 0,
          pts: 0,
        },
      },
      salesComputation: {
        1: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        2: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        3: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        4: {
          from: 0,
          upto: 0,
          pts: 0,
        },
        5: {
          from: 0,
          upto: 0,
          pts: 0,
        },
      },
      fertilizationSchedule: {
        firstVisit: 0,
        secondVisit: 0,
        thirdVisit: 0,
      },
      fertilizationVolume: {
        firstVisit: 0,
        secondVisit: 0,
        thirdVisit: 0,
      },
      cropProtectionSchedule: {
        firstVisit: 0,
        secondVisit: 0,
        thirdVisit: 0,
      },
      cropProtectionVolume: {
        firstVisit: 0,
        secondVisit: 0,
        thirdVisit: 0,
      },
    },
    after: {
      total: {
        creditHistory: 0,
        loanCycles: 0,
        agricultureActivity: 0,
      },
      creditHistory: {
        repaymentBehavior: 0,
        paymentBeforeDue: 0,
        paymentGracePeriod: 0,
        paymentBeyondGracePeriod: 0,
        gracePeriodDays: 0,
      },
      loanCycles: {
        loanCycle: 0,
        conditions: {
          1: {
            from: 0,
            loan: 2,
            pts: 0,
          },
          2: {
            from: 0,
            loan: 4,
            pts: 0,
          },
          3: {
            from: 0,
            loan: 6,
            pts: 0,
          },
          4: {
            from: 0,
            loan: 8,
            pts: 0,
          },
        },
      },
      agricultureActivity: {
        targetYieldAchievement: 0,
      },
      targetYield: {
        yield1: 0,
        yield2: 0,
        yield3: 0,
      },
    },
  } as CreditScoreType,
  extend(devtools({ key: 'creditScoreState' }), subscribable()),
);

export const useCreditScoreState = () => useHookstate(creditScore);
