'use client';

import { Sheet, SheetContent } from '@/components/ui/sheet';

import MenuList from './menu-list';

interface IMobileMenuProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function MobileMenu({ open, onOpenChange }: IMobileMenuProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="left" className="flex flex-col px-0">
        <img className="mx-auto mt-2 block h-[2.8rem] lg:hidden" src="/kita-logo.png" alt="kitaph logo" />

        <section className="h-[calc(100vh-76.8px)] space-y-3 py-[32px] pr-4">
          <MenuList onItemClick={() => onOpenChange(false)} />
        </section>
      </SheetContent>
    </Sheet>
  );
}
