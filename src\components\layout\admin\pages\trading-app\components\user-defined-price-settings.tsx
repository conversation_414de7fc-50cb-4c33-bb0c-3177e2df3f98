'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import { DEFAULT_GROUP_TAB } from '@/lib/constants';
import { useGroupPriceStore } from '@/lib/store/admin/trading-app/group-price-store';
import { useGroupUserStore } from '@/lib/store/admin/trading-app/group-users-store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import AddGroupUserDialog from './add-group-user.dialog';
import AddGroupDialog from './add-group.dialog';
import GroupInfoCard from './group-info.card';
import { GroupTable } from './group-table';
import { ColumnGroup } from './group-table/columns';
import { GroupUserTable } from './group-user-table';
import { ColumnGroupUser } from './group-user-table/columns';

interface UserDefinedPriceSettingsProps {
  userType: UserType.NONFARMER | UserType.FARMER;
}

export default function UserDefinedPriceSettings({ userType }: UserDefinedPriceSettingsProps) {
  const { getGroups, state } = useGroupPriceStore();
  const { getUsers, state: groupUserState } = useGroupUserStore();
  const tab = useHookstate(state[userType].tab);
  const group = useHookstate(state[userType].selectedGroup);
  const pagination = useHookstate(groupUserState.pagination);

  useEffect(() => {
    getGroups(userType);
  }, []);

  useEffect(() => {
    if (tab.value === DEFAULT_GROUP_TAB) return;
    getUsers(tab.value);
  }, [tab, pagination.page, pagination.pageSize]);

  return (
    <div className="mt-8">
      <div className="font-dmSans text-xl font-bold text-primary">User-Defined Price Settings</div>

      <div className="mt-6 flex items-center gap-8">
        {/* Tabs */}
        <div className="flex-1">
          <HorizontalScrollBar>
            <div className="flex items-center gap-8 pb-2">
              <button
                className={cn(
                  'transition-all shrink-0 duration-300 ease-in-out',
                  tab.value === DEFAULT_GROUP_TAB
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => tab.set(DEFAULT_GROUP_TAB)}
              >
                All Groups
              </button>
              {state[userType].data.data.value.map((group) => (
                <button
                  key={group.id}
                  className={cn(
                    'transition-all shrink-0 duration-300 ease-in-out',
                    tab.value === group.id
                      ? 'font-bold text-primary/90 underline underline-offset-8'
                      : 'text-gray-500 hover:font-bold hover:text-primary/80',
                  )}
                  onClick={() => tab.set(group.id)}
                >
                  {group.name}
                </button>
              ))}
            </div>
          </HorizontalScrollBar>
        </div>

        {tab.value === DEFAULT_GROUP_TAB && <AddGroupDialog userType={userType} />}
      </div>

      <div key={JSON.stringify(state[userType].data.data.get({ noproxy: true }))} className="mt-4">
        {tab.value === DEFAULT_GROUP_TAB ? (
          <GroupTable data={state[userType].data.data.get({ noproxy: true })} columns={ColumnGroup} />
        ) : (
          <div>
            <GroupInfoCard groupId={tab.value} userType={userType} />

            <div className="my-6 grid place-items-end">
              <AddGroupUserDialog userType={userType} groupPriceId={group.value ? group.id.value : null} />
            </div>

            <div>
              <GroupUserTable
                columns={ColumnGroupUser}
                data={groupUserState.data.data.get({ noproxy: true })}
                meta={groupUserState.data.meta.get({ noproxy: true })}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
