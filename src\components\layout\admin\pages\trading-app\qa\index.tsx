'use client';

import { useEffect } from 'react';

import { useQaStore } from '@/lib/store/admin/trading-app/qa-store';

import { StaffTable } from './components/staff-table';
import { ColumnStaff } from './components/staff-table/columns';

export default function QaPage() {
  const { getStaff, state } = useQaStore();

  useEffect(() => {
    getStaff();
  }, []);

  return (
    <div>
      <StaffTable data={state.data.get({ noproxy: true })} columns={ColumnStaff} />
    </div>
  );
}
