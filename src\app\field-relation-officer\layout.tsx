'use client';

import { useEffect } from 'react';

import FROLayout from '@/features/field-relation-officer/layout';
import usePublic from '@/lib/hooks/usePublic';
import { useGlobalStatePersist } from '@/lib/store/persist';
import barangays from '@/public/address/barangay.json';
import cities from '@/public/address/city.json';
import provinces from '@/public/address/province.json';
import regions from '@/public/address/region.json';

import { IBarangay } from './_components/types';

export default function Layout({ children }: { children: React.ReactNode }) {
  const gStateP = useGlobalStatePersist();
  const { getAllCrops } = usePublic();

  useEffect(() => {
    if (!gStateP.fro.options.address.regions.length) {
      gStateP.fro.options.address.regions.set(regions);
      gStateP.fro.options.address.provinces.set(provinces);
      gStateP.fro.options.address.cities.set(cities);
      gStateP.fro.options.address.barangays.set(barangays as IBarangay[]);
    }
    getAllCrops();
  }, []);

  return <FROLayout>{children}</FROLayout>;
}
