'use client';

import { useHookstate } from '@hookstate/core';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import useCrops from '@/lib/hooks/useCrops';
import { useGlobalState } from '@/lib/store';

import { PriceHistoryTable } from './_components/price-history-table';
import { columns } from './_components/price-history-table/columns';

export default function CropPriceHistory() {
  const gState = useGlobalState();
  const searchParams = useSearchParams();
  const cropId = searchParams.get('cid');
  const cropName = searchParams.get('cropName');
  const { getCropPriceRangeHistory } = useCrops();
  const pagination = useHookstate(gState.admin.pagination.cropPriceHistory);

  useEffect(() => {
    if (cropId) {
      getCropPriceRangeHistory(cropId);
    }
  }, [cropId, pagination.page, pagination.pageSize]);

  return (
    <div className="px-6 py-8">
      <h1 className="pb-6 text-2xl font-bold">{`${cropName} Price History`}</h1>

      <PriceHistoryTable
        columns={columns}
        data={gState.admin.crops.priceHistory.data.get({ noproxy: true })}
        metadata={gState.admin.crops.priceHistory.meta.get({ noproxy: true })}
      />
    </div>
  );
}
