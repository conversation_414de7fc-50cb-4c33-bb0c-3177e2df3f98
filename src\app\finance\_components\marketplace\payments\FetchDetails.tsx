'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import useLoanPayment from '@/lib/hooks/useLoanPayment';

export default function FetchDetails() {
  const detailsId = useSearchParams().get('id');
  const { fetchRequest } = useLoanPayment();
  const router = useRouter();

  useEffect(() => {
    if (detailsId) {
      fetchRequest(detailsId);
    } else {
      toast.error('Oops! Something went wrong', {
        description: 'Invalid ID, Please try again',
      });
      router.push('/finance');
    }
  }, [detailsId]);

  return null;
}
