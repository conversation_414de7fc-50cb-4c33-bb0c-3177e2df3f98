'use client';

import { useInfiniteQuery } from '@tanstack/react-query';

import axios from '@/lib/api';

import { EProducttype, IProductResponse } from '../types/product.types';

// fetcher
export const fetchProducts = async (pageParam: number, productType: EProducttype, status: string[] = ['1']) => {
  const { data } = await axios.get('/marketplace/product/viewAll', {
    params: {
      page: pageParam,
      pageSize: 10,
      productType: [productType],
      search: '',
      productTypeCategory: [],
      status,
    },
  });
  return data.data as IProductResponse;
};

// hook
export const useProducts = (productType: EProducttype = null) => {
  const productsQuery = useInfiniteQuery({
    queryKey: ['products', { productType }],
    queryFn: ({ pageParam }) => fetchProducts(pageParam as number, productType),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage.meta.current_page === lastPage.meta.last_page) return undefined;
      return lastPage.meta.current_page + 1;
    },
    enabled: !!productType,
  });

  return { productsQuery };
};
