'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { toast } from 'sonner';

import axios from '@/lib/api';

// Types for sub-item operations
export interface IAddFarmPlanTemplateSubItem {
  farmPlanTemplateItemId: number;
  expectedDate: string | Date;
  itemName: string;
  unit: string;
  quantity: number;
  unitCost: number;
  notes?: string;
  marketplaceProductId?: number;
  reason: string;
}

export interface IUpdateFarmPlanTemplateSubItem {
  farmPlanTemplateSubItemId: number;
  expectedDate?: string | Date;
  itemName?: string;
  unit?: string;
  quantity?: number;
  unitCost?: number;
  notes?: string;
  marketplaceProductId?: number;
  reason: string;
}

export interface IRemoveFarmPlanTemplateSubItem {
  farmPlanTemplateSubItemId: number;
  reason: string;
}

// API functions
const addFarmPlanTemplateSubItem = async (data: IAddFarmPlanTemplateSubItem) => {
  const formattedData = {
    ...data,
    expectedDate: data.expectedDate instanceof Date ? format(data.expectedDate, 'yyyy-MM-dd') : data.expectedDate,
  };
  const { data: res } = await axios.post('/agronomist/farmplan/template/subitem/add', formattedData);
  return res;
};

const updateFarmPlanTemplateSubItem = async (data: IUpdateFarmPlanTemplateSubItem) => {
  const formattedData = {
    ...data,
    expectedDate: data.expectedDate instanceof Date ? format(data.expectedDate, 'yyyy-MM-dd') : data.expectedDate,
  };
  const { data: res } = await axios.post('/agronomist/farmplan/template/subitem/update', formattedData);
  return res;
};

const removeFarmPlanTemplateSubItem = async (data: IRemoveFarmPlanTemplateSubItem) => {
  const { data: res } = await axios.post('/agronomist/farmplan/template/subitem/remove', data);
  return res;
};

// Hook for adding farm plan template sub-items
export const useAddFarmPlanTemplateSubItem = (farmPlanTemplateId?: number) => {
  const queryClient = useQueryClient();

  const addSubItemMutation = useMutation({
    mutationFn: addFarmPlanTemplateSubItem,
    onSuccess: (res) => {
      toast.success('Success', {
        description: res.message || 'Sub-item added successfully',
      });

      // Refetch template data
      if (farmPlanTemplateId) {
        queryClient.refetchQueries({
          queryKey: ['farmPlanTemplateById', farmPlanTemplateId],
        });
      }

      // Refetch audit logs
      if (farmPlanTemplateId) {
        queryClient.refetchQueries({
          queryKey: ['farmPlanTemplateAuditLogs', { farmPlanTemplateId }],
        });
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || error.message || 'Failed to add sub-item';
      toast.error('Error', {
        description: errorMessage,
      });
    },
  });

  return {
    addSubItem: addSubItemMutation.mutateAsync,
    isAdding: addSubItemMutation.isPending,
    addSubItemMutation,
  };
};

// Hook for updating farm plan template sub-items
export const useUpdateFarmPlanTemplateSubItem = (farmPlanTemplateId?: number) => {
  const queryClient = useQueryClient();

  const updateSubItemMutation = useMutation({
    mutationFn: updateFarmPlanTemplateSubItem,
    onSuccess: (res) => {
      toast.success('Success', {
        description: res.message || 'Sub-item updated successfully',
      });

      // Refetch template data
      if (farmPlanTemplateId) {
        queryClient.refetchQueries({
          queryKey: ['farmPlanTemplateById', farmPlanTemplateId],
        });
      }

      // Refetch audit logs
      if (farmPlanTemplateId) {
        queryClient.refetchQueries({
          queryKey: ['farmPlanTemplateAuditLogs', { farmPlanTemplateId }],
        });
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || error.message || 'Failed to update sub-item';
      toast.error('Error', {
        description: errorMessage,
      });
    },
  });

  return {
    updateSubItem: updateSubItemMutation.mutateAsync,
    isUpdating: updateSubItemMutation.isPending,
    updateSubItemMutation,
  };
};

// Hook for removing farm plan template sub-items
export const useRemoveFarmPlanTemplateSubItem = (farmPlanTemplateId?: number) => {
  const queryClient = useQueryClient();

  const removeSubItemMutation = useMutation({
    mutationFn: removeFarmPlanTemplateSubItem,
    onSuccess: (res) => {
      toast.success('Success', {
        description: res.message || 'Sub-item removed successfully',
      });

      // Refetch template data
      if (farmPlanTemplateId) {
        queryClient.refetchQueries({
          queryKey: ['farmPlanTemplateById', farmPlanTemplateId],
        });
      }

      // Refetch audit logs
      if (farmPlanTemplateId) {
        queryClient.refetchQueries({
          queryKey: ['farmPlanTemplateAuditLogs', { farmPlanTemplateId }],
        });
      }
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || error.message || 'Failed to remove sub-item';
      toast.error('Error', {
        description: errorMessage,
      });
    },
  });

  return {
    removeSubItem: removeSubItemMutation.mutateAsync,
    isRemoving: removeSubItemMutation.isPending,
    removeSubItemMutation,
  };
};
