'use client';

import { useHookstate } from '@hookstate/core';
import { LoaderIcon } from 'lucide-react';
import { useRef } from 'react';

import useFarmer from '@/lib/hooks/fro/useFarmer';
import { useGlobalState } from '@/lib/store';
import { urlify } from '@/lib/utils';

export default function ProfileImage({ data }) {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();

  const loading = useHookstate(false);
  const inputRef = useRef(null);

  const handleFileUpload = async (e) => {
    if (loading.value) return;

    try {
      loading.set(true);
      const file = e.target.files[0];
      console.log('uploaded file:', file);

      await updateFarmer({ userImage: file });
    } catch (e) {
      console.log(e);
    } finally {
      loading.set(false);
    }
  };

  const handleUploadClick = (e) => {
    e.preventDefault();
    if (inputRef.current) {
      inputRef.current.click();
    }
  };

  return (
    <div className="flex justify-center px-8">
      <div className="group relative cursor-pointer rounded-full">
        <img
          key={data?.user_img || 'default'}
          className="mx-auto size-32 rounded-full border border-white bg-white ring ring-white"
          src={data?.user_img ? urlify(data?.user_img, 'users/profile') : '/assets/user-default.jpg'}
          alt=""
        />
        <input
          ref={inputRef}
          type="file"
          accept="image/*" // Allow only image files
          hidden
          onChange={handleFileUpload}
          disabled={loading.value}
        />

        {loading.value ? (
          <div className="absolute inset-0 grid place-items-center rounded-full bg-black/30">
            <LoaderIcon className="size-8 animate-spin text-white" />
          </div>
        ) : (
          <div
            className="invisible absolute inset-0 grid place-items-center rounded-full bg-black/30 group-hover:visible"
            onClick={handleUploadClick}
          >
            <div className="text-white">Upload</div>
          </div>
        )}
      </div>
    </div>
  );
}
