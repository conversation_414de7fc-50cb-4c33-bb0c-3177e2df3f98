'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ColumnDef } from '@tanstack/react-table';
import { useForm } from 'react-hook-form';
import { FiPlus, FiTrash2 } from 'react-icons/fi';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import useFarmer, { RequirementFormType, RequirementSchema } from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { LandbankRequirementLabels } from '../../../../constants';
import { LandbankRequirementCheckbox } from '../components/LandbankRequirementCheckbox';
import { LandbankSelectAllHeader } from '../components/LandbankSelectAllHeader';
import { RequirementProps } from '../landbank-reqts-types';

export const columns: ColumnDef<RequirementProps>[] = [
  {
    id: 'checkbox',
    header: ({ table }) => <LandbankSelectAllHeader table={table} />,
    cell: ({ row }) => {
      const existingReq = row.original;
      if (!existingReq?.attachment) return null;

      return <LandbankRequirementCheckbox row={row} />;
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="File Name" />,
    cell: ({ row }) => {
      const requirementType = row.original.type;
      return LandbankRequirementLabels[requirementType];
    },
  },
  {
    id: 'attachments',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Attachments" />,
    cell: ({ row }) => <AttachmentsCell row={row} />, // Use component here
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <RequirementAction row={row} />,
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Uploaded" />,
    cell: ({ row }) => {
      const data = row.original;
      const dateValue = data.created_at;
      const date = dateValue ? new Date(dateValue) : null;
      if (!date || isNaN(date.getTime())) {
        return ' ';
      }

      return (
        <div className="min-w-max">
          {date.toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
  },
  {
    accessorKey: 'processedBy.username',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Uploaded By" />,
    cell: ({ row }) => row.original?.processedBy?.username || '-',
  },
];

const AttachmentsCell = ({ row }) => {
  const existingReq = row.original;
  const dialogOpen = useHookstate(false);

  return (
    <>
      {existingReq?.attachment ? (
        <Button variant="link" onClick={() => window.open(existingReq.attachment, '_blank')}>
          View File
        </Button>
      ) : (
        <>
          <Button size="sm" color="primary" onClick={() => dialogOpen.set(true)}>
            Browse
          </Button>
          <AddRequirementModal
            requirement={row.original}
            open={dialogOpen.value}
            onOpenChange={dialogOpen.set}
            onSuccess={() => dialogOpen.set(false)}
          />
        </>
      )}
    </>
  );
};

const RequirementAction = ({ row }) => {
  const gState = useGlobalState();
  const { deleteLandbankReqt } = useFarmer();
  const deleteDialog = useHookstate(false);
  const loading = useHookstate(false);
  const requirement = row.original;
  const profileTab = useHookstate(gState.selected.accountInfo.tabs.profile);
  const activeStep = useHookstate(gState.selected.accountInfo.tabs.activeStep);
  const addDialog = useHookstate(false);

  const handleDelete = async () => {
    try {
      loading.set(true);
      await deleteLandbankReqt(requirement.type);
      activeStep.set(activeStep.value);
      profileTab.set(profileTab.value);
      deleteDialog.set(false);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div className="flex gap-2">
      {requirement?.attachment ? (
        <AlertDialog open={deleteDialog.value} onOpenChange={deleteDialog.set}>
          <AlertDialogTrigger>
            <FiTrash2 className="size-5 cursor-pointer text-red-600 hover:text-red-700" />
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the attached file.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete} disabled={loading.value}>
                {loading.value ? 'Deleting...' : 'Continue'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      ) : (
        <>
          <FiPlus className="size-5 cursor-pointer hover:text-primary" onClick={() => addDialog.set(true)} />
          <AddRequirementModal
            requirement={row.original}
            open={addDialog.value}
            onOpenChange={addDialog.set}
            onSuccess={() => addDialog.set(false)}
          />
        </>
      )}
    </div>
  );
};

const AddRequirementModal = ({ requirement, open, onOpenChange, onSuccess }) => {
  const gState = useGlobalState();
  const { updateLandbankReqt } = useFarmer();
  const loading = useHookstate(false);
  const profileTab = useHookstate(gState.selected.accountInfo.tabs.profile);
  const activeStep = useHookstate(gState.selected.accountInfo.tabs.activeStep);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<RequirementFormType>({
    resolver: zodResolver(RequirementSchema),
    defaultValues: {
      type: requirement.type,
      attachment: undefined,
    },
  });

  const handleAddSubmit = async (data: RequirementFormType) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        attachment: data.attachment[0],
      };

      await updateLandbankReqt(updatedData);
      activeStep.set(activeStep.value);
      profileTab.set(profileTab.value);
      onSuccess?.();
      onOpenChange(false);
      reset();
    } finally {
      loading.set(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Requirement</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleAddSubmit)}>
          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="type">Requirement Type</Label>
              <Input
                value={LandbankRequirementLabels[requirement.type]}
                readOnly
                className="cursor-not-allowed bg-gray-100"
              />
              <input type="hidden" {...register('type')} />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="type">Upload File</Label>
              <Input
                type="file"
                {...register('attachment')}
                accept=".pdf,.doc,.docx,.jpg,.png"
                className={cn(errors.attachment && 'border-red-500')}
              />
              {errors.attachment && (
                <p className="text-sm text-red-500">
                  {typeof errors.attachment.message === 'string' ? errors.attachment.message : 'File is required'}
                </p>
              )}
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading.value}>
              {loading.value ? 'Uploading...' : 'Upload'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
