'use client';

import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { LoanRequestStatus } from '@/app/admin/marketplace/_components/Enums';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function LoanRequestTable({ columns, data, meta = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;

        router.push(`/admin/marketplace/payment/request/details?id=${data.id}`);
        console.log('row clicked', data);
      },
    },
  });

  return (
    <div className="space-y-4">
      {/* Stats */}
      <div className="grid grid-cols-1 gap-4 pb-4 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-5">
        {/* Total Orders */}
        <DashboardStat
          value={gState.finance.loanPayments.dashboard.allPayment.count.value}
          amount={gState.finance.loanPayments.dashboard.allPayment.amount.value}
          label="All Payments"
          className="border border-transparent bg-dashboard-blue focus:border-dashboard-blue-foreground focus:shadow-xl"
          valueClassName="text-dashboard-blue-foreground"
          img="/assets/icon/loanpayments/all_payments_icon.png"
          onClick={() => gState.finance.loanPayments.pagination.requests.status.set([])}
        />

        {/* Pending */}
        <DashboardStat
          value={gState.finance.loanPayments.dashboard.forApproval.count.value}
          amount={gState.finance.loanPayments.dashboard.forApproval.amount.value}
          label="For Approval"
          className="border border-transparent bg-dashboard-yellow focus:border-dashboard-yellow-foreground focus:shadow-xl"
          valueClassName="text-dashboard-yellow-foreground"
          img="/assets/icon/loanpayments/for_approval.svg"
          onClick={() =>
            gState.finance.loanPayments.pagination.requests.status.set([LoanRequestStatus.PENDING.toString()])
          }
        />

        {/* Preparing */}
        <DashboardStat
          value={gState.finance.loanPayments.dashboard.partiallyPaid.count.value}
          amount={gState.finance.loanPayments.dashboard.partiallyPaid.amount.value}
          label="Partially Paid"
          className="border border-transparent bg-dashboard-purple focus:border-dashboard-purple-foreground focus:shadow-xl"
          valueClassName="text-dashboard-purple-foreground"
          img="/assets/icon/loanpayments/partially_paid.png"
          onClick={() =>
            gState.finance.loanPayments.pagination.requests.status.set([LoanRequestStatus.PARTIALLY_PAID.toString()])
          }
        />

        {/* Order Ready */}
        <DashboardStat
          value={gState.finance.loanPayments.dashboard.paid.count.value}
          amount={gState.finance.loanPayments.dashboard.paid.amount.value}
          label="Paid"
          className="border border-transparent bg-dashboard-green focus:border-dashboard-green-foreground focus:shadow-xl"
          valueClassName="text-dashboard-green-foreground"
          img="/assets/icon/loanpayments/paid.png"
          onClick={() =>
            gState.finance.loanPayments.pagination.requests.status.set([LoanRequestStatus.PAID.toString()])
          }
        />

        {/* Cancelled */}
        <DashboardStat
          value={gState.finance.loanPayments.dashboard.rejected.count.value}
          amount={gState.finance.loanPayments.dashboard.rejected.amount.value}
          label="Rejected"
          className="border border-transparent bg-dashboard-red-dark focus:border-dashboard-red-dark-foreground focus:shadow-xl"
          valueClassName="text-dashboard-red-dark-foreground"
          img="/assets/icon/loanpayments/rejected.png"
          onClick={() =>
            gState.finance.loanPayments.pagination.requests.status.set([LoanRequestStatus.REJECTED.toString()])
          }
        />
      </div>

      <DataTableToolbar id="admin-loan-payment-table" table={table} meta={meta} />

      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      const target = event.target as HTMLElement;
                      const isButton = target.tagName.toLowerCase() === 'button';
                      const isP = target.tagName.toLowerCase() === 'p';
                      const isHeading = target.tagName.toLowerCase() === 'h2';
                      const isOpen = target.dataset.state === 'open';
                      const isDisabled = target.classList.contains('flex');

                      if (!isButton && !isP && !isHeading && !isOpen && !isDisabled) {
                        table.options.meta?.getRowClicked?.(row);
                      }
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {meta ? (
        <DataTablePaginationMeta
          table={table}
          meta={meta}
          onChangePageSize={(pageSize) => {
            gState.finance.loanPayments.pagination.requests.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            gState.finance.loanPayments.pagination.requests.page.set(page);
          }}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}

export const DashboardStat = ({
  value,
  amount,
  label,
  img,
  onClick = () => {},
  className = '',
  valueClassName = '',
}) => {
  return (
    <button
      className={cn(
        'flex items-center justify-start px-4 rounded-xl gap-4 font-poppins py-6 cursor-pointer',
        'transition-all duration-300 ease-in-out',
        className,
      )}
      onClick={onClick}
    >
      <div>
        <img className="h-[2.6rem]" src={img} alt={label} />
      </div>
      <div className="flex-1 space-y-0.5 text-center text-[#151D48]">
        <div className="min-w-max font-medium">{label}</div>
        <div className={cn('text-4xl font-bold', valueClassName)}>{value}</div>
        <div className="min-w-max font-bold">
          {Number(amount).toLocaleString('en-US', {
            style: 'currency',
            currency: 'PHP',
          })}
        </div>
      </div>
    </button>
  );
};
