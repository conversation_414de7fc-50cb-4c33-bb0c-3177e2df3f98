'use client';

import { useHookstate } from '@hookstate/core';
import * as VisuallyHidden from '@radix-ui/react-visually-hidden';

import { Button } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogTitle } from '@/components/ui/dialog';

export function ErrorDialog({ state }) {
  // use this if you want to control the alert dialog programatically
  const dialogState = useHookstate(state);

  return (
    <Dialog open={dialogState.value} onOpenChange={dialogState.set}>
      <DialogContent className="font-sans sm:max-w-xl">
        <VisuallyHidden.Root>
          <DialogTitle>Error</DialogTitle>
        </VisuallyHidden.Root>

        <div className="flex gap-10 p-8">
          <div>
            <img className="mx-auto" src="/assets/undraw/cancel.png" alt="" />
          </div>

          <div className="">
            <div className="my-4 text-xl font-bold">Error</div>
            <div>The total percentage must equal 100%. Please adjust your values accordingly.</div>
          </div>
        </div>

        <DialogFooter className="mt-4 sm:justify-between">
          <DialogClose asChild>
            <Button type="button" size="lg" className="px-12" variant="outline">
              Cancel
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button type="submit" size="lg" className="px-12">
              Try Again
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
