'use client';

import { format } from 'date-fns';
import { CalendarIcon, ChevronDown, ChevronUp, Edit, Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';
import { cn } from '@/lib/utils';

import {
  useAddFarmPlanTemplateSubItem,
  useRemoveFarmPlanTemplateSubItem,
  useUpdateFarmPlanTemplateSubItem,
} from '../../../hooks/useFarmPlanTemplateSubItems';
import { ITemplateForm } from '../create-form/types/template';
import AddSubItemDialog, { ISubItemFormData } from './add-sub-item-dialog';
import EditSubItemDialog, { IEditSubItemFormData } from './edit-sub-item-dialog';
import ReasonDialog from './reason-dialog';

interface IKitaSubsidizedCostsProps {
  form: UseFormReturn<ITemplateForm>;
  farmPlanTemplateId?: number;
}

export default function KitaSubsidizedCosts({ form, farmPlanTemplateId }: IKitaSubsidizedCostsProps) {
  const state = useHeadAgronomistState();

  // Determine if we're in edit mode (when farmPlanTemplateId is provided)
  const isEditMode = !!farmPlanTemplateId;

  // Collapsible state
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Audit logging hooks
  const { addSubItem, isAdding } = useAddFarmPlanTemplateSubItem(farmPlanTemplateId);
  const { updateSubItem, isUpdating } = useUpdateFarmPlanTemplateSubItem(farmPlanTemplateId);
  const { removeSubItem, isRemoving } = useRemoveFarmPlanTemplateSubItem(farmPlanTemplateId);

  // Dialog states
  const [addDialog, setAddDialog] = useState(false);
  const [editDialog, setEditDialog] = useState<{
    open: boolean;
    data?: any;
    index?: number;
  }>({ open: false });
  const [reasonDialog, setReasonDialog] = useState<{
    open: boolean;
    type: 'remove';
    data?: any;
    index?: number;
  }>({ open: false, type: 'remove' });

  // Get form methods from props
  const { control, watch, setValue } = form;

  // Field array for managing multiple items
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'items.9.subItems', // Target the first item (Seed Requirements) in the items array
  });

  // Watch for changes to calculate totals
  const watchKitaSubsidizedCost = watch('items.9.subItems');

  // Get farmPlanTemplateItemId for audit logging
  const farmPlanTemplateItemId = watch('items.9.farmPlanTemplateItemId');

  // Calculate total amounts when quantity or unit cost changes
  useEffect(() => {
    if (!watchKitaSubsidizedCost) return;

    watchKitaSubsidizedCost.forEach((item: any, index: number) => {
      const quantity = Number(item.quantity) || 0;
      const unitCost = Number(item.unitCost) || 0;
      const totalAmount = quantity * unitCost;

      if (totalAmount !== item.totalAmount) {
        setValue(`items.9.subItems.${index}.totalAmount`, totalAmount);
      }
    });
  }, [watchKitaSubsidizedCost, setValue]);

  // Calculate subtotal
  const subtotal =
    watchKitaSubsidizedCost?.reduce((sum: number, item: any) => sum + (Number(item.totalAmount) || 0), 0) || 0;

  // Add a new row
  const addRow = () => {
    if (farmPlanTemplateId && farmPlanTemplateItemId) {
      // Use audit logging for edit mode
      setAddDialog(true);
    } else {
      // Fallback for create mode
      append({
        farmPlanTemplateSubItemId: 0,
        expectedDate: new Date(),
        itemName: '',
        unit: '',
        quantity: 1,
        unitCost: 0,
        notes: '',
        marketplaceProductId: undefined,
        totalAmount: 0, // For UI calculation only
      });
    }
  };

  // Handle remove with audit logging
  const handleRemove = (index: number) => {
    const subItem = watchKitaSubsidizedCost?.[index];
    if (farmPlanTemplateId && subItem?.farmPlanTemplateSubItemId && subItem.farmPlanTemplateSubItemId > 0) {
      // Show reason dialog for audit logging
      setReasonDialog({
        open: true,
        type: 'remove',
        data: subItem,
        index,
      });
    } else {
      // Direct removal for create mode or new items
      remove(index);
    }
  };

  // Handle add dialog confirmation
  const handleAddConfirm = async (data: ISubItemFormData) => {
    if (!farmPlanTemplateItemId) return;

    await addSubItem({
      farmPlanTemplateItemId,
      expectedDate: data.expectedDate,
      itemName: data.itemName,
      unit: data.unit,
      quantity: data.quantity,
      unitCost: data.unitCost,
      notes: data.notes || '',
      marketplaceProductId: data.marketplaceProductId,
      reason: data.reason,
    });
  };

  // Handle edit button click
  const handleEdit = (index: number) => {
    const subItem = watchKitaSubsidizedCost?.[index];
    if (farmPlanTemplateId && subItem?.farmPlanTemplateSubItemId && subItem.farmPlanTemplateSubItemId > 0) {
      // Show edit dialog for audit logging
      setEditDialog({
        open: true,
        data: subItem,
        index,
      });
    }
  };

  // Handle edit dialog confirmation
  const handleEditConfirm = async (data: IEditSubItemFormData) => {
    if (!data.farmPlanTemplateSubItemId) return;

    await updateSubItem({
      farmPlanTemplateSubItemId: data.farmPlanTemplateSubItemId,
      expectedDate: data.expectedDate,
      itemName: data.itemName,
      unit: data.unit,
      quantity: data.quantity,
      unitCost: data.unitCost,
      notes: data.notes || '',
      marketplaceProductId: data.marketplaceProductId,
      reason: data.reason,
    });
  };

  // Handle reason dialog confirmation
  const handleReasonConfirm = async (reason: string) => {
    if (!reasonDialog.data?.farmPlanTemplateSubItemId) return;

    await removeSubItem({
      farmPlanTemplateSubItemId: reasonDialog.data.farmPlanTemplateSubItemId,
      reason,
    });
  };

  useEffect(() => {
    state.section3.group.kitaSubsidizedCost.set(subtotal);
  }, [subtotal]);

  return (
    <>
      <AddSubItemDialog
        open={addDialog}
        onOpenChange={setAddDialog}
        title="Add KITA Subsidized Cost"
        description="Fill out the form below to add a new KITA subsidized cost requirement."
        onConfirm={handleAddConfirm}
        confirmText="Add"
        isLoading={isAdding}
        productType={null}
      />
      <EditSubItemDialog
        open={editDialog.open}
        onOpenChange={(open) => setEditDialog((prev) => ({ ...prev, open }))}
        title="Edit KITA Subsidized Cost"
        description="Update the KITA subsidized cost details below."
        onConfirm={handleEditConfirm}
        confirmText="Update"
        isLoading={isUpdating}
        productType={null}
        initialData={
          editDialog.data
            ? {
                farmPlanTemplateSubItemId: editDialog.data.farmPlanTemplateSubItemId,
                expectedDate: editDialog.data.expectedDate,
                itemName: editDialog.data.itemName,
                unit: editDialog.data.unit,
                quantity: editDialog.data.quantity,
                unitCost: editDialog.data.unitCost,
                notes: editDialog.data.notes,
                marketplaceProductId: editDialog.data.marketplaceProductId,
                reason: '', // Always start with empty reason for editing
              }
            : undefined
        }
      />
      <ReasonDialog
        open={reasonDialog.open}
        onOpenChange={(open) => setReasonDialog((prev) => ({ ...prev, open }))}
        title="Remove KITA Subsidized Cost"
        description="Please provide a reason for removing this KITA subsidized cost requirement."
        onConfirm={handleReasonConfirm}
        confirmText="Remove"
        isLoading={isRemoving}
      />
      <div className="mt-6">
        <div className="rounded-md border border-[#2E3B7C]">
          <div
            className="flex cursor-pointer items-center justify-between bg-[#2E3B7C] px-3 py-2 text-white md:px-4"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            <h3 className="text-sm font-semibold md:text-base">KITA Subsidized Costs</h3>
            <div className="flex items-center gap-2">
              <span
                className={cn('rounded-md min-w-max bg-orange-500 px-2 py-1 text-xs font-medium', {
                  hidden: fields.length === 0,
                })}
              >
                {fields.length} items
              </span>
              {isCollapsed ? <ChevronDown className="size-4 md:size-5" /> : <ChevronUp className="size-4 md:size-5" />}
            </div>
          </div>

          {!isCollapsed && (
            <>
              <div className="pt-3">
                <HorizontalScrollBar>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[150px]">Expected Date</TableHead>
                        <TableHead className="w-[200px]">Activity/Items</TableHead>
                        <TableHead className="w-[100px]">Unit</TableHead>
                        <TableHead className="w-[100px]">Quantity</TableHead>
                        <TableHead className="w-[120px]">Unit Cost</TableHead>
                        <TableHead className="w-[120px]">Total Amount</TableHead>
                        <TableHead className="w-[200px]">Notes</TableHead>
                        <TableHead className="w-[80px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {fields.map((field, index) => (
                        <TableRow key={field.id}>
                          {/* Expected Date */}
                          <TableCell className="p-2 md:p-4">
                            <Controller
                              control={control}
                              name={`items.9.subItems.${index}.expectedDate`}
                              render={({ field }) => (
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="outline"
                                      disabled={isEditMode}
                                      className={cn(
                                        'w-full justify-start text-left font-normal text-xs md:text-sm',
                                        !field.value && 'text-muted-foreground',
                                      )}
                                    >
                                      <CalendarIcon className="mr-1 size-3 md:mr-2 md:size-4" />
                                      {field.value ? (
                                        format(new Date(field.value as string | Date), 'MMM d, yyyy')
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0">
                                    <Calendar
                                      mode="single"
                                      selected={field.value ? new Date(field.value as string | Date) : undefined}
                                      onSelect={field.onChange}
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                              )}
                            />
                          </TableCell>

                          {/* Items */}
                          <TableCell className="p-2 md:p-4">
                            <Controller
                              control={control}
                              name={`items.9.subItems.${index}.itemName`}
                              rules={{
                                required: 'Item is required',
                                validate: {
                                  hasSelected: (v) => v.length > 0 || 'Item is required',
                                },
                              }}
                              render={({ field }) => (
                                <Input
                                  className={cn('text-xs md:text-sm', {
                                    'input-error':
                                      form.formState.errors.items &&
                                      form.formState.errors.items[9]?.subItems[index]?.itemName,
                                  })}
                                  type="text"
                                  value={field.value}
                                  onChange={field.onChange}
                                  disabled={isEditMode}
                                />
                              )}
                            />
                            {form.formState.errors.items &&
                              form.formState.errors?.items[9]?.subItems[index]?.itemName && (
                                <p className="form-error text-xs md:text-sm">
                                  {form.formState.errors.items[9]?.subItems[index]?.itemName.message}
                                </p>
                              )}
                          </TableCell>

                          {/* Unit */}
                          <TableCell className="p-2 md:p-4">
                            <Controller
                              control={control}
                              name={`items.9.subItems.${index}.unit`}
                              rules={{
                                required: `Unit is required`,
                              }}
                              render={({ field }) => (
                                <Input
                                  className={cn('text-xs md:text-sm', {
                                    'input-error':
                                      form.formState.errors.items &&
                                      form.formState.errors.items[9]?.subItems[index]?.unit,
                                  })}
                                  value={field.value}
                                  onChange={field.onChange}
                                  disabled={isEditMode}
                                />
                              )}
                            />
                            {form.formState.errors.items && form.formState.errors.items[9]?.subItems[index]?.unit && (
                              <p className="form-error text-xs md:text-sm">
                                {form.formState.errors.items[9]?.subItems[index]?.unit.message}
                              </p>
                            )}
                          </TableCell>

                          {/* Quantity */}
                          <TableCell className="p-2 md:p-4">
                            <Controller
                              control={control}
                              name={`items.9.subItems.${index}.quantity`}
                              rules={{
                                required: `Quantity is required`,
                                validate: {
                                  isNumber: (v) => !isNaN(Number(v)) || 'Quantity must be a number',
                                  isPositive: (v) => Number(v) >= 0 || 'Quantity must be a positive number',
                                },
                              }}
                              render={({ field }) => (
                                <Input
                                  className={cn('text-xs md:text-sm', {
                                    'input-error':
                                      form.formState.errors.items &&
                                      form.formState.errors.items[9]?.subItems[index]?.quantity,
                                  })}
                                  value={field.value as number}
                                  onChange={(e) => {
                                    const value = e.target.value === '' ? 1 : Number(e.target.value);

                                    field.onChange(value);
                                    form.setValue(
                                      `items.9.subItems.${index}.totalAmount`,
                                      value * Number(form.getValues(`items.9.subItems.${index}.unitCost`)),
                                    );
                                  }}
                                  type="number"
                                  placeholder="0"
                                  min={1}
                                  disabled={isEditMode}
                                />
                              )}
                            />
                            {form.formState.errors.items &&
                              form.formState.errors.items[9]?.subItems[index]?.quantity && (
                                <p className="form-error text-xs md:text-sm">
                                  {form.formState.errors.items[9]?.subItems[index]?.quantity.message}
                                </p>
                              )}
                          </TableCell>

                          {/* Unit Cost */}
                          <TableCell className="p-2 md:p-4">
                            <Controller
                              control={control}
                              name={`items.9.subItems.${index}.unitCost`}
                              rules={{
                                required: `Unit cost is required`,
                                validate: {
                                  isNumber: (v) => !isNaN(Number(v)) || 'Unit cost must be a number',
                                  isPositive: (v) => Number(v) >= 0 || 'Unit cost must be a positive number',
                                },
                              }}
                              render={({ field }) => (
                                <Input
                                  className={cn('text-xs md:text-sm', {
                                    'input-error':
                                      form.formState.errors.items &&
                                      form.formState.errors.items[9]?.subItems[index]?.unitCost,
                                  })}
                                  value={Number(field.value)}
                                  onChange={(e) => {
                                    const value = e.target.value === '' ? 0 : Number(e.target.value);
                                    if (isNaN(value)) return;

                                    form.setValue(
                                      `items.9.subItems.${index}.totalAmount`,
                                      value * Number(form.getValues(`items.9.subItems.${index}.quantity`)),
                                    );
                                    field.onChange(value);
                                  }}
                                  type="text"
                                  placeholder="0"
                                  disabled={isEditMode}
                                />
                              )}
                            />
                            {form.formState.errors.items &&
                              form.formState.errors.items[9]?.subItems[index]?.unitCost && (
                                <p className="form-error text-xs md:text-sm">
                                  {form.formState.errors.items[9]?.subItems[index]?.unitCost.message}
                                </p>
                              )}
                          </TableCell>

                          {/* Total Amount */}
                          <TableCell className="p-2 md:p-4">
                            <Input
                              value={watchKitaSubsidizedCost?.[index]?.totalAmount?.toLocaleString() || '0'}
                              disabled
                              className="w-full bg-gray-50 text-xs md:text-sm"
                            />
                          </TableCell>

                          {/* Notes */}
                          <TableCell className="p-2 md:p-4">
                            <Controller
                              control={control}
                              name={`items.9.subItems.${index}.notes`}
                              render={({ field }) => (
                                <Input
                                  value={field.value as string}
                                  onChange={field.onChange}
                                  placeholder="Note"
                                  className="w-full text-xs md:text-sm"
                                  disabled={isEditMode}
                                />
                              )}
                            />
                          </TableCell>

                          {/* Actions */}
                          <TableCell className="p-2 md:p-4">
                            <div className="flex items-center gap-1">
                              {isEditMode && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEdit(index)}
                                  className="text-blue-500"
                                >
                                  <Edit className="size-3 md:size-4" />
                                </Button>
                              )}
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemove(index)}
                                className="text-red-500"
                              >
                                <Trash2 className="size-3 md:size-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </HorizontalScrollBar>
              </div>

              <div className="flex flex-col space-y-3 p-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:p-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-semibold md:text-base">Sub-Total</span>
                  <span className="text-sm font-semibold text-blue-700 md:text-base">
                    PHP {subtotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={addRow}
                  className="flex items-center gap-1 text-xs text-blue-600 md:text-sm"
                >
                  <Plus className="size-3 md:size-4" />
                  Add Item
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}
