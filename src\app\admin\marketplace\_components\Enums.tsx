'use client';

export enum UserType {
  SUPERADMIN = 999,
  ADMIN = 998,
  PRODUCT_MANAGER = 997,
  WALLET_MANAGER = 996,
  INVOICE_MANAGER = 995,
  // finance
  FINANCE_1 = 499,
  FINANCE_2 = 498,
  // marketplace
  SALE_1 = 399,
  SALE_2 = 398,
  OPERATION_1 = 299,
  OPERATION_2 = 298,
  // tradingpost
  ENCODER_1 = 199,
  ENCODER_2 = 198,
  // end-user
  TRADING_APP_STAFF = 99,
  HEAD_AGRONOMIST = 59,
  AGRONOMIST = 50,
  NONFARMER = 3,
  DEMAND = 2,
  FARMER = 1,
  // field relation officer
  FIELD_RELATION_OFFICER = 899,
}

export enum UserStatusType {
  PENDING_FROM_ADMIN = 0,
  ACTIVATED = 1,
  BLOCKED = 2,
  PENDING_FROM_ENCODER = 3,
  IMPORT_USERS = 4,
}
export const UserStatusLabels = {
  [UserStatusType.PENDING_FROM_ADMIN]: {
    label: 'Pending from Admin',
    color: 'bg-orange-500 hover:bg-orange-500/90',
  },
  [UserStatusType.ACTIVATED]: {
    label: 'Activated',
    color: 'bg-green-500 hover:bg-green-500/90',
  },
  [UserStatusType.BLOCKED]: {
    label: 'Rejected',
    color: 'bg-red-500 hover:bg-red-500/90',
  },
  [UserStatusType.PENDING_FROM_ENCODER]: {
    label: 'Pending from Encoder',
    color: 'bg-blue-500 hover:bg-blue-500/90',
  },
  [UserStatusType.IMPORT_USERS]: {
    label: '',
    color: '',
  },
};

export enum PaymentMethodType {
  CASH = 1,
  EWALLET = 2,
  MULTIPLE = 3,
}
export const PaymentMethodLabels = ['', 'Cash', 'Ewallet', 'Cash & Ewallet'];

export enum PaymentStatusType {
  PENDING = 0,
  PAID = 1,
  FAILED = 2,
}

export enum FulfillmentType {
  PICKUP = 1,
  DELIVER = 2,
}
export const FulfillmentTypeLabels = ['', 'Pickup', 'Delivery'];

export enum OrderStatusType {
  PENDING = 0,
  PREPARING = 1,
  ORDER_READY = 2,
  COMPLETED = 3,
  CANCELLED = 999,
}
export const OrderStatusLabels = {
  [OrderStatusType.PENDING]: {
    label: 'Pending',
    color: 'bg-orange-500 hover:bg-orange-500/90',
  },
  [OrderStatusType.PREPARING]: {
    label: 'Preparing',
    color: 'bg-purple-500 hover:bg-purple-500/90',
  },
  [OrderStatusType.ORDER_READY]: {
    label: 'Order Ready',
    color: 'bg-blue-500 hover:bg-blue-500/90',
  },
  [OrderStatusType.COMPLETED]: {
    label: 'Completed',
    color: 'bg-green-500 hover:bg-green-500/90',
  },
  [OrderStatusType.CANCELLED]: {
    label: 'Cancelled',
    color: 'bg-red-500 hover:bg-red-500/90',
  },
};

export enum PRODUCT_TYPE {
  CROPS = 0,
  SEEDS = 1,
  FERTILIZERS = 2,
  CHEMICALS = 3,
  OTHERS = 4,
}
export const PRODUCT_TYPE_LABELS = ['Crops', 'Seeds', 'Fertilizers', 'Crop Protection', 'Others'];
export const PRODUCT_TYPE_ACCESSOR = ['crop', 'seed', 'fertilizer', 'chemical', 'otherProduct'];

export enum ProductStatus {
  INACTIVE = 0,
  ACTIVE = 1,
}

export enum VatableCode {
  NONVATABLE = 'NV',
  VATABLE = 'V',
}

export enum UNIT {
  LITER = 'Liter',
  MILLILITER = 'Milliliter',
  GRAMS = 'Grams',
  KILO = 'Kilo',
  PACK = 'Pack',
  BOX = 'Box',
  SACK = 'Sack',
}

export enum RequestStatus {
  PENDING = 0,
  APPROVE = 1,
  REJECTED = 2,
}
export const RequestStatusLabels = {
  [RequestStatus.PENDING]: {
    label: 'For Approval',
    color: 'bg-orange-500 hover:bg-orange-500/90',
  },
  [RequestStatus.APPROVE]: {
    label: 'Approved',
    color: 'bg-green-500 hover:bg-green-500/90',
  },
  [RequestStatus.REJECTED]: {
    label: 'Rejected',
    color: 'bg-red-500 hover:bg-red-500/90',
  },
};

export enum TransactionType {
  E_WALLET_TOP_UP = 1,
  LOAN_PAYMENT = 2,
}
export const TransactionTypeLabels = {
  [TransactionType.E_WALLET_TOP_UP]: {
    label: 'Loan Availed',
    color: 'bg-blue-500 hover:bg-blue-500/90',
  },
  [TransactionType.LOAN_PAYMENT]: {
    label: 'Loan Payment',
    color: 'bg-green-500 hover:bg-green-500/90',
  },
};

export enum PaymentStatus {
  UNPAID = 0,
  PAID = 1,
  PARTIALLY_PAID = 2,
  REJECTED = 3,
}
export const PaymentStatusLabels = {
  [PaymentStatus.UNPAID]: {
    label: 'Unpaid',
    color: 'bg-orange-500 hover:bg-orange-500/90',
  },
  [PaymentStatus.PAID]: {
    label: 'Paid',
    color: 'bg-green-500 hover:bg-green-500/90',
  },
  [PaymentStatus.PARTIALLY_PAID]: {
    label: 'Partially Paid',
    color: 'bg-blue-500 hover:bg-blue-500/90',
  },
  [PaymentStatus.REJECTED]: {
    label: 'Rejected',
    color: 'bg-red-500 hover:bg-red-500/90',
  },
};

export enum LoanRequestStatus {
  PENDING = 0,
  PAID = 1,
  PARTIALLY_PAID = 2,
  REJECTED = 3,
}
export const LoanRequestStatusLabels = {
  [LoanRequestStatus.PENDING]: {
    label: 'For Approval',
    color: 'bg-orange-500 hover:bg-orange-500/90',
  },
  [LoanRequestStatus.PAID]: {
    label: 'Paid',
    color: 'bg-green-500 hover:bg-green-500/90',
  },
  [LoanRequestStatus.PARTIALLY_PAID]: {
    label: 'Partially Paid',
    color: 'bg-blue-500 hover:bg-blue-500/90',
  },
  [LoanRequestStatus.REJECTED]: {
    label: 'Rejected',
    color: 'bg-red-500 hover:bg-red-500/90',
  },
};

export enum LoanModeOfPayment {
  CASH = 'CASH',
  OTC_BANKING = 'OTC BANKING',
}
