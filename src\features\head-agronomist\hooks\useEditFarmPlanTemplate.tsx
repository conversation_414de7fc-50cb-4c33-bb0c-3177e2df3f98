'use client';

import { useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';

import { IFarmPlanTemplate } from '../types/farmplan-templates';

// Fetch template by ID
export const fetchFarmPlanTemplateById = async (id: number) => {
  const { data } = await axios.get(`/agronomist/farmplan/template/view/${id}`);
  return data.data as IFarmPlanTemplate;
};

// Hook for fetching template by ID
export const useEditFarmPlanTemplate = (id: number) => {
  const farmPlanTemplateByIdQuery = useQuery({
    queryKey: ['farmPlanTemplateById', id],
    queryFn: () => fetchFarmPlanTemplateById(id),
    enabled: id > 0,
  });

  return { farmPlanTemplateByIdQuery };
};
