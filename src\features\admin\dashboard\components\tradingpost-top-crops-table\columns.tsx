'use client';

import { ColumnDef } from '@tanstack/react-table';

import { formatNumberWithCommas, toCurrency } from '@/lib/utils';

import { ITradingPostTopCrops } from '../../types';

export const columnsTradingPostTopCrops: ColumnDef<ITradingPostTopCrops>[] = [
  {
    accessorKey: 'crop_id',
    header: ({ column }) => <div>#</div>,
  },
  {
    accessorKey: 'crop_name',
    header: ({ column }) => <div className="min-w-max">Crop Name</div>,
    cell: ({ row }) => {
      return <div className="w-max">{row.original.crop_name}</div>;
    },
  },
  {
    accessorKey: 'crop_volume',
    header: ({ column }) => <div className="min-w-max">Volume</div>,
    cell: ({ row }) => {
      return <div className="">{`${formatNumberWithCommas(row.getValue('crop_volume'))} kg`}</div>;
    },
  },
  {
    accessorKey: 'crop_price',
    header: ({ column }) => <div className="min-w-max text-right">Latest Price</div>,
    cell: ({ row }) => {
      return <div className="text-right">{`${toCurrency(row.getValue('crop_price'))}/kg`}</div>;
    },
  },
  {
    accessorKey: 'crop_sales',
    header: ({ column }) => <div className="min-w-max text-right">Sales</div>,
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('crop_sales'))}</div>;
    },
  },
];
