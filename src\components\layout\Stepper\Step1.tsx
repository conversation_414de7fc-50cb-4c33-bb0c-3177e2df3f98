'use client';

import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { STEPPER_FORM } from '@/lib/constants';
import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';
import { useStateContext } from '@/providers/app/state';

import {
  EDUCATIONAL_ATTAINMENT,
  IS_GRADUATE,
  OCCUPATION,
  OCCUPATION_STATUS,
  OPTIONS_CONSTRUCTION,
  OPTIONS_CRAFT,
  OPTIONS_FARMING,
  OPTIONS_FISHING,
  OPTIONS_LIVESTOCK,
  OPTIONS_PROCESSING,
  OPTIONS_SERVICING,
} from './Enums';

export default function Step1() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { updateFarmer } = useFarmer();
  const { setState, state } = useStateContext();
  const data = gStateP.admin.members['details'].get({ noproxy: true });

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      educationalAttainment: data.farmer.educational_attainment || '',
      educationalIsGraduate: `${data.farmer.educational_is_graduate}` || IS_GRADUATE.NO,
      educationalDegree: data.farmer.educational_degree || '',
      occupation: data.farmer.occupation || '',
      occupationStatus: data.farmer.occupation_status || '',
      occupationEmployerName: data.farmer.occupation_employer_name || '',
      occupationEmployerAddress: data.farmer.occupation_employer_address || '',
      occupationAnnualIncome: data.farmer.occupation_annual_income || '',
      occupationBusinessName: data.farmer.occupation_business_name || '',
      occupationBusinessAddress: data.farmer.occupation_business_address || '',
      occupationBusinessContact: data.farmer.occupation_business_contact || '',
      skillsFarming: data?.farmer?.skills_farming?.split(',').map((s) => ({ label: s, value: s })) || [],
      skillsFishing: data?.farmer?.skills_fishing == '1' ? ['FISHING'].map((s) => ({ label: s, value: s })) : [],
      skillsLivestock: data?.farmer?.skills_livestock?.split(',').map((s) => ({ label: s, value: s })) || [],
      skillsConstruction: data?.farmer?.skills_construction?.split(',').map((s) => ({ label: s, value: s })) || [],
      skillsProcessing: data?.farmer?.skills_processing?.split(',').map((s) => ({ label: s, value: s })) || [],
      skillsServicing: data?.farmer?.skills_servicing?.split(',').map((s) => ({ label: s, value: s })) || [],
      skillsCraft: data?.farmer?.skills_craft?.split(',').map((s) => ({ label: s, value: s })) || [],
      skillsOthers: data?.farmer?.skills_others?.split(',').map((s) => ({ label: s, value: s })) || [],
    },
  });
  const watchEduAttainment = watch('educationalAttainment');
  const watchEduIsGrad = watch('educationalIsGraduate');

  const onSubmit = (_data: any) => {
    const skills = ['Farming', 'Livestock', 'Construction', 'Processing', 'Servicing', 'Craft', 'Others'];
    const updatedData = { ..._data, userId: data.farmer.user_id };

    const fishing = _data['skillsFishing'].find((s) => s.value === 'FISHING');
    updatedData['skillsFishing'] = fishing ? '1' : '0';

    skills.forEach((skill) => {
      updatedData[`skills${skill}`] = _data[`skills${skill}`].map((s) => s.value).join(',');
    });

    console.log('Career Details: ', updatedData);
    updateFarmer(updatedData);

    // setState((v) => ({
    //   ...v,
    //   ..._data,
    //   userId: data.id,
    // }));
    // !gState.stepper.isLastStep.value && gState.stepper.activeStep.set((cur) => cur + 1);
  };

  return (
    <form id={STEPPER_FORM[1]} onSubmit={handleSubmit(onSubmit)}>
      <div className="font-dmSans text-xl font-bold text-primary">Educational Background</div>
      <div className="mt-6 grid grid-cols-3 gap-4 space-y-4">
        {/* Educational Attainment */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="educationalAttainment" className="pb-1 font-normal">
            Educational Attainment
          </Label>
          <Controller
            control={control}
            name="educationalAttainment"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.educationalAttainment &&
                      'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Educational Attainment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {Object.values(EDUCATIONAL_ATTAINMENT).map((educ) => (
                      <SelectItem key={educ} value={educ}>
                        {educ}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.educationalAttainment && <p className="form-error">{`${errors.educationalAttainment.message}`}</p>}
        </div>

        {/* Educational Is Graduate */}
        {[
          EDUCATIONAL_ATTAINMENT.VOCATIONAL,
          EDUCATIONAL_ATTAINMENT.COLLEGE,
          EDUCATIONAL_ATTAINMENT.POST_GRADUATE,
        ].includes(watchEduAttainment) && (
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="educationalIsGraduate" className="pb-1 font-normal">
              Graduate?
            </Label>
            <Controller
              control={control}
              name="educationalIsGraduate"
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <Select onValueChange={onChange} value={value}>
                  <SelectTrigger
                    className={cn(
                      'focus-visible:ring-primary',
                      errors.educationalIsGraduate &&
                        'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                    )}
                  >
                    <SelectValue placeholder="Did you Graduate?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value={IS_GRADUATE.YES}>YES</SelectItem>
                      <SelectItem value={IS_GRADUATE.NO}>NO</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            />
            {errors.educationalIsGraduate && <p className="form-error">{`${errors.educationalIsGraduate.message}`}</p>}
          </div>
        )}

        {/* Educational Is Graduate */}
        {[
          EDUCATIONAL_ATTAINMENT.VOCATIONAL,
          EDUCATIONAL_ATTAINMENT.COLLEGE,
          EDUCATIONAL_ATTAINMENT.POST_GRADUATE,
        ].includes(watchEduAttainment) &&
          watchEduIsGrad === IS_GRADUATE.YES && (
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="educationalDegree" className="pb-1 font-normal">
                Educational Degree
              </Label>
              <Input
                {...register('educationalDegree')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.educationalDegree && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Educational Degree"
              />
              {errors.educationalDegree && <p className="form-error">{`${errors.educationalDegree.message}`}</p>}
            </div>
          )}
      </div>

      <div className="font-dmSans mt-6 text-xl font-bold text-primary">Occupational Background</div>
      <div className="mt-6 grid grid-cols-3 gap-4 space-y-4">
        {/* Occupation */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="occupation" className="pb-1 font-normal">
            Occupation
          </Label>
          <Controller
            control={control}
            name="occupation"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.occupation && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Occupation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {Object.values(OCCUPATION).map((occu) => (
                      <SelectItem key={occu} value={occu}>
                        {occu}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.occupation && <p className="form-error">{`${errors.occupation.message}`}</p>}
        </div>

        {/* Employment */}
        {[
          OCCUPATION.GOVERNMENT_EMPLOYEE,
          OCCUPATION.PRIVATE_EMPLOYEE,
          OCCUPATION.CHURCH_SERVANTS_WORKERS,
          OCCUPATION.OVERSEAS_FILIPINO_WORKER_OFW,
          OCCUPATION.FARMER_FISHERFOLK,
          OCCUPATION.LABORER,
        ].includes(watch('occupation')) && (
          <>
            {/* Occupation Status */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationStatus" className="pb-1 font-normal">
                Occupation Status
              </Label>
              <Controller
                control={control}
                name="occupationStatus"
                render={({ field: { onChange, onBlur, value, ref } }) => (
                  <Select onValueChange={onChange} value={value}>
                    <SelectTrigger
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.occupationStatus && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                      )}
                    >
                      <SelectValue placeholder="Select Occupation Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {/* <SelectItem value={OCCUPATION_STATUS.EMPLOYED}>
                          {OCCUPATION_STATUS.EMPLOYED}
                        </SelectItem>
                        <SelectItem value={OCCUPATION_STATUS.UNEMPLOYED}>
                          {OCCUPATION_STATUS.UNEMPLOYED}
                        </SelectItem>
                        <SelectItem value={OCCUPATION_STATUS.SELF_EMPLOYED}>
                          {OCCUPATION_STATUS.SELF_EMPLOYED}
                        </SelectItem>
                        <SelectItem value={OCCUPATION_STATUS.STUDENT}>{OCCUPATION_STATUS.STUDENT}</SelectItem>
                        <SelectItem value={OCCUPATION_STATUS.RETIRED}>{OCCUPATION_STATUS.RETIRED}</SelectItem>
                        <SelectItem value={OCCUPATION_STATUS.HOMEMAKER}>
                          {OCCUPATION_STATUS.HOMEMAKER}
                        </SelectItem> */}
                        <SelectItem value="TEMPORARY">TEMPORARY</SelectItem>
                        <SelectItem value="PERMANENT">PERMANENT</SelectItem>
                        <SelectItem value="CONTRACTUAL">CONTRACTUAL</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.occupationStatus && <p className="form-error">{`${errors.occupationStatus.message}`}</p>}
            </div>

            {/* Employer Name */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationEmployerName" className="pb-1 font-normal">
                Employer Name
              </Label>
              <Input
                {...register('occupationEmployerName')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationEmployerName && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Employer Name"
              />
              {errors.occupationEmployerName && (
                <p className="form-error">{`${errors.occupationEmployerName.message}`}</p>
              )}
            </div>

            {/* Employer Address */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationEmployerAddress" className="pb-1 font-normal">
                Employer Address
              </Label>
              <Input
                {...register('occupationEmployerAddress')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationEmployerAddress && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Employer Address"
              />
              {errors.occupationEmployerAddress && (
                <p className="form-error">{`${errors.occupationEmployerAddress.message}`}</p>
              )}
            </div>

            {/* Annual Income */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationAnnualIncome" className="pb-1 font-normal">
                Annual Income
              </Label>
              <Input
                {...register('occupationAnnualIncome', {
                  required: false,
                  validate: {
                    isGreaterThanZero: (v) => (v ? /^\d+(?:\.\d{0,2})?$/.test(v) || 'Invalid Amount' : true),
                  },
                })}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationAnnualIncome && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Annual Income"
              />
              {errors.occupationAnnualIncome && (
                <p className="form-error">{`${errors.occupationAnnualIncome.message}`}</p>
              )}
            </div>
          </>
        )}

        {/* Business */}
        {[
          OCCUPATION.SELF_EMPLOYED_PRACTICING_PROFESSIONAL,
          OCCUPATION.SELF_EMPLOYED_NON_PROFESSIONAL,
          OCCUPATION.BUSINESS_PERSON_ENTREPRENEUR,
        ].includes(watch('occupation')) && (
          <>
            {/* Business Name */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationBusinessName" className="pb-1 font-normal">
                Business Name
              </Label>
              <Input
                {...register('occupationBusinessName')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationBusinessName && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Business Name"
              />
              {errors.occupationBusinessName && (
                <p className="form-error">{`${errors.occupationBusinessName.message}`}</p>
              )}
            </div>

            {/* Business Address */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationBusinessAddress" className="pb-1 font-normal">
                Business Address
              </Label>
              <Input
                {...register('occupationBusinessAddress')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationBusinessAddress && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Business Address"
              />
              {errors.occupationBusinessAddress && (
                <p className="form-error">{`${errors.occupationBusinessAddress.message}`}</p>
              )}
            </div>

            {/* Business Contact */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationBusinessContact" className="pb-1 font-normal">
                Business Contact
              </Label>
              <Input
                {...register('occupationBusinessContact', {
                  required: false,
                  validate: {
                    isValidMobileNumber: (v) =>
                      v
                        ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                          'Invalid mobile number format (e.g. ***********)'
                        : true,
                  },
                })}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationBusinessContact && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Mobile No."
              />
              {errors.occupationBusinessContact && (
                <p className="form-error">{`${errors.occupationBusinessContact.message}`}</p>
              )}
            </div>

            {/* Annual Income */}
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="occupationAnnualIncome" className="pb-1 font-normal">
                Annual Income
              </Label>
              <Input
                {...register('occupationAnnualIncome', {
                  required: false,
                  validate: {
                    isGreaterThanZero: (v) => (v ? /^\d+(?:\.\d{0,2})?$/.test(v) || 'Invalid Amount' : true),
                  },
                })}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationAnnualIncome && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Annual Income"
              />
              {errors.occupationAnnualIncome && (
                <p className="form-error">{`${errors.occupationAnnualIncome.message}`}</p>
              )}
            </div>
          </>
        )}
      </div>

      <div className="font-dmSans mt-6 text-xl font-bold text-primary">Special Skills</div>
      <div className="mt-6 grid grid-cols-3 items-start gap-4">
        {/* Farming */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsFarming" className="pb-1 font-normal">
            Farming
          </Label>
          <Controller
            control={control}
            name="skillsFarming"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_FARMING}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsFarming && <p className="form-error">{`${errors.skillsFarming.message}`}</p>}
        </div>

        {/* Fishing */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsFishing" className="pb-1 font-normal">
            Fishing
          </Label>
          <Controller
            control={control}
            name="skillsFishing"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_FISHING}
                placeholder="Select from selection"
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsFishing && <p className="form-error">{`${errors.skillsFishing.message}`}</p>}
        </div>

        {/* Livestock */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsLivestock" className="pb-1 font-normal">
            Livestock
          </Label>
          <Controller
            control={control}
            name="skillsLivestock"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_LIVESTOCK}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsLivestock && <p className="form-error">{`${errors.skillsLivestock.message}`}</p>}
        </div>

        {/* Construction */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsConstruction" className="pb-1 font-normal">
            Construction
          </Label>
          <Controller
            control={control}
            name="skillsConstruction"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_CONSTRUCTION}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsConstruction && <p className="form-error">{`${errors.skillsConstruction.message}`}</p>}
        </div>

        {/* Processing */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsProcessing" className="pb-1 font-normal">
            Processing
          </Label>
          <Controller
            control={control}
            name="skillsProcessing"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_PROCESSING}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsProcessing && <p className="form-error">{`${errors.skillsProcessing.message}`}</p>}
        </div>

        {/* Servicing */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsServicing" className="pb-1 font-normal">
            Servicing
          </Label>
          <Controller
            control={control}
            name="skillsServicing"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_SERVICING}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsServicing && <p className="form-error">{`${errors.skillsServicing.message}`}</p>}
        </div>

        {/* Crafting */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsCraft" className="pb-1 font-normal">
            Crafting
          </Label>
          <Controller
            control={control}
            name="skillsCraft"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_CRAFT}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsCraft && <p className="form-error">{`${errors.skillsCraft.message}`}</p>}
        </div>

        {/* Other Skills */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="skillsOthers" className="pb-1 font-normal">
            Other Skills
          </Label>
          <Controller
            control={control}
            name="skillsOthers"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={[]}
                placeholder="Specify here..."
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.skillsOthers && <p className="form-error">{`${errors.skillsOthers.message}`}</p>}
        </div>
      </div>
    </form>
  );
}
