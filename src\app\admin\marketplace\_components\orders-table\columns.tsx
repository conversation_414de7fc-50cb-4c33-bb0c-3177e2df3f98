'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { UploadIcon } from 'lucide-react';
import { CSVLink } from 'react-csv';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import useMarketplace from '@/lib/hooks/useMarketplace';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { OrderStatusType } from '../Enums';

export const status = {
  0: {
    text: 'Pending',
    icon: <div className="mr-1 size-2 rounded-full bg-amber-500" />,
  },
  1: {
    text: 'Preparing',
    icon: <div className="mr-1 size-2 rounded-full bg-purple-500" />,
  },
  2: {
    text: 'Order Ready',
    icon: <div className="mr-1 size-2 rounded-full bg-blue-500" />,
  },
  3: {
    text: 'Completed',
    icon: <div className="mr-1 size-2 rounded-full bg-green-500" />,
  },
  999: {
    text: 'Cancelled',
    icon: <div className="mr-1 size-2 rounded-full bg-red-500" />,
  },
};

export const columns = [
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      return <StatusAction row={row} />;
    },
    accessorFn: (row) => {
      return status[row.order_status].text;
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max capitalize">{`${data.customer.farmer.first_name} ${data.customer.farmer.last_name}`}</div>
      );
    },
    accessorFn: (row) => `${row.customer.farmer.first_name} ${row.customer.farmer.last_name}`,
  },
  {
    id: 'order_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Order ID" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{`${data.reference_number}`}</div>;
    },
    accessorFn: (row) => {
      return `${row.reference_number}`;
    },
  },
  {
    id: 'amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Amount" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${Number(data.total_price).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      return `${row.total_price}`;
    },
  },
  {
    id: 'total_items',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total Items" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.marketplaceProductOrders.map((invoice) => invoice.quantity).reduce((a, b) => a + b, 0)}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.marketplaceProductOrders.map((invoice) => invoice.quantity).reduce((a, b) => a + b, 0)}`;
    },
  },
  {
    id: 'ordered_date_time',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Ordered Date & Time" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.updated_at).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${new Date(row.updated_at).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      })}`;
    },
  },
  {
    id: 'mode_payment',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Mode of Payment" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{['', 'Cash', 'e-Wallet', 'Multiple'][data.payment_method]}</div>;
    },
    accessorFn: (row) => {
      return `${['', 'Cash', 'e-Wallet', 'Multiple'][row.payment_method]}`;
    },
  },
];

export const StatusAction = ({ row }) => {
  const data = row.original;
  const { updateOrderStatus } = useMarketplace();

  const alertConfirm = useHookstate(false);
  const statusUpdate = useHookstate('0');

  return (
    <div id="status-column" className="min-w-max">
      <Select
        onValueChange={(v) => {
          statusUpdate.set(v);
          alertConfirm.set(true);
        }}
        value={`${data.order_status}`}
        disabled={data.order_status === OrderStatusType.CANCELLED}
      >
        <SelectTrigger className={cn('focus-visible:ring-primary justify-between gap-4')}>
          <div className="flex items-center gap-2">
            <div>{status[data.order_status].icon}</div>
            <SelectValue placeholder="Select gender" />
          </div>
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="0">Pending</SelectItem>
            <SelectItem value="1">Preparing</SelectItem>
            <SelectItem value="2">Order Ready</SelectItem>
            <SelectItem value="3">Completed</SelectItem>
            <SelectItem value="999">Cancelled</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>

      <AlertDialog open={alertConfirm.value} onOpenChange={alertConfirm.set}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {`This action cannot be undone. This will update the order status from ${
                status[data.order_status].text
              } to ${status[statusUpdate.value].text}`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                updateOrderStatus({
                  marketplaceOrderId: data.id,
                  status: statusUpdate.value,
                });
              }}
            >
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

const exportHeaders = [
  { label: 'Status', key: 'status' },
  { label: 'Name', key: 'name' },
  { label: 'Order ID', key: 'order_id' },
  { label: 'Amount', key: 'amount' },
  { label: 'Total Items', key: 'total_items' },
  { label: 'Order Date & Time', key: 'ordered_date_time' },
  { label: 'Mode of Payment', key: 'mode_payment' },
];

export function OrdersExport() {
  const gState = useGlobalState();
  const state = useHookstate(gState.admin.marketplace.ordersExport);

  const getData = () => {
    const data = state.data.get({ noproxy: true });
    return data.map((item) => {
      return {
        status: status[item.order_status].text,
        name: `${item.customer.farmer.first_name} ${item.customer.farmer.last_name}`,
        order_id: item.reference_number,
        amount: Number(item.total_price).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        }),
        total_items: item.marketplaceProductOrders.map((invoice) => invoice.quantity).reduce((a, b) => a + b, 0),
        ordered_date_time: format(new Date(item.updated_at), 'MMM dd, yyyy | hh:mm a'),
        mode_payment: ['', 'Cash', 'e-Wallet', 'Multiple'][item.payment_method],
      };
    });
  };

  return (
    <div>
      <Button size="sm" className="h-8" asChild>
        <CSVLink
          data={getData()}
          headers={exportHeaders}
          filename={`orders-report-${format(new Date(), 'yyyy-MM-dd')}.csv`}
          target="_blank"
        >
          <UploadIcon className="mr-2 size-3" />
          Export
        </CSVLink>
      </Button>
    </div>
  );
}
