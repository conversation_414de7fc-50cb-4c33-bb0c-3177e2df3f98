'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ColumnDef } from '@tanstack/react-table';
import { ChevronDown, FileLineChart, ImageIcon, Pencil, Plus, TextCursorInput, UploadCloud } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Controller, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import CropCsv from '@/components/layout/admin/product-management/crops/CropCsv';
import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge, BadgeProps } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import useCrops, { CropSingleSchema, UpdateCropSchema } from '@/lib/hooks/useCrops';
import { useGlobalState } from '@/lib/store';
import { ICropPriceRangeBacklogUpload } from '@/lib/types/crop.types';
import { ICropWithRelations } from '@/lib/types/paginated-crops.types';
import { bytesToSize, cn } from '@/lib/utils';

import NewPriceCSV from '../new-price-csv';

export const columns: ColumnDef<ICropWithRelations>[] = [
  {
    id: 'image',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Image" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          <img
            className="h-14 w-full object-contain"
            src={data.image || '/assets/default-product.png'}
            alt={data.name}
          />
        </div>
      );
    },
  },
  {
    id: 'crops_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Crops Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.name}</div>;
    },
    accessorFn: (row) => row.name,
  },
  {
    id: 'sap_item_code',
    header: ({ column }) => <DataTableColumnHeader column={column} title="SAP Item Code" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.sap_item_code}</div>;
    },
    accessorFn: (row) => row.sap_item_code,
  },
  {
    id: 'price_yesterday',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Price Yesterday" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.cropPrices.length >= 2 ? data.cropPrices[1].selling_price : 0}</div>;
    },
    accessorFn: (row) => (row.cropPrices.length >= 2 ? row.cropPrices[1].selling_price : 0),
  },
  {
    id: 'price_today',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Price Today" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.cropPrices.length > 0 ? data.cropPrices[0].selling_price : 0}</div>;
    },
    accessorFn: (row) => (row.cropPrices.length > 0 ? row.cropPrices[0].selling_price : 0),
  },
  {
    id: 'production_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Production Price" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.cropPrices.length > 0 ? data.cropPrices[0].production_price : 0}</div>;
    },
    accessorFn: (row) => (row.cropPrices.length > 0 ? row.cropPrices[0].production_price : 0),
  },
  {
    id: 'days_harvest',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Days to Harvest" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{`${data.harvest_days ? data.harvest_days : 0} days`}</div>;
    },
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const statusMessage = ['Disabled', 'Active'];
      const variant = ['error', 'success'] as BadgeProps;

      return (
        <div className="min-w-max">
          <Badge variant={variant[data.status]}>{statusMessage[data.status]}</Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'updated_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Updated At" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.cropPrices.length > 0
            ? new Date(data.cropPrices[0].updated_at).toLocaleTimeString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
              })
            : new Date(data.updated_at).toLocaleTimeString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true,
              })}
        </div>
      );
    },
    accessorFn: (row) =>
      row.cropPrices.length > 0
        ? new Date(row.cropPrices[0].updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })
        : new Date(row.updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

const OPTIONS: Option[] = [];

export const ActionHeader = () => {
  const router = useRouter();
  const gState = useGlobalState();
  const { addCropSingle, addCrops, updatePriceRangeBacklog, getCrops } = useCrops();

  const dialogInput = useHookstate(false);
  const dialogUpload = useHookstate(false);
  const dialogUploadPrice = useHookstate(false);
  const dropdown = useHookstate(false);
  const dropdownPrice = useHookstate(false);
  const loading = useHookstate(false);

  const { acceptedFiles, getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
    },
  });
  const [preview, setPreview] = useState(null);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(CropSingleSchema),
    defaultValues: {
      name: '',
      harvestDays: '',
      sapItemCode: '',
      keywords: [] as Option[],
    },
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        keywords: data.keywords.map((item) => item.value).join(', '),
        image: acceptedFiles.length > 0 ? acceptedFiles[0] : null,
        sapItemCode: data.sapItemCode?.trim() || '',
      };

      console.log('onSubmit: ', updatedData);
      await addCropSingle(updatedData);

      dialogInput.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  useEffect(() => {
    if (acceptedFiles.length === 0) {
      setPreview(null);
      return;
    }

    const objectUrl = URL.createObjectURL(acceptedFiles[0]);
    setPreview(objectUrl);

    // free memory when ever this component is unmounted
    return () => URL.revokeObjectURL(objectUrl);
  }, [acceptedFiles]);

  const files = acceptedFiles.map((file: any) => (
    <div key={file.path}>
      <Badge>
        {file.path} - {bytesToSize(Number(file.size))}
      </Badge>
    </div>
  ));

  return (
    <div className="flex items-center justify-end gap-2">
      {/* Add Product Via */}
      <DropdownMenu onOpenChange={dropdownPrice.set}>
        <DropdownMenuTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" size="sm">
            Add Crops via
            <ChevronDown
              className={cn('ml-2 h-4 w-4 transition duration-300 ease-in-out', dropdown.value && 'rotate-180')}
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => dialogInput.set(true)}>
            <TextCursorInput className="mr-2 size-4" />
            Input
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              dialogUpload.set(true);
              // router.push('/admin/product-management/crops/upload-crops')
            }}
          >
            <UploadCloud className="mr-2 size-4" />
            Upload via CSV
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Add New Price Via */}
      <DropdownMenu onOpenChange={dropdownPrice.set}>
        <DropdownMenuTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" size="sm">
            Add New Price via
            <ChevronDown
              className={cn('ml-2 h-4 w-4 transition duration-300 ease-in-out', dropdownPrice.value && 'rotate-180')}
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => {
              router.push('/admin/product-management/crops/add-new-price');
            }}
          >
            <TextCursorInput className="mr-2 size-4" />
            Manual Input
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              getCrops();
              dialogUploadPrice.set(true);
            }}
          >
            <UploadCloud className="mr-2 size-4" />
            Upload via CSV
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Add product via input */}
      <Dialog open={dialogInput.value} onOpenChange={dialogInput.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-lg">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Crops</DialogTitle>
            <DialogDescription>{`Add new crops to your list. You can add multiple crops at a time.`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="">
            <div className="space-y-6 px-6">
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="name" className="pb-1 font-normal">
                  Crop Name
                </Label>
                <Input
                  {...register('name')}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.name && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter crop name"
                />
                {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="sapItemCode" className="pb-1 font-normal">
                  SAP Item Code
                </Label>
                <Input
                  {...register('sapItemCode')}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.sapItemCode && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter SAP Item Code"
                />
                {errors.sapItemCode && <p className="form-error">{`${errors.sapItemCode.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="harvestDays" className="pb-1 font-normal">
                  Days to Harvest
                </Label>
                <Input
                  {...register('harvestDays')}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.harvestDays && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter days to harvest"
                />
                {errors.harvestDays && <p className="form-error">{`${errors.harvestDays.message}`}</p>}
              </div>

              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="keywords" className="pb-1 font-normal">
                  Keywords
                </Label>
                <Controller
                  control={control}
                  name="keywords"
                  rules={{ required: false }}
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <MultipleSelector
                      ref={ref}
                      value={value as Option[]}
                      onChange={onChange}
                      defaultOptions={OPTIONS}
                      placeholder="e.g., corn, mais, maize, etc."
                      creatable
                      emptyIndicator={
                        <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                          e.g., corn, mais, maize, etc.
                        </p>
                      }
                    />
                  )}
                />
                {errors.keywords && <p className="form-error">{`${errors.keywords.message}`}</p>}
              </div>

              <div className="flex flex-col gap-6 md:flex-row">
                {/* Image Dropzone */}
                <div className="md:w-1/2">
                  <div className="border-spacing-4 cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                    <div {...getRootProps({ className: 'dropzone' })}>
                      <input {...getInputProps()} />

                      <ImageIcon className="mx-auto mb-2 size-12 text-gray-500" />
                      <p>{`Drag 'n' drop image here, or`}</p>

                      <div className="py-4 font-bold text-primary">Browse</div>
                    </div>

                    <div>{files}</div>
                  </div>
                </div>

                {/* Preview */}
                <div className="rounded-lg border border-gray-300 md:w-1/2">
                  {!preview ? (
                    <div className="flex h-full items-center justify-center">
                      <div className="">
                        <ImageIcon className="mx-auto mb-2 size-12 text-gray-500" />
                        <div>Image Preview</div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex h-full items-center">
                      <img className="mx-auto h-[185px] object-cover" src={preview} alt="" />
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-between gap-2 p-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              {loading.value ? (
                <ButtonLoading />
              ) : (
                <Button className="px-12" type="submit">
                  Add
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add product via upload */}
      <Dialog open={dialogUpload.value} onOpenChange={dialogUpload.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-lg">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Crops</DialogTitle>
            <DialogDescription>{`Upload CSV file to add crops`}</DialogDescription>
          </DialogHeader>

          <CropCsv />

          <div className="flex justify-between gap-2 p-6">
            <DialogClose asChild>
              <Button className="px-12" variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="px-12" type="button" disabled={gState.admin.cropsUpload.length === 0}>
                  Add
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will add new crops to your list.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    type="button"
                    onClick={async () => {
                      const crops = {
                        crops: gState.admin.cropsUpload.value,
                      };
                      await addCrops(JSON.parse(JSON.stringify(crops)));
                      dialogUpload.set(false);
                    }}
                  >
                    Continue
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add new price via upload */}
      <Dialog open={dialogUploadPrice.value} onOpenChange={dialogUploadPrice.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-4xl">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Price</DialogTitle>
            <DialogDescription>{`Upload CSV file to add new price. None-Existing crops will be created as new crop.`}</DialogDescription>
          </DialogHeader>

          <NewPriceCSV />

          <div className="flex justify-between gap-2 p-6">
            <DialogClose asChild>
              <Button className="px-12" variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="px-12" type="button" disabled={gState.admin.cropsUploadPrice.length === 0}>
                  Add
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will add new price.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    type="button"
                    onClick={async () => {
                      // Get original crops data for reference
                      const cropsData = gState.admin.crops.data.get({ noproxy: true });
                      const uploadPriceData = gState.admin.cropsUploadPrice.get({ noproxy: true });

                      // Apply price correction logic to CSV data
                      const correctedCropsPrices = uploadPriceData.map((crop) => {
                        // Find original crop data
                        const originalCrop = cropsData.find((c) => c.id === crop.cropId);
                        const hasOriginalPrice = originalCrop?.cropPriceRanges?.length > 0;

                        // Get original values
                        const originalLowPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].low_price : 0;
                        const originalHighPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].high_price : 0;
                        const originalLowBaptcPrice = hasOriginalPrice
                          ? originalCrop.cropPriceRanges[0].low_baptc_price
                          : 0;
                        const originalHighBaptcPrice = hasOriginalPrice
                          ? originalCrop.cropPriceRanges[0].high_baptc_price
                          : 0;
                        const originalLowNvatPrice = hasOriginalPrice
                          ? originalCrop.cropPriceRanges[0].low_nvat_price
                          : 0;
                        const originalHighNvatPrice = hasOriginalPrice
                          ? originalCrop.cropPriceRanges[0].high_nvat_price
                          : 0;

                        // Convert string prices to numbers for calculation
                        const lowPriceNum = parseFloat(crop.lowPrice) || 0;
                        const highPriceNum = parseFloat(crop.highPrice) || 0;
                        const lowBaptcPriceNum = parseFloat(crop.lowBaptcPrice) || 0;
                        const highBaptcPriceNum = parseFloat(crop.highBaptcPrice) || 0;
                        const lowNvatPriceNum = parseFloat(crop.lowNvatPrice) || 0;
                        const highNvatPriceNum = parseFloat(crop.highNvatPrice) || 0;

                        // Step 1: Replace 0 in low price with original only when high price equals original high price
                        let correctedLowPrice =
                          lowPriceNum === 0 && highPriceNum === originalHighPrice && highPriceNum !== 0
                            ? originalLowPrice
                            : lowPriceNum;
                        let correctedHighPrice = highPriceNum;
                        let correctedLowBaptcPrice =
                          lowBaptcPriceNum === 0 &&
                          highBaptcPriceNum === originalHighBaptcPrice &&
                          highBaptcPriceNum !== 0
                            ? originalLowBaptcPrice
                            : lowBaptcPriceNum;
                        let correctedHighBaptcPrice = highBaptcPriceNum;
                        let correctedLowNvatPrice =
                          lowNvatPriceNum === 0 && highNvatPriceNum === originalHighNvatPrice && highNvatPriceNum !== 0
                            ? originalLowNvatPrice
                            : lowNvatPriceNum;
                        let correctedHighNvatPrice = highNvatPriceNum;

                        // Step 2: Fix invalid ranges where low > high
                        // Trading Post Price
                        if (correctedLowPrice > correctedHighPrice && correctedHighPrice > 0) {
                          correctedLowPrice = correctedHighPrice;
                          correctedHighPrice = correctedHighPrice; // Both values become the valid bound (high price)
                        }

                        // BAPTC Price
                        if (correctedLowBaptcPrice > correctedHighBaptcPrice && correctedHighBaptcPrice > 0) {
                          correctedLowBaptcPrice = correctedHighBaptcPrice;
                          correctedHighBaptcPrice = correctedHighBaptcPrice; // Both values become the valid bound (high price)
                        }

                        // NVAT Price
                        if (correctedLowNvatPrice > correctedHighNvatPrice && correctedHighNvatPrice > 0) {
                          correctedLowNvatPrice = correctedHighNvatPrice;
                          correctedHighNvatPrice = correctedHighNvatPrice; // Both values become the valid bound (high price)
                        }

                        return {
                          ...crop,
                          lowPrice: correctedLowPrice.toString(),
                          highPrice: correctedHighPrice.toString(),
                          lowBaptcPrice: correctedLowBaptcPrice.toString(),
                          highBaptcPrice: correctedHighBaptcPrice.toString(),
                          lowNvatPrice: correctedLowNvatPrice.toString(),
                          highNvatPrice: correctedHighNvatPrice.toString(),
                        };
                      });

                      const _data = {
                        cropsPrices: correctedCropsPrices,
                      } as ICropPriceRangeBacklogUpload;

                      console.log('CSV upload original: ', uploadPriceData);
                      console.log('CSV upload corrected: ', correctedCropsPrices);

                      await updatePriceRangeBacklog(_data);
                      dialogUploadPrice.set(false);
                    }}
                  >
                    Continue
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Action = ({ row }) => {
  const data = row.original;
  const updateDialog = useHookstate(false);
  const { updateCrop } = useCrops();
  const loading = useHookstate(false);

  const { acceptedFiles, getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
    },
  });
  const [preview, setPreview] = useState(null);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(UpdateCropSchema),
    defaultValues: {
      cropId: `${data.id}`,
      name: data.name,
      harvestDays: data.harvest_days ? `${data.harvest_days}` : '',
      keywords: data.keywords
        ? data.keywords.split(', ').map((item) => ({ label: item, value: item }))
        : ([] as Option[]),
      status: `${data.status}`,
      sapItemCode: data.sap_item_code || '',
    },
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        keywords: data.keywords.map((item) => item.value).join(', '),
        image: acceptedFiles.length > 0 ? acceptedFiles[0] : null,
        sapItemCode: data.sapItemCode?.trim() || '',
      };

      console.log('onSubmit: ', updatedData);
      await updateCrop(updatedData);

      updateDialog.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  useEffect(() => {
    if (!row.original.image) {
      setPreview(null);
      return;
    }

    setPreview(row.original.image);
  }, [row]);

  useEffect(() => {
    if (acceptedFiles.length === 0) {
      setPreview(null);
      return;
    }

    const objectUrl = URL.createObjectURL(acceptedFiles[0]);
    setPreview(objectUrl);

    // free memory when ever this component is unmounted
    return () => URL.revokeObjectURL(objectUrl);
  }, [acceptedFiles]);

  const files = acceptedFiles.map((file: any) => (
    <div key={file.path}>
      <Badge>
        {file.path} - {bytesToSize(Number(file.size))}
      </Badge>
    </div>
  ));

  return (
    <>
      <div className="flex justify-end gap-2">
        <div className="flex justify-end gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button asChild className="h-8 px-2 lg:px-3" variant="outline" size="sm">
                <Link href={`/admin/crop-price-history/?cid=${data.id}&cropName=${data.name}`}>
                  <FileLineChart className="size-4" />
                </Link>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Price History</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="h-8 px-2 lg:px-3"
                variant="outline"
                size="sm"
                onClick={() => {
                  updateDialog.set(true);
                }}
              >
                <Pencil className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit Crop</p>
            </TooltipContent>
          </Tooltip>

          {/* Update Crops Dialog */}
          <Dialog open={updateDialog.value} onOpenChange={updateDialog.set}>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-primary">Update Crop</DialogTitle>
                <DialogDescription>{`Fill the form below to update crop`}</DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="space-y-6">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="cropId" className="pb-1 font-normal">
                      Crop ID
                    </Label>
                    <Input
                      {...register('cropId')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.cropId && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter crop id"
                      disabled
                    />
                    {errors.cropId && <p className="form-error">{`${errors.cropId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="name" className="pb-1 font-normal">
                      Crop Name
                    </Label>
                    <Input
                      {...register('name')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.name && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter crop name"
                    />
                    {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="sapItemCode" className="pb-1 font-normal">
                      SAP Item Code
                    </Label>
                    <Input
                      {...register('sapItemCode')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.sapItemCode && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter SAP Item Code"
                    />
                    {errors.sapItemCode && <p className="form-error">{`${errors.sapItemCode.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="harvestDays" className="pb-1 font-normal">
                      Days to Harvest
                    </Label>
                    <Input
                      {...register('harvestDays')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.harvestDays && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter days to harvest"
                    />
                    {errors.harvestDays && <p className="form-error">{`${errors.harvestDays.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="keywords" className="pb-1 font-normal">
                      Keywords
                    </Label>
                    <Controller
                      control={control}
                      name="keywords"
                      rules={{ required: false }}
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <MultipleSelector
                          ref={ref}
                          value={value as Option[]}
                          onChange={onChange}
                          defaultOptions={OPTIONS}
                          placeholder="e.g., corn, mais, maize, etc."
                          creatable
                          emptyIndicator={
                            <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                              e.g., corn, mais, maize, etc.
                            </p>
                          }
                        />
                      )}
                    />
                    {errors.keywords && <p className="form-error">{`${errors.keywords.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="status" className="pb-1 font-normal">
                      Status
                    </Label>
                    <Controller
                      control={control}
                      name="status"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="0">Disabled</SelectItem>
                              <SelectItem value="1">Active</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
                  </div>

                  <div className="flex flex-col gap-6 md:flex-row">
                    {/* Image Dropzone */}
                    <div className="md:w-1/2">
                      <div className="border-spacing-4 cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                        <div {...getRootProps({ className: 'dropzone' })}>
                          <input {...getInputProps()} />

                          <ImageIcon className="mx-auto mb-2 size-12 text-gray-500" />
                          <p>{`Drag 'n' drop image here, or`}</p>

                          <div className="py-4 font-bold text-primary">Browse</div>
                        </div>

                        <div>{files}</div>
                      </div>
                    </div>

                    {/* Preview */}
                    <div className="rounded-lg border border-gray-300 md:w-1/2">
                      {!preview ? (
                        <div className="flex h-full items-center justify-center">
                          <div className="">
                            <ImageIcon className="mx-auto mb-2 size-12 text-gray-500" />
                            <div>Image Preview</div>
                          </div>
                        </div>
                      ) : (
                        <div className="flex h-full items-center">
                          <img className="mx-auto h-[185px] object-cover" src={preview} alt="" />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between gap-2 pt-6">
                  <DialogClose asChild>
                    <Button className="px-12" variant="outline" type="button">
                      Cancel
                    </Button>
                  </DialogClose>
                  {loading.value ? (
                    <ButtonLoading />
                  ) : (
                    <Button className="px-12" type="submit">
                      Update
                    </Button>
                  )}
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  );
};
