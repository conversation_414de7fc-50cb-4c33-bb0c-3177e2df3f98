'use client';

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';

const RADIAN = Math.PI / 180;
const data = [
  { name: 'POOR', value: 15, color: '#E82D48' },
  { name: 'FAIR', value: 45, color: '#EDA635' },
  { name: 'GOOD', value: 10, color: '#EFD933' },
  { name: 'VERY GOOD', value: 15, color: '#8CE249' },
  { name: 'EXCELLENT', value: 15, color: '#3FD14A' },
];
const cx = 114;
const cy = 114;
const iR = 72;
const oR = 114;

const needle = (value, data, cx, cy, iR, oR, color) => {
  let total = 0;
  data.forEach((v) => {
    total += v.value;
  });
  const ang = 180.0 * (1 - value / total);
  const length = (iR + 2 * oR) / 3;
  const sin = Math.sin(-RADIAN * ang);
  const cos = Math.cos(-RADIAN * ang);
  const r = 5;
  const x0 = cx + 5;
  const y0 = cy + 5;
  const xba = x0 + r * sin;
  const yba = y0 - r * cos;
  const xbb = x0 - r * sin;
  const ybb = y0 + r * cos;
  const xp = x0 + length * cos;
  const yp = y0 + length * sin;

  return [
    <circle key={0} cx={x0} cy={y0} r={r} fill={color} stroke="none" />,
    <path key={1} d={`M${xba} ${yba}L${xbb} ${ybb} L${xp} ${yp} L${xba} ${yba}`} stroke="#none" fill={color} />,
  ];
};

function getIntroOfPage(label) {
  if (label === 'POOR') {
    return '1-15%';
  } else if (label === 'FAIR') {
    return '16-59%';
  } else if (label === 'GOOD') {
    return '60-70%';
  } else if (label === 'VERY GOOD') {
    return '71-85%';
  } else if (label === 'EXCELLENT') {
    return '86-100%';
  }
}

function CustomTooltip({ payload, label, active }) {
  if (active) {
    return (
      <div className="-translate-y-10 rounded-lg border border-gray-200 bg-white px-4 py-2 shadow-xl">
        <p className="">{`${payload[0].name} : ${getIntroOfPage(payload[0].name)}`}</p>
      </div>
    );
  }

  return null;
}

export default function CreditScore({ value = 82 }) {
  return (
    <ResponsiveContainer>
      <PieChart>
        <Pie
          dataKey="value"
          startAngle={180}
          endAngle={0}
          data={data}
          cx={cx}
          cy={cy}
          innerRadius={iR}
          outerRadius={oR}
          fill="#8884d8"
          stroke="none"
        >
          {data.map((entry, index) => (
            <Cell className="outline-none" key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        {needle(value, data, cx, cy, iR, oR, '#36417D')}
        <Tooltip content={(v) => <CustomTooltip payload={v.payload} label={v.label} active={v.active} />} />
      </PieChart>
    </ResponsiveContainer>
  );
}
