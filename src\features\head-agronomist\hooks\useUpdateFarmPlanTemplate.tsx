'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { ITemplateForm } from '../components/templates/create-form/types/template';

// Update template interface (extends ITemplateForm with ID)
export interface IUpdateTemplateForm extends ITemplateForm {
  farmPlanTemplateId: number;
}

// Mutation function for updating farm plan template
const updateFarmPlanTemplate = async (data: IUpdateTemplateForm) => {
  const { data: res } = await axios.post('/agronomist/farmplan/template/update', data);
  return res;
};

export const useUpdateFarmPlanTemplate = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();

  const updateTemplateMutation = useMutation({
    mutationFn: updateFarmPlanTemplate,
  });

  // Submit template function
  const submitTemplate = async (data: IUpdateTemplateForm) => {
    try {
      setIsSubmitting(true);

      // Format the data, especially dates in subItems
      const formData: IUpdateTemplateForm = {
        ...data,
        items: data.items.map((item) => ({
          ...item,
          subItems: item.subItems.map((subItem) => ({
            ...subItem,
            expectedDate:
              subItem.expectedDate instanceof Date ? format(subItem.expectedDate, 'yyyy-MM-dd') : subItem.expectedDate,
          })),
        })),
      };

      console.log('Form submitted:', formData);
      const res = await updateTemplateMutation.mutateAsync(formData);

      console.log('Form response:', res);
      toast.success('Success', {
        description: res.message || 'Farm plan template updated successfully',
      });

      // Refetch templates
      queryClient.refetchQueries({
        queryKey: ['farmPlanTemplates'],
      });

      // Refetch current template
      queryClient.refetchQueries({
        queryKey: ['farmPlanTemplateById', data.farmPlanTemplateId],
      });

      // Navigate back to templates list
      router.push('/head-agronomist');
    } catch (e: any) {
      const error = e?.response?.data?.message || e.message;
      console.error('Error updating template:', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitTemplate,
    isSubmitting,
    updateTemplateMutation,
  };
};
