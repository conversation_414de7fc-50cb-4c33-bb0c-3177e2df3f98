'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Controller, useForm } from 'react-hook-form';

import LoadingScreen from '@/components/LoadingScreen';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';

import useLogin, { LoginSchema } from '@/lib/hooks/useLogin';
import useIsLogin from '@/lib/hooks/utils/useIsLogin';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

export default function Home() {
  const gStateP = useGlobalStatePersist();
  const { onLogin } = useLogin();
  const { loading } = useIsLogin();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: gStateP.cred.email.value,
      password: gStateP.cred.password.value,
      keep_me_logged_in: gStateP.keep_me_logged_in.value,
    },
  });

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <div className="flex">
      <div
        className={cn(
          `hidden w-[48%] h-screen rounded-br-[200px]`,
          `bg-[url('/assets/bg.png')] bg-center bg-cover`,
          `md:block`,
        )}
      >
        <div className="flex h-[46vh] items-center">
          <img className="mx-auto md:max-h-[6.5rem] lg:max-h-32 2xl:max-h-36" src="/kita-logo.png" alt="kitaph logo" />
        </div>
      </div>
      <ScrollArea className="h-screen flex-1">
        <main className="flex min-h-screen items-center justify-center py-12">
          <form onSubmit={handleSubmit(onLogin)} className="font-dmSans w-80 space-y-6 lg:w-96">
            <div className="flex items-center pb-3 md:hidden">
              <img className="mx-auto max-h-16" src="/kita-logo.png" alt="kitaph logo" />
            </div>

            <h1 className="text-3xl font-bold leading-10 text-primary">Sign In</h1>
            <div className="text-lg font-normal leading-relaxed text-slate-400">
              Enter your email and password to sign in!
            </div>

            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="email" className="text-lg font-bold text-primary">
                <span className="">Email/Username</span>
                <span className="text-red-500">*</span>
              </Label>
              <Input
                {...register('email')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.email && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="<EMAIL>"
              />
              {errors.email && <p className="form-error">{`${errors.email.message}`}</p>}
            </div>

            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="password" className="text-lg font-bold text-primary">
                <span className="">Password</span>
                <span className="text-red-500">*</span>
              </Label>
              <InputPassword
                {...register('password')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.password && 'border-red-500 focus-visible:ring-red-500',
                )}
                placeholder="Min. 8 characters"
              />
              {errors.password && <p className="form-error">{`${errors.password.message}`}</p>}
            </div>

            <div className="flex  justify-between">
              <div className="flex items-center space-x-2">
                <Controller
                  control={control}
                  name="keep_me_logged_in"
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <Checkbox
                      checked={value}
                      onCheckedChange={onChange}
                      className="border-primary data-[state=checked]:bg-primary"
                    />
                  )}
                />

                <label
                  htmlFor="keep_me_logged_in"
                  className="text-sm font-medium leading-none text-primary peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Keep me logged in
                </label>
              </div>

              <Button variant="link" className="text-blue-500" type="button">
                Forgot Password?
              </Button>
            </div>

            <Button className="w-full focus:ring-2 focus:ring-primary focus:ring-offset-2" type="submit">
              Sign In
            </Button>
          </form>
        </main>
      </ScrollArea>
    </div>
  );
}
