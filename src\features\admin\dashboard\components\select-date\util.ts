import {
  endOfDay,
  endOfMonth,
  endOfWeek,
  endOfYear,
  startOfDay,
  startOfMonth,
  startOfWeek,
  startOfYear,
  subDays,
  subMonths,
  subWeeks,
  subYears,
} from 'date-fns';

import { TSelectDate } from './types';

export const getDateRange = (selectDate: TSelectDate) => {
  switch (selectDate) {
    case 'week':
      return {
        startDate: startOfWeek(new Date()),
        endDate: endOfWeek(new Date()),
        previousStartDate: startOfWeek(subWeeks(new Date(), 1)),
        previousEndDate: endOfWeek(subWeeks(new Date(), 1)),
      };
    case 'month':
      return {
        startDate: startOfMonth(new Date()),
        endDate: endOfMonth(new Date()),
        previousStartDate: startOfMonth(subMonths(new Date(), 1)),
        previousEndDate: endOfMonth(subMonths(new Date(), 1)),
      };
    case 'year':
      return {
        startDate: startOfYear(new Date()),
        endDate: endOfYear(new Date()),
        previousStartDate: startOfYear(subYears(new Date(), 1)),
        previousEndDate: endOfYear(subYears(new Date(), 1)),
      };
    default:
      return {
        startDate: startOfDay(new Date()),
        endDate: endOfDay(new Date()),
        previousStartDate: startOfDay(subDays(new Date(), 1)),
        previousEndDate: endOfDay(subDays(new Date(), 1)),
      };
  }
};

export const getFrom = (selectDate: TSelectDate) => {
  switch (selectDate) {
    case 'week':
      return 'from last week';
    case 'month':
      return 'from last month';
    case 'year':
      return 'from last year';
    default:
      return 'from yesterday';
  }
};

const formatPercentage = (percentage: number): string => {
  return Number.isInteger(percentage) ? `${percentage}` : `${percentage.toFixed(1)}`;
};

export const getRemarks = (selectDate: TSelectDate, current: number, previous: number) => {
  if (current || previous) {
    const percentage = ((current - previous) / previous) * 100;
    const formatted = formatPercentage(percentage);
    const sign = percentage > 0 ? '+' : '';

    return `${sign}${formatted}% ${getFrom(selectDate)}`;
  }
  return `0% ${getFrom(selectDate)}`;
};

export function safeJsonParse<T = unknown>(input: string | undefined | null, fallback?: T): T {
  try {
    if (!input || input === 'undefined' || input === 'null') return;
    return JSON.parse(input);
  } catch {
    // do nothing
    // return fallback;
  }
}
