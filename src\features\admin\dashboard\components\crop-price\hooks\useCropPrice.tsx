'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useQuery } from '@tanstack/react-query';
import { format, startOfMonth } from 'date-fns';

import axios from '@/lib/api';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { ICropPriceTrend, ICrops } from '../types';
import { generateRandomColor } from '../utils/color-generator';

// State
const initialState = {
  cropIds: [] as string[],
  startDate: startOfMonth(new Date()),
  endDate: startOfMonth(new Date()),
};
const dashboardCropPriceState = hookstate(initialState, devtools({ key: 'dashboardCropPriceState' }));

// Fetcher
const fetchCrops = async (currentUser = 'admin') => {
  const { data } = await axios.get(`/${currentUser}/crops/viewAll`);
  return (data.data as ICrops[]).filter((x) => x.status === 1);
};

const fetchCropPriceTrends = async (currentUser = 'admin') => {
  const { data } = await axios.get(`/${currentUser}/dashboard/viewCropPricesTrend`, {
    params: {
      ...dashboardCropPriceState.value,
    },
  });
  return (data.data as ICropPriceTrend[]).map((x) => ({
    ...x,
    color: generateRandomColor(),
  }));
};

// Hooks
function useCropPrice(currentUser = 'admin') {
  const state = useHookstate(dashboardCropPriceState);
  const gStateP = useGlobalStatePersist();

  const cropsQuery = useQuery({
    queryKey: ['crops', { currentUser, username: gStateP.user.user.username.value }],
    queryFn: () => fetchCrops(currentUser),
    enabled: !!gStateP.user.value,
  });

  const cropPriceTrendsQuery = useQuery({
    queryKey: ['crop-price-trends', { currentUser, username: gStateP.user.user.username.value, ...state.value }],
    queryFn: () => fetchCropPriceTrends(currentUser),
    enabled: !!gStateP.user.value,
  });

  const processChartData = (cropsData: ICropPriceTrend[], startDate, endDate, priceType) => {
    const dailyData = [];

    cropsData.forEach((crop) => {
      const cropName = crop.name;
      const priceRanges = crop.cropPriceRanges;

      priceRanges.forEach((range) => {
        const date = new Date(range.updated_at);
        if (date < startDate || date > endDate) return;

        const name = `${format(date, 'MMM dd, yyyy')}`;
        const findIndex = dailyData.findIndex((x) => x.name === name);

        if (findIndex >= 0) {
          dailyData[findIndex][cropName] =
            priceType === 'highPrice'
              ? range.high_price
              : priceType === 'lowPrice'
                ? range.low_price
                : range.high_price; // Default to high_price
        }

        if (findIndex === -1) {
          dailyData.push({
            name,
            [cropName]:
              priceType === 'highPrice'
                ? range.high_price
                : priceType === 'lowPrice'
                  ? range.low_price
                  : range.high_price, // Default to high_price
          });
        }
      });
    });

    // Format the data for the chart
    const chartData = dailyData.sort((a: any, b: any) => new Date(a.name).getTime() - new Date(b.name).getTime());
    return chartData;
  };

  return { state, cropsQuery, cropPriceTrendsQuery, processChartData };
}

export { dashboardCropPriceState };
export default useCropPrice;
