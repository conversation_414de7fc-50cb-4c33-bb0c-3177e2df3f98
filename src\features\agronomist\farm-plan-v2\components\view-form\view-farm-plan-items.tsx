'use client';

import { format } from 'date-fns';

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { IFarmPlanResponse } from '../../hooks/useEditFarmPlan';

interface IViewFarmPlanItemsProps {
  farmPlan: IFarmPlanResponse;
}

export default function ViewFarmPlanItems({ farmPlan }: IViewFarmPlanItemsProps) {
  const farmPlanItems = farmPlan.farmPlanItems || [];

  if (farmPlanItems.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-kitaph-primary">Farm Plan Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">No farm plan items found.</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {farmPlanItems.map((item) => (
        <Card key={item.id}>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-kitaph-primary">{item.name}</CardTitle>
            <div className="text-sm text-gray-600">
              Type: {item.type} | Total Amount: ₱{item.total_amount?.toLocaleString() || '0'}
            </div>
          </CardHeader>
          <CardContent>
            {item.farmPlanSubItems && item.farmPlanSubItems.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item Name</TableHead>
                    <TableHead>Expected Date</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Unit</TableHead>
                    <TableHead>Unit Cost</TableHead>
                    <TableHead>Total Amount</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {item.farmPlanSubItems.map((subItem) => (
                    <TableRow key={subItem.id}>
                      <TableCell className="font-medium">{subItem.item_name}</TableCell>
                      <TableCell>
                        {subItem.expected_date ? format(new Date(subItem.expected_date), 'MMM dd, yyyy') : 'N/A'}
                      </TableCell>
                      <TableCell>{subItem.quantity}</TableCell>
                      <TableCell>{subItem.unit}</TableCell>
                      <TableCell>₱{subItem.unit_cost?.toLocaleString() || '0'}</TableCell>
                      <TableCell>₱{subItem.total_amount?.toLocaleString() || '0'}</TableCell>
                      <TableCell>{subItem.notes || '-'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-4 text-gray-500">No sub-items found for this category.</div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
