import { Link1Icon } from '@radix-ui/react-icons';
import { Plus, X } from 'lucide-react';
import React from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  useFieldArray,
  UseFormGetValues,
  UseFormRegister,
  UseFormWatch,
} from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { GovernmentIdentificationEnum } from '@/app/admin/(accounts)/account-info/_components/tab-content/account-profile/Enums';
import { cn } from '@/lib/utils';

import { TPersonalInformationSchema } from '../schemas';

interface IIdentificationDocsProps {
  register: UseFormRegister<TPersonalInformationSchema>;
  control: Control<TPersonalInformationSchema>;
  errors: FieldErrors<TPersonalInformationSchema>;
  values: UseFormGetValues<TPersonalInformationSchema>;
  watch: UseFormWatch<TPersonalInformationSchema>;
}

const IdentificationDocs = ({ register, control, errors, values, watch }: IIdentificationDocsProps) => {
  const governId = useFieldArray({
    name: 'governmentIdentification',
    control,
  });

  return (
    <div className="mt-6">
      <FormTitle title="Identification Documents" />
      <div className="flex flex-col gap-4 divide-y-2 divide-dashed">
        {governId.fields.map((field, index) => {
          const errorForField = errors?.governmentIdentification?.[index];
          const currentType = watch(`governmentIdentification.${index}.governmentIdType`);
          const currentUpload = watch(`governmentIdentification.${index}.upload`);

          return (
            <div key={field.id} className="grid gap-x-4 gap-y-8 pb-3 pt-6 sm:grid-cols-2 xl:grid-cols-3">
              {/* ID Type */}
              <FormField
                name={`governmentIdentification.${index}.governmentIdType`}
                label="Government Identification Type"
                errors={(errorForField as FieldErrors) || {}}
                required
              >
                <Controller
                  control={control}
                  name={`governmentIdentification.${index}.governmentIdType` as const}
                  rules={{ required: 'ID type is required' }}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger className={cn(errorForField?.governmentIdType && 'border-red-500')}>
                        <SelectValue placeholder="Select ID Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {Object.values(GovernmentIdentificationEnum).map((idType) => (
                            <SelectItem key={idType + index} value={idType}>
                              {idType}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
              </FormField>

              {/* ID Number */}
              <FormField
                name={`governmentIdentification.${index}.governmentIdNumber`}
                label="Government ID Number"
                errors={(errorForField as FieldErrors) || {}}
                required={!!currentType}
              >
                <Input
                  {...register(`governmentIdentification.${index}.governmentIdNumber`, {
                    required: currentType ? 'Government ID Number is required' : false,
                  })}
                  className={cn(errorForField?.governmentIdNumber && 'border-red-500')}
                  placeholder="Enter ID Number"
                />
              </FormField>

              {/* Upload ID */}
              <FormField
                name={`governmentIdentification.${index}.upload`}
                label="Upload ID Image"
                errors={(errorForField as FieldErrors) || {}}
                required={!!currentType}
              >
                <div className="flex items-center gap-4">
                  <Input
                    {...register(`governmentIdentification.${index}.upload`, {
                      required: currentType && !currentUpload ? 'Upload is required' : false,
                    })}
                    type="file"
                    className={cn(errorForField?.upload && 'border-red-500')}
                  />

                  <div className={cn(index === 0 && 'invisible')}>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="border-red-500 text-red-500 hover:bg-red-100"
                      onClick={() => governId.remove(index)}
                    >
                      <X className="size-5" />
                    </Button>
                  </div>
                </div>
                <div
                  className={cn(
                    'flex items-center gap-2 text-sm font-bold text-blue-600',
                    !field.upload && 'text-red-600 pointer-events-none cursor-not-allowed',
                  )}
                >
                  <Link1Icon /> {field.upload ? field.governmentIdType : 'No File'}
                </div>
              </FormField>
            </div>
          );
        })}
      </div>

      <div className="flex pb-6 pt-2">
        <Button
          className="border-slate-300"
          type="button"
          onClick={() =>
            governId.append({
              governmentIdType: '',
              governmentIdNumber: '',
              upload: null,
            })
          }
        >
          <Plus className="mr-2 size-5 text-white" />
          <span>Add More ID info</span>
        </Button>
      </div>
    </div>
  );
};

export default IdentificationDocs;
