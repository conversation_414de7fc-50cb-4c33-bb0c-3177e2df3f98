'use client';

import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

import { cn } from '@/lib/utils';

import { ICreateFarmPlan } from '../../types/create-farm-plan.types';

interface IAgronomistInfoProps {
  form: UseFormReturn<ICreateFarmPlan>;
}

export default function AgronomistInfo({ form }: IAgronomistInfoProps) {
  const { control } = form;

  return (
    <div className="mt-6 grid grid-cols-1 gap-6">
      {/* Agronomist Information */}
      <div>
        <div className="mb-4">
          <Label htmlFor="agronomistName" className="pb-1">
            Prepared by: (Name of Agronomist)
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Controller
            control={control}
            name="agronomistName"
            rules={{
              required: 'Agronomist name is required',
            }}
            render={({ field }) => (
              <div>
                <Input
                  value={field.value}
                  onChange={field.onChange}
                  className={cn({
                    'input-error': form.formState.errors.agronomistName,
                  })}
                />
                {form.formState.errors.agronomistName && (
                  <p className="form-error">{form.formState.errors.agronomistName.message}</p>
                )}
              </div>
            )}
          />
        </div>

        <div className="mb-4">
          <Label htmlFor="agronomistPrcNumber" className="pb-1 font-normal">
            PRC License No:
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Controller
            control={control}
            name="agronomistPrcNumber"
            rules={{
              required: 'PRC License number is required',
            }}
            render={({ field }) => (
              <div>
                <Input
                  value={field.value}
                  onChange={field.onChange}
                  className={cn({
                    'input-error': form.formState.errors.agronomistPrcNumber,
                  })}
                />
                {form.formState.errors.agronomistPrcNumber && (
                  <p className="form-error">{form.formState.errors.agronomistPrcNumber.message}</p>
                )}
              </div>
            )}
          />
        </div>

        <div>
          <Label htmlFor="agronomistValidUntil" className="pb-1 font-normal">
            Valid Until:
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Controller
            control={control}
            name="agronomistValidUntil"
            rules={{
              required: 'Valid until date is required',
            }}
            render={({ field }) => (
              <div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !field.value && 'text-muted-foreground',
                        form.formState.errors.agronomistValidUntil && 'border-red-500',
                      )}
                    >
                      <CalendarIcon className="mr-2 size-4" />
                      {field.value ? (
                        format(new Date(field.value as string | Date), 'MMM d, yyyy')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value as string | Date) : undefined}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {form.formState.errors.agronomistValidUntil && (
                  <p className="form-error">{form.formState.errors.agronomistValidUntil.message}</p>
                )}
              </div>
            )}
          />
        </div>
      </div>

      {/* Head Agronomist Information */}
      <div className="mt-14">
        <div className="mb-4">
          <Label htmlFor="headAgronomistName" className="pb-1">
            Noted by: (Name of Head Agronomist)
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Controller
            control={control}
            name="headAgronomistName"
            rules={{
              required: 'Head agronomist name is required',
            }}
            render={({ field }) => (
              <div>
                <Input
                  value={field.value}
                  onChange={field.onChange}
                  className={cn({
                    'input-error': form.formState.errors.headAgronomistName,
                  })}
                />
                {form.formState.errors.headAgronomistName && (
                  <p className="form-error">{form.formState.errors.headAgronomistName.message}</p>
                )}
              </div>
            )}
          />
        </div>

        <div className="mb-4">
          <Label htmlFor="headAgronomistPrcNumber" className="pb-1 font-normal">
            PRC License No:
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Controller
            control={control}
            name="headAgronomistPrcNumber"
            rules={{
              required: 'PRC License number is required',
            }}
            render={({ field }) => (
              <div>
                <Input
                  value={field.value}
                  onChange={field.onChange}
                  className={cn({
                    'input-error': form.formState.errors.headAgronomistPrcNumber,
                  })}
                />
                {form.formState.errors.headAgronomistPrcNumber && (
                  <p className="form-error">{form.formState.errors.headAgronomistPrcNumber.message}</p>
                )}
              </div>
            )}
          />
        </div>

        <div>
          <Label htmlFor="headAgronomistValidUntil" className="pb-1 font-normal">
            Valid Until:
            <span className="ml-1 font-bold text-red-500">*</span>
          </Label>
          <Controller
            control={control}
            name="headAgronomistValidUntil"
            rules={{
              required: 'Valid until date is required',
            }}
            render={({ field }) => (
              <div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !field.value && 'text-muted-foreground',
                        form.formState.errors.headAgronomistValidUntil && 'border-red-500',
                      )}
                    >
                      <CalendarIcon className="mr-2 size-4" />
                      {field.value ? (
                        format(new Date(field.value as string | Date), 'MMM d, yyyy')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={field.value ? new Date(field.value as string | Date) : undefined}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {form.formState.errors.headAgronomistValidUntil && (
                  <p className="form-error">{form.formState.errors.headAgronomistValidUntil.message}</p>
                )}
              </div>
            )}
          />
        </div>
      </div>
    </div>
  );
}
