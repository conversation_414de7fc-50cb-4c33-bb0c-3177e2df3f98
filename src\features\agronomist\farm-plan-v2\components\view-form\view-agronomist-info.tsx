'use client';

import { format } from 'date-fns';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IFarmPlanResponse } from '../../hooks/useEditFarmPlan';

interface IViewAgronomistInfoProps {
  farmPlan: IFarmPlanResponse;
}

export default function ViewAgronomistInfo({ farmPlan }: IViewAgronomistInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-kitaph-primary">Agronomist Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Agronomist Information */}
        <div className="space-y-3">
          <div className="font-medium text-gray-700">Prepared by:</div>
          <div>
            <div className="text-sm font-medium text-gray-600">Name</div>
            <div className="text-base">{farmPlan.agronomist_name || 'N/A'}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">PRC Number</div>
            <div className="text-base">{farmPlan.agronomist_prc_number || 'N/A'}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Valid Until</div>
            <div className="text-base">
              {farmPlan.agronomist_valid_until
                ? format(new Date(farmPlan.agronomist_valid_until), 'MMM dd, yyyy')
                : 'N/A'}
            </div>
          </div>
        </div>

        {/* Head Agronomist Information */}
        <div className="space-y-3 border-t pt-6">
          <div className="font-medium text-gray-700">Noted by:</div>
          <div>
            <div className="text-sm font-medium text-gray-600">Name</div>
            <div className="text-base">{farmPlan.head_agronomist_name || 'N/A'}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">PRC Number</div>
            <div className="text-base">{farmPlan.head_agronomist_prc_number || 'N/A'}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Valid Until</div>
            <div className="text-base">
              {farmPlan.head_agronomist_valid_until
                ? format(new Date(farmPlan.head_agronomist_valid_until), 'MMM dd, yyyy')
                : 'N/A'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
