'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { toast } from 'sonner';

import { SearchResult } from '@/components/ui/action-search-bar';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import axios from '@/lib/api';
import { catchError, urlify } from '@/lib/utils';

import { AddGroupUserBulkType, AddGroupUserType, IGroupUsers } from './group-users-store.types';

const initialState = {
  data: {
    meta: null,
    data: [],
  } as IGroupUsers,
  pagination: {
    page: 1,
    pageSize: 10,
  },
};

export const groupUserState = hookstate(
  initialState,
  devtools({
    key: 'groupUserState',
  }),
);

export const useGroupUserStore = (currentUser = 'admin') => {
  const state = useHookstate(groupUserState);

  const searchUser = async (query: string, userType: UserType.NONFARMER | UserType.FARMER) => {
    let result: SearchResult = {
      actions: [],
    };
    try {
      const res = await axios
        .get(`/${currentUser}/tradingapp/group/user/search`, {
          params: {
            search: query,
            limit: 100,
            userType,
          },
        })
        .then((res) => res.data.data);
      result.actions = res.map((v) => ({
        id: v.farmer ? v.farmer.user_id : v.nonFarmer.user_id,
        label: v.username,
        description: v.farmer
          ? `${v.farmer.first_name} ${v.farmer.last_name}`
          : `${v.nonFarmer.first_name} ${v.nonFarmer.last_name}`,
        icon: (
          <div>
            <img
              className="size-4 rounded-full"
              src={v.user_img ? urlify(v.user_img, 'users/profile') : '/assets/user-default.jpg'}
              alt=""
            />
          </div>
        ),
        end: v.farmer ? 'Farmer' : 'Non-Farmer',
      }));
    } catch (e) {
      console.error(e);
    }
    return result;
  };

  const getUsers = async (groupId: number) => {
    try {
      const res = await axios
        .get(`/${currentUser}/tradingapp/group/user/viewAll`, {
          params: {
            tradingAppGroupPriceId: groupId,
            page: state.pagination.page.value,
            pageSize: state.pagination.pageSize.value,
          },
        })
        .then((res) => res.data.data);
      state.data.set(res);
    } catch (e) {
      console.error(e);
    }
  };

  const addUser = async (data: AddGroupUserType) => {
    try {
      toast.loading('Adding user...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/group/user/create`, data);
      await getUsers(data.tradingAppGroupPriceId);

      toast.dismiss();
      toast.success('Success', {
        description: 'User added successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'addUser');
    }
  };

  const addUserBulk = async (data: AddGroupUserBulkType) => {
    try {
      toast.loading('Adding users...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/group/user/create/bulk`, data);
      await getUsers(data.tradingAppGroupPriceId);

      toast.dismiss();
      toast.success('Success', {
        description: 'Users added successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'addUser');
    }
  };

  const removeUser = async (userId: number | string, groupId: number) => {
    try {
      toast.loading('Removing user...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/group/user/delete/${userId}`);
      await getUsers(groupId);

      toast.dismiss();
      toast.success('Success', {
        description: 'User removed successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'removeUser');
    }
  };

  return { state, searchUser, addUser, getUsers, removeUser, addUserBulk };
};
