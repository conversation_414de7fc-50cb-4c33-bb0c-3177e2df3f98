'use client';

import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { ELogAction, IFarmPlanTemplateLog } from '../../../types/farmplan-templates';

// Action badge styling
const getActionBadgeVariant = (action: ELogAction) => {
  switch (action) {
    case ELogAction.ADDED:
      return 'default'; // green
    case ELogAction.UPDATE:
      return 'secondary'; // blue
    case ELogAction.REMOVED:
      return 'destructive'; // red
    default:
      return 'outline';
  }
};

const getActionLabel = (action: ELogAction) => {
  switch (action) {
    case ELogAction.ADDED:
      return 'Added';
    case ELogAction.UPDATE:
      return 'Updated';
    case ELogAction.REMOVED:
      return 'Removed';
    default:
      return action;
  }
};

export const auditLogsColumns: ColumnDef<IFarmPlanTemplateLog>[] = [
  {
    id: 'date_time',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time" />,
    cell: ({ row }) => {
      const data = row.original;
      const dateValue = data.created_at;
      const date = dateValue ? new Date(dateValue) : null;
      if (!date || isNaN(date.getTime())) {
        return ' ';
      }

      return <div className="min-w-max">{`${format(date, 'dd MMM yyyy')} | ${format(date, 'hh:mm a')}`}</div>;
    },
    accessorFn: (row) => {
      const date = new Date(row.created_at);
      return format(date, 'dd MMM yyyy');
    },
  },
  {
    id: 'action',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Action" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Badge variant={getActionBadgeVariant(data.action)} className="min-w-max">
          {getActionLabel(data.action)}
        </Badge>
      );
    },
    accessorFn: (row) => row.action,
  },
  {
    id: 'category',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Category" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.category}</div>;
    },
    accessorFn: (row) => row.category,
  },
  {
    id: 'item_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Item Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.item_name}</div>;
    },
    accessorFn: (row) => row.item_name,
  },
  {
    id: 'reason',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Reason" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="max-w-xs truncate" title={data.reason}>
          {data.reason}
        </div>
      );
    },
    accessorFn: (row) => row.reason,
  },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {`${data.processedBy.agronomist.first_name} ${data.processedBy.agronomist.last_name}`}
        </div>
      );
    },
    accessorFn: (row) => `${row.processedBy.agronomist.first_name} ${row.processedBy.agronomist.last_name}`,
  },
];
