'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';

import { IFarmPlanResponse } from '../../hooks/useEditFarmPlan';

interface IViewFarmPlanTemplateInfoProps {
  farmPlan: IFarmPlanResponse;
}

export default function ViewFarmPlanTemplateInfo({ farmPlan }: IViewFarmPlanTemplateInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-kitaph-primary">Farm Plan Template Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="text-sm font-medium text-gray-600">Template ID</div>
          <div className="text-base font-medium">{farmPlan.farm_plan_template_id || 'N/A'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Contingency for Fluctuation (%)</div>
          <div className="text-base">{farmPlan.contingency_for_fluctuation || 0}%</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Interest Rate (%)</div>
          <div className="text-base">{farmPlan.interest_rate || 0}%</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Number of Months per Tenor</div>
          <div className="text-base">{farmPlan.number_of_months_per_tenor || 0} months</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">AOR per Month</div>
          <div className="text-base">₱{farmPlan.aor_per_month?.toLocaleString() || '0'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Crop</div>
          <div className="text-base">{farmPlan.crop?.name || 'N/A'}</div>
        </div>

        <div>
          <div className="text-sm font-medium text-gray-600">Harvest Days</div>
          <div className="text-base">{farmPlan.crop?.harvest_days || 'N/A'} days</div>
        </div>
      </CardContent>
    </Card>
  );
}
