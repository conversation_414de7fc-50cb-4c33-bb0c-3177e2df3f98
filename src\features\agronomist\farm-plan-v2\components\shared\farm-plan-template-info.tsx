'use client';

import { useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { useFarmPlanTemplate } from '@/features/head-agronomist/hooks/useFarmPlanTemplates';
import { ETemplateItemType, IFarmPlanTemplate } from '@/features/head-agronomist/types/farmplan-templates';
import { cn } from '@/lib/utils';

import useFarmer from '../../hooks/useFarmer';
import { ICreateFarmPlan } from '../../types/create-farm-plan.types';
import TemplateSearchInput from './template-search-input';

interface IFarmPlanTemplateInfoProps {
  form: UseFormReturn<ICreateFarmPlan>;
  onTemplateReset?: () => void;
}

export default function FarmPlanTemplateInfo({ form, onTemplateReset }: IFarmPlanTemplateInfoProps) {
  const { state } = useFarmer();
  const { farmPlanTemplatesQuery, state: templateState } = useFarmPlanTemplate();
  const [selectedTemplate, setSelectedTemplate] = useState<IFarmPlanTemplate | null>(null);

  return (
    <div className="">
      <div className="">
        <Controller
          name="farmPlanTemplateId"
          control={form.control}
          rules={{
            required: `Farm plan template is required`,
            validate: {
              hasSelected: (v) => v > 0 || `Farm plan template is required`,
            },
          }}
          render={({ field }) => (
            <TemplateSearchInput
              label="Farm Plan Template"
              value={templateState.search.value}
              onChange={(value) => {
                templateState.search.set(value);
                // Only reset field value if user is clearing the input or typing something different from selected template
                if (!value || (selectedTemplate && value !== selectedTemplate.crop.name)) {
                  field.onChange(0);
                }
              }}
              templates={farmPlanTemplatesQuery.data?.data.data || []}
              isLoading={farmPlanTemplatesQuery.isFetching || farmPlanTemplatesQuery.isLoading}
              isError={!!form.formState.errors.farmPlanTemplateId}
              selectedTemplate={selectedTemplate}
              onTemplateSelect={(template) => {
                setSelectedTemplate(template);
                field.onChange(template?.id ?? 0);

                // autofill crop id and crop name from selected template
                form.setValue('cropId', template?.crop.id ?? 0);
                form.setValue('contingencyForFluctuation', template?.contingency_for_fluctuation ?? 0);

                // autofill interest rate, number of months per tenor, and aor per month
                form.setValue('interestRate', template?.interest_rate ?? 0);
                form.setValue('numberOfMonthsPerTenor', template?.number_of_months_per_tenor ?? 0);
                form.setValue('aorPerMonth', template?.aor_per_month ?? 0);

                // autofill head agronomist
                form.setValue(
                  'headAgronomistName',
                  template
                    ? `${template?.agronomistUser.agronomist.first_name} ${template?.agronomistUser.agronomist.last_name}`
                    : '',
                );

                // update the items with template.farmPlanTemplateItems
                if (template?.farmPlanTemplateItems) {
                  const transformedItems = template.farmPlanTemplateItems.map((item) => ({
                    farmPlanItemId: item.id,
                    name: item.name,
                    type: item.type as ETemplateItemType,
                    subItems: item.farmPlanTemplateSubItems.map((subItem) => ({
                      farmPlanSubItemId: subItem.id,
                      expectedDate: subItem.expected_date,
                      itemName: subItem.item_name,
                      unit: subItem.unit,
                      quantity: subItem.quantity,
                      unitCost: subItem.unit_cost,
                      notes: subItem.notes,
                      marketplaceProductId: subItem.marketplace_product_id,
                      totalAmount: subItem.total_amount,
                    })),
                  }));

                  form.setValue('items', transformedItems);
                }

                // Trigger form reset to force remount with new values
                onTemplateReset?.();
              }}
            />
          )}
        />
        {form.formState.errors.farmPlanTemplateId && (
          <p className="form-error">{form.formState.errors.farmPlanTemplateId.message}</p>
        )}
      </div>

      <div className="">
        <Label htmlFor="croppingType">Cropping Type</Label>
        <Controller
          name="croppingType"
          control={form.control}
          rules={{
            required: `Cropping type is required for farm planning`,
            validate: {
              min: (v) => v.length >= 3 || `Invalid cropping type`,
            },
          }}
          render={({ field, fieldState }) => (
            <Input
              id="croppingType"
              value={field.value || ''}
              onChange={field.onChange}
              placeholder="Enter cropping type"
              className={cn({
                'input-error': fieldState.error,
              })}
            />
          )}
        />

        {form.formState.errors.croppingType && (
          <p className="form-error">{form.formState.errors.croppingType.message}</p>
        )}
      </div>

      <div className="">
        <Label htmlFor="farmLocation">Farm Location</Label>
        <Input value={state.selected.value ? state.selected.value.farmer.farmerInfo.farm_address : ''} disabled />
      </div>
    </div>
  );
}
