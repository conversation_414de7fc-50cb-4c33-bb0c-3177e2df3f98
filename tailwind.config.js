/** @type {import('tailwindcss').Config} */

const colors = require('tailwindcss/colors');
const withMT = require('@material-tailwind/react/utils/withMT');

module.exports = withMT({
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/features/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      screens: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px', // or '1600px' - consider common desktop resolutions
        '3xl': '1920px', // Full HD - common desktop
        '4xl': '2560px', // QHD/2K - higher resolution monitors
        '5xl': '3840px', // UHD/4K - high-end displays
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)'],
        mono: ['var(--font-geist-mono)'],
        inter: ['Inter'],
        poppins: ['Poppins'],
        dmSans: ['DM Sans', 'sans-serif'],
      },
      colors: {
        ...colors,
        kitaph: {
          primary: {
            600: '#1d2866',
            DEFAULT: '#2B3674',
          },
          blue: {
            DEFAULT: '#274493',
          },
        },
        adminDashboard: {
          title: '#05004E',
          pink: {
            DEFAULT: '#FA5A7D',
            bg: '#FFE2E5',
          },
          green: {
            DEFAULT: '#3CD856',
            bg: '#DCFCE7',
          },
          blue: {
            DEFAULT: '#6BA4FB',
            bg: '#DCE9FC',
          },
          purple: {
            DEFAULT: '#BF83FF',
            bg: '#F3E8FF',
          },
        },
        nonloanHolder: {
          all: '#FFBCF8',
          excellent: '#99E7B3',
          veryGood: '#B1C7FF',
          good: '#E2C7FF',
          fair: '#FFBB89',
          poor: '#FFBBB4',
        },
        loanHolder: {
          all: '#FFBCF8',
          active: '#9DEDB8',
          paidOff: '#B1C7FF',
          gracePeriod: '#FFBB89',
          overdue: '#FFBBB4',
        },
        loanApplicants: {
          all: '#FFBCF8',
          approved: '#9DEDB8',
          pending: '#FFBB89',
          rejected: '#FFBBB4',
        },
        dashboard: {
          red: {
            DEFAULT: '#FFE2E5',
            foreground: '#DA5043',
          },
          yellow: {
            DEFAULT: '#FFF4DE',
            foreground: '#FFB000',
          },
          purple: {
            DEFAULT: '#F3E8FF',
            foreground: '#9D46FC',
          },
          blue: {
            DEFAULT: '#DCE5FC',
            foreground: '#7198FF',
          },
          green: {
            DEFAULT: '#DCFCE7',
            foreground: '#49C272',
          },
          'red-dark': {
            DEFAULT: '#FFBBB4',
            foreground: '#DA5043',
          },
        },
        progress: {
          green: {
            DEFAULT: '#56B12D',
            background: '#E2FBD7',
          },
          purple: {
            DEFAULT: '#A39DFF',
            background: '#DAD7FE',
          },
          yellow: {
            DEFAULT: '#E2B93B',
            background: '#FFE5D3',
          },
          blue: {
            DEFAULT: '#31DDF4',
            background: '#CCF8FE',
          },
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.3s ease-in-out',
        'accordion-up': 'accordion-up 0.3s ease-in-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
});
