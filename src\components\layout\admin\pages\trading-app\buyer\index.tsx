'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';
import { cn } from '@/lib/utils';

import Rate from '../components/rate';
import UserDefinedPriceSettings from '../components/user-defined-price-settings';
import { BuyerTable } from './components/buyer-table';
import { ColumnBuyer } from './components/buyer-table/columns';
import buyerState, { useBuyer } from './hooks/useBuyer';

const BUYER_TAB = [
  {
    name: 'List of Buyers',
    value: 'buyers-list',
  },
  {
    name: 'Pricing Configuration',
    value: 'pricing-config',
  },
];

export default function BuyerPage() {
  const buyerTab = useHookstate('buyers-list');
  const pagination = useHookstate(buyerState.params);
  const { data, getBuyers } = useBuyer();

  const searchDebounce = useHookstateDebounce(pagination.search, 500);
  const pageDebounce = useHookstateDebounce(pagination.page, 500);

  useEffect(() => {
    getBuyers();
  }, [pageDebounce, pagination.pageSize, searchDebounce]);

  return (
    <div>
      {/* Tab */}
      <ScrollArea className="w-[calc(100vw-48px)] sm:w-auto">
        <div className="mr-6 flex w-max items-center gap-8 pb-1">
          {BUYER_TAB.map((tab) => {
            const isSelected = tab.value === buyerTab.value;

            return (
              <button
                key={tab.value}
                className={cn(
                  'transition-all duration-300 ease-in-out min-w-max',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => buyerTab.set(tab.value)}
              >
                {tab.name}
              </button>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      <div className="mt-6">
        {buyerTab.value === 'pricing-config' && (
          <>
            <Rate userType={UserType.NONFARMER} />
            <UserDefinedPriceSettings userType={UserType.NONFARMER} />
          </>
        )}

        {buyerTab.value === 'buyers-list' && (
          <>
            <BuyerTable columns={ColumnBuyer} data={data.data} metadata={data.meta} />
          </>
        )}
      </div>
    </div>
  );
}
