'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { useGlobalStatePersist } from '@/lib/store/persist';

import { rolePaths } from '../useLogin';

export default function useProtected() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();

  useEffect(() => {
    // Retrieving the user object from the persisted global state.
    const user = gStateP['user'].value;

    // If no user is found, redirect to the homepage.
    if (!user) {
      router.push('/');
      return;
    }

    const userHasRequiredRole = Object.entries(rolePaths).some(([role, path]) => {
      if (pathname.includes(path)) {
        return user[role];
      }
      return false;
    });

    // If the user does not have the required role for the current path, redirect them to the homepage
    if (!userHasRequiredRole) {
      router.push('/');
    }

    // If the user passes all checks, stop the loading state.
    setLoading(false);
  }, []);

  return { loading };
}
