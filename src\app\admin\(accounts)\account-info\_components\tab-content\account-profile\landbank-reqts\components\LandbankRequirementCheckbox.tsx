'use client';

import { useEffect } from 'react';

import { Checkbox } from '@/components/ui/checkbox';

import { useGlobalState } from '@/lib/store';

export const LandbankRequirementCheckbox = ({ row }) => {
  const gState = useGlobalState();
  const existingReq = row.original;
  const attachment = existingReq?.attachment;

  useEffect(() => {
    if (gState.landbankReqs.selectAll.get() && attachment) {
      row.toggleSelected(true);
      if (!gState.landbankReqs.selectedAttachments.get().includes(attachment)) {
        gState.landbankReqs.selectedAttachments.merge([attachment]);
      }
    }
  }, [gState.landbankReqs.selectAll]);

  const handleCheckedChange = (checked: boolean | string) => {
    const isChecked = checked === true;
    row.toggleSelected(isChecked);

    gState.landbankReqs.selectedAttachments.set((prev) =>
      isChecked ? [...prev, attachment] : prev.filter((url) => url !== attachment),
    );
  };

  return attachment ? (
    <Checkbox checked={row.getIsSelected()} onCheckedChange={handleCheckedChange} aria-label="Select row" />
  ) : null;
};
