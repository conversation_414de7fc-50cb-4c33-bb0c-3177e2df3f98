'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Plus, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { STEPPER_FORM } from '@/lib/constants';
import useFarmer, { FarmerSchema } from '@/lib/hooks/useFarmer';
import { UserSchema } from '@/lib/hooks/useUsers';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';
import { useStateContext } from '@/providers/app/state';

import { FamilyRelationshipEnums, FarmerOccupationEnum, GovernmentIdentificationEnum } from './Enums';

export default function Step3() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { updateFarmer } = useFarmer();
  const { setState, state } = useStateContext();
  const data = gStateP.admin.members['details'].get({ noproxy: true });

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      familyProfile:
        data.farmer.familyProfiles.map((s) => ({
          name: s.name,
          relationship: s.relationship,
          birthDate: s.birth_date,
          occupation: s.occupation,
          occupationEmployerName: s.occupation_employer_name,
          occupationAnnualIncome: s.occupation_annual_income,
          isBarbazaMember: `${s.is_barbaza_member}`,
          isBeneficiaries: `${s.is_beneficiaries}`,
        })) || [],
      // familyProfile: [
      //   {
      //     name: '',
      //     relationship: '',
      //     birthDate: '',
      //     occupation: '',
      //     occupationEmployerName: '',
      //     occupationAnnualIncome: '',
      //     isBarbazaMember: '',
      //     isBeneficiaries: '',
      //   },
      // ],
    },
  });
  const familyProfile = useFieldArray({
    name: 'familyProfile',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      userId: data.farmer.user_id,
    };

    console.log('Family Profile: ', updatedData);
    updateFarmer(updatedData);
    // setState((v) => ({
    //   ...v,
    //   ..._data,
    //   userId: data.id,
    // }));
    // !gState.stepper.isLastStep.value && gState.stepper.activeStep.set((cur) => cur + 1);
  };

  return (
    <form id={STEPPER_FORM[3]} onSubmit={handleSubmit(onSubmit)}>
      <div className="font-dmSans text-xl font-bold text-primary">Family Members</div>
      <div className="space-y-4">
        <div className="space-y-4 divide-y-2 divide-dashed pr-10">
          {familyProfile.fields.map((field, index) => {
            const errorForField = errors?.familyProfile?.[index];

            return (
              <div key={field.id} className="grid grid-cols-3 items-start gap-4 pb-3 pt-7">
                {/* Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.name`} className="pb-1 font-normal">
                    First, Middle, Last Name
                  </Label>
                  <Input
                    {...register(`familyProfile.${index}.name` as const, {
                      required: false,
                      validate: {
                        isWrongPattern: (v) =>
                          v ? /^(\w+(\s\w+)?)?\s?\w+$/.test(v) || 'Invalid First Middle Last Name' : true,
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.name && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter First, Middle, Last Name"
                  />
                  {errorForField?.name && <p className="form-error">{`${errorForField?.name?.message}`}</p>}
                </div>

                {/* Relationship */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.relationship`} className="pb-1 font-normal">
                    Relationship
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.relationship` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.relationship &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select Relationship" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {Object.values(FamilyRelationshipEnums).map((familyRelationship) => (
                              <SelectItem key={familyRelationship} value={familyRelationship}>
                                {familyRelationship}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.relationship && (
                    <p className="form-error">{`${errorForField?.relationship?.message}`}</p>
                  )}
                </div>

                {/* Birthdate */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.birthDate`} className="pb-1 font-normal">
                    Birth Date
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`familyProfile.${index}.birthDate` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.birthDate && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="date"
                      placeholder="Enter Birthdate"
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => familyProfile.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.birthDate && <p className="form-error">{`${errorForField?.birthDate?.message}`}</p>}
                </div>

                {/* Occupation */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.occupation`} className="pb-1 font-normal">
                    Occupation
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.occupation` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.occupation &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select Occupation" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {Object.values(FarmerOccupationEnum).map((occu) => (
                              <SelectItem key={occu} value={occu}>
                                {occu}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.occupation && <p className="form-error">{`${errorForField?.occupation?.message}`}</p>}
                </div>

                {/* Employer Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.occupationEmployerName`} className="pb-1 font-normal">
                    Employer Name
                  </Label>
                  <Input
                    {...register(`familyProfile.${index}.occupationEmployerName` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.occupationEmployerName && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Employer Name"
                  />
                  {errorForField?.occupationEmployerName && (
                    <p className="form-error">{`${errorForField?.occupationEmployerName?.message}`}</p>
                  )}
                </div>

                {/* Annual Income */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.occupationAnnualIncome`} className="pb-1 font-normal">
                    Annual Income
                  </Label>
                  <Input
                    {...register(`familyProfile.${index}.occupationAnnualIncome` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.occupationAnnualIncome && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Annual Income"
                  />
                  {errorForField?.occupationAnnualIncome && (
                    <p className="form-error">{`${errorForField?.occupationAnnualIncome?.message}`}</p>
                  )}
                </div>

                {/* Barbaza Member */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.isBarbazaMember`} className="pb-1 font-normal">
                    Barbaza MPC Member
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.isBarbazaMember` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.isBarbazaMember &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Barbaza Member?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="0">NO</SelectItem>
                            <SelectItem value="1">YES</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.isBarbazaMember && (
                    <p className="form-error">{`${errorForField?.isBarbazaMember?.message}`}</p>
                  )}
                </div>

                {/* Beneficiary */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`familyProfile.${index}.isBeneficiaries`} className="pb-1 font-normal">
                    Add to your Beneficiaries
                  </Label>
                  <Controller
                    control={control}
                    name={`familyProfile.${index}.isBeneficiaries` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.isBeneficiaries &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Add to your beneficiaries?" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="0">NO</SelectItem>
                            <SelectItem value="1">YES</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.isBeneficiaries && (
                    <p className="form-error">{`${errorForField?.isBeneficiaries?.message}`}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              familyProfile.append({
                name: '',
                relationship: '',
                birthDate: '',
                occupation: '',
                occupationEmployerName: '',
                occupationAnnualIncome: '',
                isBarbazaMember: '',
                isBeneficiaries: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Family Member</span>
          </Button>
        </div>
      </div>
    </form>
  );
}
