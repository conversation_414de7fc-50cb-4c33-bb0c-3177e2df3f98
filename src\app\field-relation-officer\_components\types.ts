export interface ISafeParseResult<T> {
  success: boolean;
  data: T;
}

export interface IResponse {
  status: number;
  message: string;
}

export interface IDataPrivacy {
  isAgreeUsingData: number;
  isAgreeVisitingFarm: number;
  isAgreeSharingData: number;
  userImage: string | File;
  signature: string | File;
}

export interface IGovernmentIdentification {
  governmentIdType?: string;
  governmentIdNumber?: string;
  upload?: string | File | null;
}

export interface IPersonalInformation {
  // Personal Details
  firstName: string;
  middleName: string;
  lastName: string;
  birthDate: string;
  gender: string;
  nationality: string;
  mobileNumber: string;
  placeOfBirth: string;
  email: string;
  telephoneNumber: string;
  facebookName: string;
  civilStatus: string;
  spouseName: string;
  spouseMobileNumber: string;

  // Present Address
  addressHouseNumber: string;
  addressStreet: string;
  addressRegion: string;
  addressProvince: string;
  addressCity: string;
  addressBarangay: string;
  addressZipCode: string;
  addressLengthOfStay: string;
  residenceOwnership: string;

  // Identification Docs
  governmentIdentification: IGovernmentIdentification[];
}

export interface IFarmInformation {
  // Farm Location
  farmAddressHouseNumber: string;
  farmAddressStreet: string;
  farmAddressRegion: string;
  farmAddressProvince: string;
  farmAddressCity: string;
  farmAddressBarangay: string;
  farmAddressZipCode: string;
  farmArea: string;
  farmOwnership: string;
  otherFarmOwnership: string;
  cropsPlanted: string[];

  // Farm Practices
  waterSource: string;
  waterSourceOthers: string;
  fertilizerUsed: string;
  pesticideUsed: string;
  farmImplements: string;
  farmImplementsOthers: string;
}

export interface IBusinessInfo {
  // Financial Information
  sourceOfFunds: string;
  monthlyGrossIncome: number;

  // Farm Business Information
  isMemberOfOrganization: string;
  organizationName: string;
  organizationPosition: string;
  hasPastFarmLoans: string;
  pastFarmLoans: string;
  hasPastFarmLoanPaid: string;
  hasNeedFarmLoan: string;
  needFarmLoanReason: string;
  isInterestedToSellAtTradingPost: string;

  // Purchaser Information
  purchaserSellingLocation: string;
  purchaserFullname: string;
  purchaserContactNumber: string;
}

export interface IProvince {
  province_code: string;
  province_name: string;
  psgc_code: string;
  region_code: string;
}

export interface IRegion {
  id: number;
  psgc_code: string;
  region_name: string;
  region_code: string;
}

export interface ICity {
  city_code: string;
  city_name: string;
  province_code: string;
  psgc_code: string;
  region_code: string;
}

export interface IBarangay {
  brgy_code: string;
  brgy_name: string;
  city_code: string;
  province_code: string;
  region_code: string;
}

export interface ICrop {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
  harvest_days: number;
  is_sync: number;
  image: string | null;
  keywords: string | null;
  sap_item_code: string | null;
}

export interface ICropPlanted {
  id: number;
  farmer_id: number;
  crop_id: number;
  created_at: string;
  updated_at: string;
  crop: ICrop;
}

export interface IFarmerInfo {
  id: number;
  farmer_id: number;
  farm_address: string | null;
  farm_address_street: string | null;
  farm_area: number;
  farm_ownership: number;
  created_at: string;
  updated_at: string;
  nationality: string;
  other_mobile_number: string | null;
  year_residing: number | null;
  residence_ownership: string;
  price_based_by: string | null;
  purchaser_selling_location: string;
  purchaser_fullname: string;
  purchaser_contact_number: string;
  farm_address_house_number: string;
  farm_address_province: string;
  farm_address_city: string;
  farm_address_barangay: string;
  farm_address_zip_code: string;
  farm_address_country: string | null;
  other_farm_ownership: string | null;
  water_source: string;
  farm_implements: string;
  fertilizer_used: number;
  pesticide_used: number;
  monthly_gross_income: number;
  is_member_of_organization: number;
  organization_name: string;
  organization_position: string;
  has_past_farm_loans: number;
  past_farm_loans: string;
  has_past_farm_loan_paid: number;
  has_need_farm_loan: number;
  need_farm_loan_reason: string;
  is_interested_to_sell_at_trading_post: number;
}

export interface IFarmerDataPrivacy {
  id: number;
  farmer_id: number;
  is_agree_using_data: number;
  is_agree_sharing_data: number;
  is_agree_visiting_farm: number;
  full_name: string | null;
  user_img: string | null;
  created_at: string;
  updated_at: string;
  signature: string | null;
}

export interface IFarmerInsurance {
  id: number;
  farmer_id: number;
  land_category: string | null;
  crop: string | null;
  phase: string | null;
  owner_cultivator: string | null;
  tenant: string | null;
  clt_ep: string | null;
  lessee: string | null;
  natural_disaster_cover: string | null;
  multi_risk_cover: string | null;
  desired_amount_cover: number;
  additional_amount_cover: number;
  total_amount_cover: number;
  transplanting_date: string | null;
  harvest_date: string | null;
  sowing_date: string | null;
  seedbedding: Record<string, any>;
  planting: Record<string, any>;
  plant_care: Record<string, any>;
  insurance_premium: string | null;
  insurance_location_plan: string | null;
  created_at: string;
  updated_at: string;
}

export interface IFarmer {
  id: number;
  user_id: number;
  first_name: string;
  middle_name: string;
  last_name: string;
  birth_date: string;
  place_of_birth: string | null;
  religion: string | null;
  gender: string;
  civil_status: string;
  height: number | null;
  weight: number | null;
  mobile_number: string;
  address: string;
  address_street: string;
  address_house_number: string;
  address_province: string; // JSON stringified Province
  address_city: string; // JSON stringified City
  address_barangay: string; // JSON stringified Barangay
  address_zip_code: string;
  educational_attainment: string | null;
  educational_is_graduate: boolean | null;
  educational_degree: string | null;
  occupation: string | null;
  occupation_status: string | null;
  occupation_employer_name: string | null;
  occupation_employer_address: string | null;
  occupation_business_name: string | null;
  occupation_business_address: string | null;
  occupation_business_contact: string | null;
  occupation_annual_income: number | null;
  occupation_title: string | null;
  source_of_funds: string;
  skills_farming: string | null;
  skills_fishing: string | null;
  skills_livestock: string | null;
  skills_construction: string | null;
  skills_processing: string | null;
  skills_servicing: string | null;
  skills_craft: string | null;
  skills_others: string | null;
  vehicle_owned: string | null;
  status: number;
  created_at: string;
  updated_at: string;
  has_biometric: number;
  qr_code: string;
  has_loan: number;
  has_submitted_loan_application: number;
  permanent_address: string;
  permanent_address_street: string;
  permanent_address_house_number: string | null;
  permanent_address_province: string | null;
  permanent_address_city: string | null;
  permanent_address_barangay: string | null;
  permanent_address_zip_code: string | null;
  permanent_address_length_of_stay: number | null;
  address_length_of_stay: number;
  has_facial_recognition: number;
  telephone_number: string | null;
  facebook_name: string | null;
  mothers_maiden_name: string | null;
  spouse_mobile_number: string | null;
  spouse_name: string | null;
  farmerInfo: IFarmerInfo;
  farmerDataPrivacy: IFarmerDataPrivacy;
  farmerInsurance: IFarmerInsurance;
  cropsPlanted: ICropPlanted[];
  subCropsPlanted: any[];
  farmerVehicles: any[];
  farmerCreditScore: any;
  governmentIdentifications: any[];
  familyProfiles: any[];
  farmerBankDetails: any[];
  farmerCharacterReferences: any[];
  farmerVouchLeaders: any[];
  farmerVouchMaos: any[];
  farmerReferrers: any[];
  farmerUtilities: any[];
  farmerMobileDevice: any;
  landbank_accounts: any;
}

export interface IWallet {
  id: number;
  user_id: number;
  balance: number;
  credit: number;
  payment: number;
  created_at: string;
  updated_at: string;
}

export interface IFarmerBase {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: string | null;
  remember_me_token: string | null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: string | null;
  wallet: IWallet;
  farmer: IFarmer;
}

export interface IProvince {
  province_code: string;
  province_name: string;
  psgc_code: string;
  region_code: string;
}

export interface ICity {
  city_code: string;
  city_name: string;
  province_code: string;
  psgc_code: string;
  region_code: string;
}

export interface IBarangay {
  brgy_code: string;
  brgy_name: string;
  city_code: string;
  province_code: string;
  region_code: string;
}

export interface IParsedAddress {
  addressHouseNumber: string;
  addressProvince: string; //IProvince
  addressCity: string; //ICity
  addressBarangay: string; //IBarangay
  addressZipCode: string;
}

export interface IFarmerFarmPlan {}

export interface IFROFarmersState {
  data: [];
  metadata: any;
  edit: IFarmerBase;
  farmPlan: IFarmerFarmPlan;
}

export interface IFROFormSteps {
  activeStep: number;
  step1: IDataPrivacy | null;
  step2: IPersonalInformation | null;
  step3: IFarmInformation | null;
  step4: IBusinessInfo | null;
}

export interface IFROState {
  farmers: IFROFarmersState;
  form: IFROFormSteps;
}
