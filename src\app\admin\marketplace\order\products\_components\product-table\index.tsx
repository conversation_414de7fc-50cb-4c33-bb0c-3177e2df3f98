'use client';

import { none, useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { ChevronDownIcon, Minus, Plus, ShoppingBasket } from 'lucide-react';
import { useState } from 'react';
import { GiChemicalTank, GiFertilizerBag, GiFruitBowl, GiGreenhouse, GiRopeCoil, GiSeedling } from 'react-icons/gi';
import { toast } from 'sonner';

import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { Tab, TabItem, tabState } from '@/components/Tab';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';

import { PRODUCT_TYPE, PRODUCT_TYPE_ACCESSOR } from '@/app/admin/marketplace/_components/Enums';
import useHtmlToText from '@/lib/hooks/utils/useHtmlToText';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { capitalize, cn } from '@/lib/utils';

import CartSheet from '../Cart';

export default function ProductTable({ data, columns, meta = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const htmlToText = useHtmlToText();
  const subCat = useHookstate(gState.admin.chemicals.subcategory);
  const subCatSeed = useHookstate(gState.admin.seeds.subcategory);
  const cat = useHookstate(gState.admin.pagination.products.productTypeCategory);

  const cropTab = useHookstate(tabState);
  const isSeeds = useHookstate(false);
  const isCropProtection = useHookstate(false);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  return (
    <div>
      {/* Search */}
      <div className="mb-6 flex">
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder="Search product name"
            onChange={(event) => {
              if (meta) {
                gState.admin.pagination.products.page.set(1);
                gState.admin.pagination.products.search.set(event.target.value);
              } else {
                table.setGlobalFilter(event.target.value);
              }
            }}
            className="h-8 w-[150px] focus-visible:ring-primary lg:w-[250px]"
          />
        </div>

        <div>
          <CartSheet />
        </div>
      </div>

      {/* Tabs */}
      <div className="grid gap-4 pb-8 pt-2">
        <Tab
          className="justify-center gap-4"
          defaultValue="all"
          onValueChange={(v) => {
            if (v === '3' || v === '1') {
              cat.set([]);
            }

            if (v === 'all') {
              // table.setGlobalFilter('');
              gState.admin.pagination.products.productType.set([]);
            } else {
              // table.setGlobalFilter(v);
              gState.admin.pagination.products.page.set(1);
              gState.admin.pagination.products.productType.set([v]);
            }
          }}
        >
          <TabItem value="all">
            <GiGreenhouse className="size-6" />
            <span className="text-lg">View All</span>
          </TabItem>
          <TabItem value="0">
            <GiFruitBowl className="size-6" />
            <span className="text-lg">Crops</span>
          </TabItem>
          <TabItem value="2">
            <GiFertilizerBag className="size-6" />
            <span className="text-lg">Fertilizers</span>
          </TabItem>
          <TabItem value="1">
            <DropdownMenu
              open={isSeeds.value}
              onOpenChange={(v) => {
                if (cropTab.value === '1') {
                  isSeeds.set(v);
                }
              }}
            >
              <DropdownMenuTrigger asChild>
                <div className="flex items-center justify-between gap-2 outline-none">
                  <GiSeedling className="size-6" />
                  <span className="text-lg">Seeds</span>
                  <span>
                    <ChevronDownIcon className="size-6" />
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="center">
                <DropdownMenuRadioGroup
                  value={cat.length > 0 ? cat[0].value : 'all'}
                  onValueChange={(v) => {
                    if (v === 'all') {
                      cat.set([]);
                    } else {
                      cat.set([v]);
                    }
                  }}
                >
                  <DropdownMenuRadioItem value="all">ALL</DropdownMenuRadioItem>
                  {subCatSeed
                    .get({ noproxy: true })
                    .filter((x) => x.status === 1)
                    .map((sub) => (
                      <DropdownMenuRadioItem key={sub.id} value={sub.id}>
                        {sub.name}
                      </DropdownMenuRadioItem>
                    ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </TabItem>
          <TabItem value="3">
            <DropdownMenu
              open={isCropProtection.value}
              onOpenChange={(v) => {
                if (cropTab.value === '3') {
                  isCropProtection.set(v);
                }
              }}
            >
              <DropdownMenuTrigger asChild>
                <div className="flex items-center justify-between gap-2 outline-none">
                  <GiChemicalTank className="size-6" />
                  <span className="text-lg">Crop Protection</span>
                  <span>
                    <ChevronDownIcon className="size-6" />
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="center">
                <DropdownMenuRadioGroup
                  value={cat.length > 0 ? cat[0].value : 'all'}
                  onValueChange={(v) => {
                    if (v === 'all') {
                      cat.set([]);
                    } else {
                      cat.set([v]);
                    }
                  }}
                >
                  <DropdownMenuRadioItem value="all">ALL</DropdownMenuRadioItem>
                  {subCat
                    .get({ noproxy: true })
                    .filter((x) => x.status === 1)
                    .map((sub) => (
                      <DropdownMenuRadioItem key={sub.id} value={sub.id}>
                        {sub.name}
                      </DropdownMenuRadioItem>
                    ))}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </TabItem>
          <TabItem value="4">
            <GiRopeCoil className="size-6" />
            <span className="text-lg">Others</span>
          </TabItem>
        </Tab>
      </div>

      {/* Contents */}
      <div className={cn('grid', table.getRowModel().rows?.length ? 'grid-cols-5 gap-12' : '')}>
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row) => {
            const data = row.original as any;

            // Check if already exist, if yes add quantity
            const existingProduct = gStateP.admin.orders.data.find(
              (product) => product['marketplaceProductId'].value === data.id,
            );

            let accessor = PRODUCT_TYPE_ACCESSOR[Number(data.product_type)];
            const product = data[accessor];
            const description = htmlToText.convert(data.description);
            const weight = data.weight || 0;
            const unit = data.unit || '';

            const isSeed = Number(data.product_type) === PRODUCT_TYPE.SEEDS;
            const isChemical = Number(data.product_type) === PRODUCT_TYPE.CHEMICALS;
            const hasSub = isSeed || isChemical;
            const isLowStocks = Number(data.stocks) < data.stocks_warning;
            const isOutOfStocks = Number(data.stocks) === 0;

            accessor = accessor === 'chemical' ? 'crop protection' : accessor;

            const onMinus = () => {
              if (existingProduct['quantity'].value === 1) {
                existingProduct.set(none);
                return;
              }

              if (existingProduct['quantity'].value > 1) {
                existingProduct['quantity'].set((v) => v - 1);
              }
            };
            const onAdd = () => {
              if (data.stocks - existingProduct['quantity'].value === 0) {
                return;
              }
              existingProduct['quantity'].set((v) => v + 1);
            };

            return (
              <div key={data.id} className="font-dmSans flex flex-col">
                <Dialog>
                  <DialogTrigger asChild>
                    <div className="cursor-pointer overflow-hidden rounded-2xl transition duration-300 ease-in-out hover:shadow-lg hover:shadow-amber-500/50">
                      <img
                        className="mx-auto h-48 object-cover object-center"
                        src={data.image || '/assets/default-product.png'}
                        alt={product.name}
                      />
                    </div>
                  </DialogTrigger>
                  <DialogContent className="p-0 sm:max-w-[525px]">
                    <ScrollArea className="h-[70vh]">
                      <div className="p-9">
                        <div className="flex justify-center">
                          <div className="overflow-hidden rounded-2xl">
                            <img
                              className="mx-auto h-48 object-cover object-center"
                              src={data.image || '/assets/default-product.png'}
                              alt={product.name}
                            />
                          </div>
                        </div>

                        <div className="my-4 flex flex-1 flex-col">
                          <div className="mt-3 line-clamp-2 font-semibold text-primary">
                            {`${product.name} - ${weight} ${unit}`}
                          </div>
                        </div>

                        {/* Category */}
                        <div className="flex items-center gap-2 pt-2">
                          <div>
                            <img src="/assets/icon/category.svg" alt="" />
                          </div>
                          <div className="flex text-sm capitalize text-[#A3AED0]">
                            <div>{`${accessor}`}</div>
                            <div>{`${
                              hasSub
                                ? `, ${
                                    isSeed
                                      ? capitalize(product.seedSubcategory ? product.seedSubcategory.name : 'N/A')
                                      : capitalize(
                                          product.chemicalSubcategory ? product.chemicalSubcategory.name : 'N/A',
                                        )
                                  }`
                                : ''
                            }`}</div>
                          </div>
                        </div>

                        <div className="mt-2 flex items-center justify-between">
                          {isLowStocks ? (
                            <div className="flex translate-x-[-6px] items-center gap-2 rounded-md bg-[#F24822] px-2 py-px text-white">
                              <ShoppingBasket className="size-6" />
                              <div className="">{`${
                                existingProduct ? data.stocks - existingProduct['quantity'].value : data.stocks
                              } pcs left`}</div>
                            </div>
                          ) : (
                            <div className="flex gap-2 text-[#A3AED0]">
                              <ShoppingBasket className="size-6" />
                              <div>{`${
                                existingProduct ? data.stocks - existingProduct['quantity'].value : data.stocks
                              } pcs`}</div>
                            </div>
                          )}

                          <div className="text-lg font-semibold text-[#ED6E11]">
                            {Number(data.price).toLocaleString('en-US', {
                              style: 'currency',
                              currency: 'PHP',
                            })}
                          </div>
                        </div>

                        <div className="mt-6">
                          <div className="flex capitalize text-[#A3AED0]">Product Description</div>
                          <div
                            className={cn(
                              'prose max-w-none mt-2',
                              'prose-figcaption:text-center',
                              'prose-figure:flex prose-figure:flex-col prose-figure:items-center',
                              'prose-img:object-cover',
                            )}
                            dangerouslySetInnerHTML={{
                              __html: data.description,
                            }}
                          />
                        </div>

                        <div className="mt-4">
                          {/* quantity */}
                          {existingProduct ? (
                            <div className="flex h-[32px] items-center rounded-full border border-[#0095FF] p-1">
                              <Button className="size-6 rounded-full" variant="blue" size="icon" onClick={onMinus}>
                                <Minus className="size-4" />
                              </Button>

                              <div className="flex-1 px-4 text-center font-bold">
                                <input
                                  className="no-spinner w-20 text-center outline-none"
                                  type="text"
                                  value={existingProduct['quantity'].value}
                                  onChange={(e) => {
                                    const parsedValue = Number(e.target.value);
                                    if (parsedValue >= 0 && parsedValue <= data.stocks) {
                                      if (parsedValue === 0) {
                                        existingProduct.set(none);
                                        return;
                                      }
                                      existingProduct['quantity'].set(parsedValue);
                                    }
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.key === 'ArrowUp') {
                                      e.preventDefault();
                                      onAdd();
                                    }
                                    if (e.key === 'ArrowDown') {
                                      e.preventDefault();
                                      onMinus();
                                    }
                                  }}
                                />
                              </div>

                              <Button
                                className="size-6 rounded-full"
                                variant="blue"
                                size="icon"
                                onClick={onAdd}
                                disabled={data.stocks - existingProduct['quantity'].value === 0}
                              >
                                <Plus className="size-4" />
                              </Button>
                            </div>
                          ) : (
                            <Button
                              className="h-8 w-full rounded-full"
                              variant="blue"
                              disabled={isOutOfStocks}
                              onClick={() => {
                                gStateP.admin.orders.data.merge([
                                  {
                                    data: data,
                                    name: product.name,
                                    price: data.price,
                                    marketplaceProductId: data.id,
                                    quantity: 1,
                                  },
                                ]);
                                toast.success('Success', {
                                  description: 'Product added to cart.',
                                });
                              }}
                            >
                              Add to Cart
                            </Button>
                          )}
                        </div>
                      </div>
                    </ScrollArea>
                  </DialogContent>
                </Dialog>

                <div className="flex flex-1 flex-col">
                  <div className="mt-3 line-clamp-2 min-h-[48px] font-semibold text-primary">
                    {`${product.name} - ${weight} ${unit}`}
                  </div>
                </div>

                {/* Category */}
                <div className="flex items-center gap-2 pt-2">
                  <div>
                    <img src="/assets/icon/category.svg" alt="" />
                  </div>
                  <div className="flex text-sm capitalize text-[#A3AED0]">
                    <div>{`${accessor}`}</div>
                    <div>{`${
                      hasSub
                        ? `, ${
                            isSeed
                              ? capitalize(product.seedSubcategory ? product.seedSubcategory.name : 'N/A')
                              : capitalize(product.chemicalSubcategory ? product.chemicalSubcategory.name : 'N/A')
                          }`
                        : ''
                    }`}</div>
                  </div>
                </div>

                {/* Stocks */}
                <div className="flex items-center justify-between pt-1">
                  {isLowStocks ? (
                    <div className="flex translate-x-[-6px] items-center gap-2 rounded-md bg-[#F24822] px-2 py-px text-white">
                      <ShoppingBasket className="size-6" />
                      <div className="">{`${
                        existingProduct ? data.stocks - existingProduct['quantity'].value : data.stocks
                      } pcs left`}</div>
                    </div>
                  ) : (
                    <div className="flex gap-2 text-[#A3AED0]">
                      <ShoppingBasket className="size-6" />
                      <div>{`${
                        existingProduct ? data.stocks - existingProduct['quantity'].value : data.stocks
                      } pcs`}</div>
                    </div>
                  )}

                  <div className="font-semibold text-[#ED6E11]">
                    {Number(data.price).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="mt-4">
                  {/* quantity */}
                  {existingProduct ? (
                    <div className="flex h-[32px] items-center rounded-full border border-[#0095FF] p-1">
                      <Button className="size-6 rounded-full" variant="blue" size="icon" onClick={onMinus}>
                        <Minus className="size-4" />
                      </Button>

                      <div className="flex-1 px-4 text-center font-bold">
                        <input
                          className="no-spinner w-20 text-center outline-none"
                          type="text"
                          value={existingProduct['quantity'].value}
                          onChange={(e) => {
                            const parsedValue = Number(e.target.value);
                            if (parsedValue >= 0 && parsedValue <= data.stocks) {
                              if (parsedValue === 0) {
                                existingProduct.set(none);
                                return;
                              }
                              existingProduct['quantity'].set(parsedValue);
                            }
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'ArrowUp') {
                              e.preventDefault();
                              onAdd();
                            }
                            if (e.key === 'ArrowDown') {
                              e.preventDefault();
                              onMinus();
                            }
                          }}
                        />
                      </div>

                      <Button
                        className="size-6 rounded-full"
                        variant="blue"
                        size="icon"
                        onClick={onAdd}
                        disabled={data.stocks - existingProduct['quantity'].value === 0}
                      >
                        <Plus className="size-4" />
                      </Button>
                    </div>
                  ) : (
                    <Button
                      className="h-8 w-full rounded-full"
                      variant="blue"
                      disabled={isOutOfStocks}
                      onClick={() => {
                        gStateP.admin.orders.data.merge([
                          {
                            data: data,
                            name: product.name,
                            price: data.price,
                            marketplaceProductId: data.id,
                            quantity: 1,
                          },
                        ]);
                        toast('Success', { description: 'Product added to cart.' });
                      }}
                    >
                      Add to Cart
                    </Button>
                  )}
                </div>
              </div>
            );
          })
        ) : (
          <div>
            <div className="flex h-24 items-center justify-center text-xl text-gray-500">No results.</div>
          </div>
        )}
      </div>

      {/* Pagination */}
      <div className="mt-12">
        {meta ? (
          <DataTablePaginationMeta
            table={table}
            meta={meta}
            onChangePageSize={(pageSize) => {
              gState.admin.pagination.products.pageSize.set(pageSize);
            }}
            onChangePage={(page) => {
              gState.admin.pagination.products.page.set(page);
            }}
          />
        ) : (
          <DataTablePagination table={table} />
        )}
      </div>
    </div>
  );
}
