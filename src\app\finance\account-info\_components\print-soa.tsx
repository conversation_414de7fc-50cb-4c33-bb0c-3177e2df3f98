'use client';

import { format } from 'date-fns';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalStatePersist } from '@/lib/store/persist';
import { ISoaData } from '@/lib/store/soa-store.types';
import { cn, toDecimal } from '@/lib/utils';

interface IPrintSoaProps {
  contentRef: any;
  orderInfo: ISoaData;
}

export default function PrintSoa({ contentRef, orderInfo }: IPrintSoaProps) {
  const gStateP = useGlobalStatePersist();

  return (
    <table ref={contentRef} className="hidden w-full font-dmSans print:table">
      <thead>
        <tr>
          <td>
            <div className="header-space">&nbsp;</div>
          </td>
        </tr>
      </thead>

      <tbody>
        <tr>
          <td>
            <div className="flex min-h-[calc(100vh-86px)] flex-col">
              {/* Header */}
              <div className="header fixed inset-x-0 top-0">
                <div className="flex items-center justify-between">
                  <div>
                    <img className="h-14" src="/kita-logo.png" alt="" />
                  </div>

                  <div className="text-right text-xs italic">
                    <div>{`Date & Time Generated: ${format(new Date(), 'MMM dd, yyyy - hh:mm a')}`}</div>
                    <div className="mt-0.5">{`Order ID No. ${orderInfo.formatted.referenceNumber}`}</div>
                  </div>
                </div>
              </div>

              <h1 className="mb-6 text-center text-2xl font-bold text-kitaph-blue">STATEMENT OF ACCOUNT</h1>

              {/* Farmer Info */}
              <div className="flex text-sm">
                <div className="grid grid-cols-3 gap-6">
                  <div>
                    <div>Farmer Name</div>
                    <div>Farmer Address</div>
                    <div>No. of Hectares</div>
                    <div>Main Crop To Be Planted</div>
                    <div>Crop Type</div>
                    <div>Sub Crop Planted</div>
                    <div>Total E-wallet Amount</div>
                  </div>
                  <div className="col-span-2 font-bold">
                    <div>
                      {orderInfo.formatted.farmerName ? (
                        orderInfo.formatted.farmerName
                      ) : (
                        <div className="invisible">-</div>
                      )}
                    </div>
                    <div>
                      {orderInfo.formatted.farmAddress ? (
                        orderInfo.formatted.farmAddress
                      ) : (
                        <div className="invisible">-</div>
                      )}
                    </div>
                    <div>
                      {orderInfo.formatted.farmArea ? orderInfo.formatted.farmArea : <div className="invisible">-</div>}
                    </div>
                    <div>
                      {orderInfo.formatted.mainCropPlanted ? (
                        orderInfo.formatted.mainCropPlanted
                      ) : (
                        <div className="invisible">-</div>
                      )}
                    </div>
                    <div>
                      {orderInfo.formatted.cropType ? orderInfo.formatted.cropType : <div className="invisible">-</div>}
                    </div>
                    <div>
                      {orderInfo.formatted.subCropPlanted ? (
                        orderInfo.formatted.subCropPlanted
                      ) : (
                        <div className="invisible">-</div>
                      )}
                    </div>
                    <div>Php {toDecimal(orderInfo.formatted.walletBalanceBefore || 0)}</div>
                  </div>
                </div>
              </div>

              {/* Tables */}
              <div className="space-y-4 py-8">
                {/* Crops */}
                {orderInfo.formatted.crops.length > 0 && (
                  <Table>
                    <TableHeader className="dark border-none bg-kitaph-blue">
                      <TableRow>
                        <TableHead className="h-8 w-full text-sm font-bold text-white">Crop</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="">
                      {orderInfo.formatted.crops.map((crop, index) => {
                        return (
                          <TableRow key={index} className="border-none">
                            <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                              {crop.product}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {crop.quantity}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.price || 0)}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.total || 0)}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}

                {/* Crop Protection Requirements */}
                {orderInfo.formatted.cropProtection.length > 0 && (
                  <Table>
                    <TableHeader className="dark border-none bg-kitaph-blue">
                      <TableRow className="">
                        <TableHead className="h-8 w-full text-sm font-bold text-white">
                          Crop Protection Requirements
                        </TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="">
                      {orderInfo.formatted.cropProtection.map((crop, index) => {
                        return (
                          <TableRow key={index} className="border-none">
                            <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                              {crop.product}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {crop.quantity}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.price || 0)}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.total || 0)}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}

                {/* Fertilizer Requirements */}
                {orderInfo.formatted.fertilizer.length > 0 && (
                  <Table>
                    <TableHeader className="dark border-none bg-kitaph-blue">
                      <TableRow className="">
                        <TableHead className="h-8 w-full text-sm font-bold text-white">
                          Fertilizer Requirements
                        </TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="">
                      {orderInfo.formatted.fertilizer.map((crop, index) => {
                        return (
                          <TableRow key={index} className="border-none">
                            <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                              {crop.product}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {crop.quantity}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.price || 0)}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.total || 0)}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}

                {/* Seeds Requirements */}
                {orderInfo.formatted.seeds.length > 0 && (
                  <Table>
                    <TableHeader className="dark border-none bg-kitaph-blue">
                      <TableRow className="">
                        <TableHead className="h-8 w-full text-sm font-bold text-white">Seeds Requirements</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="">
                      {orderInfo.formatted.seeds.map((crop, index) => {
                        return (
                          <TableRow key={index} className="border-none">
                            <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                              {crop.product}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {crop.quantity}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.price || 0)}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.total || 0)}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}

                {/* Miscellaneous */}
                {orderInfo.formatted.miscellaneous.length > 0 && (
                  <Table>
                    <TableHeader className="dark border-none bg-kitaph-blue">
                      <TableRow className="">
                        <TableHead className="h-8 w-full text-sm font-bold text-white">Miscellaneous</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                        <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="">
                      {orderInfo.formatted.miscellaneous.map((crop, index) => {
                        return (
                          <TableRow key={index} className="border-none">
                            <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                              {crop.product}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {crop.quantity}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.price || 0)}
                            </TableCell>
                            <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                              {toDecimal(crop.total || 0)}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}
              </div>

              {/* Summary */}
              <div className="flex justify-end pr-4 text-sm">
                <div className="grid grid-cols-2 gap-4 text-right">
                  <div>
                    <div className="mb-4 font-bold">Total Ordered Amount</div>
                    <div>E-wallet Payment</div>
                    <div>Cash Payment</div>
                    <div>E-wallet Balance</div>
                  </div>
                  <div className="text-[17px] font-bold">
                    <div className="mb-4">Php {toDecimal(orderInfo.formatted.totalPrice || 0)}</div>
                    <div>
                      Php{' '}
                      {toDecimal(
                        orderInfo.raw.payment_method === 2
                          ? orderInfo.formatted.totalPrice // E-wallet only
                          : orderInfo.raw.payment_method === 3
                            ? orderInfo.formatted?.walletAllocation || 0 // Multiple
                            : 0, // Cash only
                      )}
                    </div>
                    <div>
                      Php{' '}
                      {toDecimal(
                        orderInfo.raw.payment_method === 1
                          ? orderInfo.formatted.totalPrice // Cash only
                          : orderInfo.raw.payment_method === 3
                            ? orderInfo.formatted?.cashAllocation || 0 // Multiple
                            : 0, // E-wallet only
                      )}
                    </div>
                    <div>Php {toDecimal(orderInfo.formatted.walletBalanceAfter || 0)}</div>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex flex-1 items-end">
                <div className="flex w-full items-stretch">
                  <div className="w-1/4 border border-r-0 border-gray-500 p-2">
                    <div>Created By</div>
                    <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center">
                      {gStateP.user.value && gStateP.user.user.admin.value
                        ? `${gStateP.user.user.admin.first_name.value} ${gStateP.user.user.admin.last_name.value}`
                        : ``}
                    </div>
                  </div>

                  <div className="w-1/4 border border-r-0 border-gray-500 p-2">
                    <div>Checked By</div>
                    <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center"></div>
                  </div>

                  <div className="w-1/4 border border-r-0 border-gray-500 p-2">
                    <div>Approved By</div>
                    <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center"></div>
                  </div>

                  <div className="w-1/4 border border-gray-500 p-2">
                    <div>Received By</div>
                    <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center">
                      {orderInfo.formatted.farmerName}
                      {/* Hello World Hello World */}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </tbody>

      <style jsx>{`
        @media print {
          html,
          body {
            height: 100%;
            overflow: initial !important;
            -webkit-print-color-adjust: exact;
          }
          @page {
            size: A4;
            margin: 14mm;
            @bottom-left {
              content: 'Page ' counter(page) ' of ' counter(pages);
            }
          }
          .header,
          .header-space {
            height: 80px;
          }
        }
      `}</style>
    </table>
  );
}
