'use client';

import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

import { LoadingButton } from '@/components/common/loading/loading-button';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { useProducts } from '@/features/head-agronomist/store/products.store';
import { EProducttype } from '@/features/head-agronomist/types/product.types';
import { cn } from '@/lib/utils';

interface IEditSubItemDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onConfirm: (data: IEditSubItemFormData) => Promise<void>;
  confirmText?: string;
  isLoading?: boolean;
  productType: EProducttype | null;
  initialData?: IEditSubItemFormData;
}

export interface IEditSubItemFormData {
  farmPlanTemplateSubItemId: number;
  expectedDate: Date;
  itemName: string;
  unit: string;
  quantity: number;
  unitCost: number;
  notes: string;
  marketplaceProductId?: number;
  reason: string;
}

export default function EditSubItemDialog({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  confirmText = 'Update',
  isLoading = false,
  productType,
  initialData,
}: IEditSubItemDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { productsQuery } = useProducts(productType || EProducttype.SEEDS);

  const form = useForm<IEditSubItemFormData>({
    defaultValues: {
      farmPlanTemplateSubItemId: 0,
      expectedDate: new Date(),
      itemName: '',
      unit: '',
      quantity: 1,
      unitCost: 0,
      notes: '',
      marketplaceProductId: undefined,
      reason: '',
    },
  });

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = form;

  // Populate form with initial data when dialog opens
  useEffect(() => {
    if (open && initialData) {
      reset({
        farmPlanTemplateSubItemId: initialData.farmPlanTemplateSubItemId,
        expectedDate: new Date(initialData.expectedDate),
        itemName: initialData.itemName,
        unit: initialData.unit,
        quantity: initialData.quantity,
        unitCost: initialData.unitCost,
        notes: initialData.notes || '',
        marketplaceProductId: initialData.marketplaceProductId,
        reason: '', // Always start with empty reason
      });
    }
  }, [open, initialData, reset]);

  const onSubmit = async (data: IEditSubItemFormData, event?: React.FormEvent) => {
    // Prevent event bubbling to parent forms
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (!data.reason.trim()) {
      form.setError('reason', {
        type: 'required',
        message: 'Reason is required',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await onConfirm(data);
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error('Error in edit sub-item dialog:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleSubmit((data) => onSubmit(data, e))(e);
          }}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Expected Date */}
            <div className="space-y-2">
              <Label htmlFor="expectedDate">Expected Date *</Label>
              <Controller
                control={control}
                name="expectedDate"
                rules={{ required: 'Expected date is required' }}
                render={({ field }) => (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal',
                          !field.value && 'text-muted-foreground',
                        )}
                      >
                        <CalendarIcon className="mr-2 size-4" />
                        {field.value ? format(field.value, 'PPP') : <span>Pick a date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                    </PopoverContent>
                  </Popover>
                )}
              />
              {errors.expectedDate && <p className="text-sm text-red-500">{errors.expectedDate.message}</p>}
            </div>

            {/* Product Selection - Only show if productType is provided */}
            {productType && (
              <div className="space-y-2">
                <Label htmlFor="marketplaceProductId">Select Product</Label>
                <Controller
                  control={control}
                  name="marketplaceProductId"
                  render={({ field }) => (
                    <Select
                      value={field.value ? String(field.value) : ''}
                      onValueChange={(value) => {
                        field.onChange(value ? Number(value) : undefined);

                        // Find the selected product and update form fields
                        if (value && productsQuery.isSuccess) {
                          const selectedProduct = productsQuery.data.pages
                            .flatMap((page) => page.data)
                            .find((item) => item.id === Number(value));

                          if (selectedProduct) {
                            const productName =
                              selectedProduct.seed?.name ||
                              selectedProduct.fertilizer?.name ||
                              selectedProduct.chemical?.name ||
                              selectedProduct.crop?.name ||
                              selectedProduct.otherProduct?.name ||
                              'Unknown Product';
                            setValue('itemName', productName);
                            setValue('unit', selectedProduct.unit);
                            setValue('unitCost', selectedProduct.price);
                          }
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a product" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {productsQuery.isSuccess &&
                            productsQuery.data.pages?.map((page) =>
                              page.data.map((item) => {
                                const productName =
                                  item.seed?.name ||
                                  item.fertilizer?.name ||
                                  item.chemical?.name ||
                                  item.crop?.name ||
                                  item.otherProduct?.name ||
                                  'Unknown Product';
                                return (
                                  <SelectItem key={item.id} value={String(item.id)} disabled={item.stocks === 0}>
                                    <div
                                      className={cn('flex items-center justify-between gap-1', {
                                        'text-red-500 font-semibold': item.stocks === 0,
                                      })}
                                    >
                                      <img className="mr-1 size-5 rounded-full md:size-6" src={item.image} alt="" />
                                      <div className="line-clamp-1 text-xs md:text-sm">{productName}</div>
                                      <div className="text-xs md:text-sm">({item.stocks})</div>
                                    </div>
                                  </SelectItem>
                                );
                              }),
                            )}
                          {productsQuery.hasNextPage && (
                            <div>
                              {productsQuery.isFetching || productsQuery.isFetchingNextPage ? (
                                <LoadingButton variant="ghost" size="sm" className="h-8 w-full text-xs">
                                  Loading
                                </LoadingButton>
                              ) : (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-full text-xs"
                                  onClick={() => productsQuery.fetchNextPage()}
                                >
                                  Load More
                                </Button>
                              )}
                            </div>
                          )}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            )}

            {/* Item Name */}
            <div className="space-y-2">
              <Label htmlFor="itemName">Item Name *</Label>
              <Controller
                control={control}
                name="itemName"
                rules={{ required: 'Item name is required' }}
                render={({ field }) => (
                  <Input {...field} placeholder="Enter item name" className="w-full" disabled={!!productType} />
                )}
              />
              {errors.itemName && <p className="text-sm text-red-500">{errors.itemName.message}</p>}
            </div>

            {/* Unit */}
            <div className="space-y-2">
              <Label htmlFor="unit">Unit *</Label>
              <Controller
                control={control}
                name="unit"
                rules={{ required: 'Unit is required' }}
                render={({ field }) => (
                  <Input {...field} placeholder="Enter unit" className="w-full" disabled={!!productType} />
                )}
              />
              {errors.unit && <p className="text-sm text-red-500">{errors.unit.message}</p>}
            </div>

            {/* Quantity */}
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity *</Label>
              <Controller
                control={control}
                name="quantity"
                rules={{
                  required: 'Quantity is required',
                  min: { value: 0.01, message: 'Quantity must be greater than 0' },
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0.01"
                    placeholder="Enter quantity"
                    className="w-full"
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                )}
              />
              {errors.quantity && <p className="text-sm text-red-500">{errors.quantity.message}</p>}
            </div>

            {/* Unit Cost */}
            <div className="space-y-2">
              <Label htmlFor="unitCost">Unit Cost *</Label>
              <Controller
                control={control}
                name="unitCost"
                rules={{
                  required: 'Unit cost is required',
                  min: { value: 0, message: 'Unit cost must be 0 or greater' },
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="Enter unit cost"
                    className="w-full"
                    disabled={!!productType}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                )}
              />
              {errors.unitCost && <p className="text-sm text-red-500">{errors.unitCost.message}</p>}
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Controller
              control={control}
              name="notes"
              render={({ field }) => <Input {...field} placeholder="Enter notes (optional)" className="w-full" />}
            />
          </div>

          {/* Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason">Reason for change *</Label>
            <Controller
              control={control}
              name="reason"
              rules={{
                required: 'Reason is required',
                minLength: {
                  value: 3,
                  message: 'Reason must be at least 3 characters',
                },
                maxLength: {
                  value: 500,
                  message: 'Reason must not exceed 500 characters',
                },
              }}
              render={({ field }) => (
                <Textarea
                  {...field}
                  placeholder="Please provide a reason for this change..."
                  className="min-h-[100px] resize-none"
                />
              )}
            />
            {errors.reason && <p className="text-sm text-red-500">{errors.reason.message}</p>}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting || isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || isLoading}>
              {isSubmitting || isLoading ? 'Updating...' : confirmText}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
