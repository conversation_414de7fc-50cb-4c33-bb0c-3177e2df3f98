'use client';

import { Search } from 'lucide-react';
import { useState } from 'react';

import { SvgSpinnersBarsRotateFade } from '@/components/common/icons/SvgSpinnersBarsRotateFade';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';

import { cn, urlify } from '@/lib/utils';

import { IUserInfo } from '../../types/farmer.types';
import { getDisplayName } from '../../utils';

interface IFarmerSearchInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  isError?: boolean;
  isLoading?: boolean;
  farmers?: IUserInfo[];
  selectedFarmer?: IUserInfo | undefined;
  onFarmerSelect?: (farmer: IUserInfo | null) => void;
}

export default function FarmerSearchInput({
  value = '',
  onChange,
  label = 'Farmer Name',
  placeholder = 'Type to search...',
  isError = false,
  isLoading = false,
  farmers,
  selectedFarmer,
  onFarmerSelect,
}: IFarmerSearchInputProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="w-full">
      <Label htmlFor="farmer-search">{label}</Label>

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Input
              id="farmer-search"
              type="text"
              placeholder={placeholder}
              value={value}
              onChange={(e) => {
                onChange(e.target.value);
                if (!isOpen) {
                  setIsOpen(true);
                }
              }}
              className={cn('pr-10', isError && 'border-red-500 focus-visible:ring-red-500')}
              autoComplete="off"
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {isLoading ? (
                <SvgSpinnersBarsRotateFade className="size-4" />
              ) : (
                <Search className="size-4 text-gray-400" />
              )}
            </div>
          </div>
        </PopoverTrigger>

        <PopoverContent className="w-full max-w-none p-0" onOpenAutoFocus={(e) => e.preventDefault()}>
          <ScrollArea className="flex w-full flex-col p-1">
            {farmers.length > 0 ? (
              <div className="max-h-60 flex-1">
                {farmers.map((farmer) => (
                  <div
                    key={farmer.id}
                    className={cn(
                      'flex cursor-pointer items-center gap-3 rounded-md px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800',
                      {
                        'bg-gray-100 dark:bg-gray-800': selectedFarmer ? selectedFarmer.id === farmer.id : false,
                      },
                    )}
                    onClick={() => {
                      onFarmerSelect(farmer);
                      setIsOpen(false);
                      onChange(getDisplayName(farmer));
                      setIsOpen(false);
                    }}
                  >
                    <img
                      className="size-8 rounded-full object-cover"
                      src={farmer.user_img ? urlify(farmer.user_img, 'users/profile') : '/assets/user-default.jpg'}
                      alt="Profile"
                    />
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium">{getDisplayName(farmer)}</p>
                      <p className="truncate text-xs text-gray-500">{farmer.username}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-sm text-gray-500">
                {value ? 'No farmers found' : 'Start typing to search farmers'}
              </div>
            )}
          </ScrollArea>
        </PopoverContent>
      </Popover>
    </div>
  );
}
