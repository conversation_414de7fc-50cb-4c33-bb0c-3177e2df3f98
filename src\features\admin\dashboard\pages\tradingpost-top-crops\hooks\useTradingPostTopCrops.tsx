'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useQuery } from '@tanstack/react-query';
import { endOfMonth, startOfMonth } from 'date-fns';

import { ITradingPostTopCrops } from '@/features/admin/dashboard/types';
import axios from '@/lib/api';
import { useGlobalStatePersist } from '@/lib/store/persist';

// State
const initialState = {
  startDate: startOfMonth(new Date()),
  endDate: endOfMonth(new Date()),
};
const tradingPostTopCropsState = hookstate(initialState, devtools({ key: 'tradingPostTopCropsState' }));

// Fetcher
const fetchTradingPostTopCrops = async (currentUser = 'admin') => {
  const { data } = await axios.get(`/${currentUser}/dashboard/viewTradingpostTopProducts`, {
    params: {
      ...tradingPostTopCropsState.value,
    },
  });
  return data.data as ITradingPostTopCrops[];
};

// hooks
const useTradingPostTopCrops = (currentUser = 'admin') => {
  const state = useHookstate(tradingPostTopCropsState);
  const gStateP = useGlobalStatePersist();

  const tradingPostTopCropsQuery = useQuery({
    queryKey: [
      'tradingPostTopCrops-all',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...state.value,
      },
    ],
    queryFn: () => fetchTradingPostTopCrops(currentUser),
    enabled: !!gStateP.user.value,
  });

  return { state, tradingPostTopCropsQuery };
};

export { tradingPostTopCropsState };
export default useTradingPostTopCrops;
