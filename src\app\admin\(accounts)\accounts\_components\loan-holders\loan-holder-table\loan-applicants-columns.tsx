'use client';

import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { LoanApplicationLabels } from '@/lib/constants/enums';
import { cn } from '@/lib/utils';

export const LoanApplicantsColumns = [
  {
    id: 'loan_status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          <Badge className={cn('min-w-[100px] justify-center', LoanApplicationLabels[data.status].color)}>
            {LoanApplicationLabels[data.status].label}
          </Badge>
        </div>
      );
    },
    accessorFn: (row) => {
      return LoanApplicationLabels[row.status].label;
    },
  },
  {
    id: 'submitted_loans_to',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Submitted Loans to" />,
    accessorFn: (row) => {
      return row.credit_score_groups;
    },
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.user.farmer.first_name} {data.user.farmer.last_name}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.user.farmer.first_name} ${row.user.farmer.last_name}`;
    },
  },
  {
    id: 'account_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
    accessorFn: (row) => {
      return `ID${row.user.id.toString().padStart(9, '0')}`;
    },
  },
  {
    id: 'before_loan_score',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Before Loan Score" />,
    accessorFn: (row) => {
      return row.before_loan_score || 'N/A';
    },
  },
  {
    id: 'loan_cycle',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Cycle No." />,
    accessorFn: (row) => {
      return row.loan_cycle_number || 'N/A';
    },
  },
  {
    id: 'submission_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Submission Date & Time" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.created_at ? format(new Date(data.created_at), 'MMM dd, yyyy | hh:mm a') : 'N/A'}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.created_at ? format(new Date(row.created_at), 'MMM dd, yyyy | hh:mm a') : 'N/A';
    },
  },
  {
    id: 'loan_approved_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Approved Date" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.approved_at ? format(new Date(data.approved_at), 'MMM dd, yyyy | hh:mm a') : 'N/A'}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.approved_at ? format(new Date(row.approved_at), 'MMM dd, yyyy | hh:mm a') : 'N/A';
    },
  },
  {
    id: 'approved_credit_group',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Approved Credit Group" />,
    cell: ({ row }) => {
      const data = row.original;

      return <div className="min-w-max">{'N/A'}</div>;
    },
    accessorFn: (row) => {
      return 'N/A';
    },
  },
  {
    id: 'approved_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Approved By" />,
    cell: ({ row }) => {
      const data = row.original;

      return <div className="min-w-max">{'N/A'}</div>;
    },
    accessorFn: (row) => {
      return 'N/A';
    },
  },
];
