import { z } from 'zod';

export interface IGroupUsers {
  meta: IMeta;
  data: IGroupUser[];
}

export interface IMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  first_page: number;
  first_page_url: string;
  last_page_url: string;
  next_page_url: string;
  previous_page_url: string;
}

export interface IGroupUser {
  id: number;
  user_id: number;
  trading_app_group_price_id: number;
  remarks: string;
  created_at: string;
  updated_at: string;
  user: IUser;
}

export interface IUser {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: null | string;
  remember_me_token: null | string;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null | string | number;
  farmer: IFarmer | null;
  nonFarmer: INonFarmer | null;
}

export interface IFarmer {
  id: number;
  user_id: number;
  first_name: string;
  middle_name: null | string;
  last_name: string;
  birth_date: string;
  place_of_birth: null | string;
  religion: null | string;
  gender: null | string;
  civil_status: null | string;
  height: null | string | number;
  weight: null | string | number;
  mobile_number: null | string;
  address: string;
  address_house_number: null | string;
  address_province: null | string;
  address_city: null | string;
  address_barangay: null | string;
  address_zip_code: null | string;
  educational_attainment: string;
  educational_is_graduate: number;
  educational_degree: string;
  occupation: string;
  occupation_status: null | string;
  occupation_employer_name: null | string;
  occupation_employer_address: null | string;
  occupation_business_name: null | string;
  occupation_business_address: null | string;
  occupation_business_contact: null | string;
  occupation_annual_income: null | string | number;
  skills_farming: string;
  skills_fishing: number;
  skills_livestock: string;
  skills_construction: null | string;
  skills_processing: null | string;
  skills_servicing: null | string;
  skills_craft: null | string;
  skills_others: null | string;
  vehicle_owned: null | string;
  status: number;
  created_at: string;
  updated_at: string;
  has_biometric: number;
  qr_code: null | string;
  has_loan: number;
  has_submitted_loan_application: number;
}

export interface INonFarmer {
  id: number;
  user_id: number;
  first_name: string;
  middle_name: null | string;
  last_name: string;
  birth_date: null | string;
  mobile_number: null | string;
  address: null | string;
  address_house_number: null | string;
  address_province: null | string;
  address_city: null | string;
  address_barangay: null | string;
  address_zip_code: null | string;
  status: number;
  has_biometric: number;
  qr_code: string;
  created_at: string;
  updated_at: string;
}

// -----------------------------------

export const AddGroupUserSchema = z.object({
  userId: z.number().positive({ message: 'Please select a username' }),
  tradingAppGroupPriceId: z.number().positive(),
  remarks: z.string().min(1, 'Please fill in all required fields before submitting.'),
});
export type AddGroupUserType = z.infer<typeof AddGroupUserSchema>;

export const AddGroupUserBulkSchema = z.object({
  userIds: z.array(z.number()).min(1, 'Please select at least one username'),
  tradingAppGroupPriceId: z.number().positive(),
  remarks: z.string().optional(),
});
export type AddGroupUserBulkType = z.infer<typeof AddGroupUserBulkSchema>;
