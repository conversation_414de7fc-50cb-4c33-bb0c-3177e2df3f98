'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

import { farmPlanTemplateState, useFarmPlanTemplate } from '@/features/head-agronomist/hooks/useFarmPlanTemplates';
import { ETemplateItemType } from '@/features/head-agronomist/types/farmplan-templates';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { useEditFarmPlan } from '../../hooks/useEditFarmPlan';
import useFarmer, { farmerState } from '../../hooks/useFarmer';
import { useUpdateFarmPlan } from '../../hooks/useUpdateFarmPlan';
import { IUpdateFarmPlan } from '../../types/edit-farm-plan.types';
import AgronomistInfo from '../shared/agronomist-info';
import CashReqSummary from '../shared/cash-requirements-summary';
import CostSummary from '../shared/cost-summary';
import FarmPlanTemplateInfo from '../shared/farm-plan-template-info';
import FarmerEquitySummary from '../shared/farmer-equity-summary';
import FarmerInfo from '../shared/farmer-info';
import InputCostSummary from '../shared/input-cost-summary';
import FoliarFertilization from '../shared/inputs-foliar-fert';
import PesticideApplication from '../shared/inputs-pesticide-application';
import SeedRequirements from '../shared/inputs-seed-requirements';
import SoilFertilizationSideDress from '../shared/inputs-soil-fert-side-dress';
import SoilFertilizationTopDress from '../shared/inputs-soil-fert-top-dress';
import KitaSubsidizedCosts from '../shared/kita-subsidized-costs';
import LaborRequirements from '../shared/labor-reqs';
import LoanRepaymentCalc from '../shared/loan-repayment-calculator';
import NonCashCosts from '../shared/non-cash-costs';
import NonKitaSubsidizedCosts from '../shared/nonkita-subsidized-costs';
import OtherFarmMaterials from '../shared/other-farm-materials';
import OtherProductionCosts from '../shared/other-production-costs';
import FarmPlanHeader from './farm-plan-header';

interface IEditFarmPlanProps {
  id: number;
}

export default function EditFarmPlanForm({ id }: IEditFarmPlanProps) {
  // State for controlling form reset
  const [resetKey, setResetKey] = useState(0);

  const gStateP = useGlobalStatePersist();
  const { farmPlanByIdQuery } = useEditFarmPlan(id);
  const { submitFarmPlan, isSubmitting } = useUpdateFarmPlan();
  const { farmersQuery } = useFarmer();
  const { farmPlanTemplatesQuery } = useFarmPlanTemplate();

  // Initialize the form with the required structure
  const form = useForm<IUpdateFarmPlan>({
    defaultValues: {
      farmPlanId: id,
      userId: 0,
      cropId: 0,
      croppingType: '',
      agronomistName: gStateP.user
        ? `${gStateP.user.user.agronomist.first_name.value} ${gStateP.user.user.agronomist.last_name.value}`
        : '',
      agronomistPrcNumber: '',
      agronomistValidUntil: '',
      headAgronomistName: '',
      headAgronomistPrcNumber: '',
      headAgronomistValidUntil: '',
      contingencyForFluctuation: 0,
      interestRate: 0,
      numberOfMonthsPerTenor: 0,
      aorPerMonth: 0,
      farmPlanTemplateId: 0,
      items: [
        {
          name: 'Seed / Seedling Requirements (SE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Basal (Top-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Additional (Side-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Foliar Fertilization (Spray) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Pesticide Application (Spray / Spread) (CP)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Other Farm Materials (OT)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Labor Requirements (LR)',
          type: ETemplateItemType.LABOR,
          subItems: [],
        },
        {
          name: 'Other Production Costs (OT)',
          type: ETemplateItemType.OTHERS,
          subItems: [],
        },
        {
          name: 'Non-Cash Costs (NC)',
          type: ETemplateItemType.NON_CASH,
          subItems: [],
        },
        {
          name: 'KITA Subsidized Costs (KS)',
          type: ETemplateItemType.KITA_SUBSIDIZED,
          subItems: [],
        },
        {
          name: 'Non-KITA Subsidized Costs (Government Subsidy, Grants, Donations, etc.)',
          type: ETemplateItemType.NON_KITA_SUBSIDIZED,
          subItems: [],
        },
      ],
    },
  });

  // Handle form submission
  const onSubmit = form.handleSubmit(async (data) => {
    await submitFarmPlan(data);
  });

  // Load existing farm plan data when query succeeds
  useEffect(() => {
    if (farmPlanByIdQuery.isSuccess && farmPlanByIdQuery.data) {
      const data = farmPlanByIdQuery.data;

      // Set basic form fields
      form.setValue('userId', data.user_id);
      form.setValue('cropId', data.crop_id);
      form.setValue('croppingType', data.cropping_type);
      form.setValue('agronomistName', data.agronomist_name);
      form.setValue('agronomistPrcNumber', data.agronomist_prc_number);
      form.setValue('agronomistValidUntil', data.agronomist_valid_until);
      form.setValue('headAgronomistName', data.head_agronomist_name);
      form.setValue('headAgronomistPrcNumber', data.head_agronomist_prc_number);
      form.setValue('headAgronomistValidUntil', data.head_agronomist_valid_until);
      form.setValue('contingencyForFluctuation', data.contingency_for_fluctuation || 0);
      form.setValue('interestRate', data.interest_rate || 0);
      form.setValue('numberOfMonthsPerTenor', data.number_of_months_per_tenor || 0);
      form.setValue('aorPerMonth', data.aor_per_month || 0);
      form.setValue('farmPlanTemplateId', data.farm_plan_template_id || 0);

      // Set farmer state for the FarmerInfo component
      if (data.user) {
        farmerState.selected.set(data.user as any);
        farmerState.params.search.set(`${data.user.farmer.first_name} ${data.user.farmer.last_name}`);
      }

      // Map farm plan items to form structure
      if (data.farmPlanItems && data.farmPlanItems.length > 0) {
        const mappedItems = data.farmPlanItems.map((item) => ({
          farmPlanItemId: item.id,
          name: item.name,
          type: item.type as ETemplateItemType,
          subItems: item.farmPlanSubItems.map((subItem) => ({
            farmPlanSubItemId: subItem.id,
            expectedDate: subItem.expected_date,
            itemName: subItem.item_name,
            unit: subItem.unit,
            quantity: subItem.quantity,
            unitCost: subItem.unit_cost,
            notes: subItem.notes || '',
            ...(subItem.marketplace_product_id &&
              subItem.marketplace_product_id !== 0 && {
                marketplaceProductId: subItem.marketplace_product_id,
              }),
            totalAmount: subItem.total_amount,
          })),
        }));

        form.setValue('items', mappedItems);
      }

      // Force form remount by incrementing reset key
      setResetKey((prev) => prev + 1);
    }
  }, [farmPlanByIdQuery.isSuccess, farmPlanByIdQuery.data, farmPlanTemplatesQuery.data, form]);

  // Separate effect to handle template state when templates are loaded
  useEffect(() => {
    if (farmPlanByIdQuery.isSuccess && farmPlanByIdQuery.data && farmPlanTemplatesQuery.isSuccess) {
      const data = farmPlanByIdQuery.data;
      if (data.farm_plan_template_id && farmPlanTemplatesQuery.data?.data.data) {
        const template = farmPlanTemplatesQuery.data.data.data.find((t) => t.id === data.farm_plan_template_id);
        if (template) {
          farmPlanTemplateState.search.set(template.crop.name);
        }
      }
    }
  }, [
    farmPlanByIdQuery.isSuccess,
    farmPlanByIdQuery.data,
    farmPlanTemplatesQuery.isSuccess,
    farmPlanTemplatesQuery.data,
  ]);

  if (farmPlanByIdQuery.isLoading) {
    return (
      <div className="p-6">
        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    );
  }

  if (farmPlanByIdQuery.isError) {
    return (
      <div className="p-6">
        <div className="text-center text-red-500">
          <p>Error loading farm plan. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <FarmPlanHeader />

      <form key={`${id}-${resetKey}`} onSubmit={onSubmit}>
        <div className="mt-6 grid gap-6 md:grid-cols-2">
          <div>
            <FarmerInfo form={form as any} />
          </div>

          <div>
            <FarmPlanTemplateInfo form={form as any} onTemplateReset={() => setResetKey((prev) => prev + 1)} />
          </div>
        </div>

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARM INPUTS FOR DELIVERY / PICK-UP (Amount to be Held in favor of KITA)
        </div>

        <SeedRequirements form={form as any} />
        <SoilFertilizationTopDress form={form as any} />
        <SoilFertilizationSideDress form={form as any} />
        <FoliarFertilization form={form as any} />
        <PesticideApplication form={form as any} />
        <OtherFarmMaterials form={form as any} />

        {/* Estimated Farm Inputs Costs Section */}
        <InputCostSummary form={form as any} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          CASH REQUIREMENTS (Labor, Overhead, Other Cash Costs, Etc.)
        </div>

        <LaborRequirements form={form as any} />
        <OtherProductionCosts form={form as any} />
        <CashReqSummary form={form as any} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARMER&apos;S EQUITY (Other Non-Cash Costs, Subsidized Costs, etc.)
        </div>
        <NonCashCosts form={form as any} />
        <KitaSubsidizedCosts form={form as any} />
        <NonKitaSubsidizedCosts form={form as any} />
        <FarmerEquitySummary form={form as any} />

        <div className="flex flex-wrap gap-6 pt-8">
          <div className="w-full md:flex-1">
            <CostSummary />
            <LoanRepaymentCalc form={form as any} />
          </div>
          <div className="w-full md:w-1/3">
            <AgronomistInfo form={form as any} />
          </div>
        </div>

        <div className="mt-16 flex justify-center">
          <Button type="submit" className="px-8" disabled={isSubmitting}>
            {isSubmitting ? 'Updating Farm Plan...' : 'Update Farm Plan'}
          </Button>
        </div>
      </form>
    </div>
  );
}
