'use client';

import { useEffect } from 'react';

import useChemicals from '@/lib/hooks/useChemicals';
import { useGlobalState } from '@/lib/store';

import { ChemicalsTable } from './chemicals-table';
import useCropProtectionTab from './chemicals-table/hooks/useCropProtectionTab';

export default function ChemicalsPage() {
  const gState = useGlobalState();
  const { getChemicals, getSubcategory, getActiveIngredients, getMode } = useChemicals();
  const cropTab = useCropProtectionTab();

  useEffect(() => {
    Promise.all([getChemicals(), getSubcategory(), getActiveIngredients(), getMode()]);
  }, []);

  return (
    <div className="px-6 py-8" key={JSON.stringify(cropTab[gState.admin.chemicals.tab.value].data)}>
      <ChemicalsTable
        columns={cropTab[gState.admin.chemicals.tab.value].columns}
        data={cropTab[gState.admin.chemicals.tab.value].data}
      />
    </div>
  );
}
