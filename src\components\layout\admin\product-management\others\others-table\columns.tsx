'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ChevronDown, Pencil, Plus, TextCursorInput, UploadCloud, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge, BadgeProps } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import useOtherProduct, { OtherProductSchema, UpdateOtherProductSchema } from '@/lib/hooks/useOthers';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import OtherProductCsv from '../OtherProductCsv';

export const otherProductColumns = [
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Product Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.name}</div>;
    },
    accessorFn: (row) => row.name,
  },
  {
    id: 'brand',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Brand" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.brand}</div>;
    },
    accessorFn: (row) => row.brand,
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const statusMessage = ['Disabled', 'Active'];
      const variant = ['error', 'success'] as BadgeProps;

      return (
        <div className="min-w-max">
          <Badge variant={variant[data.status]}>{statusMessage[data.status]}</Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.status}`,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'updated_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Updated At" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) =>
      new Date(row.updated_at).toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      }),
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

export const OtherProductActionHeader = () => {
  const router = useRouter();
  const gState = useGlobalState();
  const { addOtherProduct } = useOtherProduct();

  const dialogInput = useHookstate(false);
  const dialogUpload = useHookstate(false);
  const dropdown = useHookstate(false);
  const loading = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(OtherProductSchema),
    defaultValues: {
      otherProducts: [
        {
          name: '',
          brand: '',
        },
      ],
    },
  });
  const { fields, append, remove } = useFieldArray({
    name: 'otherProducts',
    control,
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      console.log('onSubmit: ', data);
      await addOtherProduct(data);

      dialogInput.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div className="flex items-center justify-end gap-2">
      <DropdownMenu onOpenChange={dropdown.set}>
        <DropdownMenuTrigger asChild>
          <Button className="h-8 px-2 lg:px-3" size="sm">
            Add Crops via
            <ChevronDown
              className={cn('ml-2 h-4 w-4 transition duration-300 ease-in-out', dropdown.value && 'rotate-180')}
            />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56" align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => dialogInput.set(true)}>
            <TextCursorInput className="mr-2 size-4" />
            Input
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              dialogUpload.set(true);
              // router.push('/admin/product-management/crops/upload-crops')
            }}
          >
            <UploadCloud className="mr-2 size-4" />
            Upload
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Add via input */}
      <Dialog open={dialogInput.value} onOpenChange={dialogInput.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-lg">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Product</DialogTitle>
            <DialogDescription>{`Add new product to your list. You can add multiple product at a time.`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="">
            <ScrollArea className={cn(fields.length >= 5 && 'max-h-[50vh] h-full')}>
              <div className="mt-3 grid gap-4 px-6 pb-6">
                {fields.map((field, index) => {
                  const errorForField = errors?.otherProducts?.[index];

                  return (
                    <div key={field.id} className="flex items-start gap-4">
                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor="name" className="pb-1 font-normal">
                          Crop Name
                        </Label>
                        <div className="flex items-center gap-4">
                          <Input
                            {...register(`otherProducts.${index}.name` as const)}
                            className={cn(
                              'focus-visible:ring-primary',
                              errorForField?.name && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            type="text"
                            placeholder="Enter crop name"
                          />
                          {/* <div className={cn(index === 0 && 'invisible')}>
                            <Button
                              className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                              variant="outline"
                              size="icon"
                              onClick={() => remove(index)}
                              type="button"
                            >
                              <X className="size-5" />
                            </Button>
                          </div> */}
                        </div>
                        {errorForField?.name && <p className="form-error">{`${errorForField?.name.message}`}</p>}
                      </div>

                      <div className="relative grid w-full items-center gap-1.5">
                        <Label htmlFor="brand" className="pb-1 font-normal">
                          Brand
                        </Label>
                        <div className="flex items-center gap-4">
                          <Input
                            {...register(`otherProducts.${index}.brand` as const)}
                            className={cn(
                              'focus-visible:ring-primary',
                              errorForField?.brand && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            type="text"
                            placeholder="Enter brand"
                          />
                          <div className={cn(index === 0 && 'invisible')}>
                            <Button
                              className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                              variant="outline"
                              size="icon"
                              onClick={() => remove(index)}
                              type="button"
                            >
                              <X className="size-5" />
                            </Button>
                          </div>
                        </div>
                        {errorForField?.brand && <p className="form-error">{`${errorForField?.brand.message}`}</p>}
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>

            <div className="flex justify-end px-6 pt-2">
              <Button
                variant="outline"
                size="icon"
                className="border-slate-300"
                type="button"
                onClick={() => append({ name: '', brand: '' })}
              >
                <Plus className="size-5 text-primary" />
              </Button>
            </div>

            <div className="flex justify-between gap-2 p-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              {loading.value ? (
                <ButtonLoading />
              ) : (
                <Button className="px-12" type="submit">
                  Add
                </Button>
              )}
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add via upload */}
      <Dialog open={dialogUpload.value} onOpenChange={dialogUpload.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-lg">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Add New Product</DialogTitle>
            <DialogDescription>{`Upload CSV file to add product`}</DialogDescription>
          </DialogHeader>

          <OtherProductCsv />

          <div className="flex justify-between gap-2 p-6">
            <DialogClose asChild>
              <Button className="px-12" variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="px-12" type="button" disabled={gState.admin.otherProduct.upload.length === 0}>
                  Add
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will add new product to your list.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    type="button"
                    onClick={async () => {
                      const crops = {
                        otherProducts: gState.admin.otherProduct.upload.value,
                      };
                      await addOtherProduct(JSON.parse(JSON.stringify(crops)));
                      dialogUpload.set(false);
                    }}
                  >
                    Continue
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Action = ({ row }) => {
  const data = row.original;
  const updateDialog = useHookstate(false);
  const { updateOtherProduct } = useOtherProduct();
  const loading = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(UpdateOtherProductSchema),
    defaultValues: {
      otherProductId: `${data.id}`,
      name: data.name,
      brand: data.brand,
      status: `${data.status}`,
    },
  });

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      await updateOtherProduct(data);

      updateDialog.set(false);
      reset();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <>
      <div className="flex justify-end gap-2">
        <div className="flex justify-end gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="h-8 px-2 lg:px-3"
                variant="outline"
                size="sm"
                onClick={() => {
                  updateDialog.set(true);
                }}
              >
                <Pencil className="size-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit Product</p>
            </TooltipContent>
          </Tooltip>

          {/* Update Product Dialog */}
          <Dialog open={updateDialog.value} onOpenChange={updateDialog.set}>
            <DialogContent className="sm:max-w-2xl">
              <DialogHeader>
                <DialogTitle className="text-primary">Update Product</DialogTitle>
                <DialogDescription>{`Fill the form below to update product`}</DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="space-y-6">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="otherProductId" className="pb-1 font-normal">
                      Product ID
                    </Label>
                    <Input
                      {...register('otherProductId')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.otherProductId && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter crop id"
                      disabled
                    />
                    {errors.otherProductId && <p className="form-error">{`${errors.otherProductId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="name" className="pb-1 font-normal">
                      Name
                    </Label>
                    <Input
                      {...register('name')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.name && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter product name"
                    />
                    {errors.name && <p className="form-error">{`${errors.name.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="brand" className="pb-1 font-normal">
                      Brand
                    </Label>
                    <Input
                      {...register('brand')}
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.brand && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Brand"
                    />
                    {errors.brand && <p className="form-error">{`${errors.brand.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="status" className="pb-1 font-normal">
                      Status
                    </Label>
                    <Controller
                      control={control}
                      name="status"
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select user role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="0">Disabled</SelectItem>
                              <SelectItem value="1">Active</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
                  </div>
                </div>

                <div className="flex justify-between gap-2 pt-6">
                  <DialogClose asChild>
                    <Button className="px-12" variant="outline" type="button">
                      Cancel
                    </Button>
                  </DialogClose>
                  {loading.value ? (
                    <ButtonLoading />
                  ) : (
                    <Button className="px-12" type="submit">
                      Update
                    </Button>
                  )}
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </>
  );
};
