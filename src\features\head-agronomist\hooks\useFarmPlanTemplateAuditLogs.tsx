'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { keepPreviousData, useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';

import { IFarmPlanTemplateLogsResponse } from '../types/farmplan-templates';

// State
const initialState = {
  page: 1,
  pageSize: 10,
};

const farmPlanTemplateAuditLogsState = hookstate(initialState, devtools({ key: 'farmPlanTemplateAuditLogsState' }));

// Fetcher
const fetchFarmPlanTemplateAuditLogs = async (farmPlanTemplateId: number) => {
  const { data } = await axios.get(`/agronomist/farmplan/template/${farmPlanTemplateId}/logs`, {
    params: {
      page: farmPlanTemplateAuditLogsState.page.value,
      pageSize: farmPlanTemplateAuditLogsState.pageSize.value,
    },
  });
  return data as IFarmPlanTemplateLogsResponse;
};

// hooks
export const useFarmPlanTemplateAuditLogs = (farmPlanTemplateId: number) => {
  const state = useHookstate(farmPlanTemplateAuditLogsState);

  const farmPlanTemplateAuditLogsQuery = useQuery({
    queryKey: [
      'farmPlanTemplateAuditLogs',
      {
        farmPlanTemplateId,
        page: state.page.value,
        pageSize: state.pageSize.value,
      },
    ],
    queryFn: () => fetchFarmPlanTemplateAuditLogs(farmPlanTemplateId),
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    enabled: farmPlanTemplateId > 0,
  });

  return { farmPlanTemplateAuditLogsQuery, state };
};

export { farmPlanTemplateAuditLogsState };
