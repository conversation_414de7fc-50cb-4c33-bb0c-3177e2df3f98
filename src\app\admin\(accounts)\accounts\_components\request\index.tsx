'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import useUserRequestsTab from '@/components/layout/admin/product-management/seeds/hooks/useUserRequestsTab';

import { bulkImportColumns } from '@/app/admin/(accounts)/accounts/_components/request/members-table/bulk-import-columns';
import useTradingPost from '@/lib/hooks/admin/useTradingPost';
import useUsers from '@/lib/hooks/useUsers';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';
import { useGlobalState } from '@/lib/store';

import { MembersTable } from './members-table';

interface ITableData {
  columns: any;
  data: any;
}

export default function AccountRequestPage() {
  const gState = useGlobalState();
  const { fetchMembersFiltered } = useTradingPost();
  const { getUsersBulkPaginated } = useUsers();

  const usersBulkParams = useHookstate(gState.admin.pagination.usersBulk);
  const searchDebounce = useHookstateDebounce(usersBulkParams.search, 500);
  const pageDebounce = useHookstateDebounce(usersBulkParams.page, 500);
  const pageSizeDebounce = useHookstateDebounce(usersBulkParams.pageSize, 500);

  const requestTab = useUserRequestsTab();
  const tab = useHookstate(gState.admin.pagination.request.status);
  const data = requestTab[tab.value.toString()];

  useEffect(() => {
    if (tab.value.toString() === '4') {
      getUsersBulkPaginated();
    } else {
      fetchMembersFiltered();
    }
  }, [tab, searchDebounce, pageDebounce, pageSizeDebounce]);

  return (
    <div className="p-6 lg:p-8" key={data.data.toString()}>
      <MembersTable
        columns={data.columns}
        data={data.data}
        tab={tab.value.toString()}
        metadata={tab.value.toString() === '4' ? gState.admin.usersBulk.metadata.value : null}
      />
    </div>
  );
}
