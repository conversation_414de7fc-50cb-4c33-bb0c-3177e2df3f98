export interface IMarketplaceReports {
  data: IData[];
  meta: IMeta;
}

export interface IMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  first_page: number;
  first_page_url: string;
  last_page_url: string;
  next_page_url: string;
  previous_page_url: string;
}

export interface IData {
  id: number;
  customer_id: number;
  payment_method: number;
  reference_number: string;
  total_price: number;
  order_status: number;
  payment_status: number;
  shipping_fee: number;
  shipping_address: string;
  shipping_date: string;
  fulfillment_type: number;
  created_at: string;
  updated_at: string;
  wallet_allocation: number;
  customer: Customer;
  statusHistory: StatusHistory[];
  marketplaceProductOrders: MarketplaceProductOrders[];
}

export interface Customer {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: string;
  remember_me_token: null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null;
  farmer: Farmer;
}

export interface Farmer {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  mobile_number: string;
  address: string;
  address_province: string;
  address_city: string;
}

export interface StatusHistory {
  id: number;
  marketplace_order_id: number;
  status_type: number;
  message: string;
  created_at: string;
  updated_at: string;
  processed_by_id: number;
  processedBy: ProcessedBy;
}

export interface ProcessedBy {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: null;
  remember_me_token: null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null;
  sale: null;
  admin: Admin;
}

export interface Admin {
  id: number;
  first_name: string;
  last_name: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}

export interface MarketplaceProductOrders {
  id: number;
  marketplace_order_id: number;
  marketplace_product_id: number;
  price: number;
  quantity: number;
  vatable: string;
  created_at: string;
  updated_at: string;
  marketplaceProduct: MarketplaceProduct;
}

export interface MarketplaceProduct {
  id: number;
  product_type: number;
  price: number;
  vatable: string;
  stocks: number;
  stocks_warning: number;
  code: string;
  weight: number;
  unit: string;
  image: string;
  description: string;
  seed_id: null;
  crop_id: null;
  fertilizer_id: null;
  chemical_id: null;
  status: number;
  created_at: string;
  updated_at: string;
  other_product_id: number;
  fertilizer: null;
  otherProduct: OtherProduct;
  crop: null;
  seed: null;
  chemical: null;
}

export interface OtherProduct {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
  brand: string;
}
