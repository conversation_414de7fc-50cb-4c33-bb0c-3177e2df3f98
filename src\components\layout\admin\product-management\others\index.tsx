'use client';

import { useEffect } from 'react';

import useOtherProduct from '@/lib/hooks/useOthers';

import { OthersTable } from './others-table';
import { otherProductColumns } from './others-table/columns';

export default function OtherProductPage() {
  const { getOtherProduct, otherProducts } = useOtherProduct();

  useEffect(() => {
    getOtherProduct();
  }, []);

  return (
    <div className="px-6 py-8" key={JSON.stringify(otherProducts.get({ noproxy: true }))}>
      <OthersTable columns={otherProductColumns} data={otherProducts.get({ noproxy: true })} />
    </div>
  );
}
