'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import axios from '@/lib/api';

const initialState = {
  data: {
    meta: null as unknown,
    data: [],
  },
  pagination: {
    page: 1,
    pageSize: 10,
    startDate: '',
    endDate: '',
  },
  selected: null as unknown,
  reqDetails: null as unknown,
  finance: {
    pagination: {
      page: 1,
      pageSize: 10,
      startDate: '',
      endDate: '',
      status: ['0'],
    },
  },
};

export const topDownState = hookstate(
  initialState,
  devtools({
    key: 'topDownState',
  }),
);

export const useTopDownStore = (currentUser: 'admin' | 'finance') => {
  const state = useHookstate(topDownState);
  const pagination = useHookstate(state.pagination);
  const financePagination = useHookstate(state.finance.pagination);
  const router = useRouter();

  useEffect(() => {
    getRequests();
  }, [
    pagination.page,
    pagination.pageSize,
    pagination.startDate,
    pagination.endDate,
    financePagination.page,
    financePagination.pageSize,
    financePagination.startDate,
    financePagination.endDate,
    financePagination.status[0],
  ]);

  const getRequests = async () => {
    try {
      const params = currentUser === 'admin' ? state.pagination.value : state.finance.pagination.value;

      const res = await axios
        .get(`/${currentUser}/topdown/viewAll`, {
          params,
        })
        .then((res) => res.data.data);
      state.data.set(res);
    } catch (e) {
      console.error(e);
    }
  };

  const getRequestById = async (reqId: string | number) => {
    try {
      const res = await axios.get(`/${currentUser}/topdown/view/${reqId}`).then((res) => res.data.data);
      state.reqDetails.set(res);
    } catch (e) {
      console.error(e);
    }
  };

  const requestTopDown = async (data: any) => {
    try {
      toast.loading('Requesting topup...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const res = await axios
        .post(`/${currentUser}/topdown/request`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data.data);
      console.log('requestTopDown: ', res);

      toast.dismiss();
      toast.success('Success', {
        description: 'Topdown request sent',
      });
      router.push('/admin/marketplace/');
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('requestTopDown: ', error);

      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    } finally {
      getRequests();
    }
  };

  const topdownAction = async (action: 'approve' | 'reject', data) => {
    try {
      toast.loading(action === 'approve' ? 'Approving topdown...' : 'Rejecting topdown...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post(`/${currentUser}/topdown/${action}`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('topdownAction: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: `${action === 'approve' ? 'Approve' : 'Reject'} topdown successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('topdownAction: ', error);

      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { getRequests, requestTopDown, getRequestById, topdownAction, data: state.data.data, meta: state.data.meta };
};
