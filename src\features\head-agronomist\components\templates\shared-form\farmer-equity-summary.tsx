'use client';

import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';

import { ITemplateForm } from '../create-form/types/template';

interface IFarmerEquitySummaryProps {
  form: UseFormReturn<ITemplateForm>;
}

export default function FarmerEquitySummary({ form }: IFarmerEquitySummaryProps) {
  const state = useHeadAgronomistState();

  // Calculate Total Cash Requirements
  useEffect(() => {
    const total =
      state.section3.group.nonCashCost.value +
      state.section3.group.kitaSubsidizedCost.value +
      state.section3.group.nonKitaSubsidizedCost.value;
    state.section3.totalCashRequirements.set(total);
  }, [
    state.section3.group.nonCashCost,
    state.section3.group.kitaSubsidizedCost,
    state.section3.group.nonKitaSubsidizedCost,
  ]);

  return (
    <div className="mt-6 rounded-md bg-gray-100">
      <div className="divide-y">
        {/* Total Cash Requirements */}
        <div className="flex items-center justify-end p-2">
          <span className="text-sm font-medium text-gray-700">Total Cash Requirements</span>
          <span className="w-[200px] text-right text-sm font-semibold">
            {state.section3.totalCashRequirements.value.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </span>
        </div>
      </div>
    </div>
  );
}
