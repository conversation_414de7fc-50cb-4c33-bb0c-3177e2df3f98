'use client';

import { useHookstate } from '@hookstate/core';
import { Image, Loader2 } from 'lucide-react';
import dynamic from 'next/dynamic';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Controller, useForm } from 'react-hook-form';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import useMarketplace from '@/lib/hooks/useMarketplace';
import usePublic from '@/lib/hooks/usePublic';
import { useGlobalState } from '@/lib/store';
import { bytesToSize, cn } from '@/lib/utils';

import { PRODUCT_TYPE, PRODUCT_TYPE_ACCESSOR, PRODUCT_TYPE_LABELS, UNIT } from '../../_components/Enums';

const CustomEditor = dynamic(
  () => {
    return import('@/components/Custom-Editor');
  },
  { ssr: false },
);

export default function ProductUpdatePage() {
  const gState = useGlobalState();
  const router = useRouter();
  const productId = useSearchParams().get('id');

  const { getProductById, updateProduct } = useMarketplace();
  const { getAllCrops, getChemicals, getFertilizer, getSeeds, getOtherProduct } = usePublic();
  const [selectProduct, setSelectProduct] = useState([] as any);
  const loading = useHookstate(false);
  const [isDone, setIsDone] = useState(0);

  const product = gState.admin.marketplace['editProduct'].get({ noproxy: true });

  const { acceptedFiles, getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
    },
  });
  const [preview, setPreview] = useState(null);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
  } = useForm({
    defaultValues: {
      productType: '',
      productName: '',
      price: '',
      code: '',
      stocks: '',
      stocksWarning: '10',
      weight: '',
      unit: '',
      vatable: '',
      description: '',
      status: '1',
    },
  });
  const watchProductType = watch('productType');

  const onSubmit = async (_data: any) => {
    loading.set(true);
    const updatedData = {
      ..._data,
      image: acceptedFiles.length > 0 ? acceptedFiles[0] : null,
      marketplaceProductId: productId,
    };

    if (Number(watchProductType) === PRODUCT_TYPE.CROPS) {
      updatedData.cropId = _data.productName;
    } else if (Number(watchProductType) === PRODUCT_TYPE.SEEDS) {
      updatedData.seedId = _data.productName;
    } else if (Number(watchProductType) === PRODUCT_TYPE.FERTILIZERS) {
      updatedData.fertilizerId = _data.productName;
    } else if (Number(watchProductType) === PRODUCT_TYPE.CHEMICALS) {
      updatedData.chemicalId = _data.productName;
    } else if (Number(watchProductType) === PRODUCT_TYPE.OTHERS) {
      updatedData.otherProductId = _data.productName;
    }

    // remove productName in updatedData
    delete updatedData.productName;

    console.log('onSubmit: ', updatedData);
    await updateProduct(updatedData);

    loading.set(false);
  };

  useEffect(() => {
    if (productId) {
      Promise.all([
        getAllCrops(),
        getChemicals(),
        getFertilizer(),
        getSeeds(),
        getOtherProduct(),
        getProductById(productId),
      ]);
    } else {
      console.error('Product ID not found');
      router.push('/admin/marketplace/product');
    }
  }, [productId]);

  useEffect(() => {
    if (product) {
      const accessor = PRODUCT_TYPE_ACCESSOR[Number(product.product_type)];
      const item = product[accessor];

      setPreview(product.image);
      reset((v) => ({
        ...v,
        productType: `${product.product_type}`,
        productName: `${item.id}`,
        price: `${product.price}`,
        code: `${product.code}`,
        stocks: `${product.stocks}`,
        stocksWarning: `${product.stocks_warning}`,
        weight: `${product.weight}`,
        unit: `${product.unit}`,
        vatable: `${product.vatable}`,
        description: `${product.description}`,
        status: `${product.status}`,
      }));
      setIsDone((v) => v + 1);
    }
  }, [gState.admin.marketplace['editProduct']]);

  useEffect(() => {
    if (acceptedFiles.length === 0) {
      setPreview(null);
      return;
    }

    const objectUrl = URL.createObjectURL(acceptedFiles[0]);
    setPreview(objectUrl);

    // free memory when ever this component is unmounted
    return () => URL.revokeObjectURL(objectUrl);
  }, [acceptedFiles]);

  useEffect(() => {
    if (watchProductType) {
      if (Number(watchProductType) === PRODUCT_TYPE.CROPS) {
        setSelectProduct(gState.crops.get({ noproxy: true }));
      } else if (Number(watchProductType) === PRODUCT_TYPE.SEEDS) {
        setSelectProduct(gState.seeds.get({ noproxy: true }));
      } else if (Number(watchProductType) === PRODUCT_TYPE.FERTILIZERS) {
        setSelectProduct(gState.fertilizers.get({ noproxy: true }));
      } else if (Number(watchProductType) === PRODUCT_TYPE.CHEMICALS) {
        setSelectProduct(gState.chemicals.get({ noproxy: true }));
      } else if (Number(watchProductType) === PRODUCT_TYPE.OTHERS) {
        setSelectProduct(gState.otherProduct.get({ noproxy: true }));
      } else {
        setSelectProduct([]);
      }
    }
  }, [watchProductType]);

  useEffect(() => {
    return () => {
      setIsDone((v) => v + 1);
    };
  }, []);

  const files = acceptedFiles.map((file: any) => (
    <div key={file.path}>
      <Badge>
        {file.path} - {bytesToSize(Number(file.size))}
      </Badge>
    </div>
  ));

  return (
    <div className="px-6 py-8">
      {product && (
        <div>
          <div>
            <div className="">
              <div className="font-dmSans font-bold text-primary md:text-xl lg:text-2xl">Update Product</div>
              <div className="font-dmSans text-gray-500">{`Update product details here`}</div>
            </div>
          </div>

          <form key={isDone} className="mt-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="grid items-start gap-6 sm:grid-cols-2 lg:max-w-[60vw]">
              {/* Product Type */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="productType" className="pb-1 font-normal">
                  Product Type
                </Label>
                <Controller
                  control={control}
                  name="productType"
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <Select onValueChange={onChange} value={value}>
                      <SelectTrigger
                        className={cn(
                          'focus:ring-primary',
                          errors.productType && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                        )}
                      >
                        <SelectValue placeholder="Select product type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {PRODUCT_TYPE_LABELS.map((productType, index) => (
                            <SelectItem key={index} value={`${index}`}>
                              {productType}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.productType && <p className="form-error">{`${errors.productType.message}`}</p>}
              </div>

              {/* Product Name */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="productName" className="pb-1 font-normal">
                  Product Name
                </Label>
                <Controller
                  control={control}
                  name="productName"
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <Select onValueChange={onChange} value={value} disabled={watchProductType === ''}>
                      <SelectTrigger
                        className={cn(
                          'focus:ring-primary',
                          errors.productName && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                        )}
                      >
                        <SelectValue placeholder="Select Product" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {selectProduct.map((productType, index) => (
                            <SelectItem key={index} value={`${productType.id}`}>
                              {productType.name}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.productName && <p className="form-error">{`${errors.productName.message}`}</p>}
              </div>

              {/* Price */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="price" className="pb-1 font-normal">
                  Price
                </Label>
                <Input
                  {...register('price', {
                    required: false,
                    validate: {
                      isGreaterThanZero: (v) => (v ? (Number(v) || 0) > 0 || 'Price must be greater than 0' : true),
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.price && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="e.g ₱100"
                />
                {errors.price && <p className="form-error">{`${errors.price.message}`}</p>}
              </div>

              {/* Item Code */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="code" className="pb-1 font-normal">
                  Item Code
                </Label>
                <Input
                  {...register('code')}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.code && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Item Code"
                />
                {errors.code && <p className="form-error">{`${errors.code.message}`}</p>}
              </div>

              {/* Stocks */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="stocks" className="pb-1 font-normal">
                  Stocks
                </Label>
                <Input
                  {...register('stocks', {
                    required: false,
                    validate: {
                      isGreaterThanZero: (v) => (v ? (Number(v) || 0) > 0 || 'Stocks must be greater than 0' : true),
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.stocks && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="e.g 1000"
                />
                {errors.stocks && <p className="form-error">{`${errors.stocks.message}`}</p>}
              </div>

              {/* Stocks Warning */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="stocksWarning" className="pb-1 font-normal">
                  Stocks Warning
                </Label>
                <Input
                  {...register('stocksWarning', {
                    required: false,
                    validate: {
                      isGreaterThanZero: (v) =>
                        v ? (Number(v) || 0) > 0 || 'Stocks Warning must be greater than 0' : true,
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.stocksWarning && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="e.g 10"
                />
                {errors.stocksWarning && <p className="form-error">{`${errors.stocksWarning.message}`}</p>}
              </div>

              {/* Weight */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="weight" className="pb-1 font-normal">
                  Weight
                </Label>
                <Input
                  {...register('weight', {
                    required: false,
                    validate: {
                      isGreaterThanZero: (v) => (v ? (Number(v) || 0) > 0 || 'Weight must be greater than 0' : true),
                    },
                  })}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.weight && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="e.g 2"
                />
                {errors.weight && <p className="form-error">{`${errors.weight.message}`}</p>}
              </div>

              {/* Unit of Measure */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="unit" className="pb-1 font-normal">
                  Unit of Measure
                </Label>
                <Controller
                  control={control}
                  name="unit"
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <Select onValueChange={onChange} value={value}>
                      <SelectTrigger
                        className={cn(
                          'focus:ring-primary',
                          errors.unit && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                        )}
                      >
                        <SelectValue placeholder="Select Unit of Measure" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {Object.values(UNIT).map((unit, index) => (
                            <SelectItem key={index} value={`${unit}`}>
                              {unit}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.unit && <p className="form-error">{`${errors.unit.message}`}</p>}
              </div>

              {/* Vat Code */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="vatable" className="pb-1 font-normal">
                  Vat Code
                </Label>
                <Controller
                  control={control}
                  name="vatable"
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <Select onValueChange={onChange} value={value}>
                      <SelectTrigger
                        className={cn(
                          'focus:ring-primary',
                          errors.vatable && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                        )}
                      >
                        <SelectValue placeholder="Select Vat Code" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectItem value="NV">Non-Vatable</SelectItem>
                          <SelectItem value="V">Vatable</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.vatable && <p className="form-error">{`${errors.vatable.message}`}</p>}
              </div>

              {/* Status */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="status" className="pb-1 font-normal">
                  Status
                </Label>
                <Controller
                  control={control}
                  name="status"
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <Select onValueChange={onChange} value={value}>
                      <SelectTrigger
                        className={cn(
                          'focus-visible:ring-primary',
                          errors.status && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                        )}
                      >
                        <SelectValue placeholder="Select Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectItem value="0">Inactive</SelectItem>
                          <SelectItem value="1">Active</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.status && <p className="form-error">{`${errors.status.message}`}</p>}
              </div>
            </div>

            <div className="w-auto space-y-6 pt-6 lg:max-w-[60vw]">
              {/* Description */}
              <div className="grid w-full items-center gap-1.5">
                <Label htmlFor="description" className="pb-1 font-normal">
                  Description
                </Label>
                <Controller
                  control={control}
                  name="description"
                  render={({ field: { onChange, onBlur, value, ref } }) => (
                    <CustomEditor data={value} onChange={onChange} />
                  )}
                />
                {errors.description && <p className="form-error">{`${errors.description.message}`}</p>}
              </div>

              <div className="flex flex-col gap-6 md:flex-row">
                {/* Image Dropzone */}
                <div className="md:w-1/2">
                  <div className="border-spacing-4 cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                    <div {...getRootProps({ className: 'dropzone' })}>
                      <input {...getInputProps()} />

                      {/* eslint-disable-next-line jsx-a11y/alt-text */}
                      <Image className="mx-auto mb-2 size-12 text-gray-500" />
                      <p>{`Drag 'n' drop image here, or`}</p>

                      <div className="py-4 font-bold text-primary">Browse</div>
                    </div>

                    <div>{files}</div>
                  </div>
                </div>

                {/* Preview */}
                <div className="rounded-lg border border-gray-300 md:w-1/2">
                  {!preview ? (
                    <div className="flex h-full items-center justify-center">
                      <div className="">
                        {/* eslint-disable-next-line jsx-a11y/alt-text */}
                        <Image className="mx-auto mb-2 size-12 text-gray-500" />
                        <div>Image Preview</div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex h-full items-center">
                      <img className="mx-auto h-[185px] object-cover" src={preview} alt="" />
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-center gap-4 pt-12 lg:max-w-[60vw] lg:justify-end">
              <Button
                className="px-12"
                variant="outline"
                type="button"
                onClick={() => {
                  router.back();
                }}
              >
                Cancel
              </Button>
              <Button className="px-8 sm:px-12" type="submit" disabled={loading.value}>
                {loading.value ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    Please wait
                  </>
                ) : (
                  'Update Product'
                )}
              </Button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
