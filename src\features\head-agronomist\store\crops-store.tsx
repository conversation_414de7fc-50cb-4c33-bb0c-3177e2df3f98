'use client';

import { useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';

import { ICrop } from '../types/crops.types';

// fetcher
export const fetchCrops = async () => {
  const { data } = await axios.get(`/crops/viewAll`, {
    params: {
      status: ['1'], // 1: active, 0: inactive
    },
  });
  return data.data as ICrop[];
};

// hooks
export const useCrops = () => {
  const cropsQuery = useQuery({
    queryKey: ['crops'],
    queryFn: fetchCrops,
    refetchOnWindowFocus: false,
    staleTime: 24 * 60 * 60 * 1000, // 1 day
  });

  return { cropsQuery };
};
