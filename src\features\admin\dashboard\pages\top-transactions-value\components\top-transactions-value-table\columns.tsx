'use client';

import { ColumnDef } from '@tanstack/react-table';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

import { ITopTransactionValue } from '@/features/admin/dashboard/types';
import { toCurrency, urlify } from '@/lib/utils';

export const columnsTopTransactionsValue: ColumnDef<ITopTransactionValue>[] = [
  {
    accessorKey: 'user_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="#" />,
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      return (
        <div className="flex w-max items-center gap-2.5">
          <div>
            <img
              className="size-8 rounded-full"
              src={row.original.user_img ? urlify(row.original.user_img, 'users/profile') : '/assets/user-default.jpg'}
              alt="profile"
            />
          </div>
          <div>{`${row.original.first_name} ${row.original.last_name}`}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'total_transactions',
    header: ({ column }) => (
      <DataTableColumnHeader className="justify-end" column={column} title="Total Transactions" />
    ),
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('total_transactions'))}</div>;
    },
  },
];
