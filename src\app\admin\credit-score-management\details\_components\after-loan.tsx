'use client';

import { useHookstate } from '@hookstate/core';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useEffect } from 'react';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Input, InputSign } from '@/components/ui/input';

import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useCreditScoring from '@/lib/hooks/admin/useCreditScoring';
import { useGlobalState } from '@/lib/store';
import { catchError, cn } from '@/lib/utils';

export default function AfterLoan() {
  const { updateRules } = useCreditScoring();
  const { updateGroupSilent, selectedGroup } = useCreditScoreMgt();
  const [creditScoreGroupId, setCreditScoreGroupId] = useQueryState('id', parseAsInteger);

  const gState = useGlobalState();
  const afterLoan = useHookstate(gState.creditScoring.afterRules);
  const beforeLoan = useHookstate(gState.creditScoring.beforeRules);
  const duringLoan = useHookstate(gState.creditScoring.duringRules);
  const creditHistory = useHookstate(gState.creditScoring.a_creditHistory);
  const loanCycles = useHookstate(gState.creditScoring.a_loanCycle);
  const agricultureActivity = useHookstate(gState.creditScoring.a_agricultureActivity);

  const name = [
    {
      name: 'If Payment is made on or before the due date.',
      description: '',
    },
    {
      name: 'If Payment is within grace period',
      description: 'Grace period days (Days before due date)',
    },
    {
      name: 'If Payment is beyond grace period',
      description: 'Choose from the options what will happen to farmers whose payments are past the grace period.',
    },
  ];

  useEffect(() => {
    if (creditHistory['rules']?.length) {
      const totalScore = creditHistory['rules'].reduce((sum, rule) => {
        return sum + (rule.score?.get({ noproxy: true }) || 0);
      }, 0);
      creditHistory['score'].set(totalScore);
    }

    if (agricultureActivity['rules']?.length) {
      const totalScore = agricultureActivity['rules'].reduce((sum, rule) => {
        return sum + (rule.score?.get({ noproxy: true }) || 0);
      }, 0);
      agricultureActivity['score'].set(totalScore);
    }

    if (loanCycles['rules']?.length) {
      const totalScore = loanCycles['rules'].reduce((sum, rule) => {
        return sum + (rule.score?.get({ noproxy: true }) || 0);
      }, 0);
      loanCycles['score'].set(totalScore);
    }

    // Update the total afterLoan score
    const totalafterLoanScore =
      (creditHistory['score']?.get({ noproxy: true }) || 0) +
      (agricultureActivity['score']?.get({ noproxy: true }) || 0) +
      (loanCycles['score']?.get({ noproxy: true }) || 0);

    // Initialize score property if it doesn't exist
    if (!afterLoan['score']) {
      afterLoan.merge({ score: totalafterLoanScore });
    } else {
      afterLoan['score'].set(totalafterLoanScore);
    }
  }, [creditHistory['rules'], agricultureActivity['rules'], loanCycles['rules']]);

  const handleUpdate = async () => {
    try {
      const data = {
        creditScoreGroupId: creditScoreGroupId,
        creditScoringConfig: {
          stages: [
            beforeLoan.get({ noproxy: true }),
            duringLoan.get({ noproxy: true }),
            afterLoan.get({ noproxy: true }),
          ],
        },
      };

      await updateRules(creditScoreGroupId, data);

      console.log('data: ', data);
    } catch (e) {
      catchError(e, 'handleUpdate');
    }
  };

  return (
    <div className="grid gap-4">
      <div className="max-w-5xl">
        <Accordion type="single" collapsible className="w-full">
          {/* Credit History */}
          <AccordionItem value="item-1">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Credit History</h1>
                <div className="font-bold text-primary">{`${creditHistory['score']?.value || 0}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              {creditHistory['rules']?.map((rule, index) => (
                <div key={index}>
                  <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                    <h1 className="font-medium">Repayment Behavior</h1>
                    <div className="">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={rule.score?.get({ noproxy: true }) || 0}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          rule.score.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>

                  {rule.hasConditions?.get({ noproxy: true }) && (
                    <div className="ml-10 max-w-2xl py-4 sm:ml-20">
                      <div>
                        <h1 className="text-sm font-light">Conditional</h1>
                        {rule.conditions?.get({ noproxy: true })?.map((condition, condIndex) => (
                          <div key={condIndex} className="mt-3 grid gap-3">
                            <div className="flex max-w-2xl items-center justify-between">
                              <div>
                                <h1 className="font-medium">{name[condIndex].name}</h1>
                                {condIndex === 1 && (
                                  <div className="flex items-center gap-4">
                                    <div className="text-sm font-light">{name[condIndex].description}</div>
                                    <Input
                                      className="h-8 max-w-24"
                                      placeholder="0"
                                      type="number"
                                      value={condition.upperBound}
                                      onChange={(e) => {
                                        if (e.target.value.length > 3) return;
                                        const value = Number(e.target.value);
                                        rule.conditions[condIndex].upperBound.set(value);
                                        if (condIndex < rule.conditions.length - 1) {
                                          rule.conditions[condIndex + 1].lowerBound.set(value + 1);
                                        }
                                      }}
                                    />
                                  </div>
                                )}
                                {condIndex === 2 && (
                                  <div className="mr-8 flex items-center gap-4">
                                    <div className="text-sm font-light">{name[condIndex].description}</div>
                                  </div>
                                )}
                              </div>
                              <div className="">
                                <InputSign
                                  sign="pts"
                                  className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                                  type="number"
                                  min={0}
                                  placeholder="0"
                                  value={condition.score}
                                  onChange={(e) => {
                                    if (e.target.value.length > 3) return;
                                    rule.conditions[condIndex].score.set(Number(e.target.value));
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>

          {/* No. of Loan Cycles */}
          <AccordionItem value="item-2">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Loan Cycles</h1>
                <div className="font-bold text-primary">{`${loanCycles['score']?.value || 0}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              {loanCycles['rules']?.map((rule, index) => (
                <div key={index}>
                  <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                    <h1 className="font-medium">{rule.name?.get({ noproxy: true }) || ''}</h1>
                    <div className="flex shrink-0">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={rule.score?.get({ noproxy: true }) || 0}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          rule.score.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>

                  {rule.hasConditions?.get({ noproxy: true }) && (
                    <div className="ml-10 max-w-2xl py-4 sm:ml-20">
                      <div>
                        <div className="text-sm font-light">Conditional</div>

                        {rule.conditions?.get({ noproxy: true })?.map((condition, condIndex) => (
                          <div key={condIndex} className="ml-5 mt-3 grid gap-3 sm:ml-10">
                            <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                              <div className="flex items-center gap-4">
                                {condition.order === 1 ? (
                                  <InputSign
                                    sign="PHP"
                                    signPosition="left"
                                    className="h-8 max-w-44 focus-visible:ring-primary"
                                    placeholder="0"
                                    min={0}
                                    type="number"
                                    value={condition.lowerBound}
                                    disabled
                                  />
                                ) : condition.upperBound !== null ? (
                                  <InputSign
                                    sign="PHP"
                                    signPosition="left"
                                    className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                                    placeholder="0"
                                    disabled
                                    value={condition.lowerBound}
                                  />
                                ) : (
                                  <>
                                    <div>Greater than or equal to</div>
                                    <InputSign
                                      sign="PHP"
                                      signPosition="left"
                                      className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                                      placeholder="0"
                                      disabled
                                      value={condition.lowerBound}
                                    />
                                  </>
                                )}

                                {condition.upperBound !== null && (
                                  <>
                                    <div>to</div>
                                    <InputSign
                                      sign="PHP"
                                      signPosition="left"
                                      className="h-8 max-w-44 focus-visible:ring-primary"
                                      placeholder="0"
                                      min={0}
                                      type="number"
                                      value={condition.upperBound}
                                      onChange={(e) => {
                                        if (e.target.value.length > 3) return;
                                        rule.conditions[condIndex].upperBound.set(Number(e.target.value));
                                        if (condIndex < rule.conditions.length - 2) {
                                          rule.conditions[condIndex + 1].lowerBound.set(Number(e.target.value) + 1);
                                        }
                                      }}
                                    />
                                  </>
                                )}
                              </div>

                              <div className="flex">
                                <InputSign
                                  sign="pts"
                                  className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                                  type="number"
                                  min={0}
                                  placeholder="0"
                                  value={condition.score}
                                  onChange={(e) => {
                                    if (e.target.value.length > 3) return;
                                    rule.conditions[condIndex].score.set(Number(e.target.value));
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>

          {/* Agriculture Activity */}
          <AccordionItem value="item-3">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Agriculture Activity</h1>
                <div className="font-bold text-primary">{`${agricultureActivity['score']?.value || 0}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              {agricultureActivity['rules']?.map((rule, index) => (
                <div key={index}>
                  <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                    <h1 className="font-medium">Target yield Achievement</h1>
                    <div className="">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={rule.score?.get({ noproxy: true }) || 0}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          rule.score.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>

                  {rule.hasConditions?.get({ noproxy: true }) && (
                    <div className="ml-10 max-w-2xl py-4 sm:ml-20">
                      <div>
                        {rule.conditions?.get({ noproxy: true })?.map((condition, condIndex) => (
                          <div key={condIndex} className="mt-3 grid gap-3">
                            <div className="flex max-w-2xl items-center justify-between">
                              <div>
                                <h1 className="font-medium">{condition.name}</h1>
                              </div>
                              <div className="">
                                <InputSign
                                  sign="pts"
                                  className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                                  type="number"
                                  min={0}
                                  placeholder="0"
                                  value={condition.score}
                                  onChange={(e) => {
                                    if (e.target.value.length > 3) return;
                                    rule.conditions[condIndex].score.set(Number(e.target.value));
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <div className="flex w-full max-w-5xl justify-between py-4">
          <h1 className="font-bold text-primary">Total</h1>
          <div className="font-bold text-primary">{`${afterLoan['score']?.value}%` || 0}</div>
        </div>
      </div>

      <div className="mt-8">
        <Button type="button" className="px-12" onClick={handleUpdate}>
          Submit
        </Button>
      </div>
    </div>
  );
}
