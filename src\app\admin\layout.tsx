'use client';

import { useHookstate } from '@hookstate/core';
import { BookText, BookUserIcon, HomeIcon, LogOut, Menu, Settings, Store, UserRoundCog } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { FaHandHoldingUsd } from 'react-icons/fa';
import { MdCurrencyExchange, MdOutlineCreditScore } from 'react-icons/md';

import LoadingScreen from '@/components/LoadingScreen';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

import useLogin from '@/lib/hooks/useLogin';
import useProtected from '@/lib/hooks/utils/useProtected';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

import MenuMapper from './_components/menu-mapper';

export const MENU = [
  {
    id: 0,
    name: 'Dashboard',
    icon: <HomeIcon className="size-5" />,
    href: '/admin',
  },
  {
    id: 1,
    name: 'Accounts',
    icon: <BookUserIcon className="size-5" />,
    href: '/admin/accounts/',
  },
  {
    id: 2,
    name: 'Product Management',
    icon: <BookText className="size-5" />,
    href: '/admin/product-management/',
  },
  {
    id: 3,
    name: 'Trading Post',
    icon: <FaHandHoldingUsd className="size-5" />,
    href: '/admin/trading-post/',
  },
  {
    id: 4,
    name: 'Trading App',
    icon: <MdCurrencyExchange className="size-5" />,
    href: '/admin/trading-app/',
  },
  {
    id: 5,
    name: 'Marketplace',
    icon: <Store className="size-5" />,
    href: '/admin/marketplace/',
  },
  {
    id: 6,
    name: 'User Management',
    icon: <UserRoundCog className="size-5" />,
    href: '/admin/user-management/',
  },
  {
    id: 7,
    name: 'Credit Score Management',
    icon: <MdOutlineCreditScore className="size-5" />,
    href: '/admin/credit-score-management/',
  },
];

export enum TAB_MARKETPLACE {
  ORDERS = 'orders',
  PRODUCTS = 'products',
  EWALLET = 'e-wallet',
  PAYMENTS = 'payments',
  REPORTS = 'reports',
}

export enum TAB_TRADING_APP {
  QA = 'qa',
  BUYER = 'buyer',
  SELLER = 'seller',
}

export default function AdminLayout({ children }) {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { onLogout } = useLogin();
  const { loading } = useProtected();

  const mobileMenu = useHookstate(false);

  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'Q' && (e.metaKey || e.ctrlKey) && e.shiftKey) {
        e.preventDefault();
        onLogout();
      } else if (e.key === 's' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        onSettings();
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, []);

  const onSettings = async () => {
    router.push('/admin/settings');
  };

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <ScrollArea className="h-screen">
      {gStateP.user.value && (
        <main>
          {/* Navbar */}
          <nav className="flex border-b">
            <div className="w-auto p-4 pb-3 lg:w-[290px] lg:px-2 lg:pb-4">
              <Button variant="outline" size="icon" className="shrink-0 lg:hidden" onClick={() => mobileMenu.set(true)}>
                <Menu className="size-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
              <img className="mx-auto hidden h-[2.8rem] lg:block" src="/kita-logo.png" alt="kitaph logo" />
            </div>

            <div className="flex flex-1 items-end pb-3 pl-0 pr-6 lg:border-l lg:pl-6">
              <div className="flex flex-1 items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  {/* find active Title */}
                  <h1 className="hidden font-inter text-xl font-bold lg:block">
                    {MENU.find((item) => item.id === gStateP.admin.activeMenu.value).name}
                  </h1>

                  {/* Accounts */}
                  {gStateP.admin.activeMenu.value === 1 && (
                    <>
                      <div className="hidden lg:block">
                        <Tabs
                          defaultValue="members"
                          value={gStateP.tabsAccounts.value}
                          onValueChange={(v) => {
                            gStateP.tabsAccounts.set(v);
                          }}
                          onClick={() => {
                            if (window.location.pathname !== '/admin/') {
                              router.push('/admin/accounts');
                            }
                          }}
                          className=""
                        >
                          <TabsList className="">
                            {/* <TabsTrigger value="members">Members</TabsTrigger> */}
                            <TabsTrigger value="nonloan">Non-Loan Holders</TabsTrigger>
                            <TabsTrigger value="loan">Loan Holders</TabsTrigger>
                            <TabsTrigger value="request">Member Request</TabsTrigger>
                          </TabsList>
                        </Tabs>
                      </div>

                      <div className="block lg:hidden">
                        <Select
                          defaultValue="members"
                          value={gStateP.tabsAccounts.value}
                          onValueChange={(v) => {
                            gStateP.tabsAccounts.set(v);
                          }}
                          onOpenChange={() => {
                            if (window.location.pathname !== '/admin/') {
                              router.push('/admin/accounts');
                            }
                          }}
                        >
                          <SelectTrigger className="w-[180px]" icon="up-down">
                            <SelectValue placeholder="Members" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              {/* <SelectItem value="members">Members</SelectItem> */}
                              <SelectItem value="nonloan">Non-Loan Holders</SelectItem>
                              <SelectItem value="loan">Loan Holders</SelectItem>
                              <SelectItem value="request">Request</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}

                  {/* Product Management Tabs */}
                  {gStateP.admin.activeMenu.value === 2 && (
                    <>
                      <div className="hidden lg:block">
                        <Tabs
                          defaultValue="crops"
                          value={gStateP.tabsProductMgt.value}
                          onValueChange={(v) => {
                            gStateP.tabsProductMgt.set(v);
                          }}
                          onClick={() => {
                            if (window.location.pathname !== '/admin/product-management') {
                              router.push('/admin/product-management');
                            }
                          }}
                          className=""
                        >
                          <TabsList className="">
                            <TabsTrigger value="crops">Crops</TabsTrigger>
                            <TabsTrigger value="fertilizer">Fertilizer</TabsTrigger>
                            <TabsTrigger value="chemicals">Crop Protection</TabsTrigger>
                            <TabsTrigger value="seeds">Seeds</TabsTrigger>
                            <TabsTrigger value="others">Others</TabsTrigger>
                          </TabsList>
                        </Tabs>
                      </div>

                      <div className="block lg:hidden">
                        <Select
                          defaultValue="members"
                          value={gStateP.tabsProductMgt.value}
                          onValueChange={(v) => {
                            gStateP.tabsProductMgt.set(v);
                          }}
                          onOpenChange={() => {
                            if (window.location.pathname !== '/admin/product-management') {
                              router.push('/admin/product-management');
                            }
                          }}
                        >
                          <SelectTrigger className="w-[180px]" icon="up-down">
                            <SelectValue placeholder="Members" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="crops">Crops</SelectItem>
                              <SelectItem value="fertilizer">Fertilizer</SelectItem>
                              <SelectItem value="chemicals">Crop Protection</SelectItem>
                              <SelectItem value="seeds">Seeds</SelectItem>
                              <SelectItem value="others">Others</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}

                  {/* Trading App */}
                  {gStateP.admin.activeMenu.value === 4 && (
                    <>
                      <div className="hidden lg:block">
                        <Tabs
                          defaultValue={TAB_TRADING_APP.QA}
                          value={gStateP.tabsTradingApp.value}
                          onValueChange={(v) => {
                            gStateP.tabsTradingApp.set(v);
                          }}
                          onClick={() => {
                            if (window.location.pathname !== '/admin/trading-app') {
                              router.push('/admin/trading-app');
                            }
                          }}
                          className=""
                        >
                          <TabsList className="">
                            <TabsTrigger value={TAB_TRADING_APP.QA}>QA</TabsTrigger>
                            <TabsTrigger value={TAB_TRADING_APP.BUYER}>Buyer</TabsTrigger>
                            <TabsTrigger value={TAB_TRADING_APP.SELLER}>Seller</TabsTrigger>
                          </TabsList>
                        </Tabs>
                      </div>

                      <div className="block lg:hidden">
                        <Select
                          defaultValue={TAB_TRADING_APP.QA}
                          value={gStateP.tabsTradingApp.value}
                          onValueChange={(v) => {
                            gStateP.tabsTradingApp.set(v);
                          }}
                          onOpenChange={() => {
                            if (window.location.pathname !== '/admin/trading-app') {
                              router.push('/admin/trading-app');
                            }
                          }}
                        >
                          <SelectTrigger className="w-[180px]" icon="up-down">
                            <SelectValue placeholder={TAB_TRADING_APP.QA} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value={TAB_TRADING_APP.QA}>QA</SelectItem>
                              <SelectItem value={TAB_TRADING_APP.BUYER}>Buyer</SelectItem>
                              <SelectItem value={TAB_TRADING_APP.SELLER}>Seller</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}

                  {/* Marketplace Tabs */}
                  {gStateP.admin.activeMenu.value === 5 && (
                    <>
                      <div className="hidden lg:block">
                        <Tabs
                          defaultValue={TAB_MARKETPLACE.ORDERS}
                          value={gStateP.tabsMarketplace.value}
                          onValueChange={(v) => {
                            gStateP.tabsMarketplace.set(v);
                          }}
                          onClick={() => {
                            if (window.location.pathname !== '/admin/marketplace') {
                              router.push('/admin/marketplace');
                            }
                          }}
                          className=""
                        >
                          <TabsList className="">
                            <TabsTrigger value={TAB_MARKETPLACE.ORDERS}>Orders</TabsTrigger>
                            <TabsTrigger value={TAB_MARKETPLACE.PRODUCTS}>Products</TabsTrigger>
                            <TabsTrigger value={TAB_MARKETPLACE.EWALLET}>E-Wallet</TabsTrigger>
                            <TabsTrigger value={TAB_MARKETPLACE.PAYMENTS}>Payments</TabsTrigger>
                            <TabsTrigger value={TAB_MARKETPLACE.REPORTS}>Reports</TabsTrigger>
                          </TabsList>
                        </Tabs>
                      </div>

                      <div className="block lg:hidden">
                        <Select
                          defaultValue={TAB_MARKETPLACE.ORDERS}
                          value={gStateP.tabsMarketplace.value}
                          onValueChange={(v) => {
                            gStateP.tabsMarketplace.set(v);
                          }}
                          onOpenChange={() => {
                            if (window.location.pathname !== '/admin/marketplace') {
                              router.push('/admin/marketplace');
                            }
                          }}
                        >
                          <SelectTrigger className="w-[180px]" icon="up-down">
                            <SelectValue placeholder={TAB_MARKETPLACE.ORDERS} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value={TAB_MARKETPLACE.ORDERS}>Orders</SelectItem>
                              <SelectItem value={TAB_MARKETPLACE.PRODUCTS}>Products</SelectItem>
                              <SelectItem value={TAB_MARKETPLACE.EWALLET}>E-Wallet</SelectItem>
                              <SelectItem value={TAB_MARKETPLACE.PAYMENTS}>Payments</SelectItem>
                              <SelectItem value={TAB_MARKETPLACE.REPORTS}>Reports</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}
                </div>

                {gStateP['user'].value && (
                  <div className="flex items-center gap-3">
                    <div className="hidden lg:block">
                      <span>Hello, </span>
                      <span className="font-medium capitalize">
                        {gStateP['user']['user'].email.value.split('@')[0]}
                      </span>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="link" size="icon" className="rounded-full">
                          <Avatar>
                            <AvatarImage
                              src={gStateP['user']['user'].user_img?.value}
                              alt={gStateP['user']['user'].email.value}
                            />
                            <AvatarFallback className="uppercase">
                              {gStateP['user']['user'].email.value.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="absolute -right-4 top-0 w-56">
                        <DropdownMenuLabel className="font-normal">
                          <div className="flex flex-col space-y-1">
                            <p className="text-sm font-medium capitalize leading-none">
                              {gStateP['user']['user'].email.value.split('@')[0]}
                            </p>
                            <p className="text-xs leading-none text-muted-foreground">
                              {gStateP['user']['user'].email.value}
                            </p>
                          </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                          {/* <DropdownMenuItem>
                          <User className="mr-2 size-4" />
                          <span>Profile</span>
                          <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
                        </DropdownMenuItem> */}
                          <DropdownMenuItem onClick={onSettings}>
                            <Settings className="mr-2 size-4" />
                            <span>Settings</span>
                            <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
                          </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={onLogout}>
                          <LogOut className="mr-2 size-4" />
                          <span>Log out</span>
                          <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
              </div>
            </div>
          </nav>

          {/* Mobile Menu */}
          <Sheet open={mobileMenu.value} onOpenChange={mobileMenu.set}>
            <SheetContent side="left" className="flex flex-col px-0">
              <img className="mx-auto mt-2 block h-[2.8rem] lg:hidden" src="/kita-logo.png" alt="kitaph logo" />

              <MenuMapper gStateP={gStateP} mobileMenu={mobileMenu} router={router} />
            </SheetContent>
          </Sheet>

          {/* Content */}
          <div className="flex min-h-[calc(100vh-70px)] flex-col lg:min-h-full lg:flex-row">
            <div className="hidden lg:flex">
              <MenuMapper gStateP={gStateP} mobileMenu={mobileMenu} router={router} />
            </div>

            <section className="flex-1 border-l bg-gray-50">{children}</section>
          </div>
        </main>
      )}
    </ScrollArea>
  );
}
