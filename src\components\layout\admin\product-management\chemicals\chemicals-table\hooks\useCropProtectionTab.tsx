'use client';

import { useGlobalState } from '@/lib/store';

import { activeColumns } from '../active-columns';
import { columns } from '../columns';
import { modeColumns } from '../mode-columns';
import { columns as subCategoryColumns } from '../subcategory-columns';

export default function useCropProtectionTab() {
  const gState = useGlobalState();

  return {
    all: {
      id: 'chemicals',
      columns: columns,
      data: gState.admin.chemicals.data.get({ noproxy: true }),
    },
    subcat: {
      id: 'chemicals-subcategory',
      columns: subCategoryColumns,
      data: gState.admin.chemicals.subcategory.get({ noproxy: true }),
    },
    active: {
      id: 'chemicals-active',
      columns: activeColumns,
      data: gState.admin.chemicals.active.get({ noproxy: true }),
    },
    mode: {
      id: 'chemicals-mode',
      columns: modeColumns,
      data: gState.admin.chemicals.mode.get({ noproxy: true }),
    },
  };
}
