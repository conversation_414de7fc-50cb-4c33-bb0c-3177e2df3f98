'use client';

import { useRouter } from 'next/navigation';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

export default function Header() {
  const router = useRouter();

  return (
    <div>
      <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Create Master Template</h1>
      <Breadcrumb className="mt-2">
        <BreadcrumbList>
          <BreadcrumbItem className="cursor-pointer">
            <BreadcrumbLink onClick={() => router.back()}>Farm Plan</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Create Master Template</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
