'use client';

import { useHookstate } from '@hookstate/core';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useEffect } from 'react';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Input, InputSign } from '@/components/ui/input';

import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useShuruCreditScoring from '@/lib/hooks/admin/useShuruCreditScoring';
import useShuruUtils from '@/lib/hooks/utils/useShuruUtils';
import { useCreditScoreState } from '@/lib/store/creditScore';
import { catchError, cn } from '@/lib/utils';

import { ErrorDialog } from './error-dialog';
import { SaveConfirmation } from './save-confirmation';

export default function AfterLoan() {
  const shuruData = useShuruUtils();
  const { updateRules } = useShuruCreditScoring();
  const { updateGroupSilent, selectedGroup } = useCreditScoreMgt();
  const [creditScoreGroupId, setCreditScoreGroupId] = useQueryState('id', parseAsInteger);

  const creditScore = useCreditScoreState();
  const afterTotal = useHookstate(creditScore.after.total);
  const creditHistory = useHookstate(creditScore.after.creditHistory);
  const loanCycles = useHookstate(creditScore.after.loanCycles);
  const agricultureActivity = useHookstate(creditScore.after.agricultureActivity);
  const targetYield = useHookstate(creditScore.after.targetYield);

  const errorDialog = useHookstate(false);
  const confirmDialog = useHookstate(false);
  const isLoadingConfirm = useHookstate(false);

  const onSave = async () => {
    try {
      isLoadingConfirm.set(true);
      let dataToSubmit = [];

      // Update Group Grace Period Copy
      await updateGroupSilent({
        gracePeriod: creditHistory.gracePeriodDays.value,
        creditScoreGroupId: selectedGroup.id.value,
      });

      // Credit History & Loan Cycles
      const _creditHistory = shuruData.rules['after']['Credit History'].get({ noproxy: true });
      delete _creditHistory.rule_category_score;
      _creditHistory.repaymentBehavior.max_score = creditHistory.repaymentBehavior.value;

      _creditHistory.repaymentBehavior.levels[0].score = creditHistory.paymentBeforeDue.value;
      _creditHistory.repaymentBehavior.levels[1].score = creditHistory.paymentGracePeriod.value;
      _creditHistory.repaymentBehavior.levels[1].upper_bound = creditHistory.gracePeriodDays.value;
      _creditHistory.repaymentBehavior.levels[2].lower_bound = creditHistory.gracePeriodDays.value + 1;
      _creditHistory.repaymentBehavior.levels[2].score = creditHistory.paymentBeyondGracePeriod.value;

      // Loan Cycles
      _creditHistory.totalLoanCycles.max_score = loanCycles.loanCycle.value;
      _creditHistory.totalLoanCycles.levels = _creditHistory.totalLoanCycles.levels.map((lvl) => {
        const order = lvl['order'];
        const condition = loanCycles.conditions[order].get({ noproxy: true });
        let lower_bound =
          order === 1 ? condition['from'] : loanCycles.conditions[order - 1]['loan'].get({ noproxy: true }) + 1;
        let upper_bound = order === 4 ? null : condition['loan'];

        return {
          ...lvl,
          score: condition['pts'],
          lower_bound,
          upper_bound,
        };
      });
      dataToSubmit.push(...Object.values(_creditHistory));

      // Agriculture Activity
      const _agricultureActivity = shuruData.rules['after']['Agriculture Activity'].get({ noproxy: true });
      delete _agricultureActivity.rule_category_score;
      _agricultureActivity.targetYieldAchievement.max_score = agricultureActivity.targetYieldAchievement.value;

      _agricultureActivity.targetYieldAchievement.levels[0].score = targetYield.yield1.value;
      _agricultureActivity.targetYieldAchievement.levels[1].score = targetYield.yield2.value;
      _agricultureActivity.targetYieldAchievement.levels[2].score = targetYield.yield3.value;
      dataToSubmit.push(...Object.values(_agricultureActivity));

      await updateRules(creditScoreGroupId, dataToSubmit);
    } catch (e) {
      catchError(e, 'onSave After');
    } finally {
      isLoadingConfirm.set(false);
      confirmDialog.set(false);
    }
  };

  useEffect(
    () =>
      afterTotal.subscribe((v) => {
        const _total = v.creditHistory + v.loanCycles + v.agricultureActivity;
        creditScore.total.after.set(_total);
      }),
    [],
  );

  useEffect(
    () =>
      creditHistory.subscribe((v) => {
        afterTotal.creditHistory.set(v.repaymentBehavior);
      }),
    [],
  );

  useEffect(
    () =>
      loanCycles.subscribe((v) => {
        afterTotal.loanCycles.set(v.loanCycle);
      }),
    [],
  );

  useEffect(
    () =>
      agricultureActivity.subscribe((v) => {
        afterTotal.agricultureActivity.set(v.targetYieldAchievement);
      }),
    [],
  );

  return (
    <div className="grid gap-4">
      <div className="max-w-5xl">
        <Accordion type="single" collapsible className="w-full">
          {/* Credit History */}
          <AccordionItem value="item-1">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Credit History</h1>
                <div className="font-bold text-primary">{`${afterTotal.creditHistory.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Repayment Behavior</h1>
                <div className="">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={creditHistory.repaymentBehavior.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      creditHistory.repaymentBehavior.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between pl-8 sm:pl-16">
                <h1 className="text-sm font-light">Conditional</h1>
              </div>

              <div className="flex max-w-2xl items-center justify-between pl-8 sm:pl-16">
                <h1 className="font-medium">If Payment is made on or before the due date.</h1>
                <div className="">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={creditHistory.paymentBeforeDue.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      creditHistory.paymentBeforeDue.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-2xl items-center justify-between pl-8 sm:pl-16">
                <div>
                  <h1 className="font-medium">If Payment is within grace period</h1>

                  <div className="flex items-center gap-4">
                    <div className="text-sm font-light">Grace period days (Days before due date)</div>
                    <Input
                      className="h-8 max-w-24"
                      placeholder="0"
                      type="number"
                      value={creditHistory.gracePeriodDays.value}
                      onChange={(e) => creditHistory.gracePeriodDays.set(Number(e.target.value))}
                    />
                  </div>
                </div>
                <div className="">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={creditHistory.paymentGracePeriod.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      creditHistory.paymentGracePeriod.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-2xl items-center justify-between pl-8 sm:pl-16">
                <div>
                  <h1 className="font-medium">If Payment is beyond grace period</h1>

                  <div className="flex items-center gap-4">
                    <div className="text-sm font-light">
                      Choose from the options what will happen to farmers whose payments are past the grace period.
                    </div>
                    <div className="shrink-0">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={creditHistory.paymentBeyondGracePeriod.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          creditHistory.paymentBeyondGracePeriod.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* No. of Loan Cycles */}
          <AccordionItem value="item-2">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Loan Cycles</h1>
                <div className="font-bold text-primary">{`${afterTotal.loanCycles.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">No. of Loan Cycles</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={loanCycles.loanCycle.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      loanCycles.loanCycle.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="max-w-2xl pb-4 pl-16">
                <div>
                  <div className="text-sm font-light">Conditional</div>

                  <div className="ml-6 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <Input
                          className="h-8 max-w-16 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          defaultValue={loanCycles.conditions['1'].from.value}
                          disabled
                        />
                        <div>to</div>
                        <Input
                          className="h-8 max-w-16 focus-visible:ring-primary"
                          placeholder="0"
                          min={2}
                          type="number"
                          value={loanCycles.conditions['1'].loan.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            loanCycles.conditions['1'].loan.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={loanCycles.conditions['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            loanCycles.conditions['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-6 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <Input
                          className="h-8 max-w-16 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          value={loanCycles.conditions['1'].loan.value + 1}
                          disabled
                        />
                        <div>to</div>
                        <Input
                          className="h-8 max-w-16 focus-visible:ring-primary"
                          placeholder="0"
                          min={4}
                          type="number"
                          value={loanCycles.conditions['2'].loan.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            loanCycles.conditions['2'].loan.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={loanCycles.conditions['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            loanCycles.conditions['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-6 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <Input
                          className="h-8 max-w-16 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          value={loanCycles.conditions['2'].loan.value + 1}
                          disabled
                        />
                        <div>to</div>
                        <Input
                          className="h-8 max-w-16 focus-visible:ring-primary"
                          placeholder="0"
                          min={6}
                          type="number"
                          value={loanCycles.conditions['3'].loan.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            loanCycles.conditions['3'].loan.set(Number(e.target.value));
                            loanCycles.conditions['4'].loan.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={loanCycles.conditions['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            loanCycles.conditions['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-6 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <Input
                          className="h-8 max-w-16 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={loanCycles.conditions['4'].loan.value}
                        />
                      </div>

                      <div className="relative">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              loanCycles.conditions['4'].pts.value > loanCycles.loanCycle.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={loanCycles.conditions['4'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              loanCycles.conditions['4'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {loanCycles.conditions['4'].pts.value > loanCycles.loanCycle.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${loanCycles.loanCycle.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Agriculture Activity */}
          <AccordionItem value="item-3">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Agriculture Activity</h1>
                <div className="font-bold text-primary">{`${afterTotal.agricultureActivity.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Target yield Achievement</h1>
                <div className="">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.targetYieldAchievement.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.targetYieldAchievement.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 grid max-w-2xl gap-3 sm:ml-20">
                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="mt-2">
                      If the farmer achieves a target yield within the range of 80% to 100%, assign
                    </div>

                    <div className="relative mb-4 shrink-0">
                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn(
                            'max-w-[10rem] h-8 focus-visible:ring-primary',
                            targetYield.yield1.value > agricultureActivity.targetYieldAchievement.value
                              ? 'border-red-500 focus-visible:ring-red-500'
                              : '',
                          )}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={targetYield.yield1.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            targetYield.yield1.set(Number(e.target.value));
                          }}
                        />
                      </div>
                      {targetYield.yield1.value > agricultureActivity.targetYieldAchievement.value && (
                        <div className="form-error absolute -bottom-6 w-max !text-xs">
                          {`The number you enter here should be ${agricultureActivity.targetYieldAchievement.value} or less.`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="mt-2">
                      If the farmer achieves a target yield within the range of 71% to 79%, assign
                    </div>

                    <div className="relative mb-4 shrink-0">
                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn(
                            'max-w-[10rem] h-8 focus-visible:ring-primary',
                            targetYield.yield2.value > agricultureActivity.targetYieldAchievement.value
                              ? 'border-red-500 focus-visible:ring-red-500'
                              : '',
                          )}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={targetYield.yield2.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            targetYield.yield2.set(Number(e.target.value));
                          }}
                        />
                      </div>
                      {targetYield.yield2.value > agricultureActivity.targetYieldAchievement.value && (
                        <div className="form-error absolute -bottom-6 w-max !text-xs">
                          {`The number you enter here should be ${agricultureActivity.targetYieldAchievement.value} or less.`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="mt-2">If the farmer achieves a target yield 70% and below, assign</div>

                    <div className="relative mb-4 shrink-0">
                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn(
                            'max-w-[10rem] h-8 focus-visible:ring-primary',
                            targetYield.yield3.value > agricultureActivity.targetYieldAchievement.value
                              ? 'border-red-500 focus-visible:ring-red-500'
                              : '',
                          )}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={targetYield.yield3.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            targetYield.yield3.set(Number(e.target.value));
                          }}
                        />
                      </div>
                      {targetYield.yield3.value > agricultureActivity.targetYieldAchievement.value && (
                        <div className="form-error absolute -bottom-6 w-max !text-xs">
                          {`The number you enter here should be ${agricultureActivity.targetYieldAchievement.value} or less.`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <div className="flex w-full max-w-5xl justify-between py-4">
          <h1 className="font-bold text-primary">Total</h1>
          <div className="font-bold text-primary">{`${creditScore.total.after.value}%`}</div>
        </div>

        <ErrorDialog state={errorDialog} />
        <SaveConfirmation state={confirmDialog} onSave={onSave} isLoading={isLoadingConfirm.value} />
      </div>

      <div className="mt-8">
        <Button
          className="px-12"
          onClick={() => {
            if (creditScore.total.after.value > 100 || creditScore.total.after.value < 100) {
              errorDialog.set(true);
              return;
            }

            confirmDialog.set(true);
          }}
        >
          Submit
        </Button>
      </div>
    </div>
  );
}
