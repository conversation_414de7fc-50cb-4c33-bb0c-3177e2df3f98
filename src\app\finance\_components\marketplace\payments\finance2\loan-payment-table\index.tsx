'use client';

import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { LoanRequestStatus } from '@/app/admin/marketplace/_components/Enums';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function LoanPaymentTable({ columns, data, meta = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;

        router.push(`/finance/payment/request/details?id=${data.id}`);
        console.log('row clicked', data);
      },
    },
  });

  return (
    <div className="space-y-4">
      <DataTableToolbar id="finance2-loan-payment-table" table={table} meta={meta} />

      <div>
        <Tabs
          defaultValue="0"
          value={gState.finance.loanPayments.pagination.requests.status[0].value}
          onValueChange={(v) => {
            if (v == '1') {
              gState.finance.loanPayments.pagination.requests.status.set(['1', '2']);
              return;
            }
            gState.finance.loanPayments.pagination.requests.status.set([v]);
          }}
          className=""
        >
          <TabsList className="">
            <TabsTrigger value="0">For Approval</TabsTrigger>
            <TabsTrigger value="1">Approved</TabsTrigger>
            <TabsTrigger value="3">Rejected</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="max-w-[calc(100vw-356px)] rounded-md border bg-white">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  className="hover:cursor-pointer"
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={(event) => {
                    const target = event.target as HTMLElement;
                    const isButton = target.tagName.toLowerCase() === 'button';
                    const isP = target.tagName.toLowerCase() === 'p';
                    const isHeading = target.tagName.toLowerCase() === 'h2';
                    const isOpen = target.dataset.state === 'open';
                    const isDisabled = target.classList.contains('flex');

                    if (!isButton && !isP && !isHeading && !isOpen && !isDisabled) {
                      table.options.meta?.getRowClicked?.(row);
                    }
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {meta ? (
        <DataTablePaginationMeta
          table={table}
          meta={meta}
          onChangePageSize={(pageSize) => {
            gState.finance.loanPayments.pagination.requests.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            gState.finance.loanPayments.pagination.requests.page.set(page);
          }}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}
