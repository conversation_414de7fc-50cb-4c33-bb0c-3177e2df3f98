'use client';

export default function Stats() {
  return (
    <div className="mt-8 grid grid-cols-3 items-center gap-8">
      <div className="card row-span-2 rounded-lg border-l-[10px] border-[#05CD99] bg-white px-6 py-8">
        <div className="flex items-center justify-between">
          <div className="text-sm font-bold text-gray-700">Credit Scoring</div>

          <div className="relative text-end">
            <div className="text-2xl font-bold">82%</div>
            <div className="absolute -bottom-1/2 right-0">
              <div className="min-w-max text-xs text-blue-500">+5% from yesterday</div>
            </div>
          </div>
        </div>
      </div>

      <div className="card rounded-lg border-l-[10px] border-[#04BEFE] bg-white px-6 py-8">
        <div className="flex items-center justify-between">
          <div className="text-sm font-bold text-gray-700">Farmer Profile</div>

          <div className="relative text-end">
            <div className="text-2xl font-bold">8%</div>
            <div className="absolute -bottom-1/2 right-0">
              <div className="min-w-max text-xs text-blue-500">+5% from yesterday</div>
            </div>
          </div>
        </div>
      </div>

      <div className="card rounded-lg border-l-[10px] border-[#BF83FF] bg-white px-6 py-8">
        <div className="flex items-center justify-between">
          <div className="text-sm font-bold text-gray-700">Transaction Records</div>

          <div className="relative text-end">
            <div className="text-3xl font-bold">27%</div>
            {/* <div className="absolute -bottom-1/2 right-0">
              <div className="min-w-max text-xs text-blue-500">+5% from yesterday</div>
            </div> */}
          </div>
        </div>
      </div>

      <div className="card rounded-lg border-l-[10px] border-[#FF947A] bg-white px-6 py-8">
        <div className="flex items-center justify-between">
          <div className="text-sm font-bold text-gray-700">Agriculture Activity</div>

          <div className="relative text-end">
            <div className="text-3xl font-bold">27%</div>
            {/* <div className="absolute -bottom-1/2 right-0">
              <div className="min-w-max text-xs text-blue-500">+5% from yesterday</div>
            </div> */}
          </div>
        </div>
      </div>

      <div className="card rounded-lg border-l-[10px] border-[#FFCF00] bg-white px-6 py-8">
        <div className="flex items-center justify-between">
          <div className="text-sm font-bold text-gray-700">Credit History</div>

          <div className="relative text-end">
            <div className="text-3xl font-bold">32%</div>
            {/* <div className="absolute -bottom-1/2 right-0">
              <div className="min-w-max text-xs text-blue-500">+5% from yesterday</div>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
}
