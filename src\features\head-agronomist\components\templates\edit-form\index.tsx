'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

import { useEditFarmPlanTemplate } from '@/features/head-agronomist/hooks/useEditFarmPlanTemplate';
import { useUpdateFarmPlanTemplate } from '@/features/head-agronomist/hooks/useUpdateFarmPlanTemplate';
import { useCrops } from '@/features/head-agronomist/store/crops-store';
import { ETemplateItemType } from '@/features/head-agronomist/types/farmplan-templates';

import { ITemplateForm } from '../create-form/types/template';
import CashReqSummary from '../shared-form/cash-requirements-summary';
import CostSummary from '../shared-form/cost-summary';
import CropInfo from '../shared-form/crop-info';
import FarmerEquitySummary from '../shared-form/farmer-equity-summary';
import InputCostSummary from '../shared-form/input-cost-summary';
import FoliarFertilization from '../shared-form/inputs-foliar-fert';
import PesticideApplication from '../shared-form/inputs-pesticide-application';
import SeedRequirements from '../shared-form/inputs-seed-requirements';
import SoilFertilizationSideDress from '../shared-form/inputs-soil-fert-side-dress';
import SoilFertilizationTopDress from '../shared-form/inputs-soil-fert-top-dress';
import KitaSubsidizedCosts from '../shared-form/kita-subsidized-costs';
import LaborRequirements from '../shared-form/labor-reqs';
import LoanRepaymentCalc from '../shared-form/loan-repayment-calculator';
import NonCashCosts from '../shared-form/non-cash-costs';
import NonKitaSubsidizedCosts from '../shared-form/nonkita-subsidized-costs';
import OtherFarmMaterials from '../shared-form/other-farm-materials';
import OtherProductionCosts from '../shared-form/other-production-costs';
import Header from './header';

interface EditMasterTemplateProps {
  id: number;
}

export default function EditMasterTemplate({ id }: EditMasterTemplateProps) {
  // State for controlling form reset
  const [resetKey, setResetKey] = useState(0);

  // Use the edit and update template hooks
  const { farmPlanTemplateByIdQuery } = useEditFarmPlanTemplate(id);
  const { submitTemplate, isSubmitting } = useUpdateFarmPlanTemplate();
  const { cropsQuery } = useCrops();

  // Initialize the form with the required structure
  const form = useForm<ITemplateForm>({
    defaultValues: {
      cropId: 0,
      versionNumber: '',
      location: '',
      contingencyForFluctuation: 0,
      interestRate: 0,
      numberOfMonthsPerTenor: 0,
      aorPerMonth: 0,
      items: [
        {
          name: 'Seed / Seedling Requirements (SE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Basal (Top-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Additional (Side-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Foliar Fertilization (Spray) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Pesticide Application (Spray / Spread) (CP)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Other Farm Materials (OT)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Labor Requirements (LR)',
          type: ETemplateItemType.LABOR,
          subItems: [],
        },
        {
          name: 'Other Production Costs (OT)',
          type: ETemplateItemType.OTHERS,
          subItems: [],
        },
        {
          name: 'Non-Cash Costs (NC)',
          type: ETemplateItemType.NON_CASH,
          subItems: [],
        },
        {
          name: 'KITA Subsidized Costs (KS)',
          type: ETemplateItemType.KITA_SUBSIDIZED,
          subItems: [],
        },
        {
          name: 'Non-KITA Subsidized Costs (NS)',
          type: ETemplateItemType.NON_KITA_SUBSIDIZED,
          subItems: [],
        },
      ],
    },
  });

  // Handle form submission with dialog state check
  const onSubmit = form.handleSubmit(async (data, event) => {
    // Check if any dialogs are currently open by looking for dialog elements
    const openDialogs = document.querySelectorAll('[role="dialog"][data-state="open"]');
    if (openDialogs.length > 0) {
      console.log('Form submission prevented: Dialog is open');
      event?.preventDefault();
      event?.stopPropagation();
      return;
    }

    await submitTemplate({
      ...data,
      farmPlanTemplateId: id,
    });
  });

  // Transform API data to form structure when data is loaded
  useEffect(() => {
    if (farmPlanTemplateByIdQuery.data && cropsQuery.isSuccess) {
      const template = farmPlanTemplateByIdQuery.data;

      // Transform the API data to match form structure
      const transformedData: ITemplateForm = {
        cropId: template.crop_id,
        versionNumber: template.version_number,
        location: template.location,
        contingencyForFluctuation: template.contingency_for_fluctuation,
        interestRate: template.interest_rate,
        numberOfMonthsPerTenor: template.number_of_months_per_tenor,
        aorPerMonth: template.aor_per_month,
        items: template.farmPlanTemplateItems.map((item) => ({
          farmPlanTemplateItemId: item.id,
          name: item.name,
          type: item.type as ETemplateItemType,
          subItems: item.farmPlanTemplateSubItems.map((subItem) => ({
            farmPlanTemplateSubItemId: subItem.id,
            expectedDate: subItem.expected_date,
            itemName: subItem.item_name,
            unit: subItem.unit,
            quantity: subItem.quantity,
            unitCost: subItem.unit_cost,
            notes: subItem.notes,
            marketplaceProductId: subItem.marketplace_product_id,
            totalAmount: subItem.total_amount,
          })),
        })),
      };

      // Update form values - always do a full reset to ensure UI reflects the latest data
      // This is safe because we've prevented form submission during dialog operations
      form.reset(transformedData);

      // Set reset key to indicate form has been initialized
      if (resetKey === 0) {
        setResetKey(1);
      }
    }
  }, [farmPlanTemplateByIdQuery.data, form, cropsQuery.isSuccess, resetKey]);

  // Show loading state while fetching data
  if (farmPlanTemplateByIdQuery.isLoading) {
    return (
      <div className="space-y-4 p-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    );
  }

  // Show error state if data fetch failed
  if (farmPlanTemplateByIdQuery.isError) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">Error loading template data. Please try again.</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <Header />

      <div className="my-6 rounded-md border border-blue-200 bg-blue-50 p-4">
        <p className="text-sm text-blue-700">
          Note: This master template is based on 1 hectare. For actual farm plans, input values are scaled according to
          the number of hectares.
        </p>
      </div>

      <form onSubmit={onSubmit} key={`${id}-${resetKey}`}>
        <CropInfo form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARM INPUTS FOR DELIVERY / PICK-UP (Amount to be Held in favor of KITA)
        </div>

        <SeedRequirements form={form} farmPlanTemplateId={id} />
        <SoilFertilizationTopDress form={form} farmPlanTemplateId={id} />
        <SoilFertilizationSideDress form={form} farmPlanTemplateId={id} />
        <FoliarFertilization form={form} farmPlanTemplateId={id} />
        <PesticideApplication form={form} farmPlanTemplateId={id} />
        <OtherFarmMaterials form={form} farmPlanTemplateId={id} />

        {/* Estimated Farm Inputs Costs Section */}
        <InputCostSummary form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          CASH REQUIREMENTS (Labor, Overhead, Other Cash Costs, Etc.)
        </div>

        <LaborRequirements form={form} farmPlanTemplateId={id} />
        <OtherProductionCosts form={form} farmPlanTemplateId={id} />
        <CashReqSummary form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARMER&apos;S EQUITY (Other Non-Cash Costs, Subsidized Costs, etc.)
        </div>
        <NonCashCosts form={form} farmPlanTemplateId={id} />
        <KitaSubsidizedCosts form={form} farmPlanTemplateId={id} />
        <NonKitaSubsidizedCosts form={form} farmPlanTemplateId={id} />
        <FarmerEquitySummary form={form} />

        <div className="mt-16 grid gap-4">
          <CostSummary />
          <LoanRepaymentCalc form={form} />
        </div>

        <div className="mt-16 flex justify-center">
          <Button type="submit" className="px-8" disabled={isSubmitting}>
            {isSubmitting ? 'Updating Template...' : 'Update Template'}
          </Button>
        </div>
      </form>
    </div>
  );
}
