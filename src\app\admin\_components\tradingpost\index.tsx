'use client';

import { useEffect } from 'react';
import { useDebounce } from 'use-debounce';

import useTradingPost from '@/lib/hooks/admin/useTradingPost';
import { useGlobalState } from '@/lib/store';

import { TransactionTable } from './transaction-table';
import { columns } from './transaction-table/columns';

export default function TransactionPage() {
  const gState = useGlobalState();
  const { fetchTransactions } = useTradingPost();
  const [search] = useDebounce(gState.admin.pagination.transaction.search.value, 1000);

  useEffect(() => {
    fetchTransactions();
  }, [
    gState.admin.pagination.transaction.page,
    gState.admin.pagination.transaction.pageSize,
    gState.admin.pagination.transaction['calendar'],
    search,
  ]);

  return (
    <div className="px-6 py-8">
      <TransactionTable
        columns={columns}
        data={gState.admin.transactions.data.get({ noproxy: true })}
        metadata={gState.admin.transactions['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
