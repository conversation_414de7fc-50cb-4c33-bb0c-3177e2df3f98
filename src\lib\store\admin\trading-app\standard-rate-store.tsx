'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { toast } from 'sonner';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import axios from '@/lib/api';
import { catchError } from '@/lib/utils';

const initialState = {
  [UserType.NONFARMER]: {
    percentageRate: 0 as number | string,
    logisticRate: 0 as number | string,
  },
  [UserType.FARMER]: {
    percentageRate: 0 as number | string,
    logisticRate: 0 as number | string,
  },
};

export const standardRateState = hookstate(
  initialState,
  devtools({
    key: 'standardRateState',
  }),
);

export const useStandardRateStore = (currentUser = 'admin') => {
  const state = useHookstate(standardRateState);
  const updating = useHookstate(false);

  const getStandardRate = async (userType: UserType.NONFARMER | UserType.FARMER) => {
    try {
      const res = await axios
        .get(`/${currentUser}/tradingapp/standard/price/view`, {
          params: {
            userType,
          },
        })
        .then((res) => res.data.data);
      state[userType].set({
        percentageRate: res.percentage_rate,
        logisticRate: res.logistic_rate,
      });
    } catch (e) {
      console.error(e);
    }
  };

  const updateRate = async ({
    userType,
    percentageRate,
    logisticRate,
  }: {
    userType: UserType.NONFARMER | UserType.FARMER;
    percentageRate: number;
    logisticRate: number;
  }) => {
    try {
      // validate percentage
      if (percentageRate > 100 || percentageRate < 0) {
        throw new Error('Percentage rate must be between 0 and 100');
      }

      // validate logistic rate
      if (logisticRate > 10000 || logisticRate < 0) {
        throw new Error('Logistic rate must be a positive number');
      }

      updating.set(true);

      const message = await axios
        .post(`/${currentUser}/tradingapp/standard/price/update`, {
          userType,
          percentageRate,
          logisticRate,
        })
        .then((res) => res.data.message);
      await getStandardRate(userType);

      toast.success('Success', {
        description: message,
      });
    } catch (e) {
      catchError(e, 'updateRate');
    } finally {
      updating.set(false);
    }
  };

  return { state, getStandardRate, updateRate, updating };
};
