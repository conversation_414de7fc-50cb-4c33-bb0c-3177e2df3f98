'use client';

import { Control, Controller, FieldErrors, FieldValues, UseFormRegister, UseFormWatch } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { cn } from '@/lib/utils';

import { OCCUPATION } from '../../Enums';

interface IOccupationalBackgroundFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
}

export default function OccupationalBackgroundForm({
  register,
  control,
  errors,
  watch,
}: IOccupationalBackgroundFormProps) {
  const watchOccupation = watch('occupation');
  const employmentOccupations = [
    OCCUPATION.GOVERNMENT_EMPLOYEE,
    OCCUPATION.PRIVATE_EMPLOYEE,
    OCCUPATION.CHURCH_SERVANTS,
    OCCUPATION.OFW,
    OCCUPATION.FARMER,
    OCCUPATION.LABORER,
  ];

  const businessOccupations = [
    OCCUPATION.SELF_EMPLOYED_PROFESSIONAL,
    OCCUPATION.SELF_EMPLOYED_NONPROFESSIONAL,
    OCCUPATION.BUSINESS_PERSON,
  ];

  return (
    <div>
      <FormTitle title="Occupational Background" />
      <div className="mt-6 grid gap-4 space-y-4 sm:grid-cols-2 xl:grid-cols-3">
        <FormField name="occupationTitle" label="Profession / Job Title" errors={errors}>
          <Input
            {...register('occupationTitle')}
            className={cn(
              'focus-visible:ring-primary',
              errors.occupationTitle && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Profession"
          />
        </FormField>

        <FormField name="occupation" label="Occupation" errors={errors}>
          <Controller
            control={control}
            name="occupation"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.occupation && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Occupation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {Object.values(OCCUPATION).map((occu) => (
                      <SelectItem key={occu} value={occu}>
                        {occu}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {employmentOccupations.includes(watchOccupation) && (
          <>
            <FormField name="occupationStatus" label="Occupation Status" errors={errors}>
              <Controller
                control={control}
                name="occupationStatus"
                render={({ field: { onChange, value } }) => (
                  <Select onValueChange={onChange} value={value}>
                    <SelectTrigger
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.occupationStatus && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                      )}
                    >
                      <SelectValue placeholder="Select Occupation Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectItem value="TEMPORARY">TEMPORARY</SelectItem>
                        <SelectItem value="PERMANENT">PERMANENT</SelectItem>
                        <SelectItem value="CONTRACTUAL">CONTRACTUAL</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
              />
            </FormField>

            <FormField name="occupationEmployerName" label="Employer Name" errors={errors}>
              <Input
                {...register('occupationEmployerName')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationEmployerName && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Employer Name"
              />
            </FormField>

            <FormField name="occupationEmployerAddress" label="Employer Address" errors={errors}>
              <Input
                {...register('occupationEmployerAddress')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationEmployerAddress && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Employer Address"
              />
            </FormField>

            <FormField name="occupationAnnualIncome" label="Annual Income" errors={errors}>
              <Input
                {...register('occupationAnnualIncome', {
                  required: false,
                  validate: {
                    isGreaterThanZero: (v) => (v ? /^\d+(?:\.\d{0,2})?$/.test(v) || 'Invalid Amount' : true),
                  },
                })}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationAnnualIncome && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Annual Income"
              />
            </FormField>
          </>
        )}

        {businessOccupations.includes(watchOccupation) && (
          <>
            <FormField name="occupationBusinessName" label="Business Name" errors={errors}>
              <Input
                {...register('occupationBusinessName')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationBusinessName && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Business Name"
              />
            </FormField>

            <FormField name="occupationBusinessAddress" label="Business Address" errors={errors}>
              <Input
                {...register('occupationBusinessAddress')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationBusinessAddress && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Business Address"
              />
            </FormField>

            <FormField name="occupationBusinessContact" label="Business Contact" errors={errors}>
              <Input
                {...register('occupationBusinessContact', {
                  required: false,
                  validate: {
                    isValidMobileNumber: (v) =>
                      v
                        ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                          'Invalid mobile number format (e.g. ***********)'
                        : true,
                  },
                })}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationBusinessContact && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Mobile No."
              />
            </FormField>

            <FormField name="occupationAnnualIncome" label="Annual Income" errors={errors}>
              <Input
                {...register('occupationAnnualIncome', {
                  required: false,
                  validate: {
                    isGreaterThanZero: (v) => (v ? /^\d+(?:\.\d{0,2})?$/.test(v) || 'Invalid Amount' : true),
                  },
                })}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.occupationAnnualIncome && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Annual Income"
              />
            </FormField>
          </>
        )}
      </div>
    </div>
  );
}
