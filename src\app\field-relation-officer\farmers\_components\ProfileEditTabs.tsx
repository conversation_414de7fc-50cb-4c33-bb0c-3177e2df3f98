'use client';

import { useHookstate } from '@hookstate/core';
import React from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { IFarmerBase } from '../../_components/types';
import BusinessInfo from './edit-profile/BusinessInfo';
import DataPrivacy from './edit-profile/DataPrivacy';
import FarmInformation from './edit-profile/FarmInformation';
import PersonalInfo from './edit-profile/PersonalInfo';

export default function ProfileEditTabs() {
  const { fro: gStatePFRO } = useGlobalStatePersist();
  const { fro: gStateFRO } = useGlobalState();

  const farmerData = useHookstate(gStateFRO.farmers.edit).value as IFarmerBase;

  const activeTab = useHookstate(gStatePFRO.edit.activeTab);

  return (
    <Tabs defaultValue={activeTab.value} className="mt-6 w-full">
      <TabsList className="flex w-full">
        <TabsTrigger value="data-privacy" onClick={() => activeTab.set('data-privacy')} className="flex-1">
          Consent & Data Privacy
        </TabsTrigger>
        <TabsTrigger value="personal-info" onClick={() => activeTab.set('personal-info')} className="flex-1">
          Personal Information
        </TabsTrigger>
        <TabsTrigger value="farm-info" onClick={() => activeTab.set('farm-info')} className="flex-1">
          Farm Information
        </TabsTrigger>
        <TabsTrigger value="business-info" onClick={() => activeTab.set('business-info')} className="flex-1">
          Financial & Business Info
        </TabsTrigger>
      </TabsList>

      <div className="card">
        <TabsContent value="data-privacy">
          <DataPrivacy data={farmerData.farmer.farmerDataPrivacy} />
        </TabsContent>

        <TabsContent value="personal-info">
          <PersonalInfo data={farmerData} />
        </TabsContent>

        <TabsContent value="farm-info">
          <FarmInformation data={farmerData.farmer} />
        </TabsContent>

        <TabsContent value="business-info">
          <BusinessInfo data={farmerData} />
        </TabsContent>
      </div>
    </Tabs>
  );
}
