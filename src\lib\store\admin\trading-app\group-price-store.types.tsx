import { z } from 'zod';

export interface IGroupData {
  meta: IMeta;
  data: IGroup[];
}

export interface IMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  first_page: number;
  first_page_url: string;
  last_page_url: string;
  next_page_url: string;
  previous_page_url: string;
}

export interface IGroup {
  id: number;
  percentage_rate: number;
  logistic_rate: number;
  user_type: number;
  name: string;
  remarks: string;
  status: null | number;
  created_at: string;
  updated_at: string;
}

// -----------------------------------

export const AddGroupSchema = z.object({
  name: z.string().min(1, 'Please fill in all required fields before submitting.'),
  userType: z.number().positive(),
  remarks: z.string().min(1, 'Please fill in all required fields before submitting.'),
  percentageRate: z
    .number()
    .gte(0, { message: 'Please enter a valid percentage rate (numeric values only).' })
    .lte(100, { message: 'Please enter a valid percentage rate (numeric values only).' }),
  logisticRate: z
    .number()
    .gte(0, { message: 'Please enter a valid logistic rate (numeric values only).' })
    .lte(10000, { message: 'Please enter a valid logistic rate (numeric values only).' }),
});
export type AddGroupType = z.infer<typeof AddGroupSchema>;

export const EditGroupSchema = z.object({
  tradingAppGroupPriceId: z.number().positive(),
  name: z.string().min(1, 'Please fill in all required fields before submitting.'),
  remarks: z.string().min(1, 'Please fill in all required fields before submitting.'),
  percentageRate: z
    .number()
    .gte(0, { message: 'Please enter a valid percentage rate (numeric values only).' })
    .lte(100, { message: 'Please enter a valid percentage rate (numeric values only).' }),
  logisticRate: z
    .number()
    .gte(0, { message: 'Please enter a valid logistic rate (numeric values only).' })
    .lte(10000, { message: 'Please enter a valid logistic rate (numeric values only).' }),
});
export type EditGroupType = z.infer<typeof EditGroupSchema>;
