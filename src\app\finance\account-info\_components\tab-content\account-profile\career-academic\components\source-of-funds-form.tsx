'use client';

import { Control, Controller, FieldErrors, FieldValues } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import MultipleSelector from '@/components/ui/multiple-selector';

import { OPTIONS_SOURCE_OF_FUNDS } from '../../Enums';

interface ISourceOfFundsFormProps {
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
}

export default function SourceOfFundsForm({ control, errors }: ISourceOfFundsFormProps) {
  return (
    <div>
      <div className="font-dmSans mt-6 text-xl font-bold text-primary">Source of Funds</div>
      <div className="mt-6 grid items-start gap-4 sm:grid-cols-2 2xl:grid-cols-3">
        <FormField name="sourceOfFunds" label="Source of Funds" errors={errors}>
          <Controller
            control={control}
            name="sourceOfFunds"
            render={({ field: { onChange, value } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTIONS_SOURCE_OF_FUNDS}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
        </FormField>
      </div>
    </div>
  );
}
