'use client';

import { State, useHookstate } from '@hookstate/core';
import { CaretDownIcon } from '@radix-ui/react-icons';
import { format, formatDistanceToNow } from 'date-fns';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';
import { FaCaretDown } from 'react-icons/fa';
import { toast } from 'sonner';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

import { LoanStage } from '@/lib/constants/enums';
import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useCreditScoring from '@/lib/hooks/admin/useCreditScoring';
import useLoanApplication from '@/lib/hooks/admin/useLoanApplication';
import { IKitaFormProps, usePdfForm } from '@/lib/hooks/usePdfForm';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { catchError, cn, isObjectEmpty } from '@/lib/utils';

import AccountInfo from './_components/AccountInfo';
import CircularProgress from './_components/CircularProgress';
import { CREDIT_TAB, LEFT_TAB } from './_components/constants';
import CreditScore from './_components/CreditScore';
import { DashboardStatCircular } from './_components/DashboardStatCircular';
import FetchAccountInfo from './_components/FetchAccountInfo';
import LandbankForm from './_components/LandbankForm';
import ProfileImage from './_components/ProfileImage';
import AccountProfileTabContent from './_components/tab-content/account-profile';
import AgricultureActivityTabContent from './_components/tab-content/agriculture-activity';
import CreditHistoryTabContent from './_components/tab-content/credit-history';
import CreditScoreTabContent from './_components/tab-content/credit-score';
import { SaveConfirmation } from './_components/tab-content/credit-score/save-confirmation';
import TransactionsTabContent from './_components/tab-content/transactions';

export default function AccountInfoPage() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const params = useSearchParams();
  const { downloadKitaForm } = usePdfForm();
  const account = useHookstate(gStateP.selected.account.info);
  const ait = params.get('ait');
  const cht = params.get('cht');

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { group, groups, getGroups } = useCreditScoreMgt();
  const { farmer, getCreditScoreByFarmer, beforeDetails, duringDetails, afterDetails } = useCreditScoring();
  const loanStage = useHookstate(gState.creditScoring.loanStage);
  const { loading, submitLoanApplication } = useLoanApplication();

  const leftTab = useHookstate(gState.selected.accountInfo.tabs.left);
  const creditTab = useHookstate(gState.selected.accountInfo.tabs.creditHistory);

  const data = useHookstate(gState.selected.accountInfo['info']);
  const submitConfirmation = useHookstate(false);
  const totalScore = useHookstate(0);

  const loan_stage = {
    BEFORE: 'BEFORE',
    DURING: 'DURING',
    AFTER: 'AFTER',
  };

  const onSubmitLoanApplication = async () => {
    try {
      const data = {
        userId: gState.selected.accountInfo['info']['id'].value,
        creditScoreGroupIds: groups.value.map((v) => (v.checked ? v.id : null)).filter(Boolean),
      };

      if (data.creditScoreGroupIds.length === 0) {
        toast.error('Please select at least one group');
        return;
      }

      await submitLoanApplication(data);
    } catch (e) {
      catchError(e, 'onSubmitLoanApplication');
    } finally {
      submitConfirmation.set(false);
    }
  };

  console.log({ farmerInfo: account['farmer'] });

  const handleDownloadKitaForm = () => {
    const data: IKitaFormProps = {
      givenName: account['farmer']['first_name'].value || '',
      middleName: account['farmer']['middle_name'].value || '',
      surname: account['farmer']['last_name'].value || '',
      birthPlace: account['farmer']['place_of_birth'].value || '',
      bday: account['farmer']['birth_date'].value || '',
      gender: account['farmer']['gender'].value || '',
      houseLot: account['farmer']['address_house_number'].value || '',
      street: account['farmer'][''].value || '',
      barangay: account['farmer']['address_barangay'].value || '',
      city: account['farmer']['address_city'].value || '',
      province: account['farmer']['address_province'].value || '',
      zipCode: account['farmer']['address_zip_code'].value || '',
      yearsResiding:
        account['farmer']['year_residing'].value || account['farmer']['permanent_address_length_of_stay'].value || '',
      residenceOwnership: account['farmer']['farmerInfo']['residence_ownership'].value || '',
      occupationTitle: account['farmer']['occupation_title'].value || '',
      mobileNumber: account['farmer']['mobile_number'].value || '',
      governmentIdentifications: account['farmer']['governmentIdentifications'].value || [],
      email: account['farmer'][''].value || '',
      civilStatus: account['farmer']['civil_status'].value || '',
      familyProfiles: account['farmer']['familyProfiles'].value || [],
      farmArea: account['farmer']['farmerInfo']['farm_area'].value || 0,
      farmOwnership: account['farmer']['farmerInfo']['farm_ownership'].value || 0,
      skillsFarming: account['farmer']['skills_farming'].value || '',
      skillsOthers: account['farmer']['skills_others'].value || '',
      cropsPlanted: account['farmer']['cropsPlanted'].value || '',
      sourceOfFunds: account['farmer']['source_of_funds'].value || '',
      annualIncome: account['farmer']['occupation_annual_income'].value || '',
    };

    downloadKitaForm(data);
  };

  useEffect(() => {
    if (groups.length === 0) {
      getGroups();
    }

    // Left Tab
    if (ait) {
      const find = LEFT_TAB.find((tab) => tab.value === (ait as string));
      if (find) leftTab.set(ait);
    }

    // Credit History Tab
    if (cht) {
      const find = CREDIT_TAB.find((tab) => tab.value === (cht as string));
      if (find) creditTab.set(cht);
    }
  }, []);

  useEffect(() => {
    if (loanStage.value === 'BEFORE') {
      totalScore.set(farmer['before_stage_credit_score']?.value);
    }
    if (loanStage.value === 'DURING') {
      totalScore.set(farmer['during_stage_credit_score']?.value);
    }
    if (loanStage.value === 'AFTER') {
      totalScore.set(farmer['after_stage_credit_score']?.value);
    }
  }, [gState.creditScoring.loanStage]);

  useEffect(() => {
    const farmerId = gState.selected.accountInfo?.['info']?.['farmer']?.['id']?.get({ noproxy: true });

    if (farmerId && group.value) {
      getCreditScoreByFarmer(farmerId);
    } else {
      console.warn('Farmer ID is undefined');
    }
  }, [gState.selected.accountInfo['info'], group]);

  return (
    <div className="p-6 md:p-8">
      <Suspense>
        <FetchAccountInfo />
      </Suspense>

      {/* Title */}
      <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between sm:gap-0">
        <div className="flex flex-1 justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Account Information</h1>
            <Breadcrumb className="mt-2">
              <BreadcrumbList>
                <BreadcrumbItem className="cursor-pointer">
                  <BreadcrumbLink onClick={() => router.back()}>Back</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Account Information</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <LandbankForm isDialogOpen={isDialogOpen} setIsDialogOpen={setIsDialogOpen} />
        </div>
        {leftTab.value === 'account_profile' && (
          <Select
            value=""
            onValueChange={(v) => {
              if (v === 'kita-form') {
                handleDownloadKitaForm();
                return;
              }
              setIsDialogOpen(true);
            }}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Print Document" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="kita-form" className="focus:bg-[#2F80ED] focus:text-white">
                  KITA FORM
                </SelectItem>
                <SelectItem value="lbp-cis-form" className="focus:bg-[#2F80ED] focus:text-white">
                  LBP CIS FORM
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        )}

        {leftTab.value === 'credit_score' && (
          <div className="flex items-center gap-4">
            {/* For Demo Purpose */}
            {/* <Select
              value={loanStage.value}
              onValueChange={(v) => {
                loanStage.set(v as LoanStage);
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Demo Loan Stage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Before Demo</SelectItem>
                <SelectItem value="2">During Demo</SelectItem>
                <SelectItem value="3">After Demo</SelectItem>
              </SelectContent>
            </Select> */}

            <Popover>
              <PopoverTrigger disabled={loanStage.value !== LoanStage.BEFORE} asChild>
                <Button className="w-full gap-4 sm:w-auto">
                  <span>Submit Loan Application</span>
                  <FaCaretDown className="size-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent align="end" className="px-0 pt-0">
                <ScrollArea className="flex max-h-[250px] flex-col">
                  <div className="space-y-3 px-4 pt-6">
                    {groups.map((group: State<unknown>) => {
                      const _group = group.value as any;

                      return (
                        <div key={_group.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={_group.id}
                            checked={group['checked'].value}
                            onCheckedChange={group['checked'].set}
                            disabled={group['disabled'].value}
                          />
                          <label
                            htmlFor={_group.id}
                            className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {_group.name}
                          </label>
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>

                <div className="mt-6 px-4">
                  <Button
                    variant="outline"
                    className="w-full border-kitaph-primary text-kitaph-primary"
                    onClick={() => submitConfirmation.set(true)}
                  >
                    Submit
                  </Button>
                </div>
              </PopoverContent>
            </Popover>

            <SaveConfirmation state={submitConfirmation} onSave={onSubmitLoanApplication} loading={loading} />
          </div>
        )}
      </div>

      {data.value && (
        <div key={JSON.stringify(data.value)} className="mt-8 flex flex-col gap-6 md:flex-row">
          {/* Left */}
          <div className="card rounded-xl bg-[#DEE7FF] px-0 py-8 md:w-[198px]">
            {/* Profile Image */}
            <ProfileImage data={data} />

            {/* Left Tabs */}
            <div className="mt-8">
              {LEFT_TAB.map((tab) => {
                const isSelected = tab.value === leftTab.value;

                return (
                  <Button
                    key={tab.value}
                    className={cn(
                      'w-full rounded-none md:justify-start px-8',
                      isSelected ? '' : 'bg-transaparent text-primary hover:text-primary-foreground',
                    )}
                    onClick={() => leftTab.set(tab.value)}
                  >
                    {tab.name}
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Right */}
          <div className="flex-1">
            {/* Gauge and Circular Progress */}
            {leftTab.value === 'credit_score' ? (
              <div className="relative flex flex-col gap-6 2xl:flex-row">
                <div className="card">
                  <div className="relative mx-auto mb-24 h-[126px] w-[240px] shrink-0">
                    <CreditScore value={totalScore.value} />
                    <div className="pointer-events-none absolute inset-x-0 -bottom-9 z-0 flex justify-center">
                      <CircularProgress
                        size="size-[90px]"
                        className="bg-[#DEE7FF]"
                        progressClassName="text-[#36417D]"
                        percentage={totalScore.value}
                        labelClassName="text-lg font-medium text-primary"
                      />
                    </div>
                    <div className="mt-12 text-center text-lg font-bold text-gray-700">Credit Score</div>
                  </div>

                  <div className="flex min-w-max flex-col items-center gap-4 sm:flex-row sm:justify-center">
                    <div>Credit Score Group</div>
                    <Select
                      value={group.value.toString()}
                      onValueChange={(v) => group.set(Number(v))}
                      disabled={loanStage.value === loan_stage.DURING || loanStage.value === loan_stage.AFTER}
                    >
                      <SelectTrigger className="h-8 w-[180px]">
                        <SelectValue placeholder="Select Group" />
                      </SelectTrigger>
                      <SelectContent>
                        {groups.value.map((group) => (
                          <SelectItem key={group.id} value={group.id.toString()}>
                            {group.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {loanStage.get({ noproxy: true }) === loan_stage.BEFORE && (
                  <div className="card flex flex-1 flex-col items-center justify-center gap-8 py-6">
                    <div className="text-xl font-bold text-kitaph-primary">Before Loan Stage</div>

                    <div className="flex justify-center">
                      <div className="grid grid-cols-2 gap-8 xl:grid-cols-3">
                        {beforeDetails['categories'].get({ noproxy: true })?.map((category, index) => {
                          // Define specific colors for each category
                          const colorMapping = {
                            'Agriculture Activity': {
                              circularBg: 'bg-progress-purple-background',
                              circularColor: 'text-progress-purple',
                              buttonBg: 'bg-progress-purple',
                              btnClassName: 'hover:bg-progress-purple/80',
                            },
                            'Transaction Records': {
                              circularBg: 'bg-progress-yellow-background',
                              circularColor: 'text-progress-yellow',
                              buttonBg: 'bg-progress-yellow',
                              btnClassName: 'hover:bg-progress-yellow/80',
                            },
                            'Credit Score': {
                              circularBg: 'bg-progress-blue-background',
                              circularColor: 'text-progress-blue',
                              buttonBg: 'bg-progress-blue',
                              btnClassName: 'hover:bg-progress-blue/80',
                            },
                            // Default colors if category name doesn't match
                            default: {
                              circularBg: 'bg-progress-green-background',
                              circularColor: 'text-progress-green',
                              buttonBg: 'bg-progress-green',
                              btnClassName: 'hover:bg-progress-green/80',
                            },
                          };

                          // Get the colors for the current category or use default
                          const colors = colorMapping[category.name] || colorMapping.default;

                          return (
                            <DashboardStatCircular
                              key={index}
                              name={category.name}
                              leftTab={leftTab}
                              circularBg={colors.circularBg}
                              circularColor={colors.circularColor}
                              value={category.computedScore}
                              total={category.score}
                              tabValue={category.slug}
                              buttonBg={colors.buttonBg}
                              btnClassName={colors.btnClassName}
                            />
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}

                {loanStage.get({ noproxy: true }) === LoanStage.DURING && (
                  <div className="card flex flex-1 flex-col items-center justify-center gap-8 py-6">
                    <div className="text-xl font-bold text-kitaph-primary">During Loan Stage</div>

                    <div className="flex justify-center">
                      <div className="grid grid-cols-2 gap-8 xl:grid-cols-2">
                        {duringDetails['categories'].get({ noproxy: true })?.map((category, index) => {
                          const colorMapping = {
                            'Agriculture Activity': {
                              circularBg: 'bg-progress-purple-background',
                              circularColor: 'text-progress-purple',
                              buttonBg: 'bg-progress-purple',
                              btnClassName: 'hover:bg-progress-purple/80',
                            },
                            'Transaction Records': {
                              circularBg: 'bg-progress-yellow-background',
                              circularColor: 'text-progress-yellow',
                              buttonBg: 'bg-progress-yellow',
                              btnClassName: 'hover:bg-progress-yellow/80',
                            },
                            // Default colors if category name doesn't match
                            default: {
                              circularBg: 'bg-progress-green-background',
                              circularColor: 'text-progress-green',
                              buttonBg: 'bg-progress-green',
                              btnClassName: 'hover:bg-progress-green/80',
                            },
                          };

                          // default
                          const colors = colorMapping[category.name] || colorMapping.default;

                          return (
                            <DashboardStatCircular
                              key={index}
                              name={category.name}
                              leftTab={leftTab}
                              circularBg={colors.circularBg}
                              circularColor={colors.circularColor}
                              value={category.computedScore}
                              total={category.score}
                              tabValue={category.slug}
                              buttonBg={colors.buttonBg}
                              btnClassName={colors.btnClassName}
                            />
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}

                {loanStage.get({ noproxy: true }) === loan_stage.AFTER && (
                  <div className="card flex flex-1 flex-col items-center justify-center gap-8 py-6">
                    <div className="text-xl font-bold text-kitaph-primary">After Loan Stage</div>

                    <div className="flex justify-center">
                      <div className="grid grid-cols-2 gap-8 xl:grid-cols-3">
                        {afterDetails['categories'].get({ noproxy: true })?.map((category, index) => {
                          const colorMapping = {
                            'Credit History': {
                              circularBg: 'bg-progress-purple-background',
                              circularColor: 'text-progress-purple',
                              buttonBg: 'bg-progress-purple',
                              btnClassName: 'hover:bg-progress-purple/80',
                            },
                            'Loan Cycles': {
                              circularBg: 'bg-progress-yellow-background',
                              circularColor: 'text-progress-yellow',
                              buttonBg: 'bg-progress-yellow',
                              btnClassName: 'hover:bg-progress-yellow/80',
                            },
                            'Agriculture Activity': {
                              circularBg: 'bg-progress-blue-background',
                              circularColor: 'text-progress-blue',
                              buttonBg: 'bg-progress-blue',
                              btnClassName: 'hover:bg-progress-blue/80',
                            },
                            // Default colors if category name doesn't match
                            default: {
                              circularBg: 'bg-progress-green-background',
                              circularColor: 'text-progress-green',
                              buttonBg: 'bg-progress-green',
                              btnClassName: 'hover:bg-progress-green/80',
                            },
                          };

                          // default
                          const colors = colorMapping[category.name] || colorMapping.default;

                          return (
                            <DashboardStatCircular
                              key={index}
                              name={category.name}
                              leftTab={leftTab}
                              circularBg={colors.circularBg}
                              circularColor={colors.circularColor}
                              value={category.computedScore}
                              total={category.score}
                              tabValue={category.slug}
                              buttonBg={colors.buttonBg}
                              btnClassName={colors.btnClassName}
                            />
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}

                {farmer.value && (
                  <div className="absolute bottom-3 right-3">
                    <div className="text-sm text-blue-500">
                      <Tooltip>
                        <TooltipTrigger className="text-xs">
                          {formatDistanceToNow(new Date(farmer['updated_at'].value), { addSuffix: true })}
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-center">
                            <div className="mb-1 text-xs font-light !text-gray-500">Credit Score Last Updated:</div>
                            <div className="font-bold text-gray-600">
                              {format(new Date(farmer['updated_at'].value), 'dd MMM yyyy, hh:mm a')}
                            </div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <AccountInfo />
            )}

            {/* Credit Score Tabs */}
            {leftTab.value === 'credit_score' && <CreditScoreTabContent />}

            {/* Profile Tabs */}
            {leftTab.value === 'account_profile' && <AccountProfileTabContent />}

            {/* Transactions Tabs */}
            {leftTab.value === 'transactions' && <TransactionsTabContent />}

            {/* Credit History Tabs */}
            {leftTab.value === 'credit_history' && <CreditHistoryTabContent />}

            {/* Agriculture Activity Tabs */}
            {leftTab.value === 'agriculture_activity' && <AgricultureActivityTabContent />}
          </div>
        </div>
      )}
    </div>
  );
}
