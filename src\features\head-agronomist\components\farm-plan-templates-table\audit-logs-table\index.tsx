'use client';

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { IPaginationMeta } from '../../../types/farmplan-templates';

interface IAuditLogsTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  metadata?: IPaginationMeta;
  isLoading?: boolean;
  state: any;
}

export function AuditLogsTable<TData, TValue>({
  columns,
  data,
  metadata,
  isLoading,
  state,
}: IAuditLogsTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (_row: any) => {
        // Skip since this is read-only audit logs table
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id} colSpan={header.colSpan}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    {isLoading ? 'Loading...' : 'No audit logs found.'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {metadata && (
        <DataTablePaginationMeta
          table={table}
          meta={metadata}
          onChangePageSize={(pageSize: number) => {
            state.pageSize.set(pageSize);
          }}
          onChangePage={(page: number) => {
            state.page.set(page);
          }}
        />
      )}
    </div>
  );
}
