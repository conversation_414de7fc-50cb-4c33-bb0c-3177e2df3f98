'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';

import { ICropsPlanted, IUserInfo } from '../types/farmer.types';

// State
const initialState = {
  selected: undefined as IUserInfo,
  selectedCrop: undefined as ICropsPlanted,
  params: {
    limit: 20,
    search: '',
  },
};

const farmerState = hookstate(initialState, devtools({ key: 'farmerState' }));

// Fetcher
const fetchFarmers = async () => {
  const { data } = await axios.get(`/agronomist/farmplan/farmer/search`, {
    params: {
      ...farmerState.params.value,
    },
  });
  return data.data as IUserInfo[];
};

// hooks
const useFarmer = () => {
  const state = useHookstate(farmerState);
  const searchDebounce = useHookstateDebounce(state.params.search, 500);

  const farmersQuery = useQuery({
    queryKey: ['farmers', { search: searchDebounce.value }],
    queryFn: fetchFarmers,
    enabled: !!searchDebounce.value,
    refetchOnWindowFocus: false,
  });

  return {
    state,
    farmersQuery,
  };
};

export { farmerState };
export default useFarmer;
