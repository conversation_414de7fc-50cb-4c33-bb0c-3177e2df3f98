'use client';

import { toast } from 'sonner';

import axios from '@/lib/api';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

export default function useFinance2() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const getTopupRequests = async () => {
    try {
      const _topup = await axios
        .get('/finance/topup/viewAll', {
          params: {
            page: gState.finance1.pagination.topupReqs.page.value,
            pageSize: gState.finance1.pagination.topupReqs.pageSize.value,
            search: gState.finance1.pagination.topupReqs.search.value,
            startDate: gState.finance1.pagination.topupReqs.startDate.value,
            endDate: gState.finance1.pagination.topupReqs.endDate.value,
            status: gState.finance1.pagination.topupReqs.status.value,
          },
        })
        .then((res) => res.data.data);
      console.log('getTopupRequests: ', _topup);

      if (gState.finance1.pagination.topupReqs.status[0].value === '0') {
        gState.finance2.forApproval.set(_topup.meta.total);
      }
      gState.finance1.topupReqs.set(_topup);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('getTopupRequests: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getReqDetails = async (id) => {
    try {
      const _details = await axios.get(`/finance/topup/view/${id}`).then((res) => res.data.data);
      console.log('getReqDetails: ', _details);

      gStateP.finance1['reqDetails'].set(_details);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('getReqDetails: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const topupAction = async (action: 'approve' | 'reject', data, farmerId) => {
    try {
      toast.loading(action === 'approve' ? 'Approving topup...' : 'Rejecting topup...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post(`/finance/topup/${action}`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('topupAction: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: `${action === 'approve' ? 'Approve' : 'Reject'} topup successfully`,
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('topupAction: ', error);

      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { getTopupRequests, getReqDetails, topupAction };
}
