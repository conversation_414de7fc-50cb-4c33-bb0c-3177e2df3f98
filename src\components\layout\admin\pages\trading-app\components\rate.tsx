'use client';

import { useEffect } from 'react';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import { InputSign } from '@/components/ui/input';

import { UserType } from '@/app/admin/marketplace/_components/Enums';
import { useStandardRateStore } from '@/lib/store/admin/trading-app/standard-rate-store';

interface RateProps {
  userType: UserType.NONFARMER | UserType.FARMER;
}

export default function Rate({ userType }: RateProps) {
  const { getStandardRate, state, updateRate, updating } = useStandardRateStore();

  useEffect(() => {
    getStandardRate(userType);
  }, []);

  return (
    <div>
      <div className="font-dmSans text-xl font-bold text-primary">Standard Rate</div>

      <div className="ml-8 mt-4 grid gap-4">
        <div className="flex max-w-md items-center justify-between gap-4">
          <p className="font-medium">Percentage Rate</p>
          <InputSign
            sign="%"
            type="number"
            value={state[userType].percentageRate.value}
            onChange={(e) => {
              if (e.target.value === '') {
                state[userType].percentageRate.set('');
                return;
              }

              const value = Number(e.target.value);
              if (value > 100 || value < 0) return;
              state[userType].percentageRate.set(value);
            }}
            className="max-w-36"
          />
        </div>

        <div className="flex max-w-md items-center justify-between gap-4">
          <p className="font-medium">Logistic Rate per kilo</p>
          <InputSign
            sign="₱"
            signPosition="left"
            paddingLeft="pl-[3.4rem]"
            className="max-w-36"
            type="number"
            value={state[userType].logisticRate.value}
            onChange={(e) => {
              if (e.target.value === '') {
                state[userType].logisticRate.set('');
                return;
              }

              const value = Number(e.target.value);
              if (value > 10000 || value < 0) return;
              state[userType].logisticRate.set(value);
            }}
          />
        </div>

        <div className="grid max-w-md place-items-end">
          {updating.value ? (
            <ButtonLoading className="w-full max-w-36" text="Saving" />
          ) : (
            <Button
              className="w-full max-w-36"
              onClick={() =>
                updateRate({
                  userType,
                  percentageRate: Number(state[userType].percentageRate.value),
                  logisticRate: Number(state[userType].logisticRate.value),
                })
              }
            >
              Save
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
