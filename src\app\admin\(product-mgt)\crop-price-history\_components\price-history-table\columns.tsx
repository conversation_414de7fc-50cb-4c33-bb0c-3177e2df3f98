'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { ColumnDef } from '@tanstack/react-table';
import { Plus } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import useCrops, { SingleCropPriceRangeSchema } from '@/lib/hooks/useCrops';
import { useGlobalState } from '@/lib/store';
import { ICropPriceHistory } from '@/lib/types/crop.types';
import { cn } from '@/lib/utils';

export const columns: ColumnDef<ICropPriceHistory>[] = [
  {
    accessorKey: 'low_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Low Price" />,
  },
  {
    accessorKey: 'high_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="High Price" />,
  },
  {
    accessorKey: 'low_baptc_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Low BAPTC Price" />,
  },
  {
    accessorKey: 'high_baptc_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="High BAPTC Price" />,
  },
  {
    accessorKey: 'low_nvat_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Low NVAT Price" />,
  },
  {
    accessorKey: 'high_nvat_price',
    header: ({ column }) => <DataTableColumnHeader column={column} title="High NVAT Price" />,
  },
  {
    id: 'updated_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Updated At" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {new Date(data.updated_at).toLocaleTimeString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}
        </div>
      );
    },
    accessorFn: (row) =>
      new Date(row.updated_at).toLocaleTimeString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
      }),
  },
  {
    id: 'actions',
    header: ({ column }) => <ActionHeader />,
  },
];

const ActionHeader = () => {
  const gState = useGlobalState();
  const { updatePriceRange } = useCrops();
  const searchParams = useSearchParams();
  const cId = searchParams.get('cid');
  const cropName = searchParams.get('cropName');
  const dialog = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    trigger,
  } = useForm({
    resolver: zodResolver(SingleCropPriceRangeSchema),
    defaultValues: {
      cropsPrices: [
        {
          cropId: Number(cId),
          lowPrice: 0,
          highPrice: 0,
          lowBaptcPrice: 0,
          highBaptcPrice: 0,
          lowNvatPrice: 0,
          highNvatPrice: 0,
        },
      ],
    },
  });
  const { fields, append, remove } = useFieldArray({
    name: 'cropsPrices',
    control,
  });

  // Populate form with latest price data when dialog opens
  useEffect(() => {
    if (dialog.value) {
      const priceHistoryData = gState.admin.crops.priceHistory.data.get({ noproxy: true });

      if (priceHistoryData && priceHistoryData.length > 0) {
        // Get the latest price entry (first item since it's ordered by latest)
        const latestPrice = priceHistoryData[0];

        console.log('Populating form with latest price data:', latestPrice);

        reset({
          cropsPrices: [
            {
              cropId: Number(cId),
              lowPrice: latestPrice.low_price || 0,
              highPrice: latestPrice.high_price || 0,
              lowBaptcPrice: latestPrice.low_baptc_price || 0,
              highBaptcPrice: latestPrice.high_baptc_price || 0,
              lowNvatPrice: latestPrice.low_nvat_price || 0,
              highNvatPrice: latestPrice.high_nvat_price || 0,
            },
          ],
        });
      } else {
        console.log('No price history data available, using default values');
        // Reset to default values if no price history
        reset({
          cropsPrices: [
            {
              cropId: Number(cId),
              lowPrice: 0,
              highPrice: 0,
              lowBaptcPrice: 0,
              highBaptcPrice: 0,
              lowNvatPrice: 0,
              highNvatPrice: 0,
            },
          ],
        });
      }
    }
  }, [dialog.value, cId, reset, gState.admin.crops.priceHistory.data]);

  const onSubmit = async (data: any) => {
    try {
      const cropsData = gState.admin.crops.data.get({ noproxy: true });

      // Correct price ranges with two-step logic
      const correctedData = {
        ...data,
        cropsPrices: data.cropsPrices.map((crop) => {
          // Find original crop data
          const originalCrop = cropsData.find((c) => c.id === crop.cropId);
          const hasOriginalPrice = originalCrop?.cropPriceRanges?.length > 0;

          // Get original values
          const originalLowPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].low_price : 0;
          const originalHighPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].high_price : 0;
          const originalLowBaptcPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].low_baptc_price : 0;
          const originalHighBaptcPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].high_baptc_price : 0;
          const originalLowNvatPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].low_nvat_price : 0;
          const originalHighNvatPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].high_nvat_price : 0;

          // Step 1: Replace 0 in low price with original only when high price equals original high price
          let correctedLowPrice =
            crop.lowPrice === 0 && crop.highPrice === originalHighPrice && crop.highPrice !== 0
              ? originalLowPrice
              : crop.lowPrice;
          let correctedLowBaptcPrice =
            crop.lowBaptcPrice === 0 && crop.highBaptcPrice === originalHighBaptcPrice && crop.highBaptcPrice !== 0
              ? originalLowBaptcPrice
              : crop.lowBaptcPrice;
          let correctedLowNvatPrice =
            crop.lowNvatPrice === 0 && crop.highNvatPrice === originalHighNvatPrice && crop.highNvatPrice !== 0
              ? originalLowNvatPrice
              : crop.lowNvatPrice;

          // Step 2: If low price > high price, set low price = high price
          if (correctedLowPrice > crop.highPrice && crop.highPrice > 0) {
            correctedLowPrice = crop.highPrice;
          }
          if (correctedLowBaptcPrice > crop.highBaptcPrice && crop.highBaptcPrice > 0) {
            correctedLowBaptcPrice = crop.highBaptcPrice;
          }
          if (correctedLowNvatPrice > crop.highNvatPrice && crop.highNvatPrice > 0) {
            correctedLowNvatPrice = crop.highNvatPrice;
          }

          return {
            ...crop,
            lowPrice: correctedLowPrice,
            lowBaptcPrice: correctedLowBaptcPrice,
            lowNvatPrice: correctedLowNvatPrice,
          };
        }),
      };

      console.log('onSubmit original: ', data);
      console.log('onSubmit corrected: ', correctedData);
      await updatePriceRange(correctedData);

      dialog.set(false);
      reset({
        cropsPrices: [
          {
            cropId: Number(cId),
            lowPrice: 0,
            highPrice: 0,
            lowBaptcPrice: 0,
            highBaptcPrice: 0,
            lowNvatPrice: 0,
            highNvatPrice: 0,
          },
        ],
      });
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <>
      <Dialog open={dialog.value} onOpenChange={dialog.set}>
        <DialogTrigger asChild>
          <div className="flex justify-end">
            <Button className="h-8 px-2 lg:px-3" size="sm">
              <Plus className="mr-2 size-4" />
              Add New Price
            </Button>
          </div>
        </DialogTrigger>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-primary">Add New Price</DialogTitle>
            <DialogDescription>{`Add new price for ${cropName}`}</DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)}>
            {fields.map((field, index) => {
              const cropId = errors?.cropsPrices?.[index]?.cropId;

              const lowPrice = errors?.cropsPrices?.[index]?.lowPrice;
              const highPrice = errors?.cropsPrices?.[index]?.highPrice;
              const lowBaptcPrice = errors?.cropsPrices?.[index]?.lowBaptcPrice;
              const highBaptcPrice = errors?.cropsPrices?.[index]?.highBaptcPrice;
              const lowNvatPrice = errors?.cropsPrices?.[index]?.lowNvatPrice;
              const highNvatPrice = errors?.cropsPrices?.[index]?.highNvatPrice;

              return (
                <div key={field.id} className="mt-3 grid gap-4">
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="cropId" className="pb-1 font-normal">
                      Crop ID
                    </Label>
                    <Input
                      {...register(`cropsPrices.${index}.cropId` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        cropId && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter crop name"
                      disabled
                    />
                    {cropId && <p className="form-error">{`${cropId.message}`}</p>}
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="sellingPrice" className="pb-1 font-normal">
                      Trading Post Price
                    </Label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1">
                        <Input
                          type="text"
                          {...register(`cropsPrices.${index}.lowPrice` as const, {
                            onChange: () => trigger(`cropsPrices.${index}.lowPrice` as const),
                            onBlur: () => trigger(`cropsPrices.${index}.lowPrice` as const),
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            lowPrice && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          placeholder="Low Price"
                        />
                        {lowPrice && <p className="form-error">{`${lowPrice.message}`}</p>}
                      </div>
                      <div>-</div>
                      <div className="flex-1">
                        <Input
                          type="text"
                          {...register(`cropsPrices.${index}.highPrice` as const, {
                            onChange: () => {
                              trigger(`cropsPrices.${index}.highPrice` as const);
                              trigger(`cropsPrices.${index}.lowPrice` as const);
                            },
                            onBlur: () => {
                              trigger(`cropsPrices.${index}.highPrice` as const);
                              trigger(`cropsPrices.${index}.lowPrice` as const);
                            },
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            highPrice && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          placeholder="High Price"
                        />
                        {highPrice && <p className="form-error">{`${highPrice.message}`}</p>}
                      </div>
                    </div>
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="sellingPrice" className="pb-1 font-normal">
                      BAPTC Price per kilo
                    </Label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1">
                        <Input
                          type="text"
                          {...register(`cropsPrices.${index}.lowBaptcPrice` as const, {
                            onChange: () => trigger(`cropsPrices.${index}.lowBaptcPrice` as const),
                            onBlur: () => trigger(`cropsPrices.${index}.lowBaptcPrice` as const),
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            lowBaptcPrice && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          placeholder="Low Price"
                        />
                        {lowBaptcPrice && <p className="form-error">{`${lowBaptcPrice.message}`}</p>}
                      </div>
                      <div>-</div>
                      <div className="flex-1">
                        <Input
                          type="text"
                          {...register(`cropsPrices.${index}.highBaptcPrice` as const, {
                            onChange: () => {
                              trigger(`cropsPrices.${index}.highBaptcPrice` as const);
                              trigger(`cropsPrices.${index}.lowBaptcPrice` as const);
                            },
                            onBlur: () => {
                              trigger(`cropsPrices.${index}.highBaptcPrice` as const);
                              trigger(`cropsPrices.${index}.lowBaptcPrice` as const);
                            },
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            highBaptcPrice && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          placeholder="High Price"
                        />
                        {highBaptcPrice && <p className="form-error">{`${highBaptcPrice.message}`}</p>}
                      </div>
                    </div>
                  </div>

                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="sellingPrice" className="pb-1 font-normal">
                      NVAT Price per kilo
                    </Label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1">
                        <Input
                          type="text"
                          {...register(`cropsPrices.${index}.lowNvatPrice` as const, {
                            onChange: () => trigger(`cropsPrices.${index}.lowNvatPrice` as const),
                            onBlur: () => trigger(`cropsPrices.${index}.lowNvatPrice` as const),
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            lowNvatPrice && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          placeholder="Low Price"
                        />
                        {lowNvatPrice && <p className="form-error">{`${lowNvatPrice.message}`}</p>}
                      </div>
                      <div>-</div>
                      <div className="flex-1">
                        <Input
                          type="text"
                          {...register(`cropsPrices.${index}.highNvatPrice` as const, {
                            onChange: () => {
                              trigger(`cropsPrices.${index}.highNvatPrice` as const);
                              trigger(`cropsPrices.${index}.lowNvatPrice` as const);
                            },
                            onBlur: () => {
                              trigger(`cropsPrices.${index}.highNvatPrice` as const);
                              trigger(`cropsPrices.${index}.lowNvatPrice` as const);
                            },
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            highNvatPrice && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          placeholder="High Price"
                        />
                        {highNvatPrice && <p className="form-error">{`${highNvatPrice.message}`}</p>}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}

            <div className="flex justify-between gap-2 pt-6">
              <DialogClose asChild>
                <Button className="px-12" variant="outline" type="button">
                  Cancel
                </Button>
              </DialogClose>
              <Button className="px-12" type="submit">
                Add
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};
