'use client';

import { useHookstate } from '@hookstate/core';
import { FC, useEffect } from 'react';
import { CartesianGrid, Legend, Line, LineChart, Tooltip, XAxis, YAxis } from 'recharts';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { AdvancedDateRangePicker } from '@/components/ui/advance-range-date-picker';
import { ChartContainer } from '@/components/ui/chart';
import MultiSelectDropdown from '@/components/ui/multi-select-dropdown';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';

import { LoadingTable } from '../loading-table';
import useCropPrice from './hooks/useCropPrice';

// Constants for chart width calculation
const MIN_WIDTH_PER_POINT = 50;
const CONTAINER_WIDTH_THRESHOLD = 800;

export const CropPrice: FC = () => {
  const { cropsQuery, state, cropPriceTrendsQuery, processChartData } = useCropPrice();
  const priceType = useHookstate('highPrice');
  const processedData = useHookstate(null as any);
  const highestPrice = Math.max(
    ...(cropPriceTrendsQuery.data?.map((crop) =>
      Math.max(...crop.cropPriceRanges.map((range) => range.high_price)),
    ) || [0]),
  );

  useEffect(() => {
    if (cropPriceTrendsQuery.data) {
      const _data = processChartData(
        cropPriceTrendsQuery.data,
        state.startDate.value,
        state.endDate.value,
        priceType.value,
      );
      processedData.set(_data);
    }
  }, [cropPriceTrendsQuery.data, priceType]);

  // Calculate chart width and determine if horizontal scroll is needed
  const chartWidth = Math.max(CONTAINER_WIDTH_THRESHOLD, (processedData.value?.length || 0) * MIN_WIDTH_PER_POINT);
  const needsHorizontalScroll = chartWidth > CONTAINER_WIDTH_THRESHOLD;

  return (
    <div className="card">
      <div className="flex flex-wrap items-center gap-2">
        <h2 className="font-poppins font-semibold text-adminDashboard-title">Crop Price</h2>

        {cropsQuery.isLoading ? (
          <Skeleton className="h-8 w-24" />
        ) : (
          <MultiSelectDropdown
            options={cropsQuery.data.map((crop) => ({ label: crop.name, value: `${crop.id}` })) || []}
            onChange={(values) => {
              state.cropIds.set(values);
            }}
            placeholder="Select Crops"
            triggerClassName="h-8 w-[180px] text-sm"
          />
        )}

        <AdvancedDateRangePicker
          onChange={(values) => {
            state.merge({ startDate: values.from, endDate: values.to });
          }}
        />

        <Select value={priceType.value} onValueChange={priceType.set}>
          <SelectTrigger className="h-8 w-[180px]">
            <SelectValue placeholder="Select price" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="highPrice">High Price</SelectItem>
              <SelectItem value="lowPrice">Low Price</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      <div className="mt-6">
        {cropPriceTrendsQuery.isError ? (
          <div className="py-6 text-center text-sm">No results.</div>
        ) : (
          <>
            {!cropPriceTrendsQuery.data ? (
              <LoadingTable />
            ) : needsHorizontalScroll ? (
              <HorizontalScrollBar>
                <ChartContainer
                  config={Object.fromEntries(
                    cropPriceTrendsQuery.data.map((crop) => [crop.name.toLowerCase(), { color: crop.color }]),
                  )}
                  className="max-h-[400px]"
                  style={{ width: chartWidth }}
                >
                  <LineChart width={chartWidth} data={processedData.value}>
                    <CartesianGrid vertical={false} />
                    <XAxis dataKey="name" type="category" allowDuplicatedCategory={false} />
                    <YAxis type="number" domain={[0, highestPrice + 50]} allowDataOverflow={true} />
                    <Tooltip />
                    <Legend />
                    {cropPriceTrendsQuery.data &&
                      cropPriceTrendsQuery.data.map((x) => (
                        <Line key={x.id} type="natural" dot={false} dataKey={x.name} stroke={x.color} strokeWidth={2} />
                      ))}
                  </LineChart>
                </ChartContainer>
              </HorizontalScrollBar>
            ) : (
              <ChartContainer
                config={Object.fromEntries(
                  cropPriceTrendsQuery.data.map((crop) => [crop.name.toLowerCase(), { color: crop.color }]),
                )}
                className="max-h-[400px] w-full"
              >
                <LineChart data={processedData.value}>
                  <CartesianGrid vertical={false} />
                  <XAxis dataKey="name" type="category" allowDuplicatedCategory={false} />
                  <YAxis type="number" domain={[0, highestPrice + 50]} allowDataOverflow={true} />
                  <Tooltip />
                  <Legend />
                  {cropPriceTrendsQuery.data &&
                    cropPriceTrendsQuery.data.map((x) => (
                      <Line key={x.id} type="natural" dot={false} dataKey={x.name} stroke={x.color} strokeWidth={2} />
                    ))}
                </LineChart>
              </ChartContainer>
            )}
          </>
        )}
      </div>
    </div>
  );
};
