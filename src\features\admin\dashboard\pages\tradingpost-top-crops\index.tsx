'use client';

import { useRouter } from 'next/navigation';
import { FC } from 'react';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

import { LoadingTable } from '../../components/loading-table';
import { TradingPostTopCropsTable } from './components/tradingpost-top-crops-table';
import { columnsTradingPostTopCrops } from './components/tradingpost-top-crops-table/columns';
import useTradingPostTopCrops from './hooks/useTradingPostTopCrops';

const TradingPostTopCrops: FC = () => {
  const router = useRouter();
  const { tradingPostTopCropsQuery } = useTradingPostTopCrops();

  return (
    <div className="p-6 lg:p-8">
      <div className="pb-4">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Tradingpost Top Crops</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink onClick={() => router.replace('/admin')}>Dashboard</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Tradingpost Top Crops</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {tradingPostTopCropsQuery.isLoading ? (
        <LoadingTable />
      ) : (
        <TradingPostTopCropsTable columns={columnsTradingPostTopCrops} data={tradingPostTopCropsQuery.data || []} />
      )}
    </div>
  );
};

export default TradingPostTopCrops;
