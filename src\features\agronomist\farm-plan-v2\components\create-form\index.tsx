'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { ETemplateItemType } from '@/features/head-agronomist/types/farmplan-templates';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { useCreateFarmPlan } from '../../hooks/useCreateFarmPlan';
import { ICreateFarmPlan } from '../../types/create-farm-plan.types';
import AgronomistInfo from '../shared/agronomist-info';
import CashReqSummary from '../shared/cash-requirements-summary';
import CostSummary from '../shared/cost-summary';
import FarmPlanTemplateInfo from '../shared/farm-plan-template-info';
import FarmerEquitySummary from '../shared/farmer-equity-summary';
import FarmerInfo from '../shared/farmer-info';
import InputCostSummary from '../shared/input-cost-summary';
import FoliarFertilization from '../shared/inputs-foliar-fert';
import PesticideApplication from '../shared/inputs-pesticide-application';
import SeedRequirements from '../shared/inputs-seed-requirements';
import SoilFertilizationSideDress from '../shared/inputs-soil-fert-side-dress';
import SoilFertilizationTopDress from '../shared/inputs-soil-fert-top-dress';
import KitaSubsidizedCosts from '../shared/kita-subsidized-costs';
import LaborRequirements from '../shared/labor-reqs';
import LoanRepaymentCalc from '../shared/loan-repayment-calculator';
import NonCashCosts from '../shared/non-cash-costs';
import NonKitaSubsidizedCosts from '../shared/nonkita-subsidized-costs';
import OtherFarmMaterials from '../shared/other-farm-materials';
import OtherProductionCosts from '../shared/other-production-costs';
import FarmPlanHeader from './farm-plan-header';

export default function CreateFarmPlanForm() {
  // State for controlling form reset
  const [resetKey, setResetKey] = useState(0);

  const gStateP = useGlobalStatePersist();
  const { submitFarmPlan, isSubmitting } = useCreateFarmPlan();

  // Initialize the form with the required structure
  const form = useForm<ICreateFarmPlan>({
    defaultValues: {
      userId: 0,
      cropId: 0,
      croppingType: '',
      agronomistName: gStateP.user
        ? `${gStateP.user.user.agronomist.first_name.value} ${gStateP.user.user.agronomist.last_name.value}`
        : '',
      agronomistPrcNumber: '',
      agronomistValidUntil: '',
      headAgronomistName: '',
      headAgronomistPrcNumber: '',
      headAgronomistValidUntil: '',
      contingencyForFluctuation: 0,
      interestRate: 0,
      numberOfMonthsPerTenor: 0,
      aorPerMonth: 0,
      farmPlanTemplateId: 0,
      items: [
        {
          name: 'Seed / Seedling Requirements (SE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Basal (Top-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Soil Fertilization - Additional (Side-Dress) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Foliar Fertilization (Spray) (FE)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Pesticide Application (Spray / Spread) (CP)',
          type: ETemplateItemType.INPUTS,
          subItems: [],
        },
        {
          name: 'Farm Materials, Consumables, etc.',
          type: ETemplateItemType.OTHERS,
          subItems: [],
        },
        {
          name: 'Labor Requirements',
          type: ETemplateItemType.LABOR,
          subItems: [],
        },
        {
          name: 'Other Production Costs',
          type: ETemplateItemType.OTHERS,
          subItems: [],
        },
        {
          name: 'Non-Cash Costs',
          type: ETemplateItemType.NON_CASH,
          subItems: [],
        },
        {
          name: 'KITA Subsidized Costs',
          type: ETemplateItemType.KITA_SUBSIDIZED,
          subItems: [],
        },
        {
          name: 'Non-KITA Subsidized Costs (Government Subsidy, Grants, Donations, etc.)',
          type: ETemplateItemType.NON_KITA_SUBSIDIZED,
          subItems: [],
        },
      ],
    },
  });

  // Handle form submission
  const onSubmit = form.handleSubmit(async (data) => {
    await submitFarmPlan(data);
  });

  return (
    <div>
      <FarmPlanHeader />

      <form key={resetKey} onSubmit={onSubmit}>
        <div className="mt-6 grid gap-6 md:grid-cols-2">
          <div>
            <FarmerInfo form={form} />
          </div>

          <div>
            <FarmPlanTemplateInfo form={form} onTemplateReset={() => setResetKey((prev) => prev + 1)} />
          </div>
        </div>

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARM INPUTS FOR DELIVERY / PICK-UP (Amount to be Held in favor of KITA)
        </div>

        <SeedRequirements form={form} />
        <SoilFertilizationTopDress form={form} />
        <SoilFertilizationSideDress form={form} />
        <FoliarFertilization form={form} />
        <PesticideApplication form={form} />
        <OtherFarmMaterials form={form} />

        {/* Estimated Farm Inputs Costs Section */}
        <InputCostSummary form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          CASH REQUIREMENTS (Labor, Overhead, Other Cash Costs, Etc.)
        </div>

        <LaborRequirements form={form} />
        <OtherProductionCosts form={form} />
        <CashReqSummary form={form} />

        <div className="mt-16 text-center font-semibold text-kitaph-primary">
          FARMER&apos;S EQUITY (Other Non-Cash Costs, Subsidized Costs, etc.)
        </div>
        <NonCashCosts form={form} />
        <KitaSubsidizedCosts form={form} />
        <NonKitaSubsidizedCosts form={form} />
        <FarmerEquitySummary form={form} />

        <div className="flex flex-wrap gap-6 pt-8">
          <div className="w-full md:flex-1">
            <CostSummary />
            <LoanRepaymentCalc form={form} />
          </div>
          <div className="w-full md:w-1/3">
            <AgronomistInfo form={form} />
          </div>
        </div>

        <div className="mt-6 flex justify-center">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Submitting...' : 'Submit Farm Plan'}
          </Button>
        </div>
      </form>
    </div>
  );
}
