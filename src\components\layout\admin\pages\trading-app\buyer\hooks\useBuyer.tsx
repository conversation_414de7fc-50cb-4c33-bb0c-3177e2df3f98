'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { IMeta } from '@/lib/types';
import { catchError } from '@/lib/utils';

import { IBuyer } from './useBuyer.types';

const buyerState = hookstate(
  {
    data: {
      meta: {} as IMeta,
      data: [] as I<PERSON>uyer[],
    },
    params: {
      page: 1,
      pageSize: 10,
      search: '',
    },
  },
  devtools({
    key: 'buyerState',
  }),
);

export const useBuyer = (currentUser = 'admin') => {
  const state = useHookstate(buyerState);

  const getBuyers = async () => {
    try {
      const { data } = await axios.get(`/${currentUser}/tradingapp/nonfarmer/viewAll`, {
        params: state.params.get({ noproxy: true }),
      });
      console.log('getBuyers: ', data.data);
      state.data.set(data.data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getBuyers: ', error);
    }
  };

  const updateBuyer = async (data) => {
    try {
      toast.loading('Updating buyer...', {
        description: 'Please wait...',
        duration: 10000,
      });

      await axios.post(`/${currentUser}/tradingapp/nonfarmer/update`, data);
      await getBuyers();

      toast.dismiss();
      toast.success('Success', {
        description: 'Buyer updated successfully',
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, 'updateBuyer');
    }
  };

  const toggleBuyer = async (userId: string | number, status: 'deactivate' | 'activate') => {
    try {
      toast.loading(`Updating ${status} buyer...`, {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post(`/${currentUser}/tradingapp/nonfarmer/${status}`, {
          userId,
        })
        .then((res) => res.data);

      console.log(`${status}Buyer: `, _data);
      await getBuyers();

      toast.dismiss();
      toast.success('Success', {
        description: _data.message,
      });
    } catch (e) {
      toast.dismiss();
      catchError(e, `${status}Buyer`);
    }
  };

  return {
    data: state.data.get({ noproxy: true }),
    getBuyers,
    updateBuyer,
    toggleBuyer,
  };
};

export default buyerState;
