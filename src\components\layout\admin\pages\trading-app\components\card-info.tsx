'use client';

import { Skeleton } from '@/components/ui/skeleton';

interface CardInfoProps {
  title: string;
  value: string | number;
  loading?: boolean;
}

export default function CardInfo({ title, value, loading = true }: CardInfoProps) {
  return (
    <div>
      {title && !loading ? (
        <h3 className="text-sm font-light text-primary/50">{title}</h3>
      ) : (
        <Skeleton className="h-4 w-14" />
      )}
      {value && !loading ? (
        <p className="mt-1 font-bold text-primary">{value}</p>
      ) : (
        <Skeleton className="mt-2 h-4 w-32" />
      )}
    </div>
  );
}
