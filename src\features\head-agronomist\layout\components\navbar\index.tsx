'use client';

import { Logo } from './logo';
import { MobileMenuButton } from './mobile-menu-button';
import { PageTitle } from './page-title';
import { UserProfileDropdown } from './user-profile-dropdown';

interface INavbarProps {
  onOpenMobileMenu: () => void;
  menuTitle: string;
}

export default function Navbar({ onOpenMobileMenu, menuTitle }: INavbarProps) {
  return (
    <nav className="flex border-b">
      <div className="w-auto p-4 pb-3 lg:w-[290px] lg:px-2 lg:pb-4">
        <MobileMenuButton onOpenMobileMenu={onOpenMobileMenu} />
        <Logo />
      </div>

      <div className="flex flex-1 items-end border-l px-6 pb-3">
        <div className="flex flex-1 items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <PageTitle title={menuTitle} />
          </div>

          <div className="flex items-center gap-4">
            <UserProfileDropdown />
          </div>
        </div>
      </div>
    </nav>
  );
}
