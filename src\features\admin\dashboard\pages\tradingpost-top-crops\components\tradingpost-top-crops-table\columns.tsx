'use client';

import { ColumnDef } from '@tanstack/react-table';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

import { ITradingPostTopCrops } from '@/features/admin/dashboard/types';
import { toCurrency } from '@/lib/utils';

export const columnsTradingPostTopCrops: ColumnDef<ITradingPostTopCrops>[] = [
  {
    accessorKey: 'crop_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="#" />,
  },
  {
    accessorKey: 'crop_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Crop Name" />,
    cell: ({ row }) => {
      return <div className="w-max">{row.original.crop_name}</div>;
    },
  },
  {
    accessorKey: 'crop_volume',
    header: ({ column }) => <DataTableColumnHeader className="justify-end" column={column} title="Volume" />,
    cell: ({ row }) => {
      return <div className="text-right">{Number(row.getValue('crop_volume')).toLocaleString()} kg</div>;
    },
  },
  {
    accessorKey: 'crop_price',
    header: ({ column }) => <DataTableColumnHeader className="justify-end" column={column} title="Latest Price" />,
    cell: ({ row }) => {
      return <div className="text-right">{`${toCurrency(row.getValue('crop_price'))}/kg`}</div>;
    },
  },
  {
    accessorKey: 'crop_sales',
    header: ({ column }) => <DataTableColumnHeader className="justify-end" column={column} title="Sales" />,
    cell: ({ row }) => {
      return <div className="text-right">{toCurrency(row.getValue('crop_sales'))}</div>;
    },
  },
];
