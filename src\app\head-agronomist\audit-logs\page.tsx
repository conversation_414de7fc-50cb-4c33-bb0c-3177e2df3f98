'use client';

import { ArrowLeftIcon } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

import { Button } from '@/components/ui/button';

import { AuditLogsTable } from '@/features/head-agronomist/components/farm-plan-templates-table/audit-logs-table';
import { auditLogsColumns } from '@/features/head-agronomist/components/farm-plan-templates-table/audit-logs-table/columns';
import { useEditFarmPlanTemplate } from '@/features/head-agronomist/hooks/useEditFarmPlanTemplate';
import { useFarmPlanTemplateAuditLogs } from '@/features/head-agronomist/hooks/useFarmPlanTemplateAuditLogs';

export default function AuditLogsPage() {
  const searchParams = useSearchParams();
  const farmPlanTemplateId = parseInt(searchParams.get('id') || '0');

  const { farmPlanTemplateByIdQuery } = useEditFarmPlanTemplate(farmPlanTemplateId);
  const { farmPlanTemplateAuditLogsQuery, state } = useFarmPlanTemplateAuditLogs(farmPlanTemplateId);

  const farmPlanTemplate = farmPlanTemplateByIdQuery.data;

  if (!farmPlanTemplateId || isNaN(farmPlanTemplateId)) {
    return (
      <div className="p-6">
        <div className="mb-4 flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/head-agronomist">
              <ArrowLeftIcon className="mr-2 size-4" />
              Back to Templates
            </Link>
          </Button>
        </div>
        <div className="py-8 text-center">
          <p className="text-destructive">Invalid farm plan template ID.</p>
        </div>
      </div>
    );
  }

  if (farmPlanTemplateByIdQuery.isLoading) {
    return (
      <div className="p-6">
        <div className="mb-4 flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/head-agronomist">
              <ArrowLeftIcon className="mr-2 size-4" />
              Back to Templates
            </Link>
          </Button>
        </div>
        <div className="py-8 text-center">
          <p>Loading farm plan template...</p>
        </div>
      </div>
    );
  }

  if (farmPlanTemplateByIdQuery.isError || !farmPlanTemplate) {
    return (
      <div className="p-6">
        <div className="mb-4 flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/head-agronomist">
              <ArrowLeftIcon className="mr-2 size-4" />
              Back to Templates
            </Link>
          </Button>
        </div>
        <div className="py-8 text-center">
          <p className="text-destructive">Farm plan template not found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="mb-4 flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/head-agronomist">
              <ArrowLeftIcon className="mr-2 size-4" />
              Back to Templates
            </Link>
          </Button>
        </div>

        <div>
          <h1 className="text-2xl font-bold">Audit Logs</h1>
          <p className="text-muted-foreground">
            View audit logs for {farmPlanTemplate.crop.name} - {farmPlanTemplate.location} (v
            {farmPlanTemplate.version_number})
          </p>
        </div>
      </div>

      {/* Audit Logs Table */}
      <div className="">
        <AuditLogsTable
          columns={auditLogsColumns}
          data={farmPlanTemplateAuditLogsQuery.isSuccess ? farmPlanTemplateAuditLogsQuery.data?.data?.data || [] : []}
          metadata={farmPlanTemplateAuditLogsQuery.data?.data?.meta}
          isLoading={farmPlanTemplateAuditLogsQuery.isFetching}
          state={state}
        />
      </div>
    </div>
  );
}
