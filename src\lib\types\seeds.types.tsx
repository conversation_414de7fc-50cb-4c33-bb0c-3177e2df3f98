interface ISeedSubcategory {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

interface ISeedBreed {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

interface ISeedVariety {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

interface ISeed {
  id: number;
  name: string;
  seed_subcategory_id: number;
  status: number;
  created_at: string;
  updated_at: string;
  seed_variety_id: number;
  seed_breed_id: number;
  seedSubcategory: ISeedSubcategory;
  seedBreed: ISeedBreed;
  seedVariety: ISeedVariety;
}
