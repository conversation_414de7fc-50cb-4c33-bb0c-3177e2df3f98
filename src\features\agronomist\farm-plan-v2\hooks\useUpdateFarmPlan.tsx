'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { IUpdateFarmPlan } from '../types/edit-farm-plan.types';

// Mutation function for updating farm plan
const updateFarmPlan = async (data: IUpdateFarmPlan) => {
  const { data: res } = await axios.post('/agronomist/farmplan/update', data);
  return res;
};

// Hook for updating farm plans
export const useUpdateFarmPlan = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  // Create mutation for API call
  const updateFarmPlanMutation = useMutation({
    mutationFn: updateFarmPlan,
  });

  // Submit farm plan function
  const submitFarmPlan = async (data: IUpdateFarmPlan) => {
    try {
      setIsSubmitting(true);

      // Format the data, especially dates in subItems and main date fields
      const formData: IUpdateFarmPlan = {
        ...data,
        agronomistValidUntil: data.agronomistValidUntil
          ? format(new Date(data.agronomistValidUntil), 'yyyy-MM-dd')
          : '',
        headAgronomistValidUntil: data.headAgronomistValidUntil
          ? format(new Date(data.headAgronomistValidUntil), 'yyyy-MM-dd')
          : '',
        items: data.items.map((item) => ({
          ...item,
          subItems: item.subItems.map((subItem) => ({
            ...subItem,
            expectedDate: subItem.expectedDate
              ? subItem.expectedDate instanceof Date
                ? format(subItem.expectedDate, 'yyyy-MM-dd')
                : subItem.expectedDate
              : '',
          })),
        })),
      };

      console.log('Form submitted:', formData);
      const res = await updateFarmPlanMutation.mutateAsync(formData);

      console.log('Form response:', res);
      toast.success('Success', {
        description: res.message || 'Farm plan updated successfully',
      });

      // Refetch farm plans
      queryClient.refetchQueries({
        queryKey: ['farmPlans'],
      });

      // Refetch current farm plan
      queryClient.refetchQueries({
        queryKey: ['farmPlanById', data.farmPlanId],
      });

      // Navigate back to farm plans list
      router.push('/agronomist/farm-plan');
    } catch (e: any) {
      const error = e?.response?.data?.message || e.message;
      console.error('Error updating farm plan:', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitFarmPlan,
    isSubmitting,
    updateFarmPlanMutation,
  };
};
