'use client';

import { useHookstate } from '@hookstate/core';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Suspense, useEffect } from 'react';

import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';

import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn, urlify } from '@/lib/utils';

import FetchAccountInfo from './FetchAccountInfo';
import { FinancingTable } from './financing-table';
import { columns as financingColumns } from './financing-table/columns';
import { MarketplaceTable } from './marketplace-table';
import { columns as marketplaceColumns } from './marketplace-table/columns';
import Stats from './Stats';
import { TransactionDetailsTable } from './transaction-details-table';
import { columns } from './transaction-details-table/columns';

export default function AccountInfo() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const data = gState.selected.accountInfo['info'].get({ noproxy: true });
  const address = data && data['farmer']['address'] ? JSON.parse(data['farmer']['address']) : {};
  const infoTab = useHookstate(0);
  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';

  return (
    <div className="p-8">
      <Suspense>
        <FetchAccountInfo />
      </Suspense>

      <div className="mb-4 flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          className="h-8"
          onClick={() => {
            router.back();
          }}
        >
          <ChevronLeft className="mr-2 size-4" />
          Back
        </Button>

        {isAdmin && (
          <Button
            className="px-12 text-sm"
            onClick={() => {
              gStateP.admin.members['details'].set(data);
              router.push('/admin/account-info/update');
            }}
          >
            Update Info
          </Button>
        )}
      </div>

      {/* <h1 className="text-3xl font-bold tracking-tight">Account Information</h1> */}

      <div className="mt-6 space-y-8">
        {data && (
          <>
            {/* Basic Info */}
            <div className="card">
              <div className="flex gap-8">
                <div>
                  {/* Profile Image */}
                  <div>
                    <img
                      className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                      src={data.user_img ? urlify(data.user_img, 'users/profile') : '/assets/user-default.jpg'}
                      alt=""
                    />
                  </div>
                </div>

                <div className="flex-1">
                  <div className="text-xl font-bold leading-loose text-indigo-900">Basic Info</div>
                  <dl className="grid grid-cols-2 gap-4">
                    <div className="font-dmSans">
                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${data.farmer.first_name} ${data.farmer.last_name}`}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`ID${data.id.toString().padStart(9, '0')}`}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${data.farmer.mobile_number ?? ''}`}
                        </dd>
                      </div>
                    </div>

                    <div className="font-dmSans">
                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${new Date(data.farmer.birth_date).toLocaleString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          })}`}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data.email}`}</dd>
                      </div>
                    </div>
                  </dl>
                </div>
              </div>
            </div>

            <Stats />

            {/* Tabs */}
            <div className="flex items-center gap-8">
              <button
                className={cn(
                  'transition-all duration-300 ease-in-out',
                  infoTab.value === 0
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => infoTab.set(0)}
              >
                Trading Post
              </button>
              <button
                className={cn(
                  'transition-all duration-300 ease-in-out',
                  infoTab.value === 1
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => infoTab.set(1)}
              >
                Marketplace
              </button>
              <button
                className={cn(
                  'transition-all duration-300 ease-in-out',
                  infoTab.value === 2
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => infoTab.set(2)}
              >
                Financing
              </button>
              <button
                className={cn(
                  'transition-all duration-300 ease-in-out',
                  infoTab.value === 3
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => infoTab.set(3)}
              >
                Agriculture Activity
              </button>
            </div>

            {infoTab.value === 0 && (
              <TransactionDetailsTable
                columns={columns}
                data={gState.selected.accountInfo.tradingPost.data.get({ noproxy: true })}
                metadata={gState.selected.accountInfo.tradingPost['meta'].get({ noproxy: true })}
              />
            )}

            {infoTab.value === 1 && (
              <MarketplaceTable
                columns={marketplaceColumns}
                data={gState.selected.accountInfo.marketplace.data.get({ noproxy: true })}
                metadata={gState.selected.accountInfo.marketplace['meta'].get({ noproxy: true })}
              />
            )}

            {infoTab.value === 2 && (
              <FinancingTable
                columns={financingColumns}
                data={gState.selected.accountInfo.finance.data.get({ noproxy: true })}
                metadata={gState.selected.accountInfo.finance['meta'].get({ noproxy: true })}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
}
