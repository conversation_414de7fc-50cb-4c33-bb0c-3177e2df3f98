'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ronR<PERSON>, <PERSON><PERSON><PERSON>, Printer } from 'lucide-react';
import Link from 'next/link';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

import { useEditFarmPlan } from '../../hooks/useEditFarmPlan';
import ViewAgronomistInfo from './view-agronomist-info';
import ViewCostSummary from './view-cost-summary';
import ViewFarmPlanHeader from './view-farm-plan-header';
import ViewFarmPlanItems from './view-farm-plan-items';
import ViewFarmPlanTemplateInfo from './view-farm-plan-template-info';
import ViewFarmerInfo from './view-farmer-info';

interface IViewFarmPlanProps {
  id: number;
}

export default function ViewFarmPlan({ id }: IViewFarmPlanProps) {
  const { farmPlanByIdQuery } = useEditFarmPlan(id);

  useEffect(() => {
    if (id > 0) {
      farmPlanByIdQuery.refetch();
    }
  }, [id]);

  if (farmPlanByIdQuery.isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="size-10" />
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
        <Skeleton className="h-96" />
      </div>
    );
  }

  if (farmPlanByIdQuery.isError || !farmPlanByIdQuery.data) {
    return (
      <div className="flex flex-col items-center justify-center space-y-4 py-12">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900">Farm Plan Not Found</h2>
          <p className="mt-2 text-gray-600">
            The farm plan you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
          </p>
        </div>
        <Button asChild>
          <Link href="/agronomist/farm-plan">
            <ArrowLeft className="mr-2 size-4" />
            Back to Farm Plans
          </Link>
        </Button>
      </div>
    );
  }

  const farmPlan = farmPlanByIdQuery.data;

  return (
    <div className="space-y-6">
      {/* Header matching edit farm plan style */}
      <div className="space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">View Farm Plan</h1>
        <nav className="flex items-center space-x-2 text-sm">
          <Link href="/agronomist/farm-plan" className="text-blue-600 hover:text-blue-800">
            Farm Plan
          </Link>
          <ChevronRight className="size-4 text-gray-400" />
          <span className="text-gray-900">View Farm Plan</span>
        </nav>
      </div>

      <ViewFarmPlanHeader farmPlan={farmPlan} />

      <div className="grid gap-6 md:grid-cols-2">
        <ViewFarmerInfo farmPlan={farmPlan} />
        <ViewFarmPlanTemplateInfo farmPlan={farmPlan} />
      </div>

      <div className="mt-16 text-center font-semibold text-kitaph-primary">
        FARM INPUTS FOR DELIVERY / PICK-UP (Amount to be Held in favor of KITA)
      </div>

      <ViewFarmPlanItems farmPlan={farmPlan} />

      <div className="flex flex-wrap gap-6 pt-8">
        <div className="w-full md:flex-1">
          <ViewCostSummary farmPlan={farmPlan} />
        </div>
        <div className="w-full md:w-1/3">
          <ViewAgronomistInfo farmPlan={farmPlan} />
        </div>
      </div>
    </div>
  );
}
