'use client';

import { useHookstate } from '@hookstate/core';
import { differenceInDays, format } from 'date-fns';
import { useEffect } from 'react';

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import { CREDIT_TAB } from '../../constants';
import { LoanAvailedTable } from './loan-availed-table';
import { columns } from './loan-availed-table/columns';
import { LoanHistoryTable } from './loan-history-table';
import { columnsLoanHistory } from './loan-history-table/columns';
import { LoanPaymentTable } from './loan-payment-table';
import { columns as columnLoanPayment } from './loan-payment-table/columns';

export default function CreditHistoryTabContent() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const data = useHookstate(gState.selected.accountInfo['info']);
  const currentLoan = useHookstate(gStateP.selected['currentLoan']);
  const daysDelay = useHookstate(0);
  const isDelay = useHookstate(false);

  const creditTab = useHookstate(gState.selected.accountInfo.tabs.creditHistory);
  const loanAvailed = useHookstate(gState.selected.accountInfo.finance);
  const creditScoreHistory = useHookstate(gState.creditScoring.creditScoreHistory);
  const loanPayments = useHookstate(gState.selected.accountInfo.loanPayments);

  useEffect(() => {
    if (currentLoan.value) {
      const _days = differenceInDays(new Date(), new Date(currentLoan.topupRequest.due_at.value));
      daysDelay.set(_days);
      isDelay.set(_days > 0);
    }
  }, [currentLoan]);

  return (
    <div className="pt-6">
      {/* Stats Card */}
      <div className="grid gap-4 sm:grid-cols-2 2xl:grid-cols-4">
        <div className="card space-y-2 text-center">
          <div className="text-2xl font-bold text-primary">
            {data.value
              ? Number(data.wallet.value ? data.wallet.credit.value : 0).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })
              : 0}
          </div>
          <div className="text-sm font-bold text-[#A3AED0]">Total Loan Amount</div>
        </div>

        <div className="card space-y-2 text-center">
          <div className="text-2xl font-bold text-primary">
            {currentLoan.value ? format(new Date(currentLoan.topupRequest.due_at.value), 'MMM dd, yyyy') : 'N/A'}
          </div>
          <div className="text-sm font-bold text-[#A3AED0]">Due Date</div>
        </div>

        <div className="card space-y-2 text-center">
          <div className="text-2xl font-bold text-primary">
            {data.value
              ? Number(data.wallet.value ? data.wallet.credit.value - data.wallet.payment.value : 0).toLocaleString(
                  'en-US',
                  {
                    style: 'currency',
                    currency: 'PHP',
                  },
                )
              : 0}
          </div>
          <div className="text-sm font-bold text-[#A3AED0]">Loan Balance</div>
        </div>

        <div className="card space-y-2 text-center">
          <div className="text-2xl font-bold text-primary">
            {isDelay.value ? daysDelay.value : Math.abs(daysDelay.value)}
          </div>
          <div className="text-sm font-bold text-[#A3AED0]">{isDelay.value ? 'Days Delay' : 'Days before due'}</div>
        </div>
      </div>

      {/* Tabs */}
      <ScrollArea className="my-6 w-[calc(100vw-48px)] sm:w-auto">
        <div className="flex w-max items-center gap-8">
          {CREDIT_TAB.map((tab, index) => {
            const isSelected = tab.value === creditTab.value;

            return (
              <button
                key={tab.value}
                className={cn(
                  'transition-all duration-300 ease-in-out whitespace-nowrap my-3',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => {
                  creditTab.set(tab.value);
                }}
              >
                {tab.name}
              </button>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Content */}
      <div className="">
        {creditTab.value === 'loan_availed' && (
          <LoanAvailedTable
            data={loanAvailed.data.get({ noproxy: true })}
            metadata={loanAvailed['meta'].get({ noproxy: true })}
            columns={columns}
          />
        )}
        {creditTab.value === 'loan_payments' && (
          <LoanPaymentTable
            data={loanPayments.data.get({ noproxy: true })}
            metadata={loanPayments['meta'].get({ noproxy: true })}
            columns={columnLoanPayment}
          />
        )}
        {creditTab.value === 'loan_history' && (
          <LoanHistoryTable
            data={creditScoreHistory['data'].get({ noproxy: true })}
            metadata={creditScoreHistory['meta'].get({ noproxy: true })}
            columns={columnsLoanHistory}
          />
        )}
      </div>
    </div>
  );
}
