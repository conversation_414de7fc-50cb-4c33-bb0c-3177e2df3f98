'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { TSelectDate } from '../components/select-date/types';
import { getDateRange } from '../components/select-date/util';
import {
  IMarketplaceTopProducts,
  IRegisteredFarmer,
  ISalesSummary,
  ITopTransactionValue,
  ITradingPostTopCrops,
} from '../types';

// State
const initialState = {
  selectDate: 'today' as TSelectDate,
};
const dashboardState = hookstate(initialState, devtools({ key: 'dashboardState' }));

// Fetcher
const fetchTradingPostSalesSummary = async (currentUser = 'admin') => {
  const { data } = await axios.get(`/${currentUser}/dashboard/viewTradingpostSalesSummary`, {
    params: {
      ...getDateRange(dashboardState.selectDate.value),
    },
  });
  return data.data as ISalesSummary;
};

const fetchMarketplaceSalesSummary = async (currentUser = 'admin') => {
  const { data } = await axios.get(`/${currentUser}/dashboard/viewMarketplaceSalesSummary`, {
    params: {
      ...getDateRange(dashboardState.selectDate.value),
    },
  });
  return data.data as ISalesSummary;
};

const fetchRegisteredFarmer = async (currentUser = 'admin') => {
  const { data } = await axios.get(`/${currentUser}/dashboard/viewRegisteredAccounts`, {
    params: {
      ...getDateRange(dashboardState.selectDate.value),
    },
  });
  return data.data as IRegisteredFarmer;
};

const fetchMarketplaceTopProducts = async (currentUser = 'admin') => {
  const params = getDateRange(dashboardState.selectDate.value);
  const { data } = await axios.get(`/${currentUser}/dashboard/viewMarketplaceTopProducts`, {
    params: {
      startDate: params.startDate,
      endDate: params.endDate,
    },
  });
  return data.data as IMarketplaceTopProducts[];
};

const fetchTradingPostTopCrops = async (currentUser = 'admin') => {
  const params = getDateRange(dashboardState.selectDate.value);
  const { data } = await axios.get(`/${currentUser}/dashboard/viewTradingpostTopProducts`, {
    params: {
      startDate: params.startDate,
      endDate: params.endDate,
    },
  });
  return data.data as ITradingPostTopCrops[];
};

const fetchTopTransactionValue = async (currentUser = 'admin') => {
  const params = getDateRange(dashboardState.selectDate.value);
  const { data } = await axios.get(`/${currentUser}/dashboard/viewTopTransactionValue`, {
    params: {
      startDate: params.startDate,
      endDate: params.endDate,
    },
  });
  return data.data as ITopTransactionValue[];
};

// Hooks
function useDashboard(currentUser = 'admin') {
  const state = useHookstate(dashboardState);
  const gStateP = useGlobalStatePersist();

  const tradingPostQuery = useQuery({
    queryKey: [
      'tradingPostSalesSummary',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...getDateRange(state.selectDate.value),
      },
    ],
    queryFn: () => fetchTradingPostSalesSummary(currentUser),
    enabled: !!gStateP.user.value,
  });

  const marketplaceQuery = useQuery({
    queryKey: [
      'marketplaceSalesSummary',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...getDateRange(state.selectDate.value),
      },
    ],
    queryFn: () => fetchMarketplaceSalesSummary(currentUser),
    enabled: !!gStateP.user.value,
  });

  const registeredFarmerQuery = useQuery({
    queryKey: [
      'registeredFarmer',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...getDateRange(state.selectDate.value),
      },
    ],
    queryFn: () => fetchRegisteredFarmer(currentUser),
    enabled: !!gStateP.user.value,
  });

  const marketplaceTopProductsQuery = useQuery({
    queryKey: [
      'marketplaceTopProducts',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...getDateRange(state.selectDate.value),
      },
    ],
    queryFn: () => fetchMarketplaceTopProducts(currentUser),
    enabled: !!gStateP.user.value,
  });

  const tradingPostTopCropsQuery = useQuery({
    queryKey: [
      'tradingPostTopCrops',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...getDateRange(state.selectDate.value),
      },
    ],
    queryFn: () => fetchTradingPostTopCrops(currentUser),
    enabled: !!gStateP.user.value,
  });

  const topTransactionValueQuery = useQuery({
    queryKey: [
      'topTransactionValue',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...getDateRange(state.selectDate.value),
      },
    ],
    queryFn: () => fetchTopTransactionValue(currentUser),
    enabled: !!gStateP.user.value,
  });

  return {
    tradingPostQuery,
    marketplaceQuery,
    registeredFarmerQuery,
    marketplaceTopProductsQuery,
    tradingPostTopCropsQuery,
    topTransactionValueQuery,
    state,
  };
}

export { dashboardState };
export default useDashboard;
