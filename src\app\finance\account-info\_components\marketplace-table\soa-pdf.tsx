/* eslint-disable jsx-a11y/alt-text */
'use client';

import { State } from '@hookstate/core';
import { Document, Font, Image, Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import { format } from 'date-fns';
import { createTw } from 'react-pdf-tailwind';

import { useGlobalStatePersist } from '@/lib/store/persist';
import { ISoaData } from '@/lib/store/soa-store.types';
import { toDecimal } from '@/lib/utils';

Font.register({
  family: 'DM Sans',
  fonts: [
    { src: '/fonts/dm-sans/DMSans-Regular.ttf' },
    { src: '/fonts/dm-sans/DMSans-Bold.ttf', fontWeight: 'bold' },
    { src: '/fonts/dm-sans/DMSans-BoldItalic.ttf', fontWeight: 'bold', fontStyle: 'italic' },
    { src: '/fonts/dm-sans/DMSans-Italic.ttf', fontStyle: 'italic' },
    { src: '/fonts/dm-sans/DMSans-Medium.ttf', fontWeight: 'medium' },
    { src: '/fonts/dm-sans/DMSans-MediumItalic.ttf', fontWeight: 'medium', fontStyle: 'italic' },
  ],
});

// The 'theme' object is your Tailwind theme config
const tw = createTw({
  theme: {
    fontFamily: {
      sans: ['DM Sans'],
    },
    extend: {
      colors: {
        kitaph: {
          blue: '#274493',
        },
      },
    },
  },
});

const styles = StyleSheet.create({
  pagination: {
    fontSize: 10, // Try different sizes (e.g., 8, 10, 12) in points
    position: 'absolute',
    bottom: 12, // Adjust as needed
    left: 36,
  },
});

interface ISoaPdfProps {
  soaBulk: State<ISoaData[]>;
}

export default function SoaPdf({ soaBulk }: ISoaPdfProps) {
  const gStateP = useGlobalStatePersist();

  return (
    <Document
      title={`SOA_${soaBulk.value[0]?.formatted?.farmerName.replaceAll(' ', '')}_${format(new Date(), 'yyyy-MM-dd')}`}
    >
      {soaBulk.value.map((orderInfo) => {
        return (
          <Page key={orderInfo.raw.id} size="A4" style={tw('p-12 font-sans')}>
            {/* Header */}
            <View fixed style={tw('inset-x-0 top-0 mb-6')}>
              <View style={tw('flex flex-row items-center justify-between')}>
                <Image src="/kita-logo.png" style={tw('h-14')} />

                <View style={tw('text-xs italic font-thin text-gray-500 items-end')}>
                  <Text>{`Date & Time Generated: ${format(new Date(), 'MMM dd, yyyy - hh:mm a')}`}</Text>
                  <Text style={tw('mt-0.5')}>{`Order ID No. ${orderInfo.formatted.referenceNumber}`}</Text>
                </View>
              </View>
            </View>

            <Text style={tw('text-2xl mb-2 text-center font-bold text-kitaph-blue')}>STATEMENT OF ACCOUNT</Text>

            {/* Farmer Info */}
            <View style={tw('flex-row text-sm items-center gap-8')}>
              <View style={tw('gap-0.5')}>
                <Text>Farmer Name</Text>
                <Text>Farmer Address</Text>
                <Text>No. of Hectares</Text>
                <Text>Main Crop To Be Planted</Text>
                <Text>Crop Type</Text>
                <Text>Sub Crop Planted</Text>
                <Text>Total E-wallet Amount</Text>
              </View>
              <View style={tw('font-bold gap-0.5')}>
                <Text>{orderInfo.formatted.farmerName || ' '}</Text>
                <Text>{orderInfo.formatted.farmAddress || ' '}</Text>
                <Text>{orderInfo.formatted.farmArea || ' '}</Text>
                <Text>{orderInfo.formatted.mainCropPlanted || ' '}</Text>
                <Text>{orderInfo.formatted.cropType || ' '}</Text>
                <Text>{orderInfo.formatted.subCropPlanted || ' '}</Text>
                <Text>Php {toDecimal(orderInfo.formatted.walletBalanceBefore || 0)}</Text>
              </View>
            </View>

            {/* Table */}
            <View style={tw('gap-4 py-8')}>
              {/* Crops */}
              {orderInfo.formatted.crops.length > 0 && (
                <View>
                  {/* Table Header */}
                  <View style={tw('bg-kitaph-blue text-sm font-bold text-white flex-row items-center')}>
                    <Text style={tw('py-2 px-4 flex-1')}>Crop</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Quantity</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Price</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Total</Text>
                  </View>

                  {/* Table Row */}
                  <View style={tw('mt-2')}>
                    {orderInfo.formatted.crops.map((crop, index) => {
                      return (
                        <View key={index} style={tw('flex-row text-xs items-center')}>
                          <Text style={tw('py-2 px-4 flex-1 font-medium')}>{crop.product}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{crop.quantity}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.price || 0)}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.total || 0)}</Text>
                        </View>
                      );
                    })}
                  </View>
                </View>
              )}

              {/* Crop Protection Requirements */}
              {orderInfo.formatted.cropProtection.length > 0 && (
                <View>
                  {/* Table Header */}
                  <View style={tw('bg-kitaph-blue text-sm font-bold text-white flex-row items-center')}>
                    <Text style={tw('py-2 px-4 flex-1')}>Crop Protection Requirements</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Quantity</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Price</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Total</Text>
                  </View>

                  {/* Table Row */}
                  <View style={tw('mt-2')}>
                    {orderInfo.formatted.cropProtection.map((crop, index) => {
                      return (
                        <View key={index} style={tw('flex-row text-xs items-center')}>
                          <Text style={tw('py-2 px-4 flex-1 font-medium')}>{crop.product}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{crop.quantity}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.price || 0)}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.total || 0)}</Text>
                        </View>
                      );
                    })}
                  </View>
                </View>
              )}

              {/* Fertilizer Requirements */}
              {orderInfo.formatted.fertilizer.length > 0 && (
                <View>
                  {/* Table Header */}
                  <View style={tw('bg-kitaph-blue text-sm font-bold text-white flex-row items-center')}>
                    <Text style={tw('py-2 px-4 flex-1')}>Fertilizer Requirements</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Quantity</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Price</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Total</Text>
                  </View>

                  {/* Table Row */}
                  <View style={tw('mt-2')}>
                    {orderInfo.formatted.fertilizer.map((crop, index) => {
                      return (
                        <View key={index} style={tw('flex-row text-xs items-center')}>
                          <Text style={tw('py-2 px-4 flex-1 font-medium')}>{crop.product}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{crop.quantity}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.price || 0)}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.total || 0)}</Text>
                        </View>
                      );
                    })}
                  </View>
                </View>
              )}

              {/* Seeds Requirements */}
              {orderInfo.formatted.seeds.length > 0 && (
                <View>
                  {/* Table Header */}
                  <View style={tw('bg-kitaph-blue text-sm font-bold text-white flex-row items-center')}>
                    <Text style={tw('py-2 px-4 flex-1')}>Seeds Requirements</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Quantity</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Price</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Total</Text>
                  </View>

                  {/* Table Row */}
                  <View style={tw('mt-2')}>
                    {orderInfo.formatted.seeds.map((crop, index) => {
                      return (
                        <View key={index} style={tw('flex-row text-xs items-center')}>
                          <Text style={tw('py-2 px-4 flex-1 font-medium')}>{crop.product}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{crop.quantity}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.price || 0)}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.total || 0)}</Text>
                        </View>
                      );
                    })}
                  </View>
                </View>
              )}

              {/* Miscellaneous */}
              {orderInfo.formatted.miscellaneous.length > 0 && (
                <View>
                  {/* Table Header */}
                  <View style={tw('bg-kitaph-blue text-sm font-bold text-white flex-row items-center')}>
                    <Text style={tw('py-2 px-4 flex-1')}>Miscellaneous</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Quantity</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Price</Text>
                    <Text style={tw('py-2 px-4 w-[20%] text-right')}>Total</Text>
                  </View>

                  {/* Table Row */}
                  <View style={tw('mt-2')}>
                    {orderInfo.formatted.miscellaneous.map((crop, index) => {
                      return (
                        <View key={index} style={tw('flex-row text-xs items-center')}>
                          <Text style={tw('py-2 px-4 flex-1 font-medium')}>{crop.product}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{crop.quantity}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.price || 0)}</Text>
                          <Text style={tw('py-2 px-4 w-[20%] text-right')}>{toDecimal(crop.total || 0)}</Text>
                        </View>
                      );
                    })}
                  </View>
                </View>
              )}
            </View>

            {/* Summary */}
            <View style={tw('flex-row justify-end pr-4')}>
              <View style={tw('flex-row gap-4 text-sm')}>
                <View style={tw('items-end')}>
                  <Text style={tw('mb-4 font-bold')}>Total Ordered Amount</Text>
                  <Text>E-wallet Payment</Text>
                  <Text>Cash Payment</Text>
                  <Text>E-wallet Balance</Text>
                </View>
                <View style={tw('font-bold items-end')}>
                  <Text style={tw('mb-4')}>Php {toDecimal(orderInfo.formatted.totalPrice || 0)}</Text>
                  <Text>
                    Php{' '}
                    {toDecimal(
                      orderInfo.raw.payment_method === 2
                        ? orderInfo.formatted.totalPrice // E-wallet only
                        : orderInfo.raw.payment_method === 3
                          ? orderInfo.formatted?.walletAllocation || 0 // Multiple
                          : 0, // Cash only
                    )}
                  </Text>
                  <Text>
                    Php{' '}
                    {toDecimal(
                      orderInfo.raw.payment_method === 1
                        ? orderInfo.formatted.totalPrice // Cash only
                        : orderInfo.raw.payment_method === 3
                          ? orderInfo.formatted?.cashAllocation || 0 // Multiple
                          : 0, // E-wallet only
                    )}
                  </Text>
                  <Text>Php {toDecimal(orderInfo.formatted.walletBalanceAfter || 0)}</Text>
                </View>
              </View>
            </View>

            {/* Signature */}
            <View style={tw('mt-8 flex-1 justify-end')}>
              <View style={tw('flex flex-row items-stretch max-h-[6.5em]')}>
                <View style={tw('border min-h-full p-2 border-r-0 border-gray-500 w-1/4')}>
                  <Text style={tw('text-sm')}>Created By</Text>
                  <Text style={tw('text-sm mt-9 border-t border-gray-400 mx-2 pt-0.5 text-center')}>
                    {gStateP.user.value && gStateP.user.user.admin.value
                      ? `${gStateP.user.user.admin.first_name.value} ${gStateP.user.user.admin.last_name.value}`
                      : ``}
                  </Text>
                </View>

                <View style={tw('border min-h-full p-2 border-r-0 border-gray-500 w-1/4')}>
                  <Text style={tw('text-sm')}>Checked By</Text>
                  <View style={tw('text-sm mt-9 border-t border-gray-400 mx-2 pt-0.5 text-center')}>
                    <Text style={tw('text-white')}>---</Text>
                  </View>
                </View>

                <View style={tw('border min-h-full p-2 border-r-0 border-gray-500 w-1/4')}>
                  <Text style={tw('text-sm')}>Approved By</Text>
                  <View style={tw('text-sm mt-9 border-t border-gray-400 mx-2 pt-0.5 text-center')}>
                    <Text style={tw('text-white')}>---</Text>
                  </View>
                </View>

                <View style={tw('border min-h-full p-2 border-gray-500 w-1/4')}>
                  <Text style={tw('text-sm')}>Received By</Text>
                  <Text style={tw('text-sm mt-9 border-t border-gray-400 mx-2 pt-0.5 text-center')}>
                    {orderInfo.formatted.farmerName}
                  </Text>
                </View>
              </View>
            </View>

            {/* Pagination */}
            <Text
              style={styles.pagination}
              render={({ pageNumber, totalPages }) => `Page ${pageNumber} of ${totalPages}`}
            />
          </Page>
        );
      })}
    </Document>
  );
}
