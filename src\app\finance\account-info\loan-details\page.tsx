'use client';

import { useHookstate } from '@hookstate/core';
import { useRouter } from 'next/navigation';
import { Suspense } from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

import { RequestStatus, RequestStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import FetchDetails from './_components/FetchDetails';

export default function LoanDetails() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();
  const data = useHookstate(gStateP.finance1['reqDetails']);
  const infoTab = useHookstate(0);

  return (
    <div className="space-y-6 p-6 md:p-12">
      <Suspense>
        <FetchDetails />
      </Suspense>

      <div className="">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Loan Details</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink onClick={() => router.back()}>Account Information</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Loan Details</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {data.value && (
        <div className="mt-6 rounded-lg p-6 shadow-md shadow-slate-300">
          {/* Tabs */}
          <ScrollArea className="w-[calc(100vw-100px)] pr-6 sm:w-auto">
            <div className="flex w-max items-center gap-8">
              <button
                className={cn(
                  'transition-all duration-300 ease-in-out',
                  infoTab.value === 0
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => infoTab.set(0)}
              >
                Request Details
              </button>
              <button
                className={cn(
                  'transition-all duration-300 ease-in-out',
                  infoTab.value === 1
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => infoTab.set(1)}
              >
                Attachments
              </button>
              <button
                className={cn(
                  'transition-all duration-300 ease-in-out',
                  infoTab.value === 2
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => infoTab.set(2)}
              >
                Remarks
              </button>
            </div>

            <ScrollBar orientation="horizontal" />
          </ScrollArea>

          {/* Request Details */}
          {infoTab.value === 0 && (
            <dl className="mt-8 grid gap-4 md:grid-cols-2">
              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Date & Time Request</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${new Date(data['created_at'].value).toLocaleString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric',
                      hour12: true,
                    })}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Request Amount</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${Number(data['amount'].value).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Request By</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data['processedBy']['finance'].value ? `${data['processedBy']['finance']['first_name'].value} ${data['processedBy']['finance']['last_name'].value}` : data['processedBy']['admin'].value ? `${data['processedBy']['admin']['first_name'].value} ${data['processedBy']['admin']['last_name'].value}` : `${data['processedBy']['email'].value}`}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Loan ID</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data['reference_number'].value}`}
                  </dd>
                </div>
              </div>

              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Status</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    <Badge className={cn(RequestStatusLabels[data['status'].value].color, 'capitalize')}>
                      {RequestStatusLabels[data['status'].value].label}
                    </Badge>
                  </dd>
                </div>

                {/* FOR APPROVAL / PENDING */}
                {(data['status'].value === RequestStatus.PENDING || data['status'].value === RequestStatus.APPROVE) && (
                  <>
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Date & Time Approved</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${
                          data['approvedBy'].value
                            ? new Date(data['updated_at'].value).toLocaleString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: 'numeric',
                                minute: 'numeric',
                                hour12: true,
                              })
                            : '-'
                        }`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Approved By</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data['approvedBy'].value ? (data['approvedBy']['finance'].value ? `${data['approvedBy']['finance']['first_name'].value} ${data['approvedBy']['finance']['last_name'].value}` : data['approvedBy']['admin'].value ? `${data['approvedBy']['admin']['first_name'].value} ${data['approvedBy']['admin']['last_name'].value}` : `${data['approvedBy']['email'].value}`) : '-'}`}
                      </dd>
                    </div>
                  </>
                )}

                {/* REJECTED */}
                {data['status'].value === RequestStatus.REJECTED && (
                  <>
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Date & Time Rejected</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${
                          data['approvedBy'].value
                            ? new Date(data['approvedBy']['updated_at'].value).toLocaleString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: 'numeric',
                                minute: 'numeric',
                                hour12: true,
                              })
                            : '-'
                        }`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Rejected By</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data['approvedBy'].value ? (data['approvedBy']['finance'].value ? `${data['approvedBy']['finance']['first_name'].value} ${data['approvedBy']['finance']['last_name'].value}` : data['approvedBy']['admin'].value ? `${data['approvedBy']['admin']['first_name'].value} ${data['approvedBy']['admin']['last_name'].value}` : `${data['approvedBy']['email'].value}`) : '-'}`}
                      </dd>
                    </div>
                  </>
                )}
              </div>
            </dl>
          )}

          {/* Attachments */}
          {infoTab.value === 1 && (
            <dl className="mt-8 grid grid-cols-2 gap-4">
              <div className="font-dmSans">
                {data['documents'].length > 0 &&
                  data['documents'].value.map((doc, index) => {
                    const split = doc.document.split('/');
                    const docLength = split.length;
                    const last = split[docLength - 1];

                    return (
                      <div key={doc.id} className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">{`Document ${index + 1}`}</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          <a href={doc.document} target="_blank" rel="noreferrer" className="text-blue-500 underline">
                            {last}
                          </a>
                        </dd>
                      </div>
                    );
                  })}
              </div>
            </dl>
          )}

          {/* Remarks */}
          {infoTab.value === 2 && (
            <div className="space-y-8 divide-y-2 divide-dashed">
              {data['remarks'].value.map((remark, index) => (
                <div key={remark.id} className="pt-8">
                  <div>{remark.remarks}</div>

                  <div className="mt-4 flex gap-2 text-sm text-gray-500">
                    <span>
                      {remark.remarksBy.finance
                        ? `${remark.remarksBy.finance.first_name} ${remark.remarksBy.finance.last_name}`
                        : remark.remarksBy.admin
                          ? `${remark.remarksBy.admin.first_name} ${remark.remarksBy.admin.last_name}`
                          : `${remark.remarksBy.username}`}
                    </span>
                    <span>|</span>
                    <span>
                      {new Date(remark.created_at).toLocaleString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </span>
                    <span>|</span>
                    <span>
                      {new Date(remark.created_at).toLocaleString('en-US', {
                        hour: 'numeric',
                        minute: 'numeric',
                        hour12: true,
                      })}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
