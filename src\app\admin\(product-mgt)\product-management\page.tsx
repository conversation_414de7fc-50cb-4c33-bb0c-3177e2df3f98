'use client';

import ChemicalsPage from '@/components/layout/admin/product-management/chemicals';
import CropsPage from '@/components/layout/admin/product-management/crops';
import FertilizerPage from '@/components/layout/admin/product-management/fertilizer';
import OtherProductPage from '@/components/layout/admin/product-management/others';
import SeedsPage from '@/components/layout/admin/product-management/seeds';

import { useGlobalStatePersist } from '@/lib/store/persist';

export default function ProductManagement() {
  const gStateP = useGlobalStatePersist();

  return (
    <div className="">
      {gStateP.tabsProductMgt.value === 'crops' && <CropsPage />}
      {gStateP.tabsProductMgt.value === 'fertilizer' && <FertilizerPage />}
      {gStateP.tabsProductMgt.value === 'seeds' && <SeedsPage />}
      {gStateP.tabsProductMgt.value === 'chemicals' && <ChemicalsPage />}
      {gStateP.tabsProductMgt.value === 'others' && <OtherProductPage />}
    </div>
  );
}
