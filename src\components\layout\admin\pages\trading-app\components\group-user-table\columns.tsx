'use client';

import { useHookstate } from '@hookstate/core';
import { MoreHorizontal, UserXIcon } from 'lucide-react';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { useGroupUserStore } from '@/lib/store/admin/trading-app/group-users-store';
import { IGroupUser } from '@/lib/store/admin/trading-app/group-users-store.types';

export const ColumnGroupUser = [
  {
    id: 'username',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Username" />,
    accessorFn: (row: IGroupUser) => `${row.user.username}`,
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const user = row.original.user;
      if (user.farmer) {
        return <div>{`${user.farmer.first_name} ${user.farmer.last_name}`}</div>;
      } else if (user.nonFarmer) {
        return <div>{`${user.nonFarmer.first_name} ${user.nonFarmer.last_name}`}</div>;
      }
      return <div>-</div>;
    },
    accessorFn: (row: IGroupUser) => {
      const user = row.user;
      if (user.farmer) {
        return `${user.farmer.first_name} ${user.farmer.last_name}`;
      } else if (user.nonFarmer) {
        return `${user.nonFarmer.first_name} ${user.nonFarmer.last_name}`;
      }
      return '';
    },
  },
  {
    id: 'remarks',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Remarks" />,
    cell: ({ row }) => <div className="line-clamp-2">{row.getValue('remarks')}</div>,
    accessorFn: (row: IGroupUser) => `${row.remarks}`,
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader className="text-center" column={column} title="Actions" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

const Action = ({ row }) => {
  const data: IGroupUser = row.original;
  const { removeUser } = useGroupUserStore();
  const removeConfirm = useHookstate(false);

  return (
    <>
      <div className="flex justify-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <MoreHorizontal className="size-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuItem className="flex items-center" onClick={() => removeConfirm.set(true)}>
              <UserXIcon className="mr-2 size-4" />
              <span>Remove</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Deactivate Confirmation */}
        <AlertDialog open={removeConfirm.value} onOpenChange={removeConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will remove {data.user.username} from this group. This process cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  removeUser(data.id, data.trading_app_group_price_id);
                }}
              >
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};
