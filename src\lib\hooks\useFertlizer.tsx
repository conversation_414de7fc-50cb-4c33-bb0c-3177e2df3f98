'use client';

import { toast } from 'sonner';
import { z } from 'zod';

import { IFertilizer } from '@/components/layout/admin/product-management/fertilizer/fertilizer-table/fertilizer.types';

import axios from '@/lib/api';

import { useGlobalState } from '../store';

export const FertilizerSchema = z.object({
  fertilizers: z.array(
    z.object({
      name: z
        .string()
        .min(2, 'Fertilizer name is required')
        .max(50, 'Fertilizer name is too long')
        .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
          message: 'Fertilizer name must contain only letters and spaces',
        }),
      type: z.enum(['0', '1', '2'], {
        errorMap: () => ({ message: 'Fertilizer type is required' }),
      }),
      grade: z
        .string()
        .min(1, 'Grade is required')
        .refine((val) => !val || /^(\d{1,2}-\d{1,2}-\d{1,2}|\d{1,2}-\d{1,2})$/.test(val), 'Invalid grade format'),
    }),
  ),
});
export type FertilizerType = z.infer<typeof FertilizerSchema>;

export const UpdateFertlizerSchema = z.object({
  fertilizerId: z.string().min(1, 'Fertilizer ID is required'),
  name: z
    .string()
    .min(2, 'Fertilizer name is required')
    .max(50, 'Fertilizer name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Fertilizer name must contain only letters and spaces',
    }),
  status: z.string().min(1, 'Fertilizer status is required'),
  type: z.enum(['0', '1', '2'], {
    errorMap: () => ({ message: 'Fertilizer type is required' }),
  }),
  grade: z
    .string()
    .min(1, 'Grade is required')
    .refine((val) => !val || /^(\d{1,2}-\d{1,2}-\d{1,2}|\d{1,2}-\d{1,2})$/.test(val), 'Invalid grade format'),
});
export type UpdateFertilizerType = z.infer<typeof UpdateFertlizerSchema>;

export default function useFertlizer() {
  const gState = useGlobalState();

  const getFertilizer = async () => {
    try {
      const _crops = await axios.get('/admin/fertilizers/viewAll').then((res) => res.data.data);
      console.log('getFertilizer: ', _crops);
      gState.admin.fertilizer.data.set(_crops as IFertilizer[]);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getFertilizer: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const addFertilizer = async (data: FertilizerType) => {
    console.log('addFertilizer: ', data);

    try {
      const _data = await axios.post('/admin/fertilizers/create', data).then((res) => res.data);
      console.log('addFertilizer: ', _data);
      await getFertilizer();

      toast.success('Success', {
        description: 'Fertilizer added',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('addFertilizer: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateFertilizer = async (data) => {
    console.log('updateFertilizer: ', data);

    try {
      const _data = await axios.post('/admin/fertilizers/update', data).then((res) => res.data);
      console.log('updateFertilizer: ', _data);
      await getFertilizer();

      toast.success('Success', {
        description: 'Fertilizer updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateFertilizer: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { getFertilizer, addFertilizer, updateFertilizer };
}
