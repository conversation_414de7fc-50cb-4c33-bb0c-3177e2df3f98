'use client';

import { format } from 'date-fns';
import { FC } from 'react';
import { CSVLink } from 'react-csv';

import { Button } from '@/components/ui/button';

import useMarketplaceTopProducts from '../hooks/useMarketplaceTopProducts';

const exportHeaders = [
  { label: '#', key: 'product_id' },
  { label: 'Product Name', key: 'product_name' },
  { label: 'Quantity', key: 'product_quantity' },
  { label: 'OUM', key: 'product_unit' },
  { label: 'Price per pc', key: 'product_price' },
  { label: 'Sales', key: 'product_sales' },
];

export const ExportMarketplaceTopProducts: FC = () => {
  const { marketplaceTopProductsQuery } = useMarketplaceTopProducts();

  return (
    <div>
      <Button size="sm" className="h-8" asChild>
        <CSVLink
          data={marketplaceTopProductsQuery.data || []}
          headers={exportHeaders}
          filename={`marketplace-top-products-${format(new Date(), 'yyyy-MM-dd')}.csv`}
          target="_blank"
        >
          Export
        </CSVLink>
      </Button>
    </div>
  );
};
