'use client';

import { useHookstate } from '@hookstate/core';
import { differenceInDays, format } from 'date-fns';
import { PaperclipIcon, Trash2Icon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Controller, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogClose, DialogContent, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { DEFAULT_ADMIN_ACTIVE_MENU } from '@/lib/constants';
import useFinance from '@/lib/hooks/useFinance';
import useLoanPayment from '@/lib/hooks/useLoanPayment';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn, urlify } from '@/lib/utils';

export default function RequestPage() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { getAccountInfo } = useFinance();
  const { payLoan } = useLoanPayment();
  const confirmDialog = useHookstate(false);

  const data = useHookstate(gStateP.selected['farmer']);
  const currentLoan = useHookstate(gStateP.selected['currentLoan']);
  const daysDelay = useHookstate(0);
  const isDelay = useHookstate(false);

  const address = data.value && data['farmer']['address'].value ? JSON.parse(data['farmer']['address'].value) : {};
  const router = useRouter();
  const loading = useHookstate(false);

  const [files, setFiles] = useState([]);
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc', '.docx'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.doc', '.docx'],
    },
    onDrop: (acceptedFiles) => {
      const newFiles = acceptedFiles.filter((file) => !files.some((f) => f.name === file.name));
      setFiles((v) => [...v, ...newFiles]);
    },
    multiple: true,
  });

  const removeFile = (file) => {
    const newFiles = [...files];
    newFiles.splice(newFiles.indexOf(file), 1);
    setFiles(newFiles);
  };

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    reset,
    watch,
  } = useForm({
    defaultValues: {
      amount: '',
      modeOfPayment: '',
      paymentDate: '',
      penaltyAmount: '',
      remarks: '',
    },
  });

  const onSubmit = async (data: any) => {
    try {
      loading.set(true);

      const formData = {
        ...data,
        userId: gStateP.selected['farmer']['id'].value,
        documents: files,
        penaltyAmount: data.penaltyAmount ? data.penaltyAmount : 0,
      };

      console.log('onSubmit: ', formData, gStateP.selected['farmer'].value);
      await payLoan(formData);

      router.push('/admin');
      gStateP.admin.activeMenu.set(DEFAULT_ADMIN_ACTIVE_MENU);
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  useEffect(() => {
    if (data) {
      getAccountInfo(data.id.value);
    }
  }, [gStateP.selected['farmer']]);

  useEffect(() => {
    if (currentLoan.value) {
      const _days = differenceInDays(new Date(), new Date(currentLoan.topupRequest.due_at.value));
      daysDelay.set(_days);
      isDelay.set(_days > 0);
    }
  }, [currentLoan]);

  return (
    <div className="p-8">
      <div className="card">
        <div className="flex gap-8">
          <div>
            {/* Profile Image */}
            <div>
              <img
                className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                src={data.user_img.value ? urlify(data.user_img.value, 'users/profile') : '/assets/user-default.jpg'}
                alt=""
              />
            </div>

            <div className="mt-4 flex justify-center">
              <Button
                onClick={() => {
                  router.push(`/admin/account-info/?id=${data['farmer'].user_id.value}`);
                }}
              >
                View more info
              </Button>
            </div>
          </div>

          <div className="flex-1">
            <div className="text-xl font-bold leading-loose text-indigo-900">Account Information</div>
            <dl className="grid grid-cols-2 gap-4">
              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data.farmer.first_name.value} ${data.farmer.last_name.value}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`ID${data.id.value.toString().padStart(9, '0')}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data.farmer.mobile_number.value ?? ''}`}
                  </dd>
                </div>
              </div>

              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${new Date(data.farmer.birth_date.value).toLocaleString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data.email.value}`}</dd>
                </div>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <div className="mt-8 grid grid-cols-4 gap-8">
        <div className="card flex items-center rounded-lg bg-white p-6">
          <div className="">
            <div className="text-xs font-bold text-kitaph-primary">Due Date</div>

            <div className="relative">
              <div className="mt-1 text-2xl font-bold">
                {currentLoan.value ? format(new Date(currentLoan.topupRequest.due_at.value), 'MMM dd, yyyy') : 'N/A'}
              </div>
            </div>
          </div>
        </div>

        <div className="card flex items-center rounded-lg bg-white p-6">
          <div className="">
            <div className="text-xs font-bold text-kitaph-primary">
              {isDelay.value ? 'Days Delay' : 'Days before due'}
            </div>

            <div className="relative">
              <div className="mt-1 text-2xl font-bold">
                {isDelay.value ? daysDelay.value : Math.abs(daysDelay.value)}
              </div>
            </div>
          </div>
        </div>

        <div className="card flex items-center rounded-lg bg-white p-6">
          <div className="">
            <div className="text-xs font-bold text-kitaph-primary">Loan Balance</div>

            <div className="relative">
              <div className="mt-1 text-2xl font-bold">
                {data.value
                  ? Number(data.wallet.credit.value - data.wallet.payment.value).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })
                  : 0}
              </div>
            </div>
          </div>
        </div>

        <div className="card flex items-center rounded-lg bg-white p-6">
          <div className="">
            <div className="text-xs font-bold text-kitaph-primary">Total Loan Amount</div>

            <div className="relative">
              <div className="mt-1 text-2xl font-bold">
                {data.value
                  ? Number(data.wallet.credit.value).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })
                  : 0}
              </div>
            </div>
          </div>
        </div>
      </div>

      <form id="request-form" className="mt-8 grid grid-cols-2 gap-8" onSubmit={handleSubmit(onSubmit)}>
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Payment Details</CardTitle>
              <CardDescription>Fill in the details below</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="amount">
                    Payment Amount <strong className="text-red-500">*</strong>
                  </Label>
                  <Input
                    {...register('amount', {
                      required: 'Payment amount is required',
                      validate: {
                        isPositive: (v) =>
                          (Number(v) > 0 &&
                            Number(v) <= Number(data.wallet.credit.value - data.wallet.payment.value)) ||
                          'Please enter an amount less than or equal to the outstanding loan balance.',
                      },
                    })}
                    className={cn(errors.amount && 'border-red-500 focus-visible:ring-red-500')}
                    type="text"
                    placeholder="e.g ₱1000.00"
                  />
                  {errors.amount && <p className="form-error">{`${errors.amount.message}`}</p>}
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="modeOfPayment">
                    Mode of Payment <strong className="text-red-500">*</strong>
                  </Label>
                  <Controller
                    control={control}
                    name="modeOfPayment"
                    rules={{ required: 'Mode of payment is required' }}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger
                          className={cn(errors.modeOfPayment && 'border-red-500 focus-visible:ring-red-500')}
                        >
                          <SelectValue placeholder="Select a mode of payment" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Mode of Payment</SelectLabel>
                            <SelectItem value="CASH">CASH</SelectItem>
                            <SelectItem value="OTC BANKING">OTC BANKING</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.modeOfPayment && <p className="form-error">{`${errors.modeOfPayment.message}`}</p>}
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="paymentDate">
                    Payment Date <strong className="text-red-500">*</strong>
                  </Label>
                  <Input
                    {...register('paymentDate', {
                      required: 'Payment date is required',
                    })}
                    className={cn(errors.paymentDate && 'border-red-500 focus-visible:ring-red-500')}
                    type="date"
                    placeholder="Select Date"
                  />
                  {errors.paymentDate && <p className="form-error">{`${errors.paymentDate.message}`}</p>}
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="penaltyAmount">Penalty Charge</Label>
                  <Input
                    {...register('penaltyAmount', {
                      required: false,
                      validate: {
                        isPositive: (v) => (v.length > 0 ? Number(v) > 0 || 'Amount must be greater than 0' : true),
                      },
                    })}
                    className={cn(errors.penaltyAmount && 'border-red-500 focus-visible:ring-red-500')}
                    type="text"
                    placeholder="e.g ₱500.00"
                  />
                  {errors.penaltyAmount && <p className="form-error">{`${errors.penaltyAmount.message}`}</p>}
                </div>

                <div className="grid gap-3">
                  <Label htmlFor="remarks">Remarks</Label>
                  <Textarea
                    {...register('remarks')}
                    className={cn(errors.remarks && 'border-red-500 focus-visible:ring-red-500')}
                    placeholder="Enter remarks here"
                  />
                  {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="">
          <Card>
            <CardHeader>
              <CardTitle>Attachments</CardTitle>
              <CardDescription>Add your attachments here</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                {/* Image Dropzone */}
                <div className="pt-2">
                  <div className="border-spacing-4 cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                    <div {...getRootProps({ className: 'dropzone' })}>
                      <input {...getInputProps()} />

                      {/* eslint-disable-next-line jsx-a11y/alt-text */}
                      <PaperclipIcon className="mx-auto mb-2 size-12 text-gray-500" />
                      <p>{`Drag 'n' drop files here, or`}</p>

                      <div className="py-4 font-bold text-primary">Browse</div>
                    </div>
                  </div>
                </div>

                {files.length > 0 && (
                  <div className="grid gap-2">
                    {files.map((file) => {
                      return (
                        <div
                          key={file.name}
                          className="flex items-center justify-between rounded-md border border-green-500 px-4 py-1"
                        >
                          <div>{file.name}</div>

                          <Button
                            className="size-8 rounded-full bg-red-50 hover:bg-red-100"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeFile(file)}
                          >
                            <Trash2Icon className="size-4 text-red-500" />
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </form>

      <div className="mt-6 flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={() => router.push('/finance')}>
          Cancel
        </Button>
        {loading.value ? (
          <ButtonLoading />
        ) : (
          <Button
            type="button"
            onClick={() => {
              confirmDialog.set(true);
            }}
          >
            Submit Request
          </Button>
        )}
      </div>

      <Dialog open={confirmDialog.value} onOpenChange={confirmDialog.set}>
        <DialogContent className="font-sans sm:max-w-lg">
          <div className="text-center">
            <div>
              <img className="mx-auto" src="/assets/undraw/confirm.png" alt="" />
            </div>
            <div className="my-4 text-xl font-bold">Confirm Payment</div>
            <div>Are you confirming that you intend to submit this payment?</div>
          </div>

          <DialogFooter className="mt-4 sm:justify-between">
            <DialogClose asChild>
              <Button variant="outline" type="button" size="lg" className="px-12">
                Review Items
              </Button>
            </DialogClose>

            {loading.value ? (
              <ButtonLoading />
            ) : (
              <DialogClose asChild>
                <Button type="submit" form="request-form" size="lg" className="px-12">
                  Confirm
                </Button>
              </DialogClose>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
