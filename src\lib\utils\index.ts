import { clsx, type ClassValue } from 'clsx';
import { differenceInHours, differenceInMinutes } from 'date-fns';
import { toast } from 'sonner';
import { twMerge } from 'tailwind-merge';

import config from '../config';

export interface IParsedAddress {
  addressHouseNumber: string;
  addressProvince: string;
  addressCity: string;
  addressBarangay: string;
  addressZipCode: string;
}

export const isDevMode = process.env.NODE_ENV === 'development';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function urlify(str: string, folder: string) {
  return str?.includes('http') ? str : `${config.setting.api_url}/uploads${folder ? `/${folder}` : ''}/${str}`;
}

export function bytesToSize(bytes: number) {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return 'n/a';
  const i = parseInt(String(Math.floor(Math.log(bytes) / Math.log(1024))), 10);
  if (i === 0) return `${bytes} ${sizes[i]}`;
  return `${(bytes / 1024 ** i).toFixed(1)} ${sizes[i]}`;
}

export function capitalize(str) {
  // Split the string into an array of words
  const words = str.split(' ');

  // Capitalize the first letter of each word
  const capitalizedWords = words.map((word) => {
    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
  });

  // Join the capitalized words back into a single string
  const capitalizedStr = capitalizedWords.join(' ');

  return capitalizedStr;
}

export const calculateDuration = (startDate, endDate) => {
  const hours = differenceInHours(endDate, startDate);
  const minutes = differenceInMinutes(endDate, startDate) % 60;

  return { hours, minutes };
};

export const uniqueIndentifier = () =>
  (new Date().getTime().toString(36) + Math.random().toString(36).slice(2)).toLowerCase();

export const catchError = (
  e: any,
  label: string = 'Error',
  title: string = 'Oops! Something went wrong',
  description: string = '',
) => {
  const error = e?.response?.data?.message || e.message;
  console.error(`${label}: `, error);

  toast.error(title, {
    description: description.length > 0 ? description : error,
  });
};

export function isObjectEmpty(obj: object) {
  return Object.keys(obj).length === 0;
}

// prevent scrolling
export const preventScroll = (e: React.WheelEvent<HTMLInputElement>) => {
  e.currentTarget.blur(); // `currentTarget` is already typed as HTMLInputElement
};

/**
 * Create a function that accepts a number and then returns it like this:
 * 1000 => 1k, 10000 => 10k, 100000 => 100k, 1000000 => 1M, 10000000 => 10M,
 * 100000000 => 100M, 1000000000 => 1B, etc
 */
export const formatNumber = (num: number) => {
  if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)}B`;
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}k`;
  return num.toString();
};

export const toCurrency = (num: number) => {
  return num.toLocaleString('en-US', {
    style: 'currency',
    currency: 'PHP',
  });
};

export const toDecimal = (num: number) => {
  return num.toLocaleString('en-US', {
    style: 'decimal',
    minimumFractionDigits: 2,
  });
};

export const formatNumberWithCommas = (num: number) => {
  return num.toLocaleString('en-US');
};

export const removeDuplicates = (arr, key) => {
  return arr.reduce((unique, item) => {
    if (!unique.some((obj) => obj[key] === item[key])) {
      unique.push(item);
    }
    return unique;
  }, []);
};

export function toAllCaps(str: string): string {
  return str.toUpperCase();
}

export const base64toFile = (base64: string | null | undefined, filename: string): File | null => {
  if (!base64 || typeof base64 !== 'string' || !base64.includes(',')) {
    return null;
  }

  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] ?? '';
  const bstr = atob(arr[1]);
  const u8arr = new Uint8Array(bstr.length);

  for (let i = 0; i < bstr.length; i++) {
    u8arr[i] = bstr.charCodeAt(i);
  }

  return new File([u8arr], filename, { type: mime });
};

export const fileToBase64 = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

export const formatId = (id) => {
  // id is being pass like this 120430
  const idString = `ID${id.toString().padStart(10, '0')}`;

  // format id like this ID0-000-120-430
  return `${idString.slice(0, 3)}-${idString.slice(3, 6)}-${idString.slice(6, 9)}-${idString.slice(9, 12)}`;
};

export const formatAddress = (rawAddress: string): string => {
  try {
    const addressObj: IParsedAddress = JSON.parse(rawAddress);

    const province = JSON.parse(addressObj.addressProvince || '{}');
    const city = JSON.parse(addressObj.addressCity || '{}');
    const barangay = JSON.parse(addressObj.addressBarangay || '{}');

    return [
      addressObj.addressHouseNumber,
      barangay.brgy_name,
      city.city_name,
      province.province_name,
      `Zip Code ${addressObj.addressZipCode}`,
    ]
      .filter(Boolean)
      .join(', ');
  } catch (error) {
    console.error('Failed to format address:', error);
    return '';
  }
};

export const handleDownload = async (attachment: string, fileName: string) => {
  const res = await fetch(attachment);
  const blob = await res.blob();
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = fileName || 'file';
  link.click();

  URL.revokeObjectURL(url);
};
