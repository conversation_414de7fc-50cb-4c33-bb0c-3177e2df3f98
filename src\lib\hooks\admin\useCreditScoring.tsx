'use client';

import { useHookstate } from '@hookstate/core';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { catchError } from '@/lib/utils';

export default function useCreditScoring() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const rules = useHookstate(gState.creditScoring.rules);
  const farmer = useHookstate(gState.creditScoring.farmer);
  const group = useHookstate(gState.admin.creditScoreMgt.groups.selected);
  const loanStage = useHookstate(gState.creditScoring.loanStage);

  const beforeDetails = useHookstate(gState.creditScoring.beforeDetails);
  const duringDetails = useHookstate(gState.creditScoring.duringDetails);
  const afterDetails = useHookstate(gState.creditScoring.afterDetails);

  // Before Loan
  const beforeRules = useHookstate(gState.creditScoring.beforeRules);
  const accountProfile = useHookstate(gState.creditScoring.accountProfile);
  const agricultureActivity = useHookstate(gState.creditScoring.agricultureActivity);
  const transactionRecords = useHookstate(gState.creditScoring.transactionRecords);

  // During Loan
  const duringRules = useHookstate(gState.creditScoring.duringRules);
  const d_agricultureActivity = useHookstate(gState.creditScoring.d_AgricultureActivity);
  const d_transactionRecords = useHookstate(gState.creditScoring.d_TransactionRecords);

  // After Loan
  const afterRules = useHookstate(gState.creditScoring.afterRules);
  const a_creditHistory = useHookstate(gState.creditScoring.a_creditHistory);
  const a_loanCycle = useHookstate(gState.creditScoring.a_loanCycle);
  const a_agricultureActivity = useHookstate(gState.creditScoring.a_agricultureActivity);

  const getLoanRules = async (creditScoreGroupId: number) => {
    try {
      const _data = await axios
        .get(`/admin/creditscore/configuration/view/${creditScoreGroupId}`)
        .then((res) => res.data);

      if (_data?.status === 1) {
        const _res = _data?.data;
        rules.set(_res);
        beforeRules.set(_res?.credit_scoring_config?.stages[0]);
        duringRules.set(_res?.credit_scoring_config?.stages[1]);
        afterRules.set(_res?.credit_scoring_config?.stages[2]);

        // Before Loan
        accountProfile.set(_res?.credit_scoring_config?.stages[0].categories[0]);
        agricultureActivity.set(_res?.credit_scoring_config?.stages[0].categories[1]);
        transactionRecords.set(_res?.credit_scoring_config?.stages[0].categories[2]);

        // During Loan
        d_agricultureActivity.set(_res?.credit_scoring_config?.stages[1].categories[0]);
        d_transactionRecords.set(_res?.credit_scoring_config?.stages[1].categories[1]);

        // After Loan
        a_creditHistory.set(_res?.credit_scoring_config?.stages[2].categories[0]);
        a_loanCycle.set(_res?.credit_scoring_config?.stages[2].categories[1]);
        a_agricultureActivity.set(_res?.credit_scoring_config?.stages[2].categories[2]);
      }
    } catch (e) {
      catchError(e, 'getRules');
      if (e?.response?.data?.message === 'Financing company not found') {
        router.push('/admin/credit-score-management');
      }
    }
  };

  const updateRules = async (creditScoreGroupId: number, data: any) => {
    try {
      const _res = await axios.post(`/admin/creditscore/configuration/update`, data).then((res) => res.data);
      console.log('updateRules: ', _res);
      await getLoanRules(creditScoreGroupId);

      toast.success('Success', {
        description: 'Rules updated successfully',
      });
    } catch (e) {
      catchError(e, 'updateRules');
    }
  };

  const getCreditScoreByFarmer = async (farmerId: number | string) => {
    try {
      const _data = await axios
        .get(
          `/${getUserType(gStateP['user']['user']['user_type'].value)}/creditscore/farmer/${farmerId}/view?creditScoreGroupId=${group.value}`,
        )
        .then((res) => res.data);

      console.log('getCreditScoreByFarmer: ', _data?.data);

      if (_data?.status === 1) {
        farmer.set(_data?.data);
        loanStage.set(_data?.data?.loan_stage);
        beforeDetails.set(_data?.data?.before_stage_credit_score_summary);
        duringDetails.set(_data?.data?.during_stage_credit_score_summary);
        afterDetails.set(_data?.data?.after_stage_credit_score_summary);
      }
    } catch (e) {
      catchError(e, 'getCreditScoreByFarmer');
    }
  };

  return {
    getLoanRules,
    updateRules,
    getCreditScoreByFarmer,
    farmer,
    beforeDetails,
    duringDetails,
    afterDetails,
  };
}
