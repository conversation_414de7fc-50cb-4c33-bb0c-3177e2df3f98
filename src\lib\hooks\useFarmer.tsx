'use client';

import { toast } from 'sonner';
import { z } from 'zod';

import { LandbankRequirementType } from '@/app/admin/(accounts)/account-info/_components/constants';
import axios from '@/lib/api';

import { getUserType } from '../constants';
import { useGlobalState } from '../store';
import { useGlobalStatePersist } from '../store/persist';
import { catchError } from '../utils';
import useCreditScoring from './admin/useCreditScoring';

export const FarmerSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  middleName: z.string(),
  birthDate: z.string(),
  address: z.string(),
  mobileNumber: z.string(),
  userImage: z.any().optional(),
  workCategory: z.string(),
});
export type FarmerType = z.infer<typeof FarmerSchema>;

export const VehicleSchema = z.object({
  vehicleOrCr: z.any().optional(),
  vehicleWeight: z.union([z.string(), z.number()]),
  vehicleModel: z.string(),
  vehiclePlateNumber: z.string(),
  vehicleOwnership: z.string(),
});
export type VehicleType = z.infer<typeof VehicleSchema>;

export const FarmSchema = z.object({
  shippingAddress: z.string(),
  farmArea: z.union([z.string(), z.number()]),
  farmOwnership: z.string(),
  agreeInputExpenses: z.string(),
});
export type FarmType = z.infer<typeof FarmSchema>;

export const CropFieldSchema = z.object({
  cropsPlanted: z.array(
    z.object({
      crop: z.union([z.string(), z.number()]),
    }),
  ),
  seeds: z.string(),
  cropsProduce: z.string(),
  herbicide: z.string(),
  fungicide: z.string(),
  insecticide: z.string(),
});
export type CropFieldType = z.infer<typeof CropFieldSchema>;

export const RequirementSchema = z.object({
  type: z.nativeEnum(LandbankRequirementType, {
    required_error: 'Please select a requirement type',
  }),
  attachment: z
    .custom<FileList>()
    .refine((files) => typeof window === 'undefined' || (files instanceof FileList && files.length > 0), {
      message: 'File is required',
    }),
});

export type RequirementFormType = z.infer<typeof RequirementSchema>;

export default function useFarmer() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { getCreditScoreByFarmer } = useCreditScoring();

  const viewAllFarmers = async () => {
    try {
      const _data = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/user/farmers/viewAll`, {
          params: {
            status: ['1'],
          },
        })
        .then((res) => res.data.data);
      gState.encoder.farmers.set(_data);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('viewAllFarmers: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const viewFarmerById = async (userId) => {
    try {
      const _data = await axios.get(`/admin/user/view/${userId}`).then((res) => res.data.data);

      gState.selected.accountInfo['info'].set(_data);
      gStateP.selected.account.info.set(_data);
    } catch (e) {
      catchError(e, 'viewFarmerById');
    }
  };

  const viewFarmPlanFarmerById = async (id) => {
    try {
      const _data = await axios.get(`/admin/farmplan/farmer/${id}`).then((res) => res.data.data);

      gState.selected['farmPlan'].set(_data);
      // gState.selected.accountInfo['info'].set(_data);
      // gStateP.selected.account.info.set(_data);
    } catch (e) {
      catchError(e, 'viewFarmPlanFarmerById');
    }
  };

  const updateFarmer = async (data) => {
    const farmerId = gState.selected.accountInfo['info'].farmer.id.value;
    try {
      const _res = await axios
        .post(
          `/${
            getUserType(gStateP['user']['user']['user_type'].value) === 'encoder' ? 'encoder' : 'admin'
          }/user/farmer/update`,
          data,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        )
        .then((res) => res.data);

      if (getUserType(gStateP['user']['user']['user_type'].value) === 'admin') {
        // get updated farmer
        const updatedFarmer = await axios.get(`/admin/user/view/${data.userId}`).then((res) => res.data.data);

        gState.selected.accountInfo['info'].set(updatedFarmer);
        gStateP.admin.members['details'].set(updatedFarmer);
        gStateP.selected.account.info.set(updatedFarmer);
      }

      toast.success('Success', {
        description: 'Farmer details updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateFarmer: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    } finally {
      getCreditScoreByFarmer(farmerId);
    }
  };

  const assignVehicle = async (data) => {
    try {
      const _res = await axios
        .post(
          `/${
            getUserType(gStateP['user']['user']['user_type'].value) === 'encoder' ? 'encoder' : 'admin'
          }/user/farmer/update`,
          data,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        )
        .then((res) => res.data);
      console.log('assignVehicle: ', _res);

      toast.success('Success', {
        description: 'Farmer vehicle details updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('assignVehicle: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const generateQR = async (farmerId, userId) => {
    try {
      const _data = await axios
        .post(`/admin/user/farmer/qr/generate`, {
          farmerId,
        })
        .then((res) => res.data);

      await viewFarmerById(userId);

      toast.success('Success', {
        description: _data.message,
      });
    } catch (e) {
      catchError(e, 'generateQR');
    }
  };

  const updateLandbankReqt = async (data) => {
    const farmerId = gState.selected.accountInfo['info'].farmer.id.value;
    const userId = gState.selected.accountInfo['info'].id.value;

    try {
      const _res = await axios
        .post(
          `/${
            getUserType(gStateP['user']['user']['user_type'].value) === 'encoder' ? 'encoder' : 'admin'
          }/user/farmers/landbank-requirement/update`,
          {
            ...data,
            farmerId: farmerId,
          },
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        )
        .then((res) => res.data);

      await viewFarmerById(userId);
      toast.success('Success', {
        description: 'Landbank requirements updated successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateLandbankReqt: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };
  const deleteLandbankReqt = async (type: string) => {
    const farmerId = gState.selected.accountInfo['info'].farmer.id.value;
    const userId = gState.selected.accountInfo['info'].id.value;

    try {
      const _res = await axios
        .post(
          `/${
            getUserType(gStateP['user']['user']['user_type'].value) === 'encoder' ? 'encoder' : 'admin'
          }/user/farmers/landbank-requirement/remove`,
          { farmerId, type },
        )
        .then((res) => res.data);

      await viewFarmerById(userId);

      toast.success('Success', {
        description: 'Requirement deleted successfully',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('deleteLandbankRequirement: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    updateFarmer,
    viewAllFarmers,
    assignVehicle,
    viewFarmerById,
    generateQR,
    updateLandbankReqt,
    deleteLandbankReqt,
    viewFarmPlanFarmerById,
  };
}
