'use client';

export enum FertilizerStatus {
  INACTIVE = 0,
  ACTIVE = 1,
}

export enum FertilizerType {
  SOLID = 0,
  LIQUID = 1,
  SOLID_LIQUID = 2,
}
export const FertilizerTypeLabel = ['Solid', 'Liquid', 'Solid & Liquid'];

export enum TopupDocumentType {
  APPLICATION_FORM = 0,
  AFFIDAVIT_FORM = 1,
  PROOF_OF_TRANSFER = 2,
  OTHERS = 3,
}

export enum LoanStage {
  BEFORE = '1',
  DURING = '2',
  AFTER = '3',
}

export enum LoanStageLabel {
  BEFORE = 'before',
  DURING = 'during',
  AFTER = 'after',
}

export enum TAB_MARKETPLACE {
  EWALLET = 'e-wallet',
  PAYMENTS = 'payments',
}

export const getLoanHolderStatus = (loanHolderUser: any) => {
  if (loanHolderUser.after_loan_end_at) {
    return {
      id: 1,
      name: 'Paid Off',
      value: 'PAID',
      color: 'bg-[#5080FF] hover:bg-[#5080FF]/90 text-white',
    };
  } else if (
    new Date(loanHolderUser.after_loan_start_at).getTime() <= Date.now() &&
    new Date(loanHolderUser.grace_period_end_at).getTime() >= Date.now()
  ) {
    return {
      id: 3,
      name: 'In Grace Period',
      value: 'GRACE',
      color: 'bg-[#FF8831] hover:bg-[#FF8831]/90 text-white',
    };
  } else if (new Date(loanHolderUser.after_loan_start_at).getTime() > Date.now()) {
    return {
      id: 0,
      name: 'Active',
      value: 'ACTIVE',
      color: 'bg-[#49C272] hover:bg-[#49C272]/90 text-white',
    };
  } else {
    return {
      id: 2,
      name: 'Overdue',
      value: 'OVERDUE',
      color: 'bg-[#DA5043] hover:bg-[#DA5043]/90 text-white',
    };
  }
};

export enum RatingType {
  POOR = 0,
  FAIR = 1,
  GOOD = 2,
  VERY_GOOD = 3,
  EXCELLENT = 4,
}

export enum TabLoanHolderEnum {
  LOAN_HOLDER = 'loan-holder',
  LOAN_APPLICANTS = 'loan-applicants',
}
export const TAB_LOAN_HOLDER = [TabLoanHolderEnum.LOAN_HOLDER, TabLoanHolderEnum.LOAN_APPLICANTS];

export enum TabEWalletEnum {
  TOP_UP = 'topup',
  TOP_DOWN = 'topdown',
}
export const TAB_EWALLET = [
  {
    name: 'Ewallet Top-up',
    value: TabEWalletEnum.TOP_UP,
  },
  {
    name: 'Ewallet Top-down',
    value: TabEWalletEnum.TOP_DOWN,
  },
];

export enum LoanApplicationStatus {
  PENDING = 0,
  APPROVE = 1,
  REJECTED = 2,
}
export const LoanApplicationLabels = {
  [LoanApplicationStatus.PENDING]: {
    label: 'Pending',
    color: 'bg-orange-500 hover:bg-orange-500/90',
  },
  [LoanApplicationStatus.APPROVE]: {
    label: 'Approved',
    color: 'bg-green-500 hover:bg-green-500/90',
  },
  [LoanApplicationStatus.REJECTED]: {
    label: 'Rejected',
    color: 'bg-red-500 hover:bg-red-500/90',
  },
};

export enum FormDimensions {
  LETTER = 'h-[11in] w-[8.5in]',
  LEGAL = 'h-[14in] w-[8.5in]',
}
