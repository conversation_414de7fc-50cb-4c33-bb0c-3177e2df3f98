'use client';

import { Control, Controller, FieldErrors, FieldValues, UseFormRegister, UseFormWatch } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { cn } from '@/lib/utils';

import { EDUCATIONAL_ATTAINMENT, IS_GRADUATE } from '../../Enums';

interface IEducationalBackgroundFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
}

export default function EducationalBackgroundForm({
  register,
  control,
  errors,
  watch,
}: IEducationalBackgroundFormProps) {
  const watchEduAttainment = watch('educationalAttainment');
  const watchEduIsGrad = watch('educationalIsGraduate');

  return (
    <div>
      <FormTitle title="Educational Background" />
      <div className="mt-6 grid gap-4 sm:grid-cols-2 xl:grid-cols-3">
        <FormField name="educationalAttainment" label="Educational Attainment" errors={errors}>
          <Controller
            control={control}
            name="educationalAttainment"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.educationalAttainment &&
                      'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Educational Attainment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {Object.values(EDUCATIONAL_ATTAINMENT).map((educ) => (
                      <SelectItem key={educ} value={educ}>
                        {educ}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {[
          EDUCATIONAL_ATTAINMENT.VOCATIONAL,
          EDUCATIONAL_ATTAINMENT.COLLEGE,
          EDUCATIONAL_ATTAINMENT.POST_GRADUATE,
        ].includes(watchEduAttainment) && (
          <>
            <FormField name="educationalIsGraduate" label="Graduate?" errors={errors}>
              <Controller
                control={control}
                name="educationalIsGraduate"
                render={({ field: { onChange, value } }) => (
                  <Select onValueChange={onChange} value={value}>
                    <SelectTrigger
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.educationalIsGraduate &&
                          'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                      )}
                    >
                      <SelectValue placeholder="Did you Graduate?" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectItem value={IS_GRADUATE.YES}>YES</SelectItem>
                        <SelectItem value={IS_GRADUATE.NO}>NO</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
              />
            </FormField>

            {watchEduIsGrad === IS_GRADUATE.YES && (
              <FormField name="educationalDegree" label="Educational Degree" errors={errors}>
                <Input
                  {...register('educationalDegree')}
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.educationalDegree && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="text"
                  placeholder="Enter Educational Degree"
                />
              </FormField>
            )}
          </>
        )}
      </div>
    </div>
  );
}
