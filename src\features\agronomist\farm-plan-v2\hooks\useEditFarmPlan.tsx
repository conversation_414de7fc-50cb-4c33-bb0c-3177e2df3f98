'use client';

import { useQuery } from '@tanstack/react-query';

import axios from '@/lib/api';

// Farm plan response interface (based on API response structure)
export interface IFarmPlanResponse {
  id: number;
  user_id: number;
  crop_id: number;
  reference_number: string;
  cropping_type: string;
  agronomist_user_id: number;
  total_amount: number;
  agronomist_name: string;
  agronomist_prc_number: string;
  agronomist_valid_until: string;
  head_agronomist_name: string;
  head_agronomist_prc_number: string;
  head_agronomist_valid_until: string;
  contingency_for_fluctuation: number;
  interest_rate: number;
  number_of_months_per_tenor: number;
  aor_per_month: number;
  farm_plan_template_id: number;
  created_at: string;
  updated_at: string;
  crop: {
    id: number;
    name: string;
    status: number;
    created_at: string;
    updated_at: string;
    harvest_days: number | null;
    is_sync: number;
    image: string | null;
    keywords: string | null;
    sap_item_code: string | null;
  };
  user: {
    id: number;
    email: string;
    username: string | null;
    status: number;
    user_type: number;
    user_img: string | null;
    created_at: string;
    updated_at: string;
    is_sync: number;
    rfid_number: string | null;
    farmer: {
      id: number;
      user_id: number;
      first_name: string;
      middle_name: string | null;
      last_name: string;
      birth_date: string | null;
      place_of_birth: string | null;
      religion: string | null;
      gender: string | null;
      civil_status: string | null;
      height: number | null;
      weight: number | null;
      mobile_number: string | null;
      address: string | null;
      address_house_number: string | null;
      address_street: string | null;
      address_province: string | null;
      address_city: string | null;
      address_barangay: string | null;
      address_zip_code: string | null;
      educational_attainment: string | null;
      educational_is_graduate: number | null;
      educational_degree: string | null;
      occupation: string | null;
      occupation_status: string | null;
      occupation_employer_name: string | null;
      occupation_employer_address: string | null;
      created_at: string;
      updated_at: string;
      farmerInfo?: {
        id: number;
        farmer_id: number;
        farm_address: string | null;
        farm_area: number | null;
        farm_ownership: number | null;
        nationality: string | null;
        other_mobile_number: string | null;
        year_residing: number | null;
        residence_ownership: string | null;
        created_at: string;
        updated_at: string;
      };
    };
  };
  farmPlanItems: Array<{
    id: number;
    farm_plan_id: number;
    name: string;
    slug: string;
    type: string;
    total_amount: number | null;
    created_at: string;
    updated_at: string;
    farmPlanSubItems: Array<{
      id: number;
      farm_plan_item_id: number;
      farm_plan_id: number;
      expected_date: string;
      item_name: string;
      unit: string;
      quantity: number;
      unit_cost: number;
      total_amount: number;
      notes: string;
      marketplace_product_id: number;
      created_at: string;
      updated_at: string;
    }>;
  }>;
}

// Fetch farm plan by ID
export const fetchFarmPlanById = async (id: number) => {
  const { data } = await axios.get(`/agronomist/farmplan/view/${id}`);
  return data.data as IFarmPlanResponse;
};

// Hook for fetching farm plan by ID
export const useEditFarmPlan = (id: number) => {
  const farmPlanByIdQuery = useQuery({
    queryKey: ['farmPlanById', id],
    queryFn: () => fetchFarmPlanById(id),
    enabled: id > 0,
  });

  return { farmPlanByIdQuery };
};
