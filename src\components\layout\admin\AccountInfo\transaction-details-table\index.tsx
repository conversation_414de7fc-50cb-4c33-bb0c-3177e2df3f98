'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function TransactionDetailsTable({ columns, data, metadata = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const rowDialog = useHookstate(false);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);

        gState.admin.transactions['details'].set(data);
        rowDialog.set(true);
      },
    },
  });

  return (
    <div className="space-y-4">
      {/* <DataTableToolbar id="transactions-details" table={table} meta={metadata} /> */}

      <div className="max-w-[calc(100vw-340px)] rounded-md border bg-white">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  className="hover:cursor-pointer"
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  onClick={(event) => {
                    if ((event.target as HTMLElement).tagName.toLowerCase() !== 'button')
                      table.options.meta?.getRowClicked?.(row);
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {metadata ? (
        <DataTablePaginationMeta
          table={table}
          meta={metadata}
          onChangePageSize={(pageSize) => {
            gState.selected.accountInfo.pagination.tradingPost.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            gState.selected.accountInfo.pagination.tradingPost.page.set(page);
          }}
        />
      ) : (
        <DataTablePagination table={table} />
      )}

      <Dialog open={rowDialog.value} onOpenChange={rowDialog.set}>
        {gState.admin.transactions['details'].value && (
          <DialogContent className="sm:max-w-[40rem]">
            <DialogHeader>
              <DialogTitle>Transaction Details</DialogTitle>
              <DialogDescription>{`Here are the details of your ${new Date(
                gState.admin.transactions['details']['created_at'].value,
              ).toLocaleString('en-US', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
              })} transaction.`}</DialogDescription>
            </DialogHeader>

            <div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="">Item Name</TableHead>
                    <TableHead>Item Weight</TableHead>
                    <TableHead>Price per kilo</TableHead>
                    <TableHead className="text-right">Gross Sales</TableHead>
                    <TableHead className="text-right">Gross Profit</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {gState.admin.transactions['details']['crops'].get({ noproxy: true }).map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell className="font-medium capitalize">{invoice.crop.name}</TableCell>
                      <TableCell>{invoice.weight}</TableCell>
                      <TableCell>
                        {Number(invoice.price ?? 0).toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                      </TableCell>
                      <TableCell className="text-right">
                        {Number(invoice.gross_sales ?? 0).toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                      </TableCell>
                      <TableCell className="text-right">
                        {Number(invoice.gross_profit ?? 0).toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
                <TableFooter>
                  <TableRow>
                    <TableCell colSpan={3}>Total</TableCell>
                    <TableCell className="text-right">
                      {gState.admin.transactions['details']['gross_sales']
                        .get({ noproxy: true })
                        .toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                    </TableCell>
                    <TableCell className="text-right">
                      {gState.admin.transactions['details']['gross_profit']
                        .get({ noproxy: true })
                        .toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                    </TableCell>
                  </TableRow>
                </TableFooter>
              </Table>
            </div>
          </DialogContent>
        )}
      </Dialog>
    </div>
  );
}
