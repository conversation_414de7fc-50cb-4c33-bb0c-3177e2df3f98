'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';

const initialState = {
  section1: {
    group: {
      inputSeedlingReq: 0,
      inputSoilFertTopDress: 0,
      inputSoilFertSideDress: 0,
      inputFoliarFert: 0,
      inputPestApp: 0,
      othersFarmMaterials: 0,
    },
    estimatedFarmInputsCosts: 0,
    contingencyForFluctuation: 0,
    amountForHolding: 0,
  },
  section2: {
    group: {
      laborReq: 0,
      otherProductionCost: 0,
    },
    totalCashRequirements: 0,
  },
  section3: {
    group: {
      nonCashCost: 0,
      kitaSubsidizedCost: 0,
      nonKitaSubsidizedCost: 0,
    },
    totalCashRequirements: 0,
  },
};
const headAgronomistState = hookstate(
  initialState,
  devtools({
    key: 'headAgronomistState',
  }),
);

export const useHeadAgronomistState = () => useHookstate(headAgronomistState);
export default headAgronomistState;
