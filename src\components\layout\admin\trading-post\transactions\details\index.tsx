'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';
import { CSVLink } from 'react-csv';

import { Button } from '@/components/ui/button';

import useTradingPost from '@/lib/hooks/admin/useTradingPost';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { TransactionDetailsTable } from './transaction-details-table';
import { columns } from './transaction-details-table/columns';

export default function TransactionDetailsPage() {
  const gStateP = useGlobalStatePersist();
  const gState = useGlobalState();
  const { fetchMemberTransaction } = useTradingPost();
  const data = useHookstate([]);
  const farmer = gStateP.admin.members['details'].get({ noproxy: true });

  useEffect(() => {
    fetchMemberTransaction(gStateP.admin.members['details']['farmer']['id'].value);
  }, []);

  useEffect(() => {
    const _data: any = gState.admin.members.transaction.data.get({ noproxy: true });
    if (_data.length > 0) {
      const toExport = [
        ['Date', 'Entry Time', 'Exit Time', 'No. of Crops', 'Total Crops Weight', 'Gross Sales', 'Gross Profit'],
      ];
      _data.map((item) => {
        const date = `${new Date(item.created_at).toLocaleDateString('en-US', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })}`;
        const entry_time = `${new Date(item.entry_time).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        })}`;
        const exit_time = `${new Date(item.exit_time).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        })}`;
        const crops_weight = `${(item.entry_weight - item.exit_weight).toLocaleString()} kg`;
        const gross_sales = `${item.gross_sales.toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        })}`;
        const gross_profit = `${item.gross_profit.toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        })}`;

        toExport.push([date, entry_time, exit_time, item.crops.length, crops_weight, gross_sales, gross_profit]);
      });
      data.set(toExport);
    }
  }, [gState.admin.members.transaction]);

  return (
    <div className="py-8">
      <div className="flex items-center justify-between pb-8">
        <button className="font-bold text-blue-800 underline underline-offset-8">Transactions</button>
        <div>
          <Button className="px-12" disabled={gState.admin.members.transaction.data.length === 0} asChild>
            <CSVLink
              data={data.value}
              filename={`${farmer.farmer.first_name}_${farmer.farmer.last_name}_Transactions.csv`}
            >
              Export
            </CSVLink>
          </Button>
        </div>
      </div>

      <TransactionDetailsTable
        columns={columns}
        data={gState.admin.members.transaction.data.get({ noproxy: true })}
        metadata={gState.admin.members.transaction['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
