'use client';

import { format } from 'date-fns';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { IFarmPlanResponse } from '../../hooks/useEditFarmPlan';

interface IViewFarmPlanHeaderProps {
  farmPlan: IFarmPlanResponse;
}

export default function ViewFarmPlanHeader({ farmPlan }: IViewFarmPlanHeaderProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center text-xl font-bold text-kitaph-primary">FARM PLAN & CALCULATOR</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-3">
          <div>
            <div className="text-sm font-medium text-gray-600">Reference Number</div>
            <div className="text-lg font-semibold">{farmPlan.reference_number}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Crop</div>
            <div className="text-lg font-semibold">{farmPlan.crop?.name || 'N/A'}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Cropping Type</div>
            <div className="text-lg font-semibold">{farmPlan.cropping_type}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Total Amount</div>
            <div className="text-lg font-semibold">₱{farmPlan.total_amount?.toLocaleString() || '0'}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Created Date</div>
            <div className="text-lg font-semibold">{format(new Date(farmPlan.created_at), 'MMM dd, yyyy')}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-600">Status</div>
            <div className="text-lg font-semibold text-green-600">Active</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
