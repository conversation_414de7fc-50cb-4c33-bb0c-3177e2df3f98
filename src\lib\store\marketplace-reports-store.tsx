'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useEffect } from 'react';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { IMarketplaceReports } from './marketplace-reports-store.types';

const initialState = {
  data: {
    data: [],
    meta: null,
  } as IMarketplaceReports,
  report: {
    data: [],
    meta: null,
  } as IMarketplaceReports,
  query: {
    page: 1,
    pageSize: 10,
    startDate: '',
    endDate: '',
    // status: ['0'],
    // search: '',
    // region: '03',
    regions: [] as string[],
  },
};

export const marketplaceReportsState = hookstate(
  initialState,
  devtools({
    key: 'marketplaceReportsState',
  }),
);

export const useMarketplaceReportsStore = () => {
  const state = useHookstate(marketplaceReportsState);

  useEffect(() => {
    state.query.page.set(1);
  }, [state.query.regions]);

  const getReports = async () => {
    try {
      const res: IMarketplaceReports = await axios
        .get(`/admin/marketplace/order/viewAll`, {
          params: {
            ...state.query.value,
          },
        })
        .then((res) => res.data.data);
      state.data.set(res);

      getReportForExport(res.meta.total);
    } catch (e) {
      // Handle any errors during the login process
      const error = e?.response?.data?.message || e.message;
      console.error('getReports:', error);
    }
  };

  const getReportForExport = async (pageSize: number) => {
    try {
      const res: IMarketplaceReports = await axios
        .get(`/admin/marketplace/order/viewAll`, {
          params: {
            ...state.query.value,
            pageSize,
          },
        })
        .then((res) => res.data.data);
      state.report.set(res);
    } catch (e) {
      console.error('getReportForExport', e);
    }
  };

  return { getReports, getReportForExport };
};
