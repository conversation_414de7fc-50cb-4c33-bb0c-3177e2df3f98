import { z } from 'zod';

export interface IBuyer {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: null | string;
  remember_me_token: null | string;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null | string;
  nonFarmer: INonFarmer;
  tradingAppUserRating: null | IRating;
}

export interface INonFarmer {
  id: number;
  user_id: number;
  first_name: string;
  middle_name: null | string;
  last_name: string;
  birth_date: null | string;
  mobile_number: null | string;
  address: null | string;
  address_house_number: null | string;
  address_province: null | string;
  address_city: null | string;
  address_barangay: null | string;
  address_zip_code: null | string;
  status: number;
  has_biometric: number;
  qr_code: string;
  created_at: string;
  updated_at: string;
}

export interface IRating {
  id: number;
  user_id: number;
  total_rating: number;
  total_rating_count: number;
  rating: number;
  created_at: string;
  updated_at: string;
}

// --------------------

export const EditBuyerSchema = z
  .object({
    password: z
      .string()
      .regex(
        /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).*$/,
        'Password must contain alphanumeric characters (A-Z, a-z, 0-9) and at least one special character (!@#$%^&*...)',
      )
      .optional(),
    password_confirmation: z.string().optional(),
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Passwords don't match",
    path: ['password_confirmation'], // This shows where the error will be attached to
  });
export type EditBuyerType = z.infer<typeof EditBuyerSchema>;
