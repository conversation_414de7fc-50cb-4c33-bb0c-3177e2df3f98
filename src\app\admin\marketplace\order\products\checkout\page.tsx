'use client';

import { useHookstate } from '@hookstate/core';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { FaMoneyBillWave, FaWallet } from 'react-icons/fa';
import { toast } from 'sonner';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import useMarketplace from '@/lib/hooks/useMarketplace';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import { PaymentMethodType } from '../../../_components/Enums';

export default function CheckoutPage() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();

  const orders = useHookstate(gStateP.admin.orders);
  const address = JSON.parse(orders['customer']['farmer']['address'].value);
  const wallet = useHookstate(orders['customer']['wallet']);
  const loading = useHookstate(false);
  const paymentMethod = useHookstate({
    cash: {
      checked: false,
      amount: 0,
    },
    ewallet: {
      checked: false,
      amount: 0,
    },
  });

  const { createOrder } = useMarketplace();

  const onSubmit = async () => {
    if (!orders.shippingDate.value) {
      toast.info('Pickup/shipping date is required', {
        description: 'Please select a pickup/shipping date',
      });
      return;
    }

    // check if total is equal to total payment amount
    const isEqual = orders.total.value === paymentMethod.cash.amount.value + paymentMethod.ewallet.amount.value;
    if (!isEqual) {
      toast.info('Payment Method', {
        description: 'Please check total payment amount',
      });
      return;
    }

    // check for payment method
    let _paymentMethod = PaymentMethodType.EWALLET;
    const isMultiple = paymentMethod.cash.checked.value && paymentMethod.ewallet.checked.value;
    if (isMultiple) {
      _paymentMethod = PaymentMethodType.MULTIPLE;
    } else if (paymentMethod.cash.checked.value) {
      _paymentMethod = PaymentMethodType.CASH;
    } else if (paymentMethod.ewallet.checked.value) {
      _paymentMethod = PaymentMethodType.EWALLET;
    }

    try {
      loading.set(true);
      const updatedData = {
        orders: orders.data.get({ noproxy: true }).map((order) => ({
          marketplaceProductId: order['marketplaceProductId'],
          quantity: order['quantity'],
        })),
        customerId: orders['customer']['id'].value,
        shippingDate: orders.shippingDate.value,
        paymentMethod: _paymentMethod,
        fulfillmentType: orders.fulfillmentType.value,
        walletAllocation: paymentMethod.ewallet.amount.value,
      };

      console.log('onSubmit: ', updatedData);
      await createOrder(updatedData);
    } catch (e) {
      console.error('onSubmit: ', e);
    } finally {
      loading.set(false);
    }
  };

  // Auto Calculate
  useEffect(() => {
    const total = orders.total.value;

    if (wallet.value) {
      const balance = wallet.balance.value;
      const isNotNegative = balance - total >= 0;

      if (isNotNegative) {
        paymentMethod.set({
          cash: {
            checked: false,
            amount: 0,
          },
          ewallet: {
            checked: true,
            amount: total,
          },
        });
      } else {
        paymentMethod.set({
          cash: {
            checked: true,
            amount: Math.abs(balance - total), // the answer here is always negative so we convert it to positive number
          },
          ewallet: {
            checked: balance === 0 ? false : true,
            amount: balance,
          },
        });
      }
    } else {
      paymentMethod.set({
        cash: {
          checked: true,
          amount: total,
        },
        ewallet: {
          checked: false,
          amount: 0,
        },
      });
    }
  }, [orders.total]);

  return (
    <div className="space-y-6 p-6 md:p-8">
      <div className="font-dmSans text-xl font-bold text-primary">Customer Details</div>
      <div className="grid grid-cols-1 items-start gap-6 border-b-2 border-dashed border-gray-300 pb-6 xl:grid-cols-2">
        {/* Left */}
        <div className="grid gap-3">
          <div className="grid grid-cols-2 gap-1 xl:grid-cols-3 xl:gap-6">
            <div className="text-primary/60">Account ID</div>
            <div className="col-span-2 text-primary">{orders['customer']['id'].value.toString().padStart(9, '0')}</div>
          </div>
          <div className="grid grid-cols-2 gap-1 xl:grid-cols-3 xl:gap-6">
            <div className="text-primary/60">Customer Name</div>
            <div className="col-span-2 text-primary">{`${orders['customer']['farmer']['first_name'].value} ${orders['customer']['farmer']['last_name'].value}`}</div>
          </div>
          <div className="grid grid-cols-2 gap-1 xl:grid-cols-3 xl:gap-6">
            <div className="text-primary/60">Address</div>
            {address && (
              <div className="col-span-2 text-primary">
                {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
              </div>
            )}
          </div>
        </div>

        {/* Right */}
        <div className="grid gap-3">
          <div className="grid grid-cols-2 gap-1 xl:grid-cols-3 xl:gap-6">
            <div className="text-primary/60">Contact No.</div>
            <div className="col-span-2 text-primary">{orders['customer']['farmer']['mobile_number'].value}</div>
          </div>
          <div className="grid grid-cols-2 gap-1 xl:grid-cols-3 xl:gap-6">
            <div className="text-primary/60">
              <span className="mr-1 font-bold text-red-500">*</span>
              <span>Pick up Date</span>
            </div>
            <div className="col-span-2 text-primary">
              <div>
                <Input
                  className={cn(
                    'focus-visible:ring-primary',
                    !orders.shippingDate.value && 'border-red-500 focus-visible:ring-red-500',
                  )}
                  type="date"
                  value={orders.shippingDate.value}
                  onChange={(e) => orders.shippingDate.set(e.target.value)}
                />
                {!orders.shippingDate.value && <p className="form-error">{`This field is required`}</p>}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="font-dmSans pt-6 text-xl font-bold text-primary">Order List</div>
      <div className="rounded-xl border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-100">
                <TableHead className="font-bold">Item Code</TableHead>
                <TableHead className="font-bold">Item Name</TableHead>
                <TableHead className="font-bold">Quantity</TableHead>
                <TableHead className="font-bold">Unit Price</TableHead>
                <TableHead className="font-bold">Total Amount</TableHead>
                <TableHead className="font-bold">UOM</TableHead>
                <TableHead className="font-bold">Vat Code</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.data.get({ noproxy: true }).map((invoice, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    <div className="min-w-max">{invoice.data.code}</div>
                  </TableCell>
                  <TableCell>
                    <div className="min-w-max">{invoice.name}</div>
                  </TableCell>
                  <TableCell>{invoice.quantity}</TableCell>
                  <TableCell>{invoice.price.toLocaleString('en-US', { style: 'currency', currency: 'PHP' })}</TableCell>
                  <TableCell>
                    {Number(invoice.price * invoice.quantity).toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'PHP',
                    })}
                  </TableCell>
                  <TableCell>{invoice.data.unit}</TableCell>
                  <TableCell>{invoice.data.vatable}</TableCell>
                </TableRow>
              ))}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell colSpan={2}>Total</TableCell>
                <TableCell>
                  {orders.data
                    .get({ noproxy: true })
                    .map((invoice) => invoice.quantity)
                    .reduce((a, b) => a + b, 0)}
                </TableCell>
                <TableCell></TableCell>
                <TableCell>
                  {orders.total.value.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'PHP',
                  })}
                </TableCell>
                <TableCell colSpan={2}></TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </HorizontalScrollBar>
      </div>

      <div className="grid grid-cols-1 gap-6 px-4 xl:grid-cols-2">
        {/* Left */}
        <div>
          <div className="font-dmSans pt-6 text-xl font-bold text-primary">Order Summary</div>

          <div className="max-w-sm space-y-2 border-b border-primary py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-primary/60">Subtotal</div>
              <div className="text-primary">
                {orders.total.value.toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-primary/60">Shipping Fee</div>
              <div className="text-primary">
                {Number(0).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
          </div>

          <div className="max-w-sm pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-primary/60">Total Amount (Vat Incl.)</div>
              <div className="font-bold text-primary">
                {orders.total.value.toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Right */}
        <div className="">
          <div className="font-dmSans pt-6 text-xl font-bold text-primary">Payment Method</div>
          <div className="pt-4">
            <div className="grid gap-3">
              <div className="flex flex-wrap items-start gap-3">
                <div
                  className={cn(
                    'w-[200px] flex items-center gap-3 rounded-md border border-primary px-4 py-3 text-primary',
                    paymentMethod.cash.checked.value ? '' : 'opacity-50',
                  )}
                >
                  <Checkbox
                    checked={paymentMethod.cash.checked.value}
                    onCheckedChange={(e: boolean) => {
                      if (!e) {
                        paymentMethod.cash.amount.set(0);
                      }
                      paymentMethod.cash.checked.set(e);
                    }}
                  />
                  <Label htmlFor="r1">Cash Payment</Label>
                  <FaMoneyBillWave className="size-4" />
                </div>

                {paymentMethod.cash.checked.value && (
                  <div className="flex-1">
                    <Input
                      className="w-[200px]"
                      type="number"
                      min={0}
                      value={paymentMethod.cash.amount.value}
                      onChange={(e) => {
                        let value = e.target.value;

                        // If the value is '0' and the user types another number, remove the leading zero
                        if (value.length > 1 && value.startsWith('0')) {
                          e.target.value = value.replace(/^0+/, '');
                        }

                        paymentMethod.cash.amount.set(Number(value));
                      }}
                    />
                  </div>
                )}
              </div>

              <div className="flex flex-wrap items-start gap-3">
                <div
                  className={cn(
                    'w-[200px] flex items-center gap-3 rounded-md border border-primary px-4 py-3 text-primary',
                    paymentMethod.ewallet.checked.value ? '' : 'opacity-50',
                  )}
                >
                  <Checkbox
                    checked={paymentMethod.ewallet.checked.value}
                    onCheckedChange={(e: boolean) => {
                      if (!e) {
                        paymentMethod.ewallet.amount.set(0);
                      }
                      paymentMethod.ewallet.checked.set(e);
                    }}
                  />
                  <div className="flex flex-col">
                    <Label htmlFor="r2">
                      {wallet.value
                        ? wallet['balance'].value.toLocaleString('en-US', { style: 'currency', currency: 'PHP' })
                        : Number(0).toLocaleString('en-US', { style: 'currency', currency: 'PHP' })}
                    </Label>
                    <p className="text-xs">e-Wallet Load</p>
                  </div>
                  <FaWallet className="size-4" />
                </div>

                {paymentMethod.ewallet.checked.value && (
                  <div className="flex-1">
                    <Input
                      className="w-[200px]"
                      type="number"
                      min={0}
                      max={wallet.value ? wallet['balance'].value : 0}
                      value={paymentMethod.ewallet.amount.value}
                      onChange={(e) => {
                        let value = e.target.value;

                        // If the value is '0' and the user types another number, remove the leading zero
                        if (value.length > 1 && value.startsWith('0')) {
                          e.target.value = value.replace(/^0+/, '');
                        }

                        if (Number(value) > wallet['balance'].value) return;
                        paymentMethod.ewallet.amount.set(Number(value));
                      }}
                    />
                  </div>
                )}
              </div>

              <div className="flex-row items-start gap-3 xl:flex">
                <div className="w-[200px] text-primary/60">Total Payment Amount</div>
                <div className="pl-3 font-bold text-primary">
                  {Number(paymentMethod.cash.amount.value + paymentMethod.ewallet.amount.value).toLocaleString(
                    'en-US',
                    {
                      style: 'currency',
                      currency: 'PHP',
                    },
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-row items-center justify-between md:flex">
        <div>
          <Button
            className="mb-4 w-full px-8 md:mb-4 md:w-auto"
            variant="outline"
            type="button"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
        </div>
        <div>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button className="w-full px-8 md:w-auto" type="button">
                Place Order
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will place the order.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={onSubmit} disabled={loading.value}>
                  {loading.value ? (
                    <>
                      <Loader2 className="mr-2 size-4" />
                      Placing Order
                    </>
                  ) : (
                    'Place Order'
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>
    </div>
  );
}
