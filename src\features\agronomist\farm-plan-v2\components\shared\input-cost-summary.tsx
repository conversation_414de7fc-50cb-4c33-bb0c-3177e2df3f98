'use client';

import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { InputSign } from '@/components/ui/input';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';
import { cn } from '@/lib/utils';

import { ICreateFarmPlan } from '../../types/create-farm-plan.types';

interface IInputCostSummaryProps {
  form: UseFormReturn<ICreateFarmPlan>;
}

export default function InputCostSummary({ form }: IInputCostSummaryProps) {
  const state = useHeadAgronomistState();

  const watchContingencyForFluctuation = form.watch('contingencyForFluctuation', 0);

  // Calculate Estimated Farm Inputs Costs
  useEffect(() => {
    const total =
      state.section1.group.inputSeedlingReq.value +
      state.section1.group.inputSoilFertTopDress.value +
      state.section1.group.inputSoilFertSideDress.value +
      state.section1.group.inputFoliarFert.value +
      state.section1.group.inputPestApp.value +
      state.section1.group.othersFarmMaterials.value;
    state.section1.estimatedFarmInputsCosts.set(total);
  }, [
    state.section1.group.inputSeedlingReq,
    state.section1.group.inputSoilFertTopDress,
    state.section1.group.inputSoilFertSideDress,
    state.section1.group.inputFoliarFert,
    state.section1.group.inputPestApp,
    state.section1.group.othersFarmMaterials,
  ]);

  useEffect(() => {
    const contingency = state.section1.estimatedFarmInputsCosts.value * (watchContingencyForFluctuation / 100);
    state.section1.contingencyForFluctuation.set(contingency);
    state.section1.amountForHolding.set(state.section1.estimatedFarmInputsCosts.value + contingency);
  }, [state.section1.estimatedFarmInputsCosts, watchContingencyForFluctuation]);

  return (
    <div className="mt-6 rounded-md bg-gray-100">
      <div className="divide-y">
        {/* Estimated Farm Inputs Costs */}
        <div className="flex items-center justify-end p-2">
          <span className="text-sm font-medium text-gray-700">Estimated Farm Inputs Costs</span>
          <span className="w-[200px] text-right text-sm font-semibold">
            {state.section1.estimatedFarmInputsCosts.value.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </span>
        </div>

        {/* Contingency For Fluctuation */}
        <div>
          <div
            className={cn('flex items-center justify-end p-2', {
              'pb-0': form.formState.errors.contingencyForFluctuation,
            })}
          >
            <span className="text-sm font-medium text-gray-700">Contingency For Fluctuation</span>
            <span className="w-[200px] text-right text-sm font-semibold">
              {watchContingencyForFluctuation.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </span>
          </div>
          {form.formState.errors.contingencyForFluctuation && (
            <p className="form-error pb-2 pr-2 text-right">{form.formState.errors.contingencyForFluctuation.message}</p>
          )}
        </div>

        {/* Amount For Holding */}
        <div className="flex items-center justify-end p-2">
          <span className="text-sm font-medium text-gray-700">Amount For Holding</span>
          <span className="w-[200px] text-right text-sm font-semibold">
            {state.section1.amountForHolding.value.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </span>
        </div>
      </div>
    </div>
  );
}
