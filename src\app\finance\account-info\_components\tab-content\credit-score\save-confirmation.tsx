'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import * as VisuallyHidden from '@radix-ui/react-visually-hidden';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogTitle } from '@/components/ui/dialog';

export function SaveConfirmation({ state, onSave = () => {}, loading = hookstate(false) }) {
  // use this if you want to control the alert dialog programatically
  const dialogState = useHookstate(state);

  return (
    <Dialog open={dialogState.value} onOpenChange={dialogState.set}>
      <DialogContent className="font-sans sm:max-w-lg">
        <VisuallyHidden.Root>
          <DialogTitle>Confirmation</DialogTitle>
        </VisuallyHidden.Root>

        <div className="text-center">
          <div>
            <img className="mx-auto" src="/assets/undraw/confirm.png" alt="" />
          </div>
          <div className="my-4 text-xl font-bold">Confirmation</div>
          <div>Are you confirming your intention to submit a loan application?</div>
          <div className="mt-4 font-bold">This action cannot be undone once submitted.</div>
        </div>

        <DialogFooter className="mt-4 sm:justify-between">
          <DialogClose asChild>
            <Button type="button" size="lg" className="px-12" variant="outline">
              Cancel
            </Button>
          </DialogClose>
          {loading.value ? (
            <ButtonLoading size="lg" className="px-12" />
          ) : (
            <Button type="button" size="lg" className="px-12" onClick={onSave}>
              Confirm
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
