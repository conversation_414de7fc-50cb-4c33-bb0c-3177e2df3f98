'use client';

import { FC } from 'react';

import { Skeleton } from '@/components/ui/skeleton';

import { cn, toDecimal } from '@/lib/utils';

import useDashboard from '../hooks/useDashboard';
import { getRemarks } from './select-date/util';

interface ITradingSalesSummaryProps {}

export const TradingSalesSummary: FC<ITradingSalesSummaryProps> = (props) => {
  const { tradingPostQuery, state } = useDashboard();
  const remarks = getRemarks(
    state.selectDate.value,
    tradingPostQuery.data?.current_sales || 0,
    tradingPostQuery.data?.previous_sales || 0,
  );

  return (
    <div className="flex flex-1 items-center gap-4 rounded-xl bg-adminDashboard-pink-bg p-4">
      <div className="shrink-0">
        <img className="h-12 2xl:h-10 3xl:h-12" src="/assets/dashboard/trading-post.png" alt="" />
      </div>

      <div>
        {tradingPostQuery.isLoading ? (
          <Skeleton className="mb-2 h-5 w-20" />
        ) : (
          <div className="text-lg font-bold text-adminDashboard-title 2xl:text-base 3xl:text-lg">{`₱ ${toDecimal(tradingPostQuery.data.current_sales)}`}</div>
        )}
        <div className="mt-1 text-sm text-[#425166] 2xl:text-xs 3xl:text-sm">Sales in Trading Post</div>
        <div
          className={cn(
            'mt-0.5 text-sm 2xl:text-[0.65rem] 3xl:text-sm',
            remarks.includes('-') ? 'text-[#F24822]' : 'text-[#4079ED]',
          )}
        >
          {tradingPostQuery.isLoading ? <Skeleton className="h-4 w-36" /> : remarks}
        </div>
      </div>
    </div>
  );
};
