'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';

import { IFertilizer } from '@/components/layout/admin/product-management/fertilizer/fertilizer-table/fertilizer.types';
import { Option } from '@/components/ui/multiple-selector';

import { IFarmerBase, IFROState } from '@/app/field-relation-officer/_components/types';

import { LoanStage } from '../constants/enums';
import { ICrop, ICropPriceHistory, ICropPriceUpload } from '../types/crop.types';
import { ICropWithRelations, IPaginationMeta } from '../types/paginated-crops.types';
import { IUserBulkUpload, IUserUpload } from '../types/user';
import { IMeta } from './marketplace-reports-store.types';

export const globalState = hookstate(
  {
    user: null as any,
    stepper: {
      activeStep: 0,
      isLastStep: false,
      isFirstStep: false,
    },
    loading: {
      submitLoanApplication: false,
    },
    admin: {
      users: {
        data: [],
        metadata: null as any,
        edit: null as any,
      },
      transactions: {
        data: [],
        meta: null as any,
        details: null as any,
      },
      members: {
        data: [],
        transaction: {
          data: [],
          meta: null as any,
        },
        nonLoanHolder: {
          data: [],
          meta: null as any,
        },
        loanHolder: {
          data: [],
          meta: null as any,
        },
        loanApplicants: {
          data: [],
          meta: null as any,
        },
        nonLoanHolderDashboard: {
          allNonLoan: 0,
          excellentNonLoan: 0,
          veryGoodNonLoan: 0,
          goodNonLoan: 0,
          fairNonLoan: 0,
          poorNonLoan: 0,
        },
        loanHolderDashboard: {
          allLoan: 0,
          paid: 0,
          active: 0,
          overdue: 0,
          grace: 0,
        },
        loanApplicantsDashboard: {
          all: 0,
          approved: 0,
          rejected: 0,
          pending: 0,
        },
      },
      crops: {
        data: [] as ICrop[],
        history: [],
        priceHistory: {
          data: [] as ICropPriceHistory[],
          meta: null as IPaginationMeta,
        },
        paginated: {
          data: [] as ICropWithRelations[],
          meta: null as IPaginationMeta,
        },
      },
      usersUpload: [] as Partial<IUserUpload>[],
      usersUploadFile: null as IUserBulkUpload['attachment'],
      usersBulk: {
        data: [] as IUserUpload[],
        metadata: null as IPaginationMeta,
      },
      otherProduct: {
        data: [],
        upload: [],
      },
      cropsUpload: [],
      cropsUploadPrice: [] as ICropPriceUpload[],
      fertilizer: {
        data: [] as IFertilizer[],
        upload: [],
      },
      seeds: {
        data: [],
        upload: [],
        category: [],
        subcategory: [],
        variety: [],
        breed: [],
        tab: 'all',
      },
      chemicals: {
        data: [],
        upload: [],
        subcategory: [],
        active: [],
        mode: [],
        activeOption: [] as Option[],
        tab: 'all',
      },
      marketplace: {
        products: {
          data: [],
          meta: null as any,
        },
        orders: {
          data: [],
          meta: null as any,
        },
        ordersExport: {
          data: [],
          meta: null as IMeta,
        },
        editProduct: null as any,
        dashboard: {
          totalOrders: 0,
          pendingOrder: 0,
          preparingOrder: 0,
          orderReadyOrder: 0,
          completedOrder: 0,
          cancelledOrder: 0,
        },
      },
      creditScoreMgt: {
        groups: {
          data: [],
          selected: 1,
        },
      },
      pagination: {
        crops: {
          page: 1,
          pageSize: 10,
          search: '',
          status: [] as string[],
        },
        cropPriceHistory: {
          page: 1,
          pageSize: 10,
        },
        nonLoanHolder: {
          page: 1,
          pageSize: 10,
          creditScoreGroupId: 1,
          ratings: [],
          lastTransactionDays: null as string,
          totalTradingpostTransactionStart: 0,
          totalTradingpostTransactionEnd: 9999999,
          totalMarketplaceTransactionStart: 0,
          totalMarketplaceTransactionEnd: 9999999,
          totalSalesTransactionStart: 0,
          totalSalesTransactionEnd: 9999999,
        },
        loanHolder: {
          page: 1,
          pageSize: 10,
          creditScoreGroupIds: [],
          loanTerm: 120,
          paymentStatuses: [],
          loanBalanceStart: 0,
          loanBalanceEnd: 9999999,
          loanAmountStart: 0,
          loanAmountEnd: 9999999,
          startDate: '',
          endDate: '',
        },
        loanApplicants: {
          page: 1,
          pageSize: 10,
          startDate: '',
          endDate: '',
        },
        transaction: {
          page: 1,
          pageSize: 10,
          search: '',
          calendar: new Date(),
        },
        transactionMember: {
          page: 1,
          pageSize: 10,
        },
        products: {
          page: 1,
          pageSize: 10,
          productType: [],
          search: '',
          productTypeCategory: [],
        },
        orders: {
          page: 1,
          pageSize: 10,
          status: [],
          startDate: '',
          endDate: '',
        },
        request: {
          status: ['0'],
        },
        usersBulk: {
          search: '',
          page: 1,
          pageSize: 10,
        },
      },
    },
    encoder: {
      tabs: 'scan',
      plateNum: '',
      plateImg: '',
      crops: [
        {
          cropId: '',
          percentage: 0,
          estimatedWeight: 0,
        },
      ],
      entryWeight: 0,
      entryTime: '',
      exitWeight: 0,
      exitTime: '',
      firstName: '',
      lastName: '',
      farmers: [],
    },
    finance1: {
      topupReqs: {
        data: [],
        meta: null as any,
      },
      pagination: {
        topupReqs: {
          page: 1,
          pageSize: 10,
          search: '',
          status: ['0'],
          startDate: '',
          endDate: '',
        },
      },
    },
    finance2: {
      forApproval: 0,
    },
    finance: {
      loanPayments: {
        dashboard: {
          allPayment: {
            count: 0,
            amount: 0,
          },
          forApproval: {
            count: 0,
            amount: 0,
          },
          partiallyPaid: {
            count: 0,
            amount: 0,
          },
          paid: {
            count: 0,
            amount: 0,
          },
          rejected: {
            count: 0,
            amount: 0,
          },
        },
        requests: {
          data: [],
          meta: null as any,
        },
        pagination: {
          dashboard: {
            startDate: '',
            endDate: '',
          },
          requests: {
            page: 1,
            pageSize: 10,
            startDate: '',
            endDate: '',
            status: ['0'],
          },
        },
      },
      accountInfo: {
        info: null as any,
        tradingPost: {
          data: [],
          meta: null as any,
        },
        marketplace: {
          data: [],
          meta: null as any,
        },
        finance: {
          data: [],
          meta: null as any,
        },
        pagination: {
          tradingPost: {
            page: 1,
            pageSize: 10,
          },
          marketplace: {
            page: 1,
            pageSize: 10,
          },
          finance: {
            page: 1,
            pageSize: 10,
          },
        },
      },
    },
    selected: {
      loanStage: LoanStage.BEFORE,
      accountInfo: {
        tabs: {
          left: 'account_profile',
          transaction: 'trading_post',
          profile: 'basic_info',
          activeStep: 0,
          isDirty: false,
          creditHistory: 'loan_availed',
        },
        info: null as any,
        loanApplication: {
          latest: null as unknown,
          data: [],
        },
        tradingPost: {
          data: [],
          meta: null as any,
        },
        marketplace: {
          data: [],
          meta: null as any,
        },
        finance: {
          data: [],
          meta: null as any,
        },
        loanPayments: {
          data: [],
          meta: null as any,
        },
        sales: {
          data: [],
          meta: null as any,
        },
        pagination: {
          tradingPost: {
            page: 1,
            pageSize: 10,
          },
          marketplace: {
            page: 1,
            pageSize: 10,
          },
          finance: {
            page: 1,
            pageSize: 10,
          },
          loanPayment: {
            page: 1,
            pageSize: 10,
          },
        },
      },
      farmPlan: null as any,
    },
    crops: [],
    cropTypes: [],
    seeds: [],
    fertilizers: [],
    chemicals: [],
    otherProduct: [],
    shuruCreditScoring: {
      rules: undefined as unknown,
      farmer: undefined as unknown,
      beforeRules: undefined as unknown,
      beforeDetails: {
        total: 0,
        accountProfile: {
          score: 0,
          maxScore: 0,
          basicInformation: {
            score: 0,
            maxScore: 0,
          },
          careerAcademics: {
            score: 0,
            maxScore: 0,
          },
          governmentIds: {
            score: 0,
            maxScore: 0,
          },
          familyProfile: {
            score: 0,
            maxScore: 0,
          },
          biometrics: {
            score: 0,
            maxScore: 0,
          },
          propertyOwnership: {
            score: 0,
            maxScore: 0,
          },
          farmDetails: {
            score: 0,
            maxScore: 0,
          },
          vouchByLeader: {
            score: 0,
            maxScore: 0,
          },
          vouchByMao: {
            score: 0,
            maxScore: 0,
          },
        },
        agricultureActivity: {
          score: 0,
          maxScore: 0,
          farmGeoTagging: {
            score: 0,
            maxScore: 0,
          },
          goodAgriculturalPractices: {
            score: 0,
            maxScore: 0,
          },
        },
        transactionRecords: {
          score: 0,
          maxScore: 0,
          marketplaceTransaction: {
            score: 0,
            maxScore: 0,
          },
          tradingPostTransaction: {
            score: 0,
            maxScore: 0,
          },
          saleOfHarvestToKita: {
            score: 0,
            maxScore: 0,
          },
        },
      },
      duringDetails: null as unknown,
    },
    creditScoring: {
      loanStage: undefined as unknown,
      rules: undefined as unknown,
      farmer: undefined as unknown,
      beforeRules: undefined as unknown,
      duringRules: undefined as unknown,
      afterRules: undefined as unknown,
      accountProfile: undefined as unknown,
      agricultureActivity: undefined as unknown,
      transactionRecords: undefined as unknown,
      d_AgricultureActivity: undefined as unknown,
      d_TransactionRecords: undefined as unknown,
      a_creditHistory: undefined as unknown,
      a_loanCycle: undefined as unknown,
      a_agricultureActivity: undefined as unknown,
      beforeDetails: undefined as unknown,
      duringDetails: undefined as unknown,
      afterDetails: undefined as unknown,
      creditScoreHistory: undefined as unknown,
      selectedLoanHistory: {
        creditScoreDetails: undefined as unknown,
        beforeRules: undefined as unknown,
        duringRules: undefined as unknown,
        afterRules: undefined as unknown,
        accountProfile: undefined as unknown,
        agricultureActivity: undefined as unknown,
        transactionRecords: undefined as unknown,
        d_AgricultureActivity: undefined as unknown,
        d_TransactionRecords: undefined as unknown,
        a_creditHistory: undefined as unknown,
        a_loanCycle: undefined as unknown,
        a_agricultureActivity: undefined as unknown,
        beforeDetails: undefined as unknown,
        duringDetails: undefined as unknown,
        afterDetails: undefined as unknown,
      },

      // beforeRules: undefined as unknown,
      // beforeDetails: {
      //   total: 0,
      //   accountProfile: {
      //     score: 0,
      //     maxScore: 0,
      //     basicInformation: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     careerAcademics: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     governmentIds: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     familyProfile: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     biometrics: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     propertyOwnership: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     farmDetails: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     vouchByLeader: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     vouchByMao: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //   },
      //   agricultureActivity: {
      //     score: 0,
      //     maxScore: 0,
      //     farmGeoTagging: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     goodAgriculturalPractices: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //   },
      //   transactionRecords: {
      //     score: 0,
      //     maxScore: 0,
      //     marketplaceTransaction: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     tradingPostTransaction: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //     saleOfHarvestToKita: {
      //       score: 0,
      //       maxScore: 0,
      //     },
      //   },
      // },
      // duringDetails: null as unknown,
    },
    creditTab: 'before_loan',
    landbankReqs: {
      selectedAttachments: [] as string[],
      selectAll: false,
    },
    fro: {
      farmers: {
        data: [],
        metadata: null as any,
        edit: null as IFarmerBase,
      },
      form: {
        activeStep: 1,
        step1: null,
        step2: null,
        step3: null,
        step4: null,
      },
    } as IFROState,
  },
  devtools({ key: 'globalState' }),
);

export const useGlobalState = () => useHookstate(globalState);
