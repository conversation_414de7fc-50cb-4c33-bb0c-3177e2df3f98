'use client';

import { FarmPlanTable } from './components/farm-plan-table';
import { columns } from './components/farm-plan-table/columns';
import useFarmPlan from './hooks/useFarmPlan';

export default function FarmPlan() {
  const { farmPlansQuery } = useFarmPlan();

  return (
    <div className="space-y-4">
      <div className="">
        <FarmPlanTable
          columns={columns}
          data={farmPlansQuery.isSuccess ? farmPlansQuery.data?.data : []}
          metadata={farmPlansQuery.data?.meta}
          isLoading={farmPlansQuery.isFetching}
        />
      </div>
    </div>
  );
}
