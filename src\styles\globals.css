@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Shadcn Custom Theme Generate:
 * https://ui-colorgen.vercel.app/custom-form
 */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --primary: 231 55.7% 25.7%;
    --primary-foreground: 210 20% 98%;

    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 231 55.7% 25.7%;

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;

    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;

    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;

    --primary: 210 20% 98%;
    --primary-foreground: 220.9 39.3% 11%;

    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;

    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 216 12.2% 83.9%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

@layer components {
  .profile-section {
    @apply px-4 py-2 rounded-md bg-white;
    box-shadow: 0px 18px 40px 0px rgba(112, 144, 176, 0.12);
  }
  .card {
    box-shadow: 0px 18px 40px 0px rgba(112, 144, 176, 0.12);
    @apply rounded-lg bg-white p-6;
  }
}

@layer utilities {
  .debug {
    @apply outline-dashed outline-red-400;
  }
  .debug > * {
    @apply outline-dashed outline-green-400;
  }
  .form-error {
    @apply text-sm italic mt-2 text-red-500 font-semibold;
  }
  .input-error {
    @apply outline outline-red-500/60 focus:outline-red-500 transition-colors duration-150 ease-in-out;
  }
  .animate {
    @apply transition-all duration-300 ease-in-out;
  }
  .shadow-queue {
    box-shadow: 0px 18px 40px 0px rgba(112, 144, 176, 0.12);
  }
  .no-spinner {
    appearance: textfield;
    -moz-appearance: textfield;
  }
  .no-spinner::-webkit-outer-spin-button,
  .no-spinner::-webkit-inner-spin-button {
    margin: 0;
    -webkit-appearance: none;
  }
}

/* CKEditor */
.ck-sticky-panel__content {
  @apply !rounded-lg;
}

.ck-content {
  @apply font-poppins prose dark:prose-invert max-w-none !px-4 focus:!border-gray-300 !shadow-none
  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#3b82f680] focus-visible:ring-offset-2;
}

.ck-sticky-panel__content {
  @apply !border;
}

.ck-rounded-corners {
  @apply !rounded-lg mt-4;
}

.ck-toolbar {
  @apply !border !rounded-lg !py-1 !px-2;
}

.ck-powered-by {
  @apply hidden;
}
/* CKEditor End */

/* Waiting Loading */
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.dot {
  animation: blink 1.5s infinite;
  animation-delay: calc(0.5s * var(--i));
}
.dot:nth-child(1) {
  --i: 0;
}
.dot:nth-child(2) {
  --i: 1;
}
.dot:nth-child(3) {
  --i: 2;
}
