'use client';

import { useHookstate } from '@hookstate/core';
import { CalendarIcon } from 'lucide-react';

import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { dashboardState } from '../../hooks/useDashboard';
import { TSelectDate } from './types';

export default function SelectDate() {
  const selectState = useHookstate(dashboardState.selectDate);

  return (
    <Select onValueChange={(v: TSelectDate) => selectState.set(v)} value={selectState.value}>
      <SelectTrigger className="flex w-[180px] items-center">
        <div className="flex items-center gap-3">
          <div className="grid place-items-center rounded-md bg-[#E0F0FA] p-1.5">
            <CalendarIcon className="size-4 text-[#2D9CDB]" />
          </div>
          <SelectValue placeholder="Select a date" />
        </div>
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem value="today">Today</SelectItem>
          <SelectItem value="week">This Week</SelectItem>
          <SelectItem value="month">This Month</SelectItem>
          <SelectItem value="year">This Year</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
