'use client';

import { none, useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { SlidersHorizontalIcon } from 'lucide-react';
import { CSVLink } from 'react-csv';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { DataTableFacetedFilterProps } from '@/components/layout/table/table-faceted-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Slider } from '@/components/ui/slider';

import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useMember from '@/lib/hooks/admin/useMember';
import { useGlobalState } from '@/lib/store';
import { cn, formatNumber } from '@/lib/utils';

/* 
  - Excellent (86-100%)
  - Very Good (71-85%)
  - Good (60-70%)
  - Fair (16-59%)
  - Poor (1-15%)
 */
const CreditScoreRating = (score: number) => {
  if (score <= 100 && score >= 86) {
    return {
      name: 'Exellent',
      from: 86,
      to: 100,
      color: 'bg-[#02964D] hover:bg-[#02964D]/90 text-white',
    };
  } else if (score <= 85 && score >= 71) {
    return {
      name: 'Very Good',
      from: 71,
      to: 85,
      color: 'bg-[#0048FF] hover:bg-[#0048FF]/90 text-white',
    };
  } else if (score <= 70 && score >= 60) {
    return {
      name: 'Good',
      from: 60,
      to: 70,
      color: 'bg-[#7A00FF] hover:bg-[#7A00FF]/90 text-white',
    };
  } else if (score <= 59 && score >= 16) {
    return {
      name: 'Fair',
      from: 16,
      to: 59,
      color: 'bg-[#ED6E11] hover:bg-[#ED6E11]/90 text-white',
    };
  } else {
    return {
      name: 'Poor',
      from: 1,
      to: 15,
      color: 'bg-[#E14023] hover:bg-[#E14023]/90 text-white',
    };
  }
};

export const NonLoanHolderColumns = [
  {
    id: 'rating',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Credit Score Rating" />,
    cell: ({ row }) => {
      const data = row.original;
      const score = CreditScoreRating(data.before_stage_credit_score);

      return (
        <div className="min-w-max">
          <Badge className={cn('min-w-[100px] justify-center', score.color)}>{score.name}</Badge>
        </div>
      );
    },
    accessorFn: (row) => {
      const score = CreditScoreRating(row.before_stage_credit_score);
      return `${score.name}`;
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.farmer.first_name} {data.farmer.last_name}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.farmer.first_name} ${row.farmer.last_name}`;
    },
  },
  {
    id: 'account_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
    accessorFn: (row) => {
      return `ID${row.farmer.user_id.toString().padStart(9, '0')}`;
    },
  },
  {
    id: 'tp_total',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Trading Post Totals" />,
    accessorFn: (row) => {
      return Number(row.farmer.farmerTransaction.trading_post_total).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      });
    },
  },
  {
    id: 'tp_ltd',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Trading Post LTD" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.farmer.farmerTransaction.trading_post_updated_at
            ? format(new Date(data.farmer.farmerTransaction.trading_post_updated_at), 'MMM dd, yyyy | hh:mm a')
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.farmer.farmerTransaction.trading_post_updated_at
        ? format(new Date(row.farmer.farmerTransaction.trading_post_updated_at), 'MMM dd, yyyy | hh:mm a')
        : '-';
    },
  },
  {
    id: 'marketplace_total',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Marketplace Totals" />,
    accessorFn: (row) => {
      return Number(row.farmer.farmerTransaction.marketplace_total).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      });
    },
  },
  {
    id: 'marketplace_ltd',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Marketplace LTD" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.farmer.farmerTransaction.marketplace_updated_at
            ? format(new Date(data.farmer.farmerTransaction.marketplace_updated_at), 'MMM dd, yyyy | hh:mm a')
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.farmer.farmerTransaction.marketplace_updated_at
        ? format(new Date(row.farmer.farmerTransaction.marketplace_updated_at), 'MMM dd, yyyy | hh:mm a')
        : '-';
    },
  },
  {
    id: 'sales_total',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Sales Totals" />,
    accessorFn: (row) => {
      return Number(row.farmer.farmerTransaction.marketplace_total).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      });
    },
  },
  {
    id: 'sales_ltd',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Sales Transaction LTD" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.farmer.farmerTransaction.sales_updated_at
            ? format(new Date(data.farmer.farmerTransaction.sales_updated_at), 'MMM dd, yyyy | hh:mm a')
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.farmer.farmerTransaction.sales_updated_at
        ? format(new Date(row.farmer.farmerTransaction.sales_updated_at), 'MMM dd, yyyy | hh:mm a')
        : '-';
    },
  },
  {
    id: 'loan_cycle',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Cycle" />,
    accessorFn: (row) => {
      return row.loan_cycle_number;
    },
  },
  {
    id: 'loan_stage',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Stage" />,
    accessorFn: (row) => {
      return row.loan_stage;
    },
  },
];

const exportHeaders = [
  { label: 'Credit Score', key: 'score' },
  { label: 'Credit Score Rating', key: 'rating' },
  { label: 'Name', key: 'name' },
  { label: 'Account ID', key: 'userId' },
  { label: 'Trading Post Totals', key: 'trpTotals' },
  { label: 'Trading Post LTD', key: 'trpLTD' },
  { label: 'Marketplace Totals', key: 'marketplaceTotals' },
  { label: 'Marketplace LTD', key: 'marketplaceLTD' },
  { label: 'Sales Totals', key: 'salesTotals' },
  { label: 'Sales Transaction LTD', key: 'salesLTD' },
  { label: 'Loan Cycle', key: 'loanCycle' },
  { label: 'Loan Stage', key: 'loanStage' },
];

export function NonLoanHolderActionHeader<TData, TValue>({
  column,
  title,
  options,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const gState = useGlobalState();
  const { nonLoanHolders, pagination } = useMember();
  const facets = column?.getFacetedUniqueValues();
  const selectedValues = new Set(column?.getFilterValue() as string[]);

  const trpMinMax = useHookstate([0, 9999999]);
  const marketplaceMinMax = useHookstate([0, 9999999]);
  const salesMinMax = useHookstate([0, 9999999]);

  const { groups } = useCreditScoreMgt();

  const getData = () => {
    const data = nonLoanHolders.data.get({ noproxy: true });
    return (data as any[]).map((row) => {
      const score = CreditScoreRating(row.before_stage_credit_score);

      return {
        score: row.before_stage_credit_score,
        rating: score.name,
        name: `${row.farmer.first_name} ${row.farmer.last_name}`,
        userId: row.farmer.user_id,
        trpTotals: Number(row.farmer.farmerTransaction.trading_post_total).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        }),
        trpLTD: row.farmer.farmerTransaction.trading_post_updated_at
          ? format(new Date(row.farmer.farmerTransaction.trading_post_updated_at), 'MMM dd, yyyy | hh:mm a')
          : '-',
        marketplaceTotals: Number(row.farmer.farmerTransaction.marketplace_total).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        }),
        marketplaceLTD: row.farmer.farmerTransaction.marketplace_updated_at
          ? format(new Date(row.farmer.farmerTransaction.marketplace_updated_at), 'MMM dd, yyyy | hh:mm a')
          : '-',
        salesTotals: Number(row.farmer.farmerTransaction.sales_total).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        }),
        salesLTD: row.farmer.farmerTransaction.sales_updated_at
          ? format(new Date(row.farmer.farmerTransaction.sales_updated_at), 'MMM dd, yyyy | hh:mm a')
          : '-',
        loanCycle: row.loan_cycle_number,
        loanStage: row.loan_stage,
      };
    });
  };

  const onReset = () => {
    column?.setFilterValue(undefined);

    trpMinMax.set([0, 9999999]);
    marketplaceMinMax.set([0, 9999999]);
    salesMinMax.set([0, 9999999]);

    pagination.lastTransactionDays.set(null);
    pagination.totalTradingpostTransactionStart.set(0);
    pagination.totalTradingpostTransactionEnd.set(9999999);
    pagination.totalMarketplaceTransactionStart.set(0);
    pagination.totalMarketplaceTransactionEnd.set(9999999);
    pagination.totalSalesTransactionStart.set(0);
    pagination.totalSalesTransactionEnd.set(9999999);
    pagination.ratings.set([]);
  };

  return (
    <div className="flex items-center gap-2">
      <Sheet>
        <SheetTrigger asChild>
          <Button className="h-8" variant="outline" size="sm">
            <SlidersHorizontalIcon className="mr-2 size-4" />
            {title}
          </Button>
        </SheetTrigger>
        <SheetContent className="flex flex-col p-0">
          <ScrollArea className="h-1 flex-1 px-6">
            <SheetHeader className="pt-6">
              <div className="flex items-center justify-between pt-4">
                <SheetTitle>{title}</SheetTitle>
                <SheetTitle>
                  <Button onClick={onReset} className="text-base text-kitaph-primary" variant="ghost">
                    Reset All
                  </Button>
                </SheetTitle>
              </div>
            </SheetHeader>

            <div className="pb-6">
              <div className="mb-2 mt-4 text-sm text-gray-500">Credit Rating</div>

              {/* {options.map((option) => {
                return (
                  <div key={option.value} className="flex items-center gap-2 py-2">
                    <Checkbox
                      id={`rating-${option.value}`}
                      checked={selectedValues.has(option.value)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          selectedValues.add(option.value);
                        } else {
                          selectedValues.delete(option.value);
                        }
                        const filterValues = Array.from(selectedValues);
                        column?.setFilterValue(filterValues.length ? filterValues : undefined);
                      }}
                    />
                    <label
                      htmlFor={`rating-${option.value}`}
                      className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {option.label}
                      {facets?.get(option.value) && (
                        <Badge variant="info">
                          <span className="ml-auto flex size-4 items-center justify-center font-mono text-xs">
                            {facets.get(option.value)}
                          </span>
                        </Badge>
                      )}
                    </label>
                  </div>
                );
              })} */}
              {options.map((option) => {
                return (
                  <div key={option.value} className="flex items-center gap-2 py-2">
                    <Checkbox
                      id={`rating-${option.value}`}
                      checked={pagination.ratings.value.includes(Number(option.value))}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          pagination.ratings.merge([Number(option.value)]);
                        } else {
                          const _findIndex = pagination.ratings.value.findIndex(
                            (item) => item === Number(option.value),
                          );
                          if (_findIndex !== -1) {
                            pagination.ratings[_findIndex].set(none);
                          }
                        }
                      }}
                    />
                    <label
                      htmlFor={`rating-${option.value}`}
                      className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {option.label}
                    </label>
                  </div>
                );
              })}

              <div className="mb-2 mt-4 text-sm text-gray-500">Last Transaction Days</div>

              <div className="flex flex-wrap gap-4 py-2">
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    pagination.lastTransactionDays.value === '30'
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => pagination.lastTransactionDays.set('30')}
                >{`< 1 Month`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    pagination.lastTransactionDays.value === '90'
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => pagination.lastTransactionDays.set('90')}
                >{`< 3 Months`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    pagination.lastTransactionDays.value === '180'
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => pagination.lastTransactionDays.set('180')}
                >{`< 6 Months`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    pagination.lastTransactionDays.value === '270'
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => pagination.lastTransactionDays.set('270')}
                >{`< 9 Months`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    pagination.lastTransactionDays.value === '365'
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => pagination.lastTransactionDays.set('365')}
                >{`< 1 Year`}</Button>
              </div>

              <div className="mb-2 mt-4 text-sm text-gray-500">Total Trading Post Sales</div>

              <div className="grid gap-4 py-2">
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    type="number"
                    placeholder="Min"
                    min={0}
                    max={trpMinMax.value[1] - 1}
                    value={trpMinMax.value[0]}
                    onChange={(e) => {
                      trpMinMax[0].set(Number(e.target.value));
                      pagination.totalTradingpostTransactionStart.set(Number(e.target.value));
                    }}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    min={trpMinMax.value[0] + 1}
                    value={trpMinMax.value[1]}
                    onChange={(e) => {
                      trpMinMax[1].set(Number(e.target.value));
                      pagination.totalTradingpostTransactionEnd.set(Number(e.target.value));
                    }}
                  />
                </div>

                <div className="flex items-center justify-between text-sm font-bold text-blue-500">
                  <div>PHP {formatNumber(pagination.totalTradingpostTransactionStart.value)}</div>
                  <div>PHP {formatNumber(pagination.totalTradingpostTransactionEnd.value)}</div>
                </div>

                <Slider
                  min={trpMinMax.value[0]}
                  max={trpMinMax.value[1]}
                  value={[
                    pagination.totalTradingpostTransactionStart.value,
                    pagination.totalTradingpostTransactionEnd.value,
                  ]}
                  onValueChange={(v) => {
                    pagination.totalTradingpostTransactionStart.set(v[0]);
                    pagination.totalTradingpostTransactionEnd.set(v[1]);
                  }}
                />
              </div>

              <div className="mb-2 mt-4 text-sm text-gray-500">Total Marketplace Transaction</div>

              <div className="grid gap-4 py-2">
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    type="number"
                    placeholder="Min"
                    min={0}
                    max={marketplaceMinMax.value[1] - 1}
                    value={marketplaceMinMax.value[0]}
                    onChange={(e) => {
                      marketplaceMinMax[0].set(Number(e.target.value));
                      pagination.totalMarketplaceTransactionStart.set(Number(e.target.value));
                    }}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    min={marketplaceMinMax.value[0] + 1}
                    value={marketplaceMinMax.value[1]}
                    onChange={(e) => {
                      marketplaceMinMax[1].set(Number(e.target.value));
                      pagination.totalMarketplaceTransactionEnd.set(Number(e.target.value));
                    }}
                  />
                </div>

                <div className="flex items-center justify-between text-sm font-bold text-blue-500">
                  <div>PHP {formatNumber(pagination.totalMarketplaceTransactionStart.value)}</div>
                  <div>PHP {formatNumber(pagination.totalMarketplaceTransactionEnd.value)}</div>
                </div>

                <Slider
                  min={marketplaceMinMax.value[0]}
                  max={marketplaceMinMax.value[1]}
                  value={[
                    pagination.totalMarketplaceTransactionStart.value,
                    pagination.totalMarketplaceTransactionEnd.value,
                  ]}
                  onValueChange={(v) => {
                    pagination.totalMarketplaceTransactionStart.set(v[0]);
                    pagination.totalMarketplaceTransactionEnd.set(v[1]);
                  }}
                />
              </div>

              <div className="mb-2 mt-4 text-sm text-gray-500">Total Sales Transaction</div>

              <div className="grid gap-4 py-2">
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    type="number"
                    placeholder="Min"
                    min={0}
                    max={salesMinMax.value[1] - 1}
                    value={salesMinMax.value[0]}
                    onChange={(e) => {
                      salesMinMax[0].set(Number(e.target.value));
                      pagination.totalSalesTransactionStart.set(Number(e.target.value));
                    }}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    min={salesMinMax.value[0] + 1}
                    value={salesMinMax.value[1]}
                    onChange={(e) => {
                      salesMinMax[1].set(Number(e.target.value));
                      pagination.totalSalesTransactionEnd.set(Number(e.target.value));
                    }}
                  />
                </div>

                <div className="flex items-center justify-between text-sm font-bold text-blue-500">
                  <div>PHP {formatNumber(pagination.totalSalesTransactionStart.value)}</div>
                  <div>PHP {formatNumber(pagination.totalSalesTransactionEnd.value)}</div>
                </div>

                <Slider
                  min={salesMinMax.value[0]}
                  max={salesMinMax.value[1]}
                  value={[pagination.totalSalesTransactionStart.value, pagination.totalSalesTransactionEnd.value]}
                  onValueChange={(v) => {
                    pagination.totalSalesTransactionStart.set(v[0]);
                    pagination.totalSalesTransactionEnd.set(v[1]);
                  }}
                />
              </div>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      <Button size="sm" className="h-8" asChild>
        <CSVLink
          data={getData()}
          headers={exportHeaders}
          filename={`${groups.length > 0 ? groups.value.find((g) => g.id === gState.admin.pagination.nonLoanHolder.creditScoreGroupId.value).name + '-' : ''}non-loan-holders-${format(new Date(), 'yyyy-MM-dd')}.csv`}
          target="_blank"
        >
          Export
        </CSVLink>
      </Button>
    </div>
  );
}
