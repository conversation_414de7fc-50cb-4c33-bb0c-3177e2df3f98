'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import useCrops from '@/lib/hooks/useCrops';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';
import { useGlobalState } from '@/lib/store';

import { CropsTable } from './crops-table';
import { columns } from './crops-table/columns';

export default function CropsPage() {
  const gState = useGlobalState();
  const { getCropsPaginated, getCrops } = useCrops();
  const cropsPagination = useHookstate(gState.admin.pagination.crops);

  const debouncedSearch = useHookstateDebounce(cropsPagination.search, 500);
  const debouncedStatus = useHookstateDebounce(cropsPagination.status, 500);
  const debouncedPage = useHookstateDebounce(cropsPagination.page, 500);
  const debouncedPageSize = useHookstateDebounce(cropsPagination.pageSize, 500);

  useEffect(() => {
    getCropsPaginated();
  }, [debouncedSearch, debouncedStatus, debouncedPage, debouncedPageSize]);

  useEffect(() => {
    getCrops();
  }, []);

  return (
    <div className="px-6 py-8">
      <CropsTable
        columns={columns}
        data={gState.admin.crops.paginated.data.get({ noproxy: true })}
        metadata={gState.admin.crops.paginated.meta.get({ noproxy: true })}
      />
    </div>
  );
}
