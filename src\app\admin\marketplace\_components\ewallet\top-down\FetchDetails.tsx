'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import { useTopDownStore } from '@/lib/store/top-down-store';

export default function FetchDetails() {
  const detailsId = useSearchParams().get('id');
  const { getRequestById } = useTopDownStore('admin');
  const router = useRouter();

  useEffect(() => {
    if (detailsId) {
      getRequestById(detailsId);
    } else {
      toast.error('Oops! Something went wrong', {
        description: 'Invalid ID, Please try again',
      });
      router.push('/admin/marketplace');
    }
  }, [detailsId]);

  return null;
}
