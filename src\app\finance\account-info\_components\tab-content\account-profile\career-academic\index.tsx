'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';

import { PROFILE_TAB } from '../../../constants';
import { EDUCATIONAL_ATTAINMENT, IS_GRADUATE, OCCUPATION } from '../Enums';
import EducationalBackgroundForm from './components/educational-background-form';
import OccupationalBackgroundForm from './components/occupational-background-form';
import SourceOfFundsForm from './components/source-of-funds-form';
import SpecialSkillsForm from './components/special-skills-form';

type OccupationFieldNames =
  | 'educationalIsGraduate'
  | 'educationalDegree'
  | 'occupationStatus'
  | 'occupationEmployerName'
  | 'occupationEmployerAddress'
  | 'occupationAnnualIncome'
  | 'occupationBusinessName'
  | 'occupationBusinessAddress'
  | 'occupationBusinessContact';

export default function CareerAcademic() {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
    resetField,
    watch,
  } = useForm({
    defaultValues: {
      // Educational Background
      educationalAttainment: data.farmer.educational_attainment.value || '',
      educationalIsGraduate:
        `${data.farmer.educational_is_graduate.value === null ? IS_GRADUATE.NO : data.farmer.educational_is_graduate.value}` ||
        IS_GRADUATE.NO,
      educationalDegree: data.farmer.educational_degree.value || '',

      // Occupation Details
      occupationTitle: data.farmer.occupation_title.value || '',
      occupation: data.farmer.occupation.value || '',
      occupationStatus: data.farmer.occupation_status.value || '',
      occupationEmployerName: data.farmer.occupation_employer_name.value || '',
      occupationEmployerAddress: data.farmer.occupation_employer_address.value || '',
      occupationAnnualIncome: data.farmer.occupation_annual_income.value || '',
      occupationBusinessName: data.farmer.occupation_business_name.value || '',
      occupationBusinessAddress: data.farmer.occupation_business_address.value || '',
      occupationBusinessContact: data.farmer.occupation_business_contact.value || '',

      // Source of Funds
      sourceOfFunds: data?.farmer?.source_of_funds?.value
        ? data?.farmer?.source_of_funds?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],

      // Skills
      skillsFarming: data?.farmer?.skills_farming?.value
        ? data?.farmer?.skills_farming?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      skillsFishing: data?.farmer?.skills_fishing.value == '1' ? ['FISHING'].map((s) => ({ label: s, value: s })) : [],
      skillsLivestock: data?.farmer?.skills_livestock?.value
        ? data?.farmer?.skills_livestock?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      skillsConstruction: data?.farmer?.skills_construction?.value
        ? data?.farmer?.skills_construction?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      skillsProcessing: data?.farmer?.skills_processing?.value
        ? data?.farmer?.skills_processing?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      skillsServicing: data?.farmer?.skills_servicing?.value
        ? data?.farmer?.skills_servicing?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      skillsCraft: data?.farmer?.skills_craft?.value
        ? data?.farmer?.skills_craft?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      skillsOthers: data?.farmer?.skills_others?.value
        ? data?.farmer?.skills_others?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
    },
  } as any);
  const watchEduAttainment = watch('educationalAttainment');
  const watchOccupation = watch('occupation');

  const onSubmit = (_data: any) => {
    const skills = ['Farming', 'Livestock', 'Construction', 'Processing', 'Servicing', 'Craft', 'Others'];
    const updatedData = { ..._data, userId: data.farmer.user_id.value };
    updatedData['sourceOfFunds'] = _data['sourceOfFunds'].map((s) => s.value).join(',');

    const fishing = _data['skillsFishing'].find((s) => s.value === 'FISHING');
    updatedData['skillsFishing'] = fishing ? '1' : '0';

    skills.forEach((skill) => {
      updatedData[`skills${skill}`] = _data[`skills${skill}`].map((s) => s.value).join(',');
    });

    console.log('Career Details: ', updatedData);
    updateFarmer(updatedData);
    dirty.set(false);
  };

  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  useEffect(() => {
    // If educationalAttainment is not VOCATIONAL, COLLEGE, or POST_GRADUATE, reset the other fields
    if (
      ![
        EDUCATIONAL_ATTAINMENT.VOCATIONAL,
        EDUCATIONAL_ATTAINMENT.COLLEGE,
        EDUCATIONAL_ATTAINMENT.POST_GRADUATE,
      ].includes(watchEduAttainment)
    ) {
      resetField('educationalIsGraduate');
      resetField('educationalDegree');
    }
  }, [watchEduAttainment, resetField]);

  useEffect(() => {
    const resetEmploymentFields: OccupationFieldNames[] = [
      'occupationStatus',
      'occupationEmployerName',
      'occupationEmployerAddress',
      'occupationAnnualIncome',
    ];
    const resetBusinessFields: OccupationFieldNames[] = [
      'occupationBusinessName',
      'occupationBusinessAddress',
      'occupationBusinessContact',
      'occupationAnnualIncome',
    ];

    if (
      [
        OCCUPATION.GOVERNMENT_EMPLOYEE,
        OCCUPATION.PRIVATE_EMPLOYEE,
        OCCUPATION.CHURCH_SERVANTS,
        OCCUPATION.OFW,
        OCCUPATION.FARMER,
        OCCUPATION.LABORER,
      ].includes(watchOccupation)
    ) {
      resetBusinessFields.forEach((val) => resetField(val));
    } else if (
      [
        OCCUPATION.SELF_EMPLOYED_PROFESSIONAL,
        OCCUPATION.SELF_EMPLOYED_NONPROFESSIONAL,
        OCCUPATION.BUSINESS_PERSON,
      ].includes(watchOccupation)
    ) {
      resetEmploymentFields.forEach((val) => resetField(val));
    } else {
      resetBusinessFields.forEach((val) => resetField(val));
      resetEmploymentFields.forEach((val) => resetField(val));
    }
  }, [watchOccupation, resetField]);

  return (
    <form id={PROFILE_TAB[1].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-6">
        <EducationalBackgroundForm register={register} control={control} errors={errors} watch={watch} />

        <OccupationalBackgroundForm register={register} control={control} errors={errors} watch={watch} />

        <SourceOfFundsForm control={control} errors={errors} />

        <SpecialSkillsForm control={control} errors={errors} />
      </div>
    </form>
  );
}
