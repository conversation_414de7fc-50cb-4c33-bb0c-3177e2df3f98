'use client';

import { useHookstate } from '@hookstate/core';
import { Check } from 'lucide-react';
import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

import { useGlobalState } from '@/lib/store';
import { urlify } from '@/lib/utils';

export default function LandbankForm({
  isDialogOpen,
  setIsDialogOpen,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
}) {
  const gState = useGlobalState();
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: 'Landbank Customer Information Sheet',
  });

  const farmerData = useHookstate(gState.selected.accountInfo['info']);
  const address =
    farmerData.value && farmerData['farmer']['address'].value ? JSON.parse(farmerData['farmer']['address'].value) : {};
  console.log('farmerData: ', farmerData.get({ noproxy: true }));

  const addressPermanent =
    farmerData.value && farmerData['farmer']['permanent_address'].value
      ? JSON.parse(farmerData['farmer']['permanent_address'].value)
      : {};

  console.log(
    'Bank: ',
    farmerData?.farmer?.landbank_accounts?.value
      ? JSON.parse(JSON.stringify(farmerData?.farmer?.landbank_accounts?.value.split(',')))
      : [],
  );

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Landbank Customer Information Sheet</DialogTitle>
          </DialogHeader>
          <div className="mx-auto flex max-h-[50vh] max-w-4xl flex-1 justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div
              ref={contentRef}
              className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/landbankForm.jpg)] bg-contain bg-top bg-no-repeat capitalize"
            >
              {/* Profile Image */}
              <img
                className="absolute right-[17px] top-8 aspect-square h-[6.7rem] object-cover"
                src={
                  farmerData?.user_img?.value
                    ? urlify(farmerData?.user_img?.value, 'users/profile')
                    : '/assets/user-default.jpg'
                }
                alt=""
              />

              {/* name */}
              <div className="absolute left-2 right-4 top-[180px] flex justify-between text-sm">
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.first_name?.value || ' '}</div>
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.middle_name?.value || ' '}</div>
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.last_name?.value || ' '}</div>
              </div>

              {/* Address */}
              <div className="absolute left-2 right-4 top-[235px] flex justify-between text-sm">
                <div className="flex flex-1">
                  <div className="ml-2 flex flex-1">{address?.addressHouseNumber || ' '}</div>
                  <div className="ml-2 flex flex-1">
                    {address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}
                  </div>
                  <div className="ml-2 flex flex-1">
                    {address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ' '}
                  </div>
                  <div className="ml-2 flex flex-1">
                    {address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ' '}
                  </div>
                </div>
                <div className="flex w-[36%]">
                  <div className="ml-6 flex flex-1 justify-center">{address?.addressZipCode || ' '}</div>
                  <div className="ml-6 flex flex-1 justify-center">Philippines</div>
                  <div className="ml-6 flex flex-1 justify-center">
                    {farmerData?.farmer?.address_length_of_stay?.value || ' '}
                  </div>
                </div>
              </div>

              {/* Permanent Address */}
              <div className="absolute left-2 right-4 top-[290px] flex justify-between text-sm">
                <div className="flex flex-1">
                  <div className="ml-2 flex flex-1">{addressPermanent?.permanentAddressHouseNumber || ' '}</div>
                  <div className="ml-2 flex flex-1">
                    {addressPermanent?.permanentAddressBarangay
                      ? JSON.parse(addressPermanent?.permanentAddressBarangay)?.brgy_name
                      : ''}
                  </div>
                  <div className="ml-2 flex flex-1">
                    {addressPermanent?.permanentAddressCity
                      ? JSON.parse(addressPermanent?.permanentAddressCity)?.city_name
                      : ' '}
                  </div>
                  <div className="ml-2 flex flex-1">
                    {addressPermanent?.permanentAddressProvince
                      ? JSON.parse(addressPermanent.permanentAddressProvince)?.province_name
                      : ' '}
                  </div>
                </div>
                <div className="flex w-[36%]">
                  <div className="ml-6 flex flex-1 justify-center">
                    {addressPermanent?.permanentAddressZipCode || ' '}
                  </div>
                  <div className="ml-6 flex flex-1 justify-center">Philippines</div>
                  <div className="ml-6 flex flex-1 justify-center">
                    {farmerData?.farmer?.permanent_address_length_of_stay?.value || ' '}
                  </div>
                </div>
              </div>

              {/* Tin No */}
              <div className="absolute left-8 right-4 top-[385px] flex justify-between text-sm">
                {farmerData?.farmer?.governmentIdentifications?.value?.find((id) => id?.government_id_type === 'TIN')
                  ?.government_id_number || ''}
              </div>

              {/* Contact Number */}
              <div className="absolute left-8 right-4 top-[425px] flex justify-between text-sm">
                <div className="ml-2 flex flex-1">{farmerData?.farmer?.mobile_number?.value || ''}</div>
                <div className="ml-2 flex flex-1"></div>
                <div className="ml-2 flex flex-1 !lowercase">{farmerData?.email?.value || ''}</div>
              </div>

              {/* Profession */}
              <div className="absolute left-8 right-4 top-[470px] flex justify-between text-sm">
                <div className="ml-2 flex">{farmerData?.farmer?.occupation_title?.value || ''}</div>
              </div>

              {/* Source of Income */}

              <div className="absolute left-[180px] top-[460px] text-sm">
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.value?.includes('SALARY') ? 'left-1 top-0' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.value?.includes('COMMISSION') ? 'left-1 top-5' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.value?.includes('BUSINESS') ? 'left-36 top-0' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.value?.includes('PENSION') ? 'left-36 top-5' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.value?.includes('OFW') ? 'left-[265px] top-0' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.value?.includes('OTHER') ? 'left-[265px] top-5' : 'hidden'
                  }`}
                />
              </div>

              {/* Birthplace */}
              <div className="absolute left-2 right-4 top-[530px] flex justify-between text-sm">
                <div className="ml-6 flex flex-1">
                  {farmerData?.farmer?.birth_date?.value
                    ? new Date(farmerData?.farmer?.birth_date?.value).toLocaleDateString('en-US', {
                        month: '2-digit',
                        day: '2-digit',
                        year: '2-digit',
                      })
                    : ''}
                </div>
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.place_of_birth?.value || ''}</div>
                <div className="ml-6 flex flex-1 justify-between">
                  <div className="flex flex-1">{farmerData?.farmer?.farmerInfo?.nationality?.value || ''}</div>
                  <div className="flex flex-1">{farmerData?.farmer?.gender?.value || ''}</div>
                </div>
              </div>

              {/* Civil Status */}
              <div
                className={`absolute right-4 top-[565px] flex justify-between text-sm ${
                  farmerData?.farmer?.civil_status?.value === 'SINGLE'
                    ? 'left-[10px]'
                    : farmerData?.farmer?.civil_status?.value === 'MARRIED'
                      ? 'left-[90px]'
                      : farmerData?.farmer?.civil_status?.value === 'WIDOWED'
                        ? 'left-[253px]'
                        : farmerData?.farmer?.civil_status?.value === 'DIVORCED'
                          ? 'left-[183px]'
                          : 'hidden'
                }`}
              >
                <Check />
              </div>

              {/* Mother's Maiden Name */}
              {/* MISSING */}
              <div className="absolute left-[400px] right-4 top-[565px] flex text-sm">
                <div className="ml-2 flex"></div>
              </div>

              {/* Government IDs */}
              <div className="absolute inset-x-4 top-[605px] flex flex-col text-sm">
                {farmerData?.farmer?.governmentIdentifications.value.slice(0, 2).map((item: any) => (
                  <div className="ml-2 flex" key={item.identifier}>
                    {item?.government_id_type + ` (${item?.government_id_number})`}
                  </div>
                ))}
              </div>

              {/* Spouse Name */}
              <div className="absolute left-[400px] right-4 top-[620px] flex text-sm">
                <div className="ml-2 flex">
                  {farmerData?.farmer?.familyProfiles?.value?.find((profile) => profile?.relationship === 'SPOUSE')
                    ?.name || ''}
                </div>
              </div>

              {/* Employer Name */}
              <div className="absolute inset-x-4 top-[680px] flex justify-between text-sm">
                <div className="ml-2 flex flex-1 justify-center">
                  {farmerData?.farmer?.occupation_employer_name?.value || ''}
                </div>
                <div className="ml-2 flex flex-1"></div>
                <div className="ml-2 flex flex-1">{farmerData?.farmer?.occupation_title?.value || ''}</div>
              </div>

              {/* Employer Address */}
              <div className="absolute left-8 right-4 top-[730px] flex justify-between text-sm">
                <div className="ml-2 flex flex-1">{farmerData?.farmer?.occupation_employer_address?.value || ''}</div>
                <div className="ml-2 flex w-1/5">Philippines</div>
              </div>

              {/* Occupation */}
              <div
                className={`absolute right-4 flex justify-between text-sm ${
                  farmerData?.farmer?.occupation?.value === 'GOVERNMENT EMPLOYEE' ||
                  farmerData?.farmer?.occupation?.value === 'PRIVATE EMPLOYEE'
                    ? 'left-[12px] top-[760px]'
                    : farmerData?.farmer?.occupation?.value === 'SELF EMPLOYED PROFESSIONAL' ||
                        farmerData?.farmer?.occupation?.value === 'SELF EMPLOYED NONPROFESSIONAL'
                      ? 'left-[12px] top-[775px]'
                      : farmerData?.farmer?.occupation?.value === 'OFW'
                        ? 'left-[145px] top-[760px]'
                        : farmerData?.farmer?.occupation?.value === 'RETIREE'
                          ? 'left-[145px] top-[775px]'
                          : 'hidden'
                }`}
              >
                <Check />
              </div>

              {/* Monthly Income */}
              <div
                className={`absolute right-4 flex justify-between text-sm ${
                  Number(farmerData?.farmer?.occupation_annual_income?.value) <= 120000
                    ? 'left-[330px] top-[760px]'
                    : Number(farmerData?.farmer?.occupation_annual_income?.value) <= 240000
                      ? 'left-[330px] top-[775px]'
                      : Number(farmerData?.farmer?.occupation_annual_income?.value) <= 360000
                        ? 'left-[480px] top-[760px]'
                        : Number(farmerData?.farmer?.occupation_annual_income?.value) <= 480000
                          ? 'left-[480px] top-[775px]'
                          : Number(farmerData?.farmer?.occupation_annual_income?.value) <= 600000
                            ? 'right-[165px] top-[760px]'
                            : Number(farmerData?.farmer?.occupation_annual_income?.value) > 600000
                              ? 'right-[165px] top-[775px]'
                              : 'hidden'
                }`}
              >
                <Check />
              </div>

              {/* Existing Account at Landbank */}
              <div className={`absolute inset-x-4 top-[818px] flex text-sm`}>
                {/* <div className="flex w-[40%] bg-red-300/10 h-11"> */}
                <div className="grid w-1/3 grid-cols-3 gap-x-14">
                  <Check
                    id="SA-ATM"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.value?.includes('SA-ATM') ? '' : 'invisible'}
                  />
                  <Check
                    id="TRUST"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.value?.includes('TRUST') ? '' : 'invisible'}
                  />
                  <Check
                    id="TRADE"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.value?.includes('TRADE') ? '' : 'invisible'}
                  />

                  <Check
                    id="CA-ATM"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.value?.includes('CA-ATM') ? '' : 'invisible'}
                  />
                  <Check
                    id="LOANS"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.value?.includes('LOANS') ? '' : 'invisible'}
                  />
                  <Check
                    id="CREDIT CARD"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.value?.includes('CREDIT CARD') ? '' : 'invisible'}
                  />

                  <Check
                    id="TIME DEPOSIT"
                    size={17}
                    className={
                      farmerData?.farmer?.landbank_accounts?.value?.includes('TIME DEPOSIT') ? '' : 'invisible'
                    }
                  />
                  <Check
                    id="TREASURY"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.value?.includes('TREASURY') ? '' : 'invisible'}
                  />
                </div>

                <div className="ml-14 flex flex-1 flex-col">
                  {farmerData?.farmer?.farmerBankDetails?.value.slice(0, 3).map((i) => (
                    <div key={i.bank_account_number} className="flex flex-1 justify-between leading-none">
                      <div className="flex flex-1 justify-center text-xs">{i.bank_name}</div>
                      <div className="flex flex-1 justify-center text-xs">{i.bank_account_type}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
