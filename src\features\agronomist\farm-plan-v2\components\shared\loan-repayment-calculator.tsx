'use client';

import { useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';

import { ICreateFarmPlan } from '../../types/create-farm-plan.types';

interface ILoanRepaymentCalcProps {
  form: UseFormReturn<ICreateFarmPlan>;
}

export default function LoanRepaymentCalc({ form }: ILoanRepaymentCalcProps) {
  const state = useHeadAgronomistState();
  const [totalLBPShare, setTotalLBPShare] = useState(0);
  const [lbpPercentage, setLBPPercentage] = useState(0);

  // Watch form values
  const watchInterestRate = form.watch('interestRate', 0);
  const watchNumberOfMonths = form.watch('numberOfMonthsPerTenor', 0);

  // Calculate Total Production Costs and LBP Share
  useEffect(() => {
    const farmInputsContingency = state.section1.amountForHolding.value;
    const cashRequirements = state.section2.totalCashRequirements.value;
    const farmersEquity = state.section3.totalCashRequirements.value;
    const total = farmInputsContingency + cashRequirements + farmersEquity;

    // Total LBP Share / Principal (TPC - FE)
    const lbpShare = total - farmersEquity;
    setTotalLBPShare(lbpShare);

    // Calculate percentage
    const percentage = total > 0 ? (lbpShare / total) * 100 : 0;
    setLBPPercentage(percentage);
  }, [state.section1.amountForHolding, state.section2.totalCashRequirements, state.section3.totalCashRequirements]);

  // Calculate derived values
  const aorPerMonth = watchInterestRate > 0 && watchNumberOfMonths > 0 ? watchInterestRate / watchNumberOfMonths : 0;
  const estimatedComputedInterest =
    totalLBPShare > 0 && watchInterestRate > 0 && watchNumberOfMonths > 0
      ? totalLBPShare * (watchInterestRate / 100) * (watchNumberOfMonths / 12)
      : 0;
  const principalPlusInterest = totalLBPShare + estimatedComputedInterest;

  const formatCurrency = (value: number) => {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formatPercentage = (value: number) => {
    return Math.round(value) + '%';
  };

  useEffect(() => {
    if (aorPerMonth > 0) form.setValue('aorPerMonth', parseFloat(aorPerMonth.toFixed(2)));
  }, [aorPerMonth]);

  return (
    <div className="mt-6">
      {/* Header */}
      <div className="border border-kitaph-blue bg-white px-4 py-3">
        <h3 className="text-center text-lg font-semibold text-kitaph-primary">Estimated Loan Repayment Calculator</h3>
      </div>

      {/* Table */}
      <div className="border border-t-0 border-kitaph-blue bg-white p-4">
        {/* Table Header */}
        <div className="grid grid-cols-4 gap-4 bg-gray-100 p-3 font-bold text-gray-600">
          <div>% Total Costs</div>
          <div className="col-span-2">Cost</div>
          <div className="text-right">AMOUNT</div>
        </div>

        {/* Table Rows */}
        <div className="divide-y divide-gray-200">
          {/* Total LBP Share / Principal */}
          <div className="grid grid-cols-4 gap-4 p-4 text-sm">
            <div className="font-medium text-kitaph-blue">{formatPercentage(lbpPercentage)}</div>
            <div className="col-span-2 text-kitaph-blue">Total LBP Share / Principal (TPC - FE)</div>
            <div className="text-right font-medium">{formatCurrency(totalLBPShare)}</div>
          </div>

          {/* Number of Months / Tenor */}
          <div className="grid grid-cols-4 gap-4 p-4 text-sm">
            <div></div>
            <div className="col-span-2 text-kitaph-blue">Number of Months / Tenor</div>
            <div className="text-right font-medium">{formatCurrency(watchNumberOfMonths)}</div>
          </div>

          {/* AOR per Month */}
          <div className="grid grid-cols-4 gap-4 p-4 text-sm">
            <div></div>
            <div className="col-span-2 text-kitaph-blue">AOR per Month</div>
            <div className="text-right font-medium">{formatCurrency(aorPerMonth)}</div>
          </div>

          {/* Estimated Computed Interest */}
          <div className="grid grid-cols-4 gap-4 p-4 text-sm">
            <div></div>
            <div className="col-span-2 text-kitaph-blue">Estimated Computed Interest</div>
            <div className="text-right font-medium">{formatCurrency(estimatedComputedInterest)}</div>
          </div>

          {/* Principal + Interest */}
          <div className="grid grid-cols-4 gap-4 bg-blue-50 p-4 text-sm font-semibold">
            <div></div>
            <div className="col-span-2 text-right font-bold text-kitaph-blue">Principal + Interest</div>
            <div className="text-right font-bold text-kitaph-blue">{formatCurrency(principalPlusInterest)}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
