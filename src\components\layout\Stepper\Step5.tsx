'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Plus, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Input, InputPassword } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { STEPPER_FORM } from '@/lib/constants';
import useFarmer, { FarmerSchema } from '@/lib/hooks/useFarmer';
import usePublic from '@/lib/hooks/usePublic';
import { UserSchema } from '@/lib/hooks/useUsers';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';
import { useStateContext } from '@/providers/app/state';

import {
  FamilyRelationshipEnums,
  FarmerOccupationEnum,
  GovernmentIdentificationEnum,
  IFarmPropertyEnum,
} from './Enums';

const OPTION_LAND_CATEGORIES = [
  {
    label: 'IRRIGATED',
    value: 'IRRIGATED',
  },
  {
    label: 'RAIN FED',
    value: 'RAIN FED',
  },
  {
    label: 'IRRIGATED & RAIN FED',
    value: 'IRRIGATED & RAIN FED',
  },
];

export default function Step5() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { updateFarmer } = useFarmer();
  const {
    getAllCrops,
    getSeeds,
    getFertilizer,
    getChemicals,
    OPTION_CROPS,
    OPTION_CHEMICAL,
    OPTION_FERTILIZER,
    OPTION_SEED,
    OPTION_CROPS_PLANTED,
  } = usePublic();
  const { setState, state } = useStateContext();
  const data = gStateP.admin.members['details'].get({ noproxy: true });

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      farmAddress: data?.farmer?.farmerInfo?.farm_address || '',
      farmArea: data?.farmer?.farmerInfo?.farm_area || ('' as any),
      farmOwnership: `${data?.farmer?.farmerInfo?.farm_ownership}` || '',
      cropsPlanted:
        data?.farmer?.cropsPlanted?.map((v) => ({
          label: v.crop.name,
          value: `${v.crop.id}-${v.crop.name}`,
        })) || [],
      seed:
        data?.farmer?.seeds?.map((v) => ({
          label: v.seed.name,
          value: `${v.seed.id}-${v.seed.name}`,
        })) || [],
      fertilizer:
        data?.farmer?.fertilizers?.map((v) => ({
          label: v.fertilizer.name,
          value: `${v.fertilizer.id}-${v.fertilizer.name}`,
        })) || [],
      chemical:
        data?.farmer?.chemicals?.map((v) => ({
          label: v.chemical.name,
          value: `${v.chemical.id}-${v.chemical.name}`,
        })) || [],
      farmerVehicle:
        data?.farmer?.farmerVehicles?.map((v) => ({
          vehicleOwned: v.vehicle_owned,
          vehiclePlateNumber: v.vehicle_plate_number,
          orcr: '',
        })) || [],
      landCategory: [],
      crop: [],
      phase: '',
      ownerCultivator: '',
      tenant: '',
      cltEp: '',
      lessee: '',
    },
  });
  const farmerVehicle = useFieldArray({
    name: 'farmerVehicle',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      farmerVehicle: _data.farmerVehicle.map((s) => ({
        vehicleOwned: s.vehicleOwned,
        vehiclePlateNumber: s.vehiclePlateNumber,
      })),
      cropsPlanted: _data.cropsPlanted.map((item) => item.value.split('-')[0]),
      seed: _data.seed.map((item) => item.value.split('-')[0]),
      fertilizer: _data.fertilizer.map((item) => item.value.split('-')[0]),
      chemical: _data.chemical.map((item) => item.value.split('-')[0]),
      landCategory: _data.landCategory.map((item) => item.value).join(','),
      crop: _data.crop.map((item) => item.value).join(','),
      userId: data.farmer.user_id,
    };

    _data.farmerVehicle.map((s) => {
      if (s.orcr) {
        updatedData = {
          ...updatedData,
          [`farmerVehicle_vehicleOrcr_${s.vehiclePlateNumber}`]: s.orcr[0],
        };
      }
    });

    console.log('Farm Details: ', updatedData);
    updateFarmer(updatedData);
    // setState((v) => ({
    //   ...v,
    //   ..._data,
    //   userId: data.id,
    // }));
    // !gState.stepper.isLastStep.value && gState.stepper.activeStep.set((cur) => cur + 1);
  };

  useEffect(() => {
    Promise.all([getAllCrops(), getSeeds(), getFertilizer(), getChemicals()]);
  }, []);

  return (
    <form id={STEPPER_FORM[5]} onSubmit={handleSubmit(onSubmit)}>
      <div className="font-dmSans text-xl font-bold text-primary">Farm Information</div>
      <div className="my-6 grid grid-cols-3 items-start gap-4">
        {/* Farm Address */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="farmAddress" className="pb-1 font-normal">
            Farm Address
          </Label>
          <Input
            {...register('farmAddress')}
            className={cn(
              'focus-visible:ring-primary',
              errors.farmAddress && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Farm Address"
          />
          {errors.farmAddress && <p className="form-error">{`${errors.farmAddress.message}`}</p>}
        </div>

        {/* Farm Area (hectare/s) */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="farmArea" className="pb-1 font-normal">
            Farm Area (hectare/s)
          </Label>
          <Input
            {...register('farmArea', {
              required: false,
              validate: {
                isGreaterThanZero: (v) => (v ? (Number(v) || 0) > 0 || 'Farm Area must be greater than 0' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.farmArea && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Farm Area (hectare/s)"
          />
          {errors.farmArea && <p className="form-error">{`${errors.farmArea.message}`}</p>}
        </div>

        {/* Farm Ownership */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="farmOwnership" className="pb-1 font-normal">
            Farm Ownership
          </Label>
          <Controller
            control={control}
            name="farmOwnership"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <Select onValueChange={onChange} value={value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.farmOwnership && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Farm Ownership" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="0">NOT OWNED</SelectItem>
                    <SelectItem value="1">OWNED</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
          {errors.farmOwnership && <p className="form-error">{`${errors.farmOwnership.message}`}</p>}
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Vehicle Information</div>
      <div className="space-y-4">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {farmerVehicle.fields.map((field, index) => {
            const errorForField = errors?.farmerVehicle?.[index];

            return (
              <div key={field.id} className="grid grid-cols-3 items-start gap-4 pb-3 pt-7">
                {/* Vehicle Plate No. */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`farmerVehicle.${index}.vehiclePlateNumber`} className="pb-1 font-normal">
                    Vehicle Plate No.
                  </Label>
                  <Input
                    {...register(`farmerVehicle.${index}.vehiclePlateNumber` as const, {
                      required: false,
                      validate: {
                        isValidPlate: (v) => (v ? /^[A-Z0-9]{5,7}$/.test(v) || 'Invalid plate number' : true),
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vehiclePlateNumber && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Plate No."
                  />
                  {errorForField?.vehiclePlateNumber && (
                    <p className="form-error">{`${errorForField?.vehiclePlateNumber?.message}`}</p>
                  )}
                </div>

                {/* Vehicle Brand & Model */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`farmerVehicle.${index}.vehicleOwned`} className="pb-1 font-normal">
                    Vehicle Brand & Model
                  </Label>
                  <Input
                    {...register(`farmerVehicle.${index}.vehicleOwned` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.vehicleOwned && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Vehicle Brand & Model"
                  />
                  {errorForField?.vehicleOwned && (
                    <p className="form-error">{`${errorForField?.vehicleOwned?.message}`}</p>
                  )}
                </div>

                {/* Upload ORCR */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`farmerVehicle.${index}.orcr`} className="pb-1 font-normal">
                    Upload ORCR
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`farmerVehicle.${index}.orcr` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.orcr && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="file"
                      placeholder="Enter Upload ORCR"
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => farmerVehicle.remove(index)}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.orcr && <p className="form-error">{`${errorForField?.orcr?.message}`}</p>}
                </div>
              </div>
            );
          })}
        </div>

        {/* Add More Vehicle */}
        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              farmerVehicle.append({
                vehicleOwned: '',
                vehiclePlateNumber: '',
                orcr: '',
              })
            }
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Vehicle</span>
          </Button>
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Crop Details</div>
      <div className="my-6 grid grid-cols-3 items-start gap-4">
        {/* Crops Planted */}
        {OPTION_CROPS_PLANTED.length > 0 && (
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="cropsPlanted" className="pb-1 font-normal">
              Crops Planted
            </Label>
            <Controller
              control={control}
              name="cropsPlanted"
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS_PLANTED}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
            {errors.cropsPlanted && <p className="form-error">{`${errors.cropsPlanted.message}`}</p>}
          </div>
        )}

        {/* Seeds */}
        {OPTION_SEED.length > 0 && (
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="seed" className="pb-1 font-normal">
              Seeds
            </Label>
            <Controller
              control={control}
              name="seed"
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_SEED}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                  groupBy="group"
                />
              )}
            />
            {errors.seed && <p className="form-error">{`${errors.seed.message}`}</p>}
          </div>
        )}

        {/* Fertilizers */}
        {OPTION_FERTILIZER.length > 0 && (
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="fertilizer" className="pb-1 font-normal">
              Fertilizers
            </Label>
            <Controller
              control={control}
              name="fertilizer"
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_FERTILIZER}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
            {errors.fertilizer && <p className="form-error">{`${errors.fertilizer.message}`}</p>}
          </div>
        )}

        {/* Chemicals */}
        {OPTION_CHEMICAL.length > 0 && (
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="chemical" className="pb-1 font-normal">
              Chemicals
            </Label>
            <Controller
              control={control}
              name="chemical"
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CHEMICAL}
                  placeholder="Select from selection"
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                  groupBy="group"
                />
              )}
            />
            {errors.chemical && <p className="form-error">{`${errors.chemical.message}`}</p>}
          </div>
        )}
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Land Details</div>
      <div className="my-6 grid grid-cols-3 items-start gap-4">
        {/* Land Category */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="landCategory" className="pb-1 font-normal">
            Land Category
          </Label>
          <Controller
            control={control}
            name="landCategory"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTION_LAND_CATEGORIES}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
              />
            )}
          />
          {errors.landCategory && <p className="form-error">{`${errors.landCategory.message}`}</p>}
        </div>

        {/* Crops */}
        {OPTION_CROPS.length > 0 && (
          <div className="grid w-full max-w-sm items-center gap-1.5">
            <Label htmlFor="crop" className="pb-1 font-normal">
              Crops
            </Label>
            <Controller
              control={control}
              name="crop"
              render={({ field: { onChange, onBlur, value, ref } }) => (
                <MultipleSelector
                  value={value}
                  onChange={onChange}
                  defaultOptions={OPTION_CROPS}
                  placeholder="Select from selection or create new"
                  creatable
                  emptyIndicator={
                    <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                  }
                />
              )}
            />
            {errors.crop && <p className="form-error">{`${errors.crop.message}`}</p>}
          </div>
        )}

        {/* Phase */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="phase" className="pb-1 font-normal">
            Phase
          </Label>
          <Input
            {...register('phase')}
            className={cn('focus-visible:ring-primary', errors.phase && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Phase"
          />
          {errors.phase && <p className="form-error">{`${errors.phase.message}`}</p>}
        </div>

        {/* Owner Cultivator */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="ownerCultivator" className="pb-1 font-normal">
            Owner Cultivator
          </Label>
          <Input
            {...register('ownerCultivator')}
            className={cn(
              'focus-visible:ring-primary',
              errors.ownerCultivator && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Owner Cultivator"
          />
          {errors.ownerCultivator && <p className="form-error">{`${errors.ownerCultivator.message}`}</p>}
        </div>

        {/* Tenant */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="tenant" className="pb-1 font-normal">
            Tenant
          </Label>
          <Input
            {...register('tenant')}
            className={cn('focus-visible:ring-primary', errors.tenant && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Tenant"
          />
          {errors.tenant && <p className="form-error">{`${errors.tenant.message}`}</p>}
        </div>

        {/* CLT/EP */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="cltEp" className="pb-1 font-normal">
            CLT/EP
          </Label>
          <Input
            {...register('cltEp')}
            className={cn('focus-visible:ring-primary', errors.cltEp && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter CLT/EP"
          />
          {errors.cltEp && <p className="form-error">{`${errors.cltEp.message}`}</p>}
        </div>

        {/* Lessee */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="lessee" className="pb-1 font-normal">
            Lessee
          </Label>
          <Input
            {...register('lessee')}
            className={cn('focus-visible:ring-primary', errors.lessee && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Lessee"
          />
          {errors.lessee && <p className="form-error">{`${errors.lessee.message}`}</p>}
        </div>
      </div>
    </form>
  );
}
