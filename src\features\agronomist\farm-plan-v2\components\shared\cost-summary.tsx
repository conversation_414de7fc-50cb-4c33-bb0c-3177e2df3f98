'use client';

import { useEffect, useState } from 'react';

import { useHeadAgronomistState } from '@/features/head-agronomist/store';

export default function CostSummary() {
  const state = useHeadAgronomistState();
  const [totalProductionCosts, setTotalProductionCosts] = useState(0);

  // Calculate Total Production Costs
  useEffect(() => {
    const farmInputsContingency = state.section1.amountForHolding.value;
    const cashRequirements = state.section2.totalCashRequirements.value;
    const farmersEquity = state.section3.totalCashRequirements.value;
    const total = farmInputsContingency + cashRequirements + farmersEquity;
    setTotalProductionCosts(total);
  }, [state.section1.amountForHolding, state.section2.totalCashRequirements, state.section3.totalCashRequirements]);

  // Calculate individual values and percentages
  const farmInputsContingency = state.section1.amountForHolding.value;
  const cashRequirements = state.section2.totalCashRequirements.value;
  const farmersEquity = state.section3.totalCashRequirements.value;

  const farmInputsPercentage = totalProductionCosts > 0 ? (farmInputsContingency / totalProductionCosts) * 100 : 0;
  const cashRequirementsPercentage = totalProductionCosts > 0 ? (cashRequirements / totalProductionCosts) * 100 : 0;
  const farmersEquityPercentage = totalProductionCosts > 0 ? (farmersEquity / totalProductionCosts) * 100 : 0;

  const formatCurrency = (value: number) => {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formatPercentage = (value: number) => {
    return Math.round(value) + '%';
  };

  return (
    <div className="mt-6">
      {/* Header */}
      <div className="border border-kitaph-blue bg-white px-4 py-3">
        <h3 className="text-center text-lg font-semibold text-kitaph-primary">Cost Summary</h3>
      </div>

      {/* Table */}
      <div className="border border-t-0 border-kitaph-blue bg-white p-4">
        {/* Table Header */}
        <div className="grid grid-cols-4 gap-4 bg-gray-100 p-3 font-bold text-gray-600">
          <div>% Total Costs</div>
          <div className="col-span-2">Cost</div>
          <div className="text-right">AMOUNT</div>
        </div>

        {/* Table Rows */}
        <div className="divide-y">
          {/* Farm Inputs + Contingency */}
          <div className="grid grid-cols-4 gap-4 p-3 text-sm">
            <div className="font-medium">{formatPercentage(farmInputsPercentage)}</div>
            <div className="col-span-2 text-kitaph-blue">Farm Inputs + Contingency</div>
            <div className="text-right font-medium">{formatCurrency(farmInputsContingency)}</div>
          </div>

          {/* Cash Requirements */}
          <div className="grid grid-cols-4 gap-4 p-3 text-sm">
            <div className="font-medium">{formatPercentage(cashRequirementsPercentage)}</div>
            <div className="col-span-2 text-kitaph-blue">Cash Requirements</div>
            <div className="text-right font-medium">{formatCurrency(cashRequirements)}</div>
          </div>

          {/* Farmer's Equity */}
          <div className="grid grid-cols-4 gap-4 p-3 text-sm">
            <div className="font-medium">{formatPercentage(farmersEquityPercentage)}</div>
            <div className="col-span-2 text-kitaph-blue">Farmer&apos;s Equity (FE)</div>
            <div className="text-right font-medium">{formatCurrency(farmersEquity)}</div>
          </div>

          {/* Total Production Costs */}
          <div className="grid grid-cols-4 gap-4 bg-blue-50 p-3 text-sm font-semibold">
            <div className="text-kitaph-blue">100%</div>
            <div className="col-span-2 text-right font-bold text-kitaph-blue">Total Production Costs (TPC)</div>
            <div className="text-right font-bold text-kitaph-blue">{formatCurrency(totalProductionCosts)}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
