'use client';

import { useEffect } from 'react';

import useLoanPayment from '@/lib/hooks/useLoanPayment';
import { useGlobalState } from '@/lib/store';

import { LoanRequestTable } from './loan-request-table';
import { columns } from './loan-request-table/columns';

export default function PaymentsPage() {
  const gState = useGlobalState();
  const { fetchDashboard, fetchRequests } = useLoanPayment();

  useEffect(() => {
    Promise.all([fetchDashboard(), fetchRequests()]);
  }, [
    gState.finance.loanPayments.pagination.requests.startDate,
    gState.finance.loanPayments.pagination.requests.endDate,
    gState.finance.loanPayments.pagination.requests.page,
    gState.finance.loanPayments.pagination.requests.pageSize,
    gState.finance.loanPayments.pagination.requests.status[0],
  ]);

  useEffect(() => {
    gState.finance.loanPayments.pagination.requests.status.set([]);
  }, []);

  return (
    <div className="p-8">
      <LoanRequestTable
        columns={columns}
        data={gState.finance.loanPayments.requests.data.get({ noproxy: true })}
        meta={gState.finance.loanPayments.requests['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
