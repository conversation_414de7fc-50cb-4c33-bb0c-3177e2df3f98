'use client';

import { toast } from 'sonner';

import axios from '@/lib/api';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

export default function useFinance1() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const getTopupRequests = async () => {
    try {
      const _topup = await axios
        .get('/finance/topup/viewAll', {
          params: {
            page: gState.finance1.pagination.topupReqs.page.value,
            pageSize: gState.finance1.pagination.topupReqs.pageSize.value,
            search: gState.finance1.pagination.topupReqs.search.value,
            startDate: gState.finance1.pagination.topupReqs.startDate.value,
            endDate: gState.finance1.pagination.topupReqs.endDate.value,
          },
        })
        .then((res) => res.data.data);
      console.log('getTopupRequests: ', _topup);

      gState.finance1.topupReqs.set(_topup);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('getTopupRequests: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getReqDetails = async (id) => {
    try {
      const _details = await axios.get(`/finance/topup/view/${id}`).then((res) => res.data.data);
      console.log('getReqDetails: ', _details);

      gStateP.finance1['reqDetails'].set(_details);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('getReqDetails: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const reqTopup = async (data) => {
    try {
      toast.loading('Requesting topup...', {
        description: 'Please wait...',
        duration: 10000,
      });

      const _data = await axios
        .post('/finance/topup/request', data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data);
      console.log('reqTopup: ', _data);

      toast.dismiss();
      toast.success('Success', {
        description: 'Topup request sent',
      });
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('reqTopup: ', error);

      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return { getTopupRequests, reqTopup, getReqDetails };
}
