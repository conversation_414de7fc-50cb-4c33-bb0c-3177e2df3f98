import { ITemplateItem } from '@/features/head-agronomist/components/templates/create-form/types/template';

export interface ICreateFarmPlan {
  userId: number;
  cropId: number;
  croppingType: string;
  agronomistName: string;
  agronomistPrcNumber: string;
  agronomistValidUntil: string | Date;
  headAgronomistName: string;
  headAgronomistPrcNumber: string;
  headAgronomistValidUntil: string | Date;
  contingencyForFluctuation: number;
  interestRate: number;
  numberOfMonthsPerTenor: number;
  aorPerMonth: number;
  farmPlanTemplateId: number;
  items: ITemplateItem[];
}
