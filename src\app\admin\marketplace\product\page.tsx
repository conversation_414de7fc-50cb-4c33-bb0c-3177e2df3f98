'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { useGlobalStatePersist } from '@/lib/store/persist';

import { TAB_MARKETPLACE } from '../../layout';

export default function ProductPage() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();

  useEffect(() => {
    gStateP.tabsMarketplace.set(TAB_MARKETPLACE.PRODUCTS);
    router.push('/admin/marketplace/');
  }, []);

  return <div>Loading...</div>;
}
