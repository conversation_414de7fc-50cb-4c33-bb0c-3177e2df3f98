'use client';

import { hookstate, useHookstate } from '@hookstate/core';
import { devtools } from '@hookstate/devtools';
import { useQuery } from '@tanstack/react-query';
import { endOfMonth, startOfMonth } from 'date-fns';

import { ITopTransactionValue } from '@/features/admin/dashboard/types';
import axios from '@/lib/api';
import { useGlobalStatePersist } from '@/lib/store/persist';

// State
const initialState = {
  startDate: startOfMonth(new Date()),
  endDate: endOfMonth(new Date()),
};
const topTransactionsValueState = hookstate(initialState, devtools({ key: 'topTransactionsValueState' }));

// Fetcher
const fetchTopTransactionValue = async (currentUser = 'admin') => {
  const { data } = await axios.get(`/${currentUser}/dashboard/viewTopTransactionValue`, {
    params: {
      ...topTransactionsValueState.value,
    },
  });
  return data.data as ITopTransactionValue[];
};

// Hooks
const useTopTransactionsValue = (currentUser = 'admin') => {
  const state = useHookstate(topTransactionsValueState);
  const gStateP = useGlobalStatePersist();

  const topTransactionValueQuery = useQuery({
    queryKey: [
      'topTransactionValue-all',
      {
        currentUser,
        username: gStateP.user.user.username.value,
        ...state.value,
      },
    ],
    queryFn: () => fetchTopTransactionValue(currentUser),
    enabled: !!gStateP.user.value,
  });

  return { state, topTransactionValueQuery };
};

export { topTransactionsValueState };
export default useTopTransactionsValue;
