# KITAPH-ADMIN

## Getting Started

Run the following command to install project dependencies:

```bash
yarn

# or

npm install
```

## Commands

Run the following command to run the project in development mode

```bash
yarn dev

# or

npm run dev
```

Run the following command to build the project in production mode

```bash
yarn build

# or

npm run build
```

To run the production build locally, run the following command

```bash
yarn start-static

# or

npm run start-static
```

Run the following command to check and fix lint errors

```bash
yarn lint:fix

# or

npm run lint:fix
```

## Config and ENV

`.env.development` - Used when in development mode

`.env.production` - Used when in production mode

`/src/lib/config.tsx` - This file serves as the configuration module, which relies on the environment file for configuration parameters such as the base URL of your API.

## Tech Stack

- [Next.js](https://nextjs.org)
- [TailwindCSS](https://tailwindcss.com/)
