'use client';

import { useEffect } from 'react';

import { columns } from '@/components/layout/admin/SelectUser/farmer/Column';
import FarmerList from '@/components/layout/admin/SelectUser/farmer/List';

import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';

export default function TopupPage() {
  const gState = useGlobalState();
  const { viewAllFarmers } = useFarmer();

  useEffect(() => {
    viewAllFarmers();
  }, []);

  return (
    <div className="flex-1 py-12">
      <div className="mx-auto w-[68vw]">
        <FarmerList columns={columns} data={gState.encoder.farmers.get({ noproxy: true })} />
      </div>
    </div>
  );
}
