import z from 'zod';

export const dataPrivacySchema = z.object({
  isAgreeUsingData: z
    .boolean()
    .refine((val) => val === true, { message: 'You must agree' })
    .transform((val) => (val ? 1 : 0)),
  isAgreeVisitingFarm: z
    .boolean()
    .refine((val) => val === true, { message: 'You must agree' })
    .transform((val) => (val ? 1 : 0)),
  isAgreeSharingData: z
    .boolean()
    .refine((val) => val === true, { message: 'You must agree' })
    .transform((val) => (val ? 1 : 0)),
  userImage: z.custom<File>((file) => file, {
    message: 'User image is required',
  }),
  signature: z.custom<File>((file) => file, {
    message: 'Signature is required',
  }),
});

export type TDataPrivacySchema = z.input<typeof dataPrivacySchema>;

export const personalInformationSchema = z
  .object({
    // Personal Details
    firstName: z.string().min(1, 'First name is required'),
    middleName: z.string().optional(),
    lastName: z.string().min(1, 'Last name is required'),
    birthDate: z.string().min(1, 'Birth date is required'),
    gender: z.string().min(1, 'Gender is required'),
    nationality: z.string().optional(),
    mobileNumber: z.string().regex(/^\d{11}$/, { message: 'Mobile number must be exactly 11 digits' }),
    placeOfBirth: z.string().optional(),
    email: z.string().optional(),
    telephoneNumber: z.string().optional(),
    facebookName: z.string().optional(),
    civilStatus: z.string().optional(),
    spouseName: z.string().optional(),
    spouseMobileNumber: z.string().optional(),

    // Present Address
    addressHouseNumber: z.string().min(1, 'House number is required'),
    addressStreet: z.string().min(1, 'Street is required'),
    addressRegion: z.string().min(1, 'Region is required'),
    addressProvince: z.string().min(1, 'Province is required'),
    addressCity: z.string().min(1, 'City/Municipality is required'),
    addressBarangay: z.string().min(1, 'Barangay is required'),
    residenceOwnership: z.string().min(1, 'Residence ownership type is required'),
    addressZipCode: z.string().optional(),
    addressLengthOfStay: z.string().optional(),

    // Identification Docs
    governmentIdentification: z
      .array(
        z.object({
          governmentIdType: z.string().min(1, 'ID type is required'),
          governmentIdNumber: z.string().min(1, 'ID number is required'),
          upload: z.any().optional(),
          // .custom<File>()
          // .optional()
          // .transform((file) => file && file[0]),
        }),
      )
      .min(1),
  })
  .superRefine((data, ctx) => {
    if (data.civilStatus === 'MARRIED') {
      if (!data.spouseName || data.spouseName.trim() === '') {
        ctx.addIssue({
          path: ['spouseName'],
          code: z.ZodIssueCode.custom,
          message: 'Spouse name is required',
        });
      }

      if (!data.spouseMobileNumber || data.spouseMobileNumber.trim() === '') {
        ctx.addIssue({
          path: ['spouseMobileNumber'],
          code: z.ZodIssueCode.custom,
          message: 'Spouse mobile number is required',
        });
      } else if (!/^\d{11}$/.test(data.spouseMobileNumber)) {
        ctx.addIssue({
          path: ['spouseMobileNumber'],
          code: z.ZodIssueCode.custom,
          message: 'Spouse mobile number must be exactly 11 digits',
        });
      }
    }
  });

export type TPersonalInformationSchema = z.infer<typeof personalInformationSchema>;

export const farmInformationSchema = z
  .object({
    // Farm Location
    farmAddressHouseNumber: z.string().min(1, 'House number is required'),
    farmAddressStreet: z.string().min(1, 'Street is required'),
    farmAddressRegion: z.string().min(1, 'Region is required'),
    farmAddressProvince: z.string().min(1, 'Province is required'),
    farmAddressCity: z.string().min(1, 'City is required'),
    farmAddressBarangay: z.string().min(1, 'Barangay is required'),
    farmArea: z.string().min(1, 'Farm area is required'),
    farmOwnership: z.string().min(1, 'Farm ownership is required'),
    cropsPlanted: z.array(z.string()).min(1, 'At least one crop must be selected'),
    otherFarmOwnership: z.string().optional(),
    farmAddressZipCode: z.string().optional(),

    // Farm Practices
    waterSource: z.preprocess(
      (val) => (Array.isArray(val) ? val.join(', ') : val),
      z.string().min(1, 'Water source is required'),
    ),
    fertilizerUsed: z.string().min(1, 'Fertilizer used is required'),
    pesticideUsed: z.string().min(1, 'Pesticide used is required'),
    farmImplements: z.preprocess(
      (val) => (Array.isArray(val) ? val.join(', ') : val),
      z.string().min(1, 'Farm implements are required'),
    ),
    waterSourceOthers: z.string().optional(),
    farmImplementsOthers: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.farmOwnership === '3' && (!data.otherFarmOwnership || data.otherFarmOwnership.trim() === '')) {
      ctx.addIssue({
        path: ['otherFarmOwnership'],
        code: z.ZodIssueCode.custom,
        message: 'Other ownership is required',
      });
    }
    if (data.waterSource === 'OTHERS' && (!data.waterSourceOthers || data.waterSourceOthers.trim() === '')) {
      ctx.addIssue({
        path: ['waterSourceOthers'],
        code: z.ZodIssueCode.custom,
        message: 'Please specify other water source',
      });
    }
    if (data.farmImplements === 'Others' && (!data.farmImplementsOthers || data.farmImplementsOthers.trim() === '')) {
      ctx.addIssue({
        path: ['farmImplementsOthers'],
        code: z.ZodIssueCode.custom,
        message: 'Other farm implement is required',
      });
    }
  });

export type TFarmInformationSchema = z.infer<typeof farmInformationSchema>;

export const businessInfoSchema = z
  .object({
    // Financial Information
    sourceOfFunds: z.preprocess(
      (val) => (Array.isArray(val) ? val.join(', ') : val),
      z.string().min(1, 'Source of funds is required'),
    ),
    monthlyGrossIncome: z
      .union([
        z.number({ invalid_type_error: 'Monthly Gross Income must be a number' }),
        z
          .string()
          .refine((val) => !isNaN(Number(val)), {
            message: 'Monthly Gross Income must be a number',
          })
          .transform(Number),
      ])
      .refine((val) => val > 0, {
        message: 'Monthly Gross Income must be greater than 0',
      }),

    // Farm Business Information
    isMemberOfOrganization: z.enum(['0', '1'], {
      required_error: 'This field is required',
    }),
    organizationName: z.string().optional(),
    organizationPosition: z.string().optional(),
    hasPastFarmLoans: z.enum(['0', '1'], {
      required_error: 'This field is required',
    }),
    pastFarmLoans: z.preprocess((val) => (Array.isArray(val) ? val.join(', ') : val), z.string().optional()),

    hasPastFarmLoanPaid: z.enum(['0', '1'], {
      required_error: 'This field is required',
    }),

    hasNeedFarmLoan: z.enum(['0', '1'], {
      required_error: 'This field is required',
    }),
    needFarmLoanReason: z.preprocess((val) => (Array.isArray(val) ? val.join(', ') : val), z.string().optional()),
    // .transform((val) => val.split(', ')),

    isInterestedToSellAtTradingPost: z.enum(['0', '1'], {
      required_error: 'This field is required',
    }),

    purchaserSellingLocation: z.string().optional(),
    purchaserFullname: z.string().optional(),
    purchaserContactNumber: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.isMemberOfOrganization === '1') {
      if (!data.organizationName?.trim()) {
        ctx.addIssue({
          path: ['organizationName'],
          code: z.ZodIssueCode.custom,
          message: 'Organization name is required',
        });
      }
      if (!data.organizationPosition?.trim()) {
        ctx.addIssue({
          path: ['organizationPosition'],
          code: z.ZodIssueCode.custom,
          message: 'Organization position is required',
        });
      }
    }

    if (data.hasPastFarmLoans === '1' && !data.pastFarmLoans.length) {
      ctx.addIssue({
        path: ['pastFarmLoans'],
        code: z.ZodIssueCode.custom,
        message: 'Please specify details about past farm loans',
      });
    }

    if (data.hasNeedFarmLoan === '1' && !data.needFarmLoanReason.length) {
      ctx.addIssue({
        path: ['needFarmLoanReason'],
        code: z.ZodIssueCode.custom,
        message: 'Please specify the reason for needing a farm loan',
      });
    }
  });

export type TBusinessInfoSchema = z.infer<typeof businessInfoSchema>;
