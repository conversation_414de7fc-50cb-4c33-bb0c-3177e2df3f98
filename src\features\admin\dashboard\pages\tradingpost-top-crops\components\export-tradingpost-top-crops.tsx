'use client';

import { format } from 'date-fns';
import { FC } from 'react';
import { CSVLink } from 'react-csv';

import { Button } from '@/components/ui/button';

import useTradingPostTopCrops from '../hooks/useTradingPostTopCrops';

const exportHeaders = [
  { label: '#', key: 'crop_id' },
  { label: 'Crop Name', key: 'crop_name' },
  { label: 'Volume', key: 'crop_volume' },
  { label: 'Price per pc', key: 'crop_price' },
  { label: 'Sales', key: 'crop_sales' },
];

export const ExportTradingPostTopCrops: FC = () => {
  const { tradingPostTopCropsQuery } = useTradingPostTopCrops();

  return (
    <div>
      <Button size="sm" className="h-8" asChild>
        <CSVLink
          data={tradingPostTopCropsQuery.data || []}
          headers={exportHeaders}
          filename={`tradingpost-top-crops-${format(new Date(), 'yyyy-MM-dd')}.csv`}
          target="_blank"
        >
          Export
        </CSVLink>
      </Button>
    </div>
  );
};
