'use client';

import { useHookstate } from '@hookstate/core';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { PROFILE_TAB } from '../../constants';

export function DirtyDialog({ state, activeStep, onDiscard = () => {} }) {
  // use this if you want to control the alert dialog programatically
  const dialogState = useHookstate(state);
  const step = useHookstate(activeStep);

  return (
    <Dialog open={dialogState.value} onOpenChange={dialogState.set}>
      <DialogContent className="font-sans sm:max-w-lg">
        <DialogHeader className="hidden">
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>

        <div className="text-center">
          <div>
            <img className="mx-auto" src="/assets/undraw/checklist.png" alt="" />
          </div>
          <div className="my-4 text-xl font-bold">Save Changes?</div>
          <div>You have unsaved changes. Would you like to save them?</div>
        </div>

        <DialogFooter className="mt-4 sm:justify-between">
          <DialogClose asChild>
            <Button type="button" size="lg" className="px-12" variant="outline" onClick={onDiscard}>
              Discard Changes
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button type="submit" size="lg" className="px-12" form={PROFILE_TAB[step.value].value}>
              Yes, Save
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
