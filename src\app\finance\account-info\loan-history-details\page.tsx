'use client';

import { useHookstate } from '@hookstate/core';
import { Timeline, TimelineConnector, TimelineHeader, TimelineIcon, TimelineItem } from '@material-tailwind/react';
import { format } from 'date-fns';
import { CircleCheckIcon } from 'lucide-react';
import { Suspense, useEffect } from 'react';
import { MdRadioButtonChecked } from 'react-icons/md';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion-custom';
import { Badge } from '@/components/ui/badge';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

import { PaymentStatus, PaymentStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { LoanStageLabel } from '@/lib/constants/enums';
import useFinance from '@/lib/hooks/admin/useFinance';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import FetchDetails from './_components/FetchDetails';

export default function LoanHistoryDetails() {
  const gStateP = useGlobalStatePersist();
  const account = useHookstate(gStateP.selected.account.info);
  const { creditScoreDetails, reqDetails } = useFinance();

  const isBefore = useHookstate(false);
  const isDuring = useHookstate(false);
  const isAfter = useHookstate(false);

  useEffect(() => {
    if (creditScoreDetails.value) {
      if (creditScoreDetails['current_loan_stage'].value === LoanStageLabel.AFTER) {
        if (reqDetails.payment_status.value === PaymentStatus.PAID) {
          isAfter.set(false);
        } else {
          isAfter.set(true);
        }
      } else if (creditScoreDetails['current_loan_stage'].value === LoanStageLabel.DURING) {
        isDuring.set(true);
      } else {
        isBefore.set(true);
      }
    }
  }, []);

  console.log('creditScoreDetails', creditScoreDetails.get({ noproxy: true }));

  return (
    <div className="space-y-6 p-6 md:p-12">
      {/* Data Fetcher */}
      <Suspense>
        <FetchDetails />
      </Suspense>

      {/* Title */}
      <div className="">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Loan History Details</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink href={`/admin/account-info/?id=${account['id'].value}&ait=credit_history`}>
                Credit History
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink
                href={`/admin/account-info/?id=${account['id'].value}&ait=credit_history&cht=loan_history`}
              >
                Loan History
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Loan History Details</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Card Details */}
      {reqDetails.value && (
        <div className="card">
          <dl className="font-dmSans grid gap-4 sm:grid-cols-2">
            <div>
              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan Start Date</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {format(reqDetails.created_at.value, 'MMM dd, yyyy')}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan End Date</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {format(reqDetails.due_at.value, 'MMM dd, yyyy')}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan Term</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {reqDetails.loan_term.value} days
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Credit Score Group</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {reqDetails.creditScoreGroup.name.value}
                </dd>
              </div>
            </div>

            <div>
              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Payment Status</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  <Badge className={cn(PaymentStatusLabels[reqDetails.payment_status.value].color, 'min-w-max')}>
                    {PaymentStatusLabels[reqDetails.payment_status.value].label}
                  </Badge>
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan Balance</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {Number(reqDetails.user.wallet.credit.value).toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'PHP',
                  })}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Final Payment Date</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {reqDetails?.paid_at.value ? format(reqDetails.paid_at.value, 'MMM dd, yyyy') : '-'}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Repayment Behavior</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">-</dd>
              </div>
            </div>
          </dl>
        </div>
      )}

      {/* Loan Stages */}
      {creditScoreDetails.value && (
        <div className="mt-6">
          <Timeline>
            <Accordion className="" type="single" collapsible>
              {/* After */}
              <AccordionItem className="" value="after">
                <TimelineItem className={cn('min-h-[5.4rem]')}>
                  <TimelineConnector className="!w-[78px] " />
                  <AccordionTrigger className="">
                    <TimelineHeader
                      className={cn(
                        'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                        isAfter.value ? 'bg-white' : 'bg-gray-200',
                      )}
                    >
                      <TimelineIcon className="p-3" variant="ghost" color={isAfter.value ? 'orange' : 'green'}>
                        {isAfter.value ? (
                          <MdRadioButtonChecked className="size-5" />
                        ) : (
                          <CircleCheckIcon className="size-5" />
                        )}
                      </TimelineIcon>
                      <div className="flex w-full items-center justify-between">
                        <div className="text-left">
                          <div className="text-lg font-bold text-kitaph-primary">After Loan Stage</div>
                          {/* <div className="text-xs font-light text-muted-foreground">{`Start Date at January, 2023 | End Date at May 31, 2024`}</div> */}
                        </div>
                        <div className="text-lg font-bold text-kitaph-primary">
                          {creditScoreDetails['credit_score']['after_stage']['farmer_stage_score'].value}%
                        </div>
                      </div>
                    </TimelineHeader>
                  </AccordionTrigger>

                  <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                    <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                      <div>Credit History</div>
                      <div className="text-kitaph-primary">{`${creditScoreDetails['credit_score']['after_stage']['rules']['creditHistory']['farmer_category_score'].value} / ${creditScoreDetails['credit_score']['after_stage']['rules']['creditHistory']['category_max_score'].value}`}</div>
                    </div>
                    <div className="grid max-w-lg gap-1 pl-10">
                      <div className="flex justify-between">
                        <div>No. of Loan Cycle</div>
                        <div>{`${creditScoreDetails['credit_score']['after_stage']['rules']['creditHistory']['data']['totalLoanCycles']['farmer_score'].value}/${creditScoreDetails['credit_score']['after_stage']['rules']['creditHistory']['data']['totalLoanCycles']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Repayment Behavior</div>
                        <div>{`${creditScoreDetails['credit_score']['after_stage']['rules']['creditHistory']['data']['repaymentBehavior']['farmer_score'].value}/${creditScoreDetails['credit_score']['after_stage']['rules']['creditHistory']['data']['repaymentBehavior']['max_score'].value}`}</div>
                      </div>
                    </div>

                    <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                      <div>Agriculture Activity</div>
                      <div className="text-kitaph-primary">{`${creditScoreDetails['credit_score']['after_stage']['rules']['agricultureActivity']['farmer_category_score'].value} / ${creditScoreDetails['credit_score']['after_stage']['rules']['agricultureActivity']['category_max_score'].value}`}</div>
                    </div>
                    <div className="grid max-w-lg gap-1 pl-10">
                      <div className="flex justify-between">
                        <div>Target Yield Achievement</div>
                        <div>{`${creditScoreDetails['credit_score']['after_stage']['rules']['agricultureActivity']['data']['targetYieldAchievement']['farmer_score'].value}/${creditScoreDetails['credit_score']['after_stage']['rules']['agricultureActivity']['data']['targetYieldAchievement']['max_score'].value}`}</div>
                      </div>
                    </div>
                  </AccordionContent>
                </TimelineItem>
              </AccordionItem>

              {/* During */}
              <AccordionItem className="" value="during">
                <TimelineItem className={cn('min-h-[5.4rem]')}>
                  <TimelineConnector className="!w-[78px]" />
                  <AccordionTrigger className="">
                    <TimelineHeader
                      className={cn(
                        'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                        isDuring.value ? 'bg-white' : 'bg-gray-200',
                      )}
                    >
                      <TimelineIcon className="p-3" variant="ghost" color={isDuring.value ? 'orange' : 'green'}>
                        {isDuring.value ? (
                          <MdRadioButtonChecked className="size-5" />
                        ) : (
                          <CircleCheckIcon className="size-5" />
                        )}
                      </TimelineIcon>
                      <div className="flex w-full items-center justify-between">
                        <div className="text-left">
                          <div className="text-lg font-bold text-kitaph-primary">During Loan Stage</div>
                          {/* <div className="text-xs font-light text-muted-foreground">{`Start Date at January, 2023 | End Date at May 31, 2024`}</div> */}
                        </div>
                        <div className="text-lg font-bold text-kitaph-primary">
                          {creditScoreDetails['credit_score']['during_stage']['farmer_stage_score'].value}%
                        </div>
                      </div>
                    </TimelineHeader>
                  </AccordionTrigger>

                  <AccordionContent className="relative z-0 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                    <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                      <div>Agriculture Activity</div>
                      <div className="text-kitaph-primary">{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['farmer_category_score'].value} / ${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['category_max_score'].value}`}</div>
                    </div>
                    <div className="grid max-w-lg gap-1 pl-10">
                      <div className="flex justify-between">
                        <div>Land Preparation</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['landPreparation']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['landPreparation']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Followed recommended no. of seeds per area</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['seedsPerArea']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['seedsPerArea']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Fertilization Schedule</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['fertilizationSchedule']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['fertilizationSchedule']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Fertilization Volume</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['fertilizationVolume']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['fertilizationVolume']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Crop Protection Schedule</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['cropProtectionSchedule']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['cropProtectionSchedule']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Crop Protection Volume</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['cropProtectionVolume']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['cropProtectionVolume']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Harvest Projection</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['harvestProjection']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['agricultureActivity']['data']['harvestProjection']['max_score'].value}`}</div>
                      </div>
                    </div>

                    <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                      <div>Transaction Records</div>
                      <div className="text-kitaph-primary">{`${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['farmer_category_score'].value} / ${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['category_max_score'].value}`}</div>
                    </div>
                    <div className="grid max-w-lg gap-1 pl-10">
                      <div className="flex justify-between">
                        <div>Marketplace Transactions</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['data']['marketplaceTransaction']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['data']['marketplaceTransaction']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Trading Post Transactions</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['data']['tradingPostTransaction']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['data']['tradingPostTransaction']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Sales Transactions</div>
                        <div>{`${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['data']['saleOfHarvestToKita']['farmer_score'].value}/${creditScoreDetails['credit_score']['during_stage']['rules']['transactionRecords']['data']['saleOfHarvestToKita']['max_score'].value}`}</div>
                      </div>
                    </div>
                  </AccordionContent>
                </TimelineItem>
              </AccordionItem>

              {/* Before */}
              <AccordionItem className="" value="before">
                <TimelineItem className={cn('min-h-[5.4rem]')}>
                  <AccordionTrigger className="">
                    <TimelineHeader
                      className={cn(
                        'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                        isBefore.value ? 'bg-white' : 'bg-gray-200',
                      )}
                    >
                      <TimelineIcon className="p-3" variant="ghost" color={isBefore.value ? 'orange' : 'green'}>
                        {isBefore.value ? (
                          <MdRadioButtonChecked className="size-5" />
                        ) : (
                          <CircleCheckIcon className="size-5" />
                        )}
                      </TimelineIcon>
                      <div className="flex w-full items-center justify-between">
                        <div className="text-left">
                          <div className="text-lg font-bold text-kitaph-primary">Before Loan Stage</div>
                          {/* <div className="text-xs font-light text-muted-foreground">{`Start Date at January, 2023 | End Date at May 31, 2024`}</div> */}
                        </div>
                        <div className="text-lg font-bold text-kitaph-primary">
                          {creditScoreDetails['credit_score']['before_stage']['farmer_stage_score'].value}%
                        </div>
                      </div>
                    </TimelineHeader>
                  </AccordionTrigger>

                  <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                    <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                      <div>Account Profile</div>
                      <div className="text-kitaph-primary">{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['farmer_category_score'].value} / ${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['category_max_score'].value}`}</div>
                    </div>
                    <div className="grid max-w-lg gap-1 pl-10">
                      <div className="flex justify-between">
                        <div>Basic Information</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['basicInformation']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['basicInformation']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Career & Academic</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['careerAcademics']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['careerAcademics']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Identification Documents</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['governmentIds']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['governmentIds']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Family Profile</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['familyProfile']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['familyProfile']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Biometrics</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['biometrics']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['biometrics']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Property Ownership</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['propertyOwnership']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['propertyOwnership']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Farm Details</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['farmDetails']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['farmDetails']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Vouch from Cooperative Leader</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['vouchByLeader']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['vouchByLeader']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Vouch from Agriculture Office</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['vouchByMao']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['accountProfile']['data']['vouchByMao']['max_score'].value}`}</div>
                      </div>
                    </div>

                    <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                      <div>Agriculture Activity</div>
                      <div className="text-kitaph-primary">{`${creditScoreDetails['credit_score']['before_stage']['rules']['agricultureActivity']['farmer_category_score'].value} / ${creditScoreDetails['credit_score']['before_stage']['rules']['agricultureActivity']['category_max_score'].value}`}</div>
                    </div>
                    <div className="grid max-w-lg gap-1 pl-10">
                      <div className="flex justify-between">
                        <div>Farm Geo-tagging</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['agricultureActivity']['data']['farmGeoTagging']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['agricultureActivity']['data']['farmGeoTagging']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Good Agriculture Practices (GAP) compliance</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['agricultureActivity']['data']['goodAgriculturalPractices']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['agricultureActivity']['data']['goodAgriculturalPractices']['max_score'].value}`}</div>
                      </div>
                    </div>

                    <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                      <div>Transaction Records</div>
                      <div className="text-kitaph-primary">{`${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['farmer_category_score'].value} / ${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['category_max_score'].value}`}</div>
                    </div>
                    <div className="grid max-w-lg gap-1 pl-10">
                      <div className="flex justify-between">
                        <div>Marketplace Transactions</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['data']['marketplaceTransaction']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['data']['marketplaceTransaction']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Trading Post Transactions</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['data']['tradingPostTransaction']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['data']['tradingPostTransaction']['max_score'].value}`}</div>
                      </div>
                      <div className="flex justify-between">
                        <div>Sales Transactions</div>
                        <div>{`${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['data']['saleOfHarvestToKita']['farmer_score'].value}/${creditScoreDetails['credit_score']['before_stage']['rules']['transactionRecords']['data']['saleOfHarvestToKita']['max_score'].value}`}</div>
                      </div>
                    </div>
                  </AccordionContent>
                </TimelineItem>
              </AccordionItem>
            </Accordion>
          </Timeline>
        </div>
      )}
    </div>
  );
}
