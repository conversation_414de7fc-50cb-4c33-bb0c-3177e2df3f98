'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import axios from '@/lib/api';

import { DEFAULT_ADMIN_ACTIVE_MENU, getUserType } from '../constants';
import { useGlobalState } from '../store';
import { useGlobalStatePersist } from '../store/persist';

export default function useFinance() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';
  const isFinance = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'finance';

  const getAccountInfo = async (id) => {
    try {
      const [_accountInfo] = await Promise.all(
        [axios.get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/user/farmer/view/${id}`)].map((p) =>
          p.then((res) => res.data.data),
        ),
      );
      gState.selected.accountInfo['info'].set(_accountInfo);
      gStateP.selected.account.info.set(_accountInfo);

      if (_accountInfo === null) {
        if (isFinance) {
          router.push('/finance');
        } else if (isAdmin) {
          router.push('/admin');
          gStateP.admin.activeMenu.set(DEFAULT_ADMIN_ACTIVE_MENU);
        }
        return;
      }

      if (_accountInfo) {
        await Promise.all([
          getTradingPost(_accountInfo.id),
          getMarketplace(_accountInfo.id),
          getFinance(_accountInfo.id),
          getSalesTransaction(_accountInfo.id),
          getLoanPayments(_accountInfo.id),
        ]);
      }
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getAccountInfo: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getTradingPost = async (id) => {
    try {
      let _tradingPost = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/tradingpost/history/view/${id}`, {
          params: {
            page: gState.selected.accountInfo.pagination.tradingPost.page.value,
            pageSize: gState.selected.accountInfo.pagination.tradingPost.pageSize.value,
            isManual: 0,
          },
        })
        .then((res) => res.data.data);

      _tradingPost.data = _tradingPost.data.map((item) => {
        let grossSales = 0;
        let grossProfit = 0;

        if (item.crops.length === 0) {
          return {
            ...item,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        } else {
          const updatedCrops = item.crops.map((crop) => {
            const weight = (item.entry_weight - item.exit_weight) * (crop.percentage / 100);

            let price = Number(crop.selling_price);
            let prodPrice = Number(crop.production_price);

            if (weight > 0) {
              grossSales += weight * price;
              grossProfit += weight * (price - prodPrice);
            }

            return {
              ...crop,
              gross_sales: weight > 0 ? weight * price : 0,
              gross_profit: weight > 0 ? weight * (price - prodPrice) : 0,
              weight,
              price,
              production_price: prodPrice,
            };
          });

          return {
            ...item,
            crops: updatedCrops,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        }
      });

      gState.selected.accountInfo.tradingPost.set(_tradingPost);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getAccountInfo: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getSalesTransaction = async (id) => {
    try {
      let _tradingPost = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/tradingpost/history/view/${id}`, {
          params: {
            page: gState.selected.accountInfo.pagination.tradingPost.page.value,
            pageSize: gState.selected.accountInfo.pagination.tradingPost.pageSize.value,
            isManual: 1,
          },
        })
        .then((res) => res.data.data);

      _tradingPost.data = _tradingPost.data.map((item) => {
        let grossSales = 0;
        let grossProfit = 0;

        if (item.crops.length === 0) {
          return {
            ...item,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        } else {
          const updatedCrops = item.crops.map((crop) => {
            const weight = (item.entry_weight - item.exit_weight) * (crop.percentage / 100);

            let price = Number(crop.selling_price);
            let prodPrice = Number(crop.production_price);

            if (weight > 0) {
              grossSales += weight * price;
              grossProfit += weight * (price - prodPrice);
            }

            return {
              ...crop,
              gross_sales: weight > 0 ? weight * price : 0,
              gross_profit: weight > 0 ? weight * (price - prodPrice) : 0,
              weight,
              price,
              production_price: prodPrice,
            };
          });

          return {
            ...item,
            crops: updatedCrops,
            gross_sales: grossSales,
            gross_profit: grossProfit,
          };
        }
      });

      gState.selected.accountInfo.sales.set(_tradingPost);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getAccountInfo: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getMarketplace = async (id) => {
    try {
      const _marketplace = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/marketplace/history/view/${id}`, {
          params: {
            page: gState.selected.accountInfo.pagination.marketplace.page.value,
            pageSize: gState.selected.accountInfo.pagination.marketplace.pageSize.value,
          },
        })
        .then((res) => res.data.data);

      gState.selected.accountInfo.marketplace.set(_marketplace);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getAccountInfo: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getFinance = async (id) => {
    try {
      const _finance = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/topup/history/view/${id}`, {
          params: {
            page: gState.selected.accountInfo.pagination.finance.page.value,
            pageSize: gState.selected.accountInfo.pagination.finance.pageSize.value,
          },
        })
        .then((res) => res.data.data);

      const _firstData = _finance.data.find(
        (item) =>
          item.type === 1 &&
          item.topupRequest &&
          item.topupRequest.status === 1 &&
          item.topupRequest.payment_status !== 1,
      );
      gStateP.selected['currentLoan'].set(_firstData);
      gState.selected.accountInfo.finance.set(_finance);

      // get credit score
      try {
        const farmerId = gState.selected.accountInfo['info'].farmer.id.value;
        const response = await axios
          .get(
            `/${getUserType(gStateP['user']['user']['user_type'].value)}/creditscore/farmer/${farmerId}/history/viewAll`,
            {
              params: {
                page: 1,
                pageSize: 10,
              },
            },
          )
          .then((res) => res.data);
        console.log('creditScoreHistory: ', response);
        const creditScoreHistory = response.data;

        if (response.status === 1) {
          gState.creditScoring.creditScoreHistory.set(creditScoreHistory);
        }
      } catch (e) {
        console.error('getFinance: ', e);
      }

      gState.selected.accountInfo.finance.set(_finance);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getAccountInfo: ', error);

      // toast.error('Oops! Something went wrong', {
      //   description: error,
      // });
    }
  };

  const getLoanPayments = async (id) => {
    try {
      const _loanPayments = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanpayment/history/view/${id}`, {
          params: {
            page: gState.selected.accountInfo.pagination.loanPayment.page.value,
            pageSize: gState.selected.accountInfo.pagination.loanPayment.pageSize.value,
          },
        })
        .then((res) => res.data.data);

      gState.selected.accountInfo.loanPayments.set(_loanPayments);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('getLoanPayments: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const getLoanPaymentDetails = async (id) => {
    try {
      const _details = await axios
        .get(`/${getUserType(gStateP['user']['user']['user_type'].value)}/loanpayment/view/${id}`)
        .then((res) => res.data.data);
      console.log('getLoanPaymentDetails: ', _details);

      gStateP.selected['loanPayment'].set(_details);
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.log('getLoanPaymentDetails: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  const updateCreditScore = async (data) => {
    try {
      const _data = await axios
        .post(`/${getUserType(gStateP['user']['user']['user_type'].value)}/user/farmer/creditscore/update`, data, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then((res) => res.data.message);

      setTimeout(() => {
        getAccountInfo(data.userId);
      }, 3000);

      console.log('updateCreditScore: ', _data);
      if (_data.length > 0) {
        toast.success('Credit score updated successfully');
      }
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('updateCreditScore: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
      });
    }
  };

  return {
    getAccountInfo,
    getTradingPost,
    getMarketplace,
    getFinance,
    getSalesTransaction,
    updateCreditScore,
    getLoanPayments,
    getLoanPaymentDetails,
  };
}
