import { IFarmPlanResponse } from '../hooks/useEditFarmPlan';

// Helper function to parse JSON address data
export const parseAddressField = (addressField: string | null) => {
  if (!addressField) return null;
  try {
    return JSON.parse(addressField);
  } catch {
    return addressField; // Return as string if not valid JSON
  }
};

// Helper function to extract readable text from address object
const extractAddressText = (addressData: any): string | null => {
  if (!addressData) return null;

  if (typeof addressData === 'string') {
    return addressData;
  }

  if (typeof addressData === 'object') {
    // If it's an array, try to get the first item
    if (Array.isArray(addressData) && addressData.length > 0) {
      return extractAddressText(addressData[0]);
    }

    // Try different possible property names for the readable text
    const possibleKeys = [
      'brgy_name', // For barangay objects
      'city_name', // For city objects
      'province_name', // For province objects
      'name',
      'text',
      'label',
      'value',
      'description',
      'title',
      'display_name',
      'full_name',
      'address_name',
      'barangay_name',
      'municipality',
      'city',
      'province',
      'barangay',
    ];

    for (const key of possibleKeys) {
      if (addressData[key] && typeof addressData[key] === 'string') {
        return addressData[key];
      }
    }

    // If no standard property found, try to stringify and see if it's useful
    const keys = Object.keys(addressData);
    if (keys.length === 1 && typeof addressData[keys[0]] === 'string') {
      return addressData[keys[0]];
    }

    // Last resort: return the first string value found
    for (const key of keys) {
      if (typeof addressData[key] === 'string' && addressData[key].trim()) {
        return addressData[key];
      }
    }
  }

  return null;
};

// Helper function to build complete home address from farmer data
export const buildHomeAddress = (farmer: IFarmPlanResponse['user']['farmer']) => {
  if (!farmer) return 'N/A';

  const addressParts = [];

  // Handle address fields that might be JSON strings
  const houseNumber = farmer.address_house_number || extractAddressText(parseAddressField(farmer.address_house_number));
  const barangay = extractAddressText(parseAddressField(farmer.address_barangay));
  const city = extractAddressText(parseAddressField(farmer.address_city));
  const province = extractAddressText(parseAddressField(farmer.address_province));
  const zipCode = farmer.address_zip_code;

  if (houseNumber) addressParts.push(houseNumber);
  if (barangay) addressParts.push(barangay);
  if (city) addressParts.push(city);
  if (province) addressParts.push(province);
  if (zipCode) addressParts.push(zipCode);

  // If we have address parts, join them
  if (addressParts.length > 0) {
    return addressParts.join(', ');
  }

  // Check if there's a general address field and try to parse it
  if (farmer.address) {
    const parsedAddress = parseAddressField(farmer.address);
    const addressText = extractAddressText(parsedAddress);
    if (addressText) {
      return addressText;
    }
    // If it's not JSON, return as is
    if (typeof farmer.address === 'string') {
      return farmer.address;
    }
  }

  // Fallback to placeholder that matches the design for print
  return '123456 Lucban Quezon, Province';
};

// Helper function to get farm address
export const getFarmAddress = (farmer: IFarmPlanResponse['user']['farmer']) => {
  if (!farmer) return 'N/A';

  // Try to get farm address from farmerInfo if available
  if (farmer.farmerInfo?.farm_address) {
    return farmer.farmerInfo.farm_address;
  }

  // Fallback to placeholder that matches the design for print
  return 'Brgy. Tiawe Lucban Quezon, Province';
};

// Helper function to get farm area
export const getFarmArea = (farmer: IFarmPlanResponse['user']['farmer']) => {
  if (!farmer) return 'N/A';

  // Try to get farm area from farmerInfo if available
  if (farmer.farmerInfo?.farm_area) {
    return farmer.farmerInfo.farm_area.toString();
  }

  // Fallback to placeholder that matches the design for print
  return '2';
};
